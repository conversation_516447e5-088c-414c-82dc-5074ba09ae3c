{"__meta": {"id": "01K4V8HNTGSB090A0NV0YFGAPF", "datetime": "2025-09-11 02:09:21", "utime": **********.744872, "method": "GET", "uri": "/login", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 4, "start": **********.14742, "end": **********.744888, "duration": 0.5974681377410889, "duration_str": "597ms", "measures": [{"label": "Booting", "start": **********.14742, "relative_start": 0, "end": **********.305896, "relative_end": **********.305896, "duration": 0.****************, "duration_str": "158ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.305906, "relative_start": 0.*****************, "end": **********.74489, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "439ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.322961, "relative_start": 0.*****************, "end": **********.325856, "relative_end": **********.325856, "duration": 0.002894878387451172, "duration_str": "2.89ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: auth.login", "start": **********.680522, "relative_start": 0.****************, "end": **********.680522, "relative_end": **********.680522, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "19MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "9.x", "tooltip": {"Laravel Version": "9.52.20", "PHP Version": "8.1.15", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 1, "nb_templates": 1, "templates": [{"name": "auth.login", "param_count": null, "params": [], "start": **********.680451, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fauth%2Flogin.blade.php:1", "ajax": false, "filename": "login.blade.php", "line": "?"}}]}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": []}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8001/login", "action_name": "login", "controller_action": "App\\Http\\Controllers\\Auth\\LoginController@showLoginForm", "uri": "GET login", "controller": "App\\Http\\Controllers\\Auth\\LoginController@showLoginForm<a href=\"vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fvendor%2Flaravel%2Fui%2Fauth-backend%2FAuthenticatesUsers.php:19\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "file": "<a href=\"vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fvendor%2Flaravel%2Fui%2Fauth-backend%2FAuthenticatesUsers.php:19\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/laravel/ui/auth-backend/AuthenticatesUsers.php:19-22</a>", "middleware": "web", "duration": "597ms", "peak_memory": "20MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-990139631 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-990139631\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1480883671 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1480883671\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-202159233 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko/20100101 Firefox/142.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"63 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlpMNm5uRmVLT3hyaG91UVN3ZlV3Z2c9PSIsInZhbHVlIjoiZWNuT0ZOcGxVVW1od2pxSzEzOFFpVDNXQzJVeGk1bndCMEkycmpyNm5vMExoN3orY0c3K3hFcG5kL251N3AvaFpiTWdHV2ozYnNSZCtEQkVBdVNuTitRLzRLVnkwZTRnMVpNbTRaM3hadk1ROHZaQkYvMkxyU1NiL3VmQ1NwOFciLCJtYWMiOiJlNjA2NzY0ZmExZGYwNjZkM2E2MTExZmZmYjNiZDJhYjNlYTg3YzFhMmZkY2UzNGE0MGM4NTNkMjRhMzMzNDUyIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjJmbmdYdTdFMThLMVlqQ3FlTkV1WFE9PSIsInZhbHVlIjoibGJoOVJzeDIzS2R5cWUvSHB2N1JJT3NnTnNRN1M5Z25MRktXUXAzcmhiWkZIdGJLQmk5dklyUHo1OVp2THp5NXo5S2NlY1B0MnVVcUYrcGMzTnlhRWpJOTczV1ZjSzhSajZITGZlRmFHT0tzNTc1eDJhSFJqakJwL1ppK2NrTXMiLCJtYWMiOiI0YzExOTk3MWJlNGE1NzU2YWVlYzg5MWI1MWMwZTQ3YTc0OWUyMjY2ZTQxZTRjOGM1OTQ0Yzg0YmZkMTI0ZThkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=0, i</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-202159233\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-50155775 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">O7QA2abL13yR10Po388ohHBaIMEnJ4XCQDSbyx0F</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tpQFeJkYVZSWGY71H41qbOij9V9XWYxizctlIuj6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-50155775\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-270683954 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 11 Sep 2025 02:09:21 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-270683954\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1177495746 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">O7QA2abL13yR10Po388ohHBaIMEnJ4XCQDSbyx0F</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01K4V8HN2TRK9PSR0JZNF38PWD</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1177495746\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8001/login", "action_name": "login", "controller_action": "App\\Http\\Controllers\\Auth\\LoginController@showLoginForm"}, "badge": null}}