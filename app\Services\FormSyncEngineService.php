<?php

namespace App\Services;

use App\Models\FormSubmission;
use App\Models\FormSubmissionSync;
use App\Models\AuditLog;
use App\Models\ErrorLog;
use App\Models\FormIntegrationSetting;
use App\Jobs\SyncFormSubmissionJob;
use App\Jobs\RetryFailedSyncJob;
use Illuminate\Support\Facades\Log;

class FormSyncEngineService
{
    protected FormIntegrationService $formIntegrationService;
    protected AuditLoggerService $auditLogger;
    protected ErrorLoggerService $errorLogger;

    public function __construct(
        FormIntegrationService $formIntegrationService,
        AuditLoggerService $auditLogger,
        ErrorLoggerService $errorLogger
    ) {
        $this->formIntegrationService = $formIntegrationService;
        $this->auditLogger = $auditLogger;
        $this->errorLogger = $errorLogger;
    }

    /**
     * Sync form submission to all configured endpoints
     */
    public function syncToAllEndpoints(FormSubmission $submission): void
    {
        $pendingSyncs = $submission->syncs()
            ->whereIn('status', [
                FormSubmissionSync::STATUS_PENDING,
                FormSubmissionSync::STATUS_RETRYING
            ])
            ->where(function ($query) {
                $query->whereNull('next_retry_at')
                      ->orWhere('next_retry_at', '<=', now());
            })
            ->get();

        foreach ($pendingSyncs as $sync) {
            SyncFormSubmissionJob::dispatch($sync);
        }

        Log::info('Dispatched sync jobs for form submission', [
            'submission_uuid' => $submission->uuid,
            'sync_jobs_count' => $pendingSyncs->count(),
        ]);
    }

    /**
     * Sync form submission to a specific endpoint
     */
    public function syncToEndpoint(
        FormSubmission $submission,
        FormIntegrationSetting $integration
    ): FormSubmissionSync {
        $sync = $submission->syncs()
            ->where('form_integration_setting_id', $integration->id)
            ->first();

        if (!$sync) {
            $sync = $submission->syncs()->create([
                'form_integration_setting_id' => $integration->id,
                'status' => FormSubmissionSync::STATUS_PENDING,
            ]);
        }

        $this->processSync($sync);

        return $sync;
    }

    /**
     * Process a single sync operation
     */
    public function processSync(FormSubmissionSync $sync): void
    {
        $startTime = microtime(true);

        try {
            // Mark sync as started
            $sync->markAsStarted();

            $this->auditLogger->logSyncEvent(
                $sync,
                AuditLog::EVENT_SYNC_STARTED,
                [
                    'integration_name' => $sync->getIntegrationName(),
                    'target_system' => $sync->getTargetSystem(),
                    'retry_count' => $sync->retry_count,
                ]
            );

            // Get the form submission data
            $formData = $sync->formSubmission->submission_data;
            $integration = $sync->formIntegrationSetting;

            // Process through the existing FormIntegrationService
            $result = $this->formIntegrationService->processFormSubmission($formData, $integration);

            $processingTime = (int) ((microtime(true) - $startTime) * 1000);

            if ($result['success']) {
                // Mark as completed
                $sync->markAsCompleted($result['data'], $processingTime);

                $this->auditLogger->logSyncEvent(
                    $sync,
                    AuditLog::EVENT_SYNC_COMPLETED,
                    [
                        'processing_time_ms' => $processingTime,
                        'response_data' => $result['data'],
                    ]
                );

                Log::info('Form sync completed successfully', [
                    'sync_uuid' => $sync->uuid,
                    'submission_uuid' => $sync->formSubmission->uuid,
                    'integration_id' => $integration->id,
                    'processing_time_ms' => $processingTime,
                ]);

            } else {
                // Mark as failed
                $errorMessage = implode('; ', $result['errors']);
                $sync->markAsFailed($errorMessage, null, $processingTime);

                $this->auditLogger->logSyncEvent(
                    $sync,
                    AuditLog::EVENT_SYNC_FAILED,
                    [
                        'processing_time_ms' => $processingTime,
                        'errors' => $result['errors'],
                    ]
                );

                $this->errorLogger->logSyncError(
                    $sync,
                    new \Exception($errorMessage),
                    ErrorLog::LEVEL_ERROR,
                    ErrorLog::TYPE_API_ERROR,
                    [
                        'integration_errors' => $result['errors'],
                        'form_data_keys' => array_keys($formData),
                    ]
                );

                // Schedule retry if possible
                if ($sync->canRetry()) {
                    $this->scheduleRetry($sync);
                }
            }

        } catch (\Exception $e) {
            $processingTime = (int) ((microtime(true) - $startTime) * 1000);

            // Mark as failed
            $sync->markAsFailed($e->getMessage(), (string) $e->getCode(), $processingTime);

            $this->auditLogger->logSyncEvent(
                $sync,
                AuditLog::EVENT_SYNC_FAILED,
                [
                    'processing_time_ms' => $processingTime,
                    'exception_class' => get_class($e),
                    'exception_message' => $e->getMessage(),
                ]
            );

            $this->errorLogger->logSyncError(
                $sync,
                $e,
                ErrorLog::LEVEL_ERROR,
                $this->determineErrorType($e)
            );

            // Schedule retry if possible
            if ($sync->canRetry()) {
                $this->scheduleRetry($sync);
            }

            Log::error('Form sync failed', [
                'sync_uuid' => $sync->uuid,
                'submission_uuid' => $sync->formSubmission->uuid,
                'error' => $e->getMessage(),
                'processing_time_ms' => $processingTime,
            ]);
        }
    }

    /**
     * Retry a failed sync
     */
    public function retrySync(FormSubmissionSync $sync): void
    {
        if (!$sync->canRetry()) {
            Log::warning('Attempted to retry sync that cannot be retried', [
                'sync_uuid' => $sync->uuid,
                'retry_count' => $sync->retry_count,
                'max_retries' => $sync->max_retries,
            ]);
            return;
        }

        $this->processSync($sync);
    }

    /**
     * Schedule a sync for retry
     */
    protected function scheduleRetry(FormSubmissionSync $sync): void
    {
        $sync->scheduleRetry();

        $this->auditLogger->logSyncEvent(
            $sync,
            AuditLog::EVENT_SYNC_RETRIED,
            [
                'retry_count' => $sync->retry_count,
                'next_retry_at' => $sync->next_retry_at,
            ]
        );

        // Dispatch retry job
        RetryFailedSyncJob::dispatch($sync)->delay($sync->next_retry_at);

        Log::info('Sync scheduled for retry', [
            'sync_uuid' => $sync->uuid,
            'retry_count' => $sync->retry_count,
            'next_retry_at' => $sync->next_retry_at,
        ]);
    }

    /**
     * Update sync status
     */
    public function updateSyncStatus(
        FormSubmissionSync $sync,
        string $status,
        array $data = []
    ): void {
        $oldStatus = $sync->status;

        $sync->update(array_merge(['status' => $status], $data));

        $this->auditLogger->logSyncEvent(
            $sync,
            AuditLog::EVENT_STATUS_UPDATED,
            [
                'old_status' => $oldStatus,
                'new_status' => $status,
                'additional_data' => $data,
            ]
        );
    }

    /**
     * Get sync statistics for monitoring
     */
    public function getSyncStatistics(int $hours = 24): array
    {
        $since = now()->subHours($hours);

        $stats = [
            'total_syncs' => FormSubmissionSync::where('created_at', '>=', $since)->count(),
            'completed_syncs' => FormSubmissionSync::where('created_at', '>=', $since)
                ->where('status', FormSubmissionSync::STATUS_COMPLETED)->count(),
            'failed_syncs' => FormSubmissionSync::where('created_at', '>=', $since)
                ->where('status', FormSubmissionSync::STATUS_FAILED)->count(),
            'pending_syncs' => FormSubmissionSync::where('created_at', '>=', $since)
                ->where('status', FormSubmissionSync::STATUS_PENDING)->count(),
            'retrying_syncs' => FormSubmissionSync::where('created_at', '>=', $since)
                ->where('status', FormSubmissionSync::STATUS_RETRYING)->count(),
        ];

        $stats['success_rate'] = $stats['total_syncs'] > 0
            ? round(($stats['completed_syncs'] / $stats['total_syncs']) * 100, 2)
            : 0;

        return $stats;
    }

    /**
     * Determine error type based on exception
     */
    protected function determineErrorType(\Exception $e): string
    {
        $message = strtolower($e->getMessage());

        if (str_contains($message, 'timeout') || str_contains($message, 'timed out')) {
            return ErrorLog::TYPE_TIMEOUT_ERROR;
        }

        if (str_contains($message, 'network') || str_contains($message, 'connection')) {
            return ErrorLog::TYPE_NETWORK_ERROR;
        }

        if (str_contains($message, 'unauthorized') || str_contains($message, 'authentication')) {
            return ErrorLog::TYPE_AUTHENTICATION_ERROR;
        }

        if (str_contains($message, 'forbidden') || str_contains($message, 'permission')) {
            return ErrorLog::TYPE_AUTHORIZATION_ERROR;
        }

        if (str_contains($message, 'validation') || str_contains($message, 'invalid')) {
            return ErrorLog::TYPE_VALIDATION_ERROR;
        }

        return ErrorLog::TYPE_SYSTEM_ERROR;
    }
}
