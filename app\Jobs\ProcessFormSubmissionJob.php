<?php

namespace App\Jobs;

use App\Models\FormSubmission;
use App\Services\FormSubmissionService;
use App\Services\ErrorLoggerService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessFormSubmissionJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected FormSubmission $submission;

    /**
     * The number of times the job may be attempted.
     */
    public int $tries = 3;

    /**
     * The maximum number of seconds the job can run.
     */
    public int $timeout = 120;

    /**
     * Create a new job instance.
     */
    public function __construct(FormSubmission $submission)
    {
        $this->submission = $submission;
        $this->onQueue('form_submissions');
    }

    /**
     * Execute the job.
     */
    public function handle(FormSubmissionService $formSubmissionService): void
    {
        Log::info('Processing form submission job started', [
            'submission_uuid' => $this->submission->uuid,
            'job_id' => $this->job->getJobId(),
        ]);

        try {
            $formSubmissionService->processSubmission($this->submission);

            Log::info('Form submission job completed successfully', [
                'submission_uuid' => $this->submission->uuid,
                'job_id' => $this->job->getJobId(),
            ]);

        } catch (\Exception $e) {
            Log::error('Form submission job failed', [
                'submission_uuid' => $this->submission->uuid,
                'job_id' => $this->job->getJobId(),
                'error' => $e->getMessage(),
                'attempt' => $this->attempts(),
            ]);

            // Log error to our error logging system
            app(ErrorLoggerService::class)->logSubmissionError(
                $this->submission,
                $e,
                \App\Models\ErrorLog::LEVEL_ERROR,
                \App\Models\ErrorLog::TYPE_QUEUE_ERROR,
                [
                    'job_id' => $this->job->getJobId(),
                    'attempt' => $this->attempts(),
                    'max_tries' => $this->tries,
                ]
            );

            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Form submission job failed permanently', [
            'submission_uuid' => $this->submission->uuid,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
        ]);

        // Update submission status to failed
        $this->submission->update([
            'status' => FormSubmission::STATUS_FAILED,
        ]);

        // Log the permanent failure
        app(ErrorLoggerService::class)->logSubmissionError(
            $this->submission,
            $exception,
            \App\Models\ErrorLog::LEVEL_CRITICAL,
            \App\Models\ErrorLog::TYPE_QUEUE_ERROR,
            [
                'permanent_failure' => true,
                'attempts' => $this->attempts(),
            ]
        );
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        return [
            'form_submission',
            'submission:' . $this->submission->uuid,
            'form:' . $this->submission->form_id,
        ];
    }
}
