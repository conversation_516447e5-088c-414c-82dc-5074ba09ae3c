@props([
    'route' => '#',
    'text' => null,
    'size' => 'sm',
    'class' => '',
    'icon' => 'pencil',
    'title' => null,
    'target' => '_self',
    'iconOnly' => true, // Default to icon only for listing views
])

<x-buttons.button 
    :href="$route"
    variant="primary" 
    :size="$size"
    :icon="$icon"
    :class="$class"
    :title="$title ?? __('global.edit')"
    :target="$target"
    :iconOnly="$iconOnly"
    data-bs-toggle="tooltip"
>
    @if($slot->isNotEmpty())
        {{ $slot }}
    @else
        {{ $text ?? (!$iconOnly ? __('global.edit') : '') }}
    @endif
</x-buttons.button>
