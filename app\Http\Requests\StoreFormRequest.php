<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class StoreFormRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        $user = Auth::user();
        return $user->isAdmin() || $user->isSuperAdmin();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'title' => 'required|string|max:255',
            // 'device_view' => 'required|string|max:255',
            'module' => 'required|string|max:255',
            'content' => 'required|json',
            'user_groups' => 'nullable|array',
            'user_groups.*' => 'exists:user_groups,id',
            // 'is_active' => 'boolean',
            'redirect_to' => 'nullable|exists:forms,id',
            'status'       => 'required|in:draft,published,archived',
            'is_standard'  => 'sometimes|boolean',
            'process_type' => "nullable|string|max:255"

        ];
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        if ($this->has('is_active')) {
            $this->merge([
                'is_active' => true
            ]);
        } else {
            $this->merge([
                'is_active' => false
            ]);
        }
    }
}
