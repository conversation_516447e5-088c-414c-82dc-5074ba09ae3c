@extends('layouts.admin')
@section('pageTitle', __('global.view') . ' ' . __('cruds.integrationConfiguration.title_singular'))

@push('styles')
<style>
    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #e3e6f0;
    }
    .card-title {
        margin-bottom: 0;
    }
    .form-group {
        margin-bottom: 1.5rem;
    }
    .form-control-static {
        min-height: 38px;
        padding-top: 7px;
        padding-bottom: 7px;
        margin-bottom: 0;
    }
    .badge {
        font-size: 0.875rem;
        padding: 0.4em 0.6em;
    }
    .table th {
        background-color: #f8f9fa;
        width: 200px;
    }
    pre {
        background-color: #f8f9fa;
        padding: 10px;
        border-radius: 4px;
        max-height: 200px;
        overflow-y: auto;
    }
    .user-info {
        font-size: 0.875rem;
        color: #6c757d;
    }
    .timestamp {
        display: block;
        font-weight: 600;
        color: #343a40;
    }
</style>
@endpush

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-cog"></i> {{ __('cruds.integrationConfiguration.title_singular') }}: {{ $integrationConfiguration->name }}
                    </h5>
                    <div class="d-flex gap-2">
                        <x-buttons.edit 
                            :route="route('admin.integration-configurations.edit', $integrationConfiguration)" 
                            :text="__('global.edit')" 
                            class="btn-sm"
                        />
                        <x-buttons.back 
                            :route="route('admin.integration-configurations.index')" 
                            :text="__('global.back_to_list')" 
                            class="btn-sm"
                        />
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group row">
                                <label class="col-md-4 col-form-label">{{ trans('cruds.integrationConfiguration.fields.integration_method') }}</label>
                                <div class="col-md-8">
                                    <p class="form-control-static">{{ $integrationConfiguration->name }}</p>
                                </div>
                            </div>
                            
                            <div class="form-group row">
                                <label class="col-md-4 col-form-label">{{ trans('cruds.integrationConfiguration.fields.endpoint_url') }}</label>
                                <div class="col-md-8">
                                    <p class="form-control-static">
                                        <a href="{{ $integrationConfiguration->endpoint_url }}" target="_blank" class="text-primary">
                                            {{ $integrationConfiguration->endpoint_url }}
                                        </a>
                                    </p>
                                </div>
                            </div>
                            
                            <div class="form-group row">
                                <label class="col-md-4 col-form-label">{{ trans('cruds.integrationConfiguration.fields.process_type') }}</label>
                                <div class="col-md-8">
                                    <p class="form-control-static">
                                        {{ $integrationConfiguration->process_type_label }}
                                    </p>
                                </div>
                            </div>
                            
                            <div class="form-group row">
                                <label class="col-md-4 col-form-label">{{ trans('cruds.integrationConfiguration.fields.endpoint_type') }}</label>
                                <div class="col-md-8">
                                    <p class="form-control-static">
                                        {{ $integrationConfiguration->integration_method_label }}
                                    </p>
                                </div>
                            </div>
                            
                            <div class="form-group row">
                                <label class="col-md-4 col-form-label">{{ trans('cruds.integrationConfiguration.fields.is_active') }}</label>
                                <div class="col-md-8">
                                    <p class="form-control-static">
                                        <span class="badge bg-{{ $integrationConfiguration->is_active ? 'success' : 'secondary' }}">
                                            {{ $integrationConfiguration->is_active ? trans('global.active') : trans('global.inactive') }}
                                        </span>
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group row">
                                <label class="col-md-4 col-form-label">{{ trans('cruds.integrationConfiguration.fields.external_system') }}</label>
                                <div class="col-md-8">
                                    <p class="form-control-static">
                                        {{ $integrationConfiguration->external_system_label }}
                                    </p>
                                </div>
                            </div>
                            
                            <div class="form-group row">
                                <label class="col-md-4 col-form-label">{{ trans('cruds.integrationConfiguration.fields.description') }}</label>
                                <div class="col-md-8">
                                    <p class="form-control-static">
                                        {{ $integrationConfiguration->description ?? '' }}
                                    </p>
                                </div>
                            </div>
                            
                            <!-- Created/Updated Information -->
                            <div class="form-group row">
                                <label class="col-md-4 col-form-label">{{ trans('global.created_at') }}</label>
                                <div class="col-md-8">
                                    <p class="form-control-static">
                                        {{ $integrationConfiguration->created_at ? $integrationConfiguration->created_at->format(config('panel.date_format')) : '' }}
                                    </p>
                                </div>
                            </div>
                            
                            <div class="form-group row">
                                <label class="col-md-4 col-form-label">{{ trans('global.created_by') }}</label>
                                <div class="col-md-8">
                                    <p class="form-control-static">
                                        {{ $integrationConfiguration->createdBy ? $integrationConfiguration->createdBy->name : '' }}
                                    </p>
                                </div>
                            </div>
                            
                            <div class="form-group row">
                                <label class="col-md-4 col-form-label">{{ trans('global.updated_at') }}</label>
                                <div class="col-md-8">
                                    <p class="form-control-static">
                                        {{ $integrationConfiguration->updated_at ? $integrationConfiguration->updated_at->format(config('panel.date_format')) : '' }}
                                    </p>
                                </div>
                            </div>
                            
                            <div class="form-group row">
                                <label class="col-md-4 col-form-label">{{ trans('global.updated_by') }}</label>
                                <div class="col-md-8">
                                    <p class="form-control-static">
                                        {{ $integrationConfiguration->updatedBy ? $integrationConfiguration->updatedBy->name : '' }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">{{ trans('cruds.integrationConfiguration.fields.request_fields') }}</h6>
                                </div>
                                <div class="card-body">
                                    @if($integrationConfiguration->request_fields)
                                        @php
                                            $fields = is_string($integrationConfiguration->request_fields)
                                                ? json_decode($integrationConfiguration->request_fields, true)
                                                : $integrationConfiguration->request_fields;
                                        @endphp
                                        @if(is_array($fields) && count($fields) > 0)
                                            <div class="table-responsive">
                                                <table class="table table-bordered table-striped table-hover">
                                                    <thead class="thead-light">
                                                        <tr>
                                                            <th>{{ trans('cruds.fields.field_name') }}</th>
                                                            <th>{{ trans('cruds.fields.data_type') }}</th>
                                                            <th>{{ trans('cruds.fields.max_length') }}</th>
                                                            <th>{{ trans('cruds.fields.required') }}</th>
                                                            <th>{{ trans('cruds.fields.default') }}</th>
                                                            <th>{{ trans('cruds.fields.description') }}</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        @foreach($fields as $field)
                                                            <tr>
                                                                <td><strong>{{ $field['name'] ?? trans('global.n_a') }}</strong></td>
                                                                <td>{{ $field['datatype'] ?? 'string' }}</td>
                                                                <td class="text-end">{{ $field['maxlength'] ?? trans('global.n_a') }}</td>
                                                                <td>{{ ($field['required'] ?? false) ? trans('global.yes') : trans('global.no') }}
                                                                </td>
                                                                <td>{{ $field['default'] ?? trans('global.n_a') }}</td>
                                                                <td>{{ $field['description'] ?? trans('global.n_a') }}</td>
                                                            </tr>
                                                        @endforeach
                                                    </tbody>
                                                </table>
                                            </div>
                                        @else
                                            <span class="text-muted">{{ trans('global.no_field_mapping_configured') }}</span>
                                        @endif
                                    @else
                                        <span class="text-muted">{{ trans('global.no_field_mapping_configured') }}</span>
                                    @endif
                </tbody>
            </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
