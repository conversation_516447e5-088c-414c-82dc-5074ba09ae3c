@extends('layouts.admin')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-history"></i> Audit Logs
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-primary btn-sm" id="refreshTable">
                            <i class="fas fa-sync"></i> Refresh
                        </button>
                        <button type="button" class="btn btn-info btn-sm" data-toggle="modal" data-target="#statisticsModal">
                            <i class="fas fa-chart-bar"></i> Statistics
                        </button>
                        <button type="button" class="btn btn-success btn-sm" data-toggle="modal" data-target="#exportModal">
                            <i class="fas fa-download"></i> Export
                        </button>
                    </div>
                </div>

                <!-- Filters -->
                <div class="card-body border-bottom">
                    <form id="filterForm" class="row">
                        <div class="col-md-2">
                            <label for="event_type">Event Type</label>
                            <select class="form-control form-control-sm" id="event_type" name="event_type">
                                <option value="">All Events</option>
                                @foreach($eventTypes as $eventType)
                                    <option value="{{ $eventType['value'] }}">{{ $eventType['label'] }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="actor_type">Actor Type</label>
                            <select class="form-control form-control-sm" id="actor_type" name="actor_type">
                                <option value="">All Actors</option>
                                @foreach($actorTypes as $actorType)
                                    <option value="{{ $actorType['value'] }}">{{ $actorType['label'] }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="submission_uuid">Submission UUID</label>
                            <input type="text" class="form-control form-control-sm" id="submission_uuid" name="submission_uuid" placeholder="Search UUID...">
                        </div>
                        <div class="col-md-2">
                            <label for="date_from">Date From</label>
                            <input type="date" class="form-control form-control-sm" id="date_from" name="date_from">
                        </div>
                        <div class="col-md-2">
                            <label for="date_to">Date To</label>
                            <input type="date" class="form-control form-control-sm" id="date_to" name="date_to">
                        </div>
                        <div class="col-md-2">
                            <label>&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary btn-sm">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                                <button type="button" class="btn btn-secondary btn-sm" id="clearFilters">
                                    <i class="fas fa-times"></i> Clear
                                </button>
                            </div>
                        </div>
                    </form>
                </div>

                <div class="card-body">
                    <div class="table-responsive">
                        <table id="auditLogsTable" class="table table-bordered table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Event Type</th>
                                    <th>Description</th>
                                    <th>Actor</th>
                                    <th>Form Submission</th>
                                    <th>Integration</th>
                                    <th>Created At</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Audit Log Details Modal -->
<div class="modal fade" id="auditLogModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Audit Log Details</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" id="auditLogDetails">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Modal -->
<div class="modal fade" id="statisticsModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Audit Log Statistics</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" id="statisticsContent">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Export Modal -->
<div class="modal fade" id="exportModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Export Audit Logs</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="exportForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="export_format">Format</label>
                        <select class="form-control" id="export_format" name="format" required>
                            <option value="csv">CSV</option>
                            <option value="json">JSON</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="export_event_type">Event Type</label>
                        <select class="form-control" id="export_event_type" name="event_type">
                            <option value="">All Events</option>
                            @foreach($eventTypes as $eventType)
                                <option value="{{ $eventType['value'] }}">{{ $eventType['label'] }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="export_actor_type">Actor Type</label>
                        <select class="form-control" id="export_actor_type" name="actor_type">
                            <option value="">All Actors</option>
                            @foreach($actorTypes as $actorType)
                                <option value="{{ $actorType['value'] }}">{{ $actorType['label'] }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="export_date_from">Date From</label>
                                <input type="date" class="form-control" id="export_date_from" name="date_from">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="export_date_to">Date To</label>
                                <input type="date" class="form-control" id="export_date_to" name="date_to">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-download"></i> Export
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Initialize DataTable
    var table = $('#auditLogsTable').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '{{ route("admin.audit-logs.index") }}',
            data: function(d) {
                d.event_type = $('#event_type').val();
                d.actor_type = $('#actor_type').val();
                d.submission_uuid = $('#submission_uuid').val();
                d.date_from = $('#date_from').val();
                d.date_to = $('#date_to').val();
            }
        },
        columns: [
            { data: 'event_type_badge', name: 'event_type', orderable: true },
            { data: 'event_description', name: 'event_description', orderable: false },
            { data: 'actor_info', name: 'actor_type', orderable: true },
            { data: 'submission_info', name: 'form_submission_id', orderable: false },
            { data: 'integration_info', name: 'form_submission_sync_id', orderable: false },
            { data: 'created_at', name: 'created_at', orderable: true },
            { data: 'action', name: 'action', orderable: false, searchable: false }
        ],
        order: [[5, 'desc']],
        pageLength: 25,
        responsive: true,
        dom: 'Bfrtip',
        buttons: []
    });

    // Filter form submission
    $('#filterForm').on('submit', function(e) {
        e.preventDefault();
        table.draw();
    });

    // Clear filters
    $('#clearFilters').on('click', function() {
        $('#filterForm')[0].reset();
        table.draw();
    });

    // Refresh table
    $('#refreshTable').on('click', function() {
        table.ajax.reload();
    });

    // View audit log details
    $(document).on('click', '.view-details', function() {
        var id = $(this).data('id');
        
        $.ajax({
            url: '{{ route("admin.audit-logs.show", ":id") }}'.replace(':id', id),
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    var data = response.data;
                    var html = `
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Event Information</h6>
                                <table class="table table-sm">
                                    <tr><td><strong>UUID:</strong></td><td>${data.uuid}</td></tr>
                                    <tr><td><strong>Event Type:</strong></td><td><span class="badge badge-info">${data.event_type_label}</span></td></tr>
                                    <tr><td><strong>Description:</strong></td><td>${data.event_description}</td></tr>
                                    <tr><td><strong>Created At:</strong></td><td>${data.created_at}</td></tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6>Actor Information</h6>
                                <table class="table table-sm">
                                    <tr><td><strong>Actor Type:</strong></td><td><span class="badge badge-secondary">${data.actor_type_label}</span></td></tr>
                                    <tr><td><strong>Actor Name:</strong></td><td>${data.actor_name}</td></tr>
                                    <tr><td><strong>IP Address:</strong></td><td>${data.ip_address || 'N/A'}</td></tr>
                                    <tr><td><strong>User Agent:</strong></td><td><small>${data.user_agent || 'N/A'}</small></td></tr>
                                </table>
                            </div>
                        </div>`;

                    if (data.form_submission) {
                        html += `
                            <div class="row mt-3">
                                <div class="col-12">
                                    <h6>Form Submission</h6>
                                    <table class="table table-sm">
                                        <tr><td><strong>UUID:</strong></td><td>${data.form_submission.uuid}</td></tr>
                                        <tr><td><strong>Form:</strong></td><td>${data.form_submission.form_title}</td></tr>
                                        <tr><td><strong>Status:</strong></td><td>${data.form_submission.status}</td></tr>
                                        <tr><td><strong>Submitted At:</strong></td><td>${data.form_submission.submitted_at}</td></tr>
                                    </table>
                                </div>
                            </div>`;
                    }

                    if (data.integration) {
                        html += `
                            <div class="row mt-3">
                                <div class="col-12">
                                    <h6>Integration</h6>
                                    <table class="table table-sm">
                                        <tr><td><strong>Name:</strong></td><td>${data.integration.name}</td></tr>
                                        <tr><td><strong>Target System:</strong></td><td>${data.integration.target_system}</td></tr>
                                        <tr><td><strong>Status:</strong></td><td>${data.integration.status}</td></tr>
                                    </table>
                                </div>
                            </div>`;
                    }

                    if (data.old_values || data.new_values) {
                        html += `
                            <div class="row mt-3">
                                <div class="col-12">
                                    <h6>Changes</h6>
                                    <div class="row">`;
                        
                        if (data.old_values) {
                            html += `
                                <div class="col-md-6">
                                    <strong>Old Values:</strong>
                                    <pre class="bg-light p-2">${JSON.stringify(data.old_values, null, 2)}</pre>
                                </div>`;
                        }
                        
                        if (data.new_values) {
                            html += `
                                <div class="col-md-6">
                                    <strong>New Values:</strong>
                                    <pre class="bg-light p-2">${JSON.stringify(data.new_values, null, 2)}</pre>
                                </div>`;
                        }
                        
                        html += `</div></div></div>`;
                    }

                    if (data.metadata) {
                        html += `
                            <div class="row mt-3">
                                <div class="col-12">
                                    <h6>Metadata</h6>
                                    <pre class="bg-light p-2">${JSON.stringify(data.metadata, null, 2)}</pre>
                                </div>
                            </div>`;
                    }

                    $('#auditLogDetails').html(html);
                    $('#auditLogModal').modal('show');
                }
            },
            error: function() {
                alert('Failed to load audit log details');
            }
        });
    });

    // Load statistics
    $('#statisticsModal').on('show.bs.modal', function() {
        $('#statisticsContent').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading...</div>');
        
        $.ajax({
            url: '{{ route("admin.audit-logs.statistics") }}',
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    var data = response.data;
                    var html = `
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Event Statistics (Last ${data.period_hours} hours)</h6>
                                <table class="table table-sm">
                                    <tr><td>Total Events:</td><td><strong>${data.statistics.total_events}</strong></td></tr>
                                    <tr><td>User Events:</td><td><strong>${data.statistics.user_events}</strong></td></tr>
                                    <tr><td>Admin Events:</td><td><strong>${data.statistics.admin_events}</strong></td></tr>
                                    <tr><td>System Events:</td><td><strong>${data.statistics.system_events}</strong></td></tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6>Event Types</h6>
                                <table class="table table-sm">`;
                    
                    data.event_types.forEach(function(eventType) {
                        html += `<tr><td>${eventType.label}:</td><td><strong>${eventType.count}</strong></td></tr>`;
                    });
                    
                    html += `</table></div></div>`;
                    
                    if (data.recent_activity.length > 0) {
                        html += `
                            <div class="row mt-3">
                                <div class="col-12">
                                    <h6>Recent Activity</h6>
                                    <div class="list-group">`;
                        
                        data.recent_activity.forEach(function(activity) {
                            html += `
                                <div class="list-group-item">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">${activity.event_type_label}</h6>
                                        <small>${activity.created_at}</small>
                                    </div>
                                    <p class="mb-1">${activity.description}</p>
                                    <small>by ${activity.actor_name}</small>
                                </div>`;
                        });
                        
                        html += `</div></div></div>`;
                    }
                    
                    $('#statisticsContent').html(html);
                }
            },
            error: function() {
                $('#statisticsContent').html('<div class="alert alert-danger">Failed to load statistics</div>');
            }
        });
    });

    // Export form submission
    $('#exportForm').on('submit', function(e) {
        e.preventDefault();
        
        var formData = $(this).serialize();
        var url = '{{ route("admin.audit-logs.export") }}?' + formData;
        
        // Create a temporary link to trigger download
        var link = document.createElement('a');
        link.href = url;
        link.download = '';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        $('#exportModal').modal('hide');
    });
});
</script>
@endpush
