// if(is_report)
if (typeof is_report != "undefined" && is_report==false) {
    console.log(is_report); 
} else {
    $(document).prop('title', 'R: ' + $(document).prop('title'));

}

function initializeDefaultsReports() {

    $.extend(true, $.fn.dataTable.defaults, {
        ordering: false,
        searching: false
    });

    $('.select2').each(function () {
        var placeholder = $(this).data('placeholder') ?? 'ALL';
        $(this).select2({
            allowClear: true,
            placeholder
        }).val("*").trigger("change");

        $(this).on("change", function () {
            if (!$(this).prop('multiple')) {
                if ($(this).val() === null) {
                    $(this).val('*').trigger('change')
                }
            }
        })
    });

    $('.select2_ajax_reports').each(function () {
        var url = $(this).data('url');
        var type = $(this).data('type');
        $(this).select2({
            minimumInputLength: 0,
            width: "100%",
            allowClear: true,
            ajax: {
                url: url,
                dataType: 'json',
                type: 'GET',
                delay: 200,
                data: function (params) {
                    var query = {
                        q: params.term,
                        type: type,
                        page: params.page || 1
                    }
                    return query;
                }
            }
        });
    });
}

function escapeCommas(row) {
    const rowArray = row.map(string => {
        if (string && (string.toString().includes(',') || string.toString().includes(';') || string.toString().includes('●'))) return `"${string}"`
        return string
    })
    return rowArray.join(',')
}

function downloadFile(blob, name) {
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.style.display = 'none'
    link.href = url
    link.download = name
    document.body.appendChild(link)
    link.click()
    window.URL.revokeObjectURL(url)
    link.parentNode.removeChild(link)
}

function pdfLayout(reportTitle, orientation, tableHeader, tableData, isChunkedData = false, reportHeaderData, runDateTime, tableStartY = 0, customCellFormat = false) {
    var pdf = new jsPDF({
        unit: 'mm',
        orientation,
        format: 'letter'
    });

    if (!isChunkedData) tableData = orientation === 'l' ? chunkArray(tableData) : chunkArray(tableData, 41)

    for (var currentPage = 0; currentPage < tableData.length; currentPage++) {
        pdf.setFontSize(12);
        pdf.setFontType('bold');
        pdf.text('HIGHLAND Tractor Parts Inc.', 10, 10);

        pdf.setFontSize(8);
        pdf.setFontType('normal');
        pdf.text('180 4th Ave, corner J. Teodoro St, Caloocan City Tel: 8361-6359 to 61;| Fax: 8361-9690', 10, 15);

        var headerLineX = orientation === 'l' ? 270 : 207;
        pdf.line(9, 17, headerLineX, 17);
        pdf.line(9, 17.2, headerLineX, 17.2);
        pdf.line(9, 17.4, headerLineX, 17.4);

        pdf.setFontType('bold');
        pdf.setFontSize(12);
        pdf.text(reportTitle, 10, 22);
        pdf.setFontType('normal');

        var pagesX = orientation === 'l' ? 240 : 175;
        pdf.setFontSize(10);
        pdf.text(`Page: ${currentPage + 1}`, pagesX, 22);

        // PDF Data
        reportHeaderData.length && reportHeaderData.forEach((textData) => {
            pdf.setFontType(textData.textDesign);
            pdf.setFontSize(textData.fontSize);
            pdf.text(textData.text, textData.x, textData.y);
            pdf.setFontType('normal');
        })

        var columnStyles = {}

        tableHeader.forEach((val) => {
            if ('columnStyle' in val) {
                columnStyles[`${val.dataKey}`] = val.columnStyle;
            }
        });

        // Table Headers Lines
        if (tableStartY) {
            pdf.line(9, tableStartY, headerLineX, tableStartY);
            pdf.line(9, (tableStartY + 6.6), headerLineX, (tableStartY + 6.6));
        } else {
            pdf.line(9, 29, headerLineX, 29);
            pdf.line(9, 35.6, headerLineX, 35.6);
        }

        pdf.autoTable(tableHeader, tableData[currentPage], {
            startY: tableStartY ? tableStartY : 29,
            theme: 'plain',
            margin: { right: 9, left: 9 },
            bodyStyles: {
                cellPadding: 1
            },
            styles: {
                fontSize: 8
            },
            headerStyles: { halign: 'center', overflow: 'hidden' },
            columnStyles,
            createdCell: function (cell, data) {
                if (typeof customCellFormat === 'function') {
                    customCellFormat(pdf, cell, data.row.raw)
                }
            },
            drawCell: function (row, data) {
                if (typeof customCellFormat === 'function') {
                    customCellFormat(pdf, row, data.row.raw)
                }

                if ('border' in data.row.raw) {
                    var yLine = row.y;
                    var xLine = orientation === 'l' ? 270 : 207;

                    pdf.setDrawColor(0, 0, 0);
                    if (data.row.raw.border === 'top' || data.row.raw.border === 'topBottom') {
                        pdf.line(9, yLine, xLine, yLine);
                    }
                    if (data.row.raw.border === 'bottom' || data.row.raw.border === 'topBottom') {
                        pdf.line(9, yLine + 4.8, xLine, yLine + 4.8);
                    }
                }

                if ('style' in data.row.raw) {
                    var style = Object.keys(data.row.raw.style);
                    style.forEach((valKey) => {
                        data.row.cells[`${valKey}`].styles.halign = `${data.row.raw.style[`${valKey}`]}`;
                    })
                }
            }
        });

        var runDateY = 200;
        var runTimeY = 204;
        var signatoriesY = 197;
        var signLineY = 207;
        var preparedByX = [76, 120];
        var NotedByX = [146, 190];
        var approvedByX = [216, 260];

        if (orientation === 'p') {
            runDateY = 263;
            runTimeY = 267;
            signatoriesY = 259;
            signLineY = 269;
            preparedByX = [56, 96];
            NotedByX = [106, 146];
            approvedByX = [156, 196];
        }

        pdf.setFontSize(8.5);
        pdf.setFontType('bold');
        pdf.text(`Run date: ${runDateTime.runDate}`, 10, runDateY);
        pdf.text(`Run time: ${runDateTime.runTime}`, 10, runTimeY);

        pdf.setDrawColor(0, 0, 0);
        pdf.text('Prepared by:', preparedByX[0], signatoriesY);
        pdf.line(preparedByX[0], signLineY, preparedByX[1], signLineY);
        pdf.text('Noted by:', NotedByX[0], signatoriesY);
        pdf.line(NotedByX[0], signLineY, NotedByX[1], signLineY);
        pdf.text('Approved by:', approvedByX[0], signatoriesY);
        pdf.line(approvedByX[0], signLineY, approvedByX[1], signLineY);

        if (!(currentPage + 1 >= tableData.length)) {
            pdf.addPage();
        }
    }

    pdf.autoPrint();
    window.open(pdf.output('bloburl'), 'name');
}

function chunkArray(array, chunkSize = 29) {
    var chunkedArray = [];
    for (var i = 0, len = array.length; i < len; i += chunkSize)
        chunkedArray.push(array.slice(i, i + chunkSize));
    return chunkedArray;
}

function mapTotalByKey(array, key) {
    return array.reduce(function (accumulator, curr) {
        return parseFloat(accumulator) + parseFloat(curr[`${key}`])
    }, 0)
}

function sumValuesByIndex(arrays, indexToSum) {
    let sum = 0;
    for (let i = 0; i < arrays.length; i++) {
        const innerArray = arrays[i];
        if (indexToSum < innerArray.length) {
        sum += innerArray[indexToSum];
        }
    }
    return sum;
}

function testInputsValidation(inputs = []) {
    $(inputs.join(", ")).on('change', function () {
        var componentId = $(this).context.id
        if ($(this).valid()) {
            $(this).removeClass('error')
            $(this).addClass('valid')
            $(`#${componentId}-error`).remove()
        }
    })
}

// Format Numbers
// Params:
// num - number value (String, number)
// minDeci - Minimum fraction digits (default value is 2)
// maxDeci - Maximum fraction digits (default value is 2)
function numFormatter(num, minDeci = 2, maxDeci = 2) {
    if (typeof num === 'string') {
        num = num.trim()
    }

    if (isNaN(num) || num === '') {
        return num;
    }

    num = parseFloat(num)
    if (num === 0 || isNaN(num)) {
        return '';
    } else {
        return num.toLocaleString('en-US', { minimumFractionDigits: minDeci, maximumFractionDigits: maxDeci });
    }
}

function addValuesOfObjectsPerKeys(mainObject, objectToAdd) {
    var objectToAddKeys = Object.keys(objectToAdd);

    if (objectToAddKeys.length) {
        objectToAddKeys.forEach((key) => {
            if (key in mainObject) {
                mainObject[`${key}`] = mainObject[`${key}`] + objectToAdd[`${key}`];
            } else {
                mainObject[`${key}`] = objectToAdd[`${key}`];
            }
        });
    }

    return mainObject;
}

function cleanString(varObject) {
    var stringsToReplace = {
        '&amp;': '&',
        '&#039;': '\'',
        '"': '\'\''
    };
    if (typeof varObject === 'object' && varObject !== null) {
        for (const key in varObject) {
            Object.keys(stringsToReplace).forEach((char) => {
                if (varObject[`${key}`] && typeof varObject[`${key}`] === 'string') {
                    varObject[`${key}`] = varObject[`${key}`].replaceAll(char, stringsToReplace[char]);
                }
            });
        }
    }

    if (typeof varObject === 'string' && varObject !== null) {
        Object.keys(stringsToReplace).forEach((char) => {
            if (varObject && typeof varObject === 'string') {
                varObject = varObject.replaceAll(char, stringsToReplace[char]); // removed key since, we are trying to clean simple string already, no key is needed
            }
        });
    }

    return varObject;
}

function cloneDataTable(dataTable) {
    let tableDataClone = [];

    for (var i = 0; i < dataTable.length; i++) {
        tableDataClone.push({ ...dataTable[i] });
    }

    return tableDataClone;
}