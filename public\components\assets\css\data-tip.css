[data-tip] {
  position: relative;
  overflow: visible;
}
[data-tip]:before,
[data-tip]:after {
  position: absolute;
  -webkit-transform: translateX(-50%);
      -ms-transform: translateX(-50%);
          transform: translateX(-50%);
  opacity: 0;
  filter: alpha(opacity=0);
  transition: opacity 0.3s ease, -webkit-transform 0.3s ease, visibility 0.3s ease;
  transition: opacity 0.3s ease, transform 0.3s ease, visibility 0.3s ease;
  visibility: hidden;
  font-size: 12px;
  line-height: 12px;
}
[data-tip]:before {
  content: '';
  border: 6px solid transparent;
}
[data-tip]:after {
  content: attr(data-tip);
  padding: 7px 10px;
  background-color: #333;
  display: block;
  color: #fff;
  white-space: nowrap;
}
[data-tip]:hover:before,
[data-tip].data-tip-visible:before,
[data-tip]:hover:after,
[data-tip].data-tip-visible:after {
  opacity: 1;
  filter: alpha(opacity=100);
  visibility: visible;
  z-index: 9999;
}
[data-tip].data-tip-bottom:before,
[data-tip].data-tip-bottom:after {
  top: 100%;
  left: 50%;
}
[data-tip].data-tip-bottom:before {
  margin-top: -12px;
  border-bottom-color: #333;
}
[data-tip].data-tip-bottom:hover:before,
[data-tip].data-tip-bottom.data-tip-visible:before,
[data-tip].data-tip-bottom:hover:after,
[data-tip].data-tip-bottom.data-tip-visible:after {
  -webkit-transform: translateX(-50%) translateY(8px);
      -ms-transform: translateX(-50%) translateY(8px);
          transform: translateX(-50%) translateY(8px);
}
[data-tip].data-tip-top:before,
[data-tip].data-tip-top:after {
  bottom: 100%;
  left: 50%;
}
[data-tip].data-tip-top:before {
  margin-bottom: -12px;
  border-top-color: #333;
}
[data-tip].data-tip-top:hover:before,
[data-tip].data-tip-top.data-tip-visible:before,
[data-tip].data-tip-top:hover:after,
[data-tip].data-tip-top.data-tip-visible:after {
  -webkit-transform: translateX(-50%) translateY(-8px);
      -ms-transform: translateX(-50%) translateY(-8px);
          transform: translateX(-50%) translateY(-8px);
}
[data-tip].data-tip-left:before,
[data-tip].data-tip-left:after {
  top: 50%;
  right: 100%;
  -webkit-transform: translateY(-50%);
      -ms-transform: translateY(-50%);
          transform: translateY(-50%);
}
[data-tip].data-tip-left:before {
  margin-right: -12px;
  border-left-color: #333;
}
[data-tip].data-tip-left:hover:before,
[data-tip].data-tip-left.data-tip-visible:before,
[data-tip].data-tip-left:hover:after,
[data-tip].data-tip-left.data-tip-visible:after {
  -webkit-transform: translateY(-50%) translateX(-8px);
      -ms-transform: translateY(-50%) translateX(-8px);
          transform: translateY(-50%) translateX(-8px);
}
[data-tip].data-tip-right:before,
[data-tip].data-tip-right:after {
  top: 50%;
  left: 100%;
  -webkit-transform: translateY(-50%);
      -ms-transform: translateY(-50%);
          transform: translateY(-50%);
}
[data-tip].data-tip-right:before {
  margin-left: -12px;
  border-right-color: #333;
}
[data-tip].data-tip-right:hover:before,
[data-tip].data-tip-right.data-tip-visible:before,
[data-tip].data-tip-right:hover:after,
[data-tip].data-tip-right.data-tip-visible:after {
  -webkit-transform: translateY(-50%) translateX(8px);
      -ms-transform: translateY(-50%) translateX(8px);
          transform: translateY(-50%) translateX(8px);
}
[data-tip].data-tip-success:after {
  background-color: #458746;
}
[data-tip].data-tip-success.data-tip-top:before {
  border-top-color: #458746;
}
[data-tip].data-tip-success.data-tip-left:before {
  border-left-color: #458746;
}
[data-tip].data-tip-success.data-tip-right:before {
  border-right-color: #458746;
}
[data-tip].data-tip-success.data-tip-bottom:before {
  border-bottom-color: #458746;
}
[data-tip].data-tip-warning:after {
  background-color: #c09854;
}
[data-tip].data-tip-warning.data-tip-top:before {
  border-top-color: #c09854;
}
[data-tip].data-tip-warning.data-tip-left:before {
  border-left-color: #c09854;
}
[data-tip].data-tip-warning.data-tip-right:before {
  border-right-color: #c09854;
}
[data-tip].data-tip-warning.data-tip-bottom:before {
  border-bottom-color: #c09854;
}
[data-tip].data-tip-danger:after {
  background-color: #b34e4d;
}
[data-tip].data-tip-danger.data-tip-top:before {
  border-top-color: #b34e4d;
}
[data-tip].data-tip-danger.data-tip-left:before {
  border-left-color: #b34e4d;
}
[data-tip].data-tip-danger.data-tip-right:before {
  border-right-color: #b34e4d;
}
[data-tip].data-tip-danger.data-tip-bottom:before {
  border-bottom-color: #b34e4d;
}
[data-tip].data-tip-info:after {
  background-color: #3986ac;
}
[data-tip].data-tip-info.data-tip-top:before {
  border-top-color: #3986ac;
}
[data-tip].data-tip-info.data-tip-left:before {
  border-left-color: #3986ac;
}
[data-tip].data-tip-info.data-tip-right:before {
  border-right-color: #3986ac;
}
[data-tip].data-tip-info.data-tip-bottom:before {
  border-bottom-color: #3986ac;
}
[data-tip].data-tip-no-animation:before,
[data-tip].data-tip-no-animation:after {
  transition: none;
}
[data-tip].data-tip-rounded:after {
  border-radius: 2px;
}
[data-tip].data-tip-fast:before,
[data-tip].data-tip-fast:after {
  transition: opacity 0.1s ease, -webkit-transform 0.1s ease, visibility 0.1s ease;
  transition: opacity 0.1s ease, transform 0.1s ease, visibility 0.1s ease;
}
[data-tip].data-tip-shadow:after {
  box-shadow: 4px 4px 8px rgba(0,0,0,0.3);
}
