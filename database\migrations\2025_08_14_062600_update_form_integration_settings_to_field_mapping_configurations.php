<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Rename table form_integration_settings to field_mapping_configurations
        Schema::rename('form_integration_settings', 'field_mapping_configurations');

        // First, check if the column exists
        $columnExists = DB::selectOne("SHOW COLUMNS FROM `form_submission_syncs` LIKE 'form_integration_setting_id'");

        if ($columnExists) {
            // Get the actual constraint name from information_schema
            $constraint = DB::selectOne("SELECT CONSTRAINT_NAME
                FROM information_schema.KEY_COLUMN_USAGE
                WHERE TABLE_NAME = 'form_submission_syncs'
                AND COLUMN_NAME = 'form_integration_setting_id'
                AND CONSTRAINT_NAME <> 'PRIMARY'");

            // Drop the foreign key constraint if it exists
            if ($constraint) {
                DB::statement("ALTER TABLE `form_submission_syncs` DROP FOREIGN KEY `{$constraint->CONSTRAINT_NAME}`");
            }

            // Drop old index if it exists (MySQL 5.7+ compatible way)
            $indexExists = DB::selectOne("SHOW INDEX FROM `form_submission_syncs` WHERE Key_name = 'idx_form_submission_syncs_integration_id'");
            if ($indexExists) {
                DB::statement('ALTER TABLE `form_submission_syncs` DROP INDEX `idx_form_submission_syncs_integration_id`');
            }

            // Rename the column
            DB::statement('ALTER TABLE `form_submission_syncs` CHANGE `form_integration_setting_id` `field_mapping_configuration_id` BIGINT UNSIGNED NULL');

            // Add new index first (required before adding foreign key)
            $newIndexExists = DB::selectOne("SHOW INDEX FROM `form_submission_syncs` WHERE Key_name = 'idx_form_submission_syncs_configuration_id'");
            if (!$newIndexExists) {
                DB::statement('CREATE INDEX `idx_form_submission_syncs_configuration_id` ON `form_submission_syncs` (`field_mapping_configuration_id`)');
            }

            // Add foreign key if it doesn't exist
            $tableName = 'form_submission_syncs';
            $constraintName = 'form_submission_syncs_field_mapping_configuration_id_foreign';
            $constraintExists = DB::selectOne("SELECT * FROM information_schema.TABLE_CONSTRAINTS
                WHERE CONSTRAINT_SCHEMA = DATABASE()
                AND TABLE_NAME = '{$tableName}'
                AND CONSTRAINT_NAME = '{$constraintName}'
                AND CONSTRAINT_TYPE = 'FOREIGN KEY'");

            if (!$constraintExists) {
                DB::statement('ALTER TABLE `form_submission_syncs` ADD CONSTRAINT `form_submission_syncs_field_mapping_configuration_id_foreign`
                    FOREIGN KEY (`field_mapping_configuration_id`) REFERENCES `field_mapping_configurations` (`id`) ON DELETE CASCADE');
            }
        }

        // Drop old unique constraint if it exists (MySQL 5.7+ compatible way)
        $uniqueExists = DB::selectOne("SHOW INDEX FROM `form_submission_syncs` WHERE Key_name = 'unique_submission_integration'");
        if ($uniqueExists) {
            DB::statement('ALTER TABLE `form_submission_syncs` DROP INDEX `unique_submission_integration`');
        }

        // Add new unique constraint
        DB::statement('ALTER TABLE `form_submission_syncs` ADD CONSTRAINT `unique_submission_configuration`
            UNIQUE (`form_submission_id`, `field_mapping_configuration_id`)');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Get the actual constraint name from information_schema
        $constraint = DB::selectOne("SELECT CONSTRAINT_NAME
            FROM information_schema.KEY_COLUMN_USAGE
            WHERE TABLE_NAME = 'form_submission_syncs'
            AND COLUMN_NAME = 'field_mapping_configuration_id'
            AND CONSTRAINT_NAME <> 'PRIMARY'");

        // Drop the foreign key constraint if it exists
        if ($constraint) {
            DB::statement("ALTER TABLE `form_submission_syncs` DROP FOREIGN KEY `{$constraint->CONSTRAINT_NAME}`");
        }

        // Drop the new index if it exists
        $indexExists = DB::selectOne("SHOW INDEX FROM `form_submission_syncs` WHERE Key_name = 'idx_form_submission_syncs_configuration_id'");
        if ($indexExists) {
            DB::statement('ALTER TABLE `form_submission_syncs` DROP INDEX `idx_form_submission_syncs_configuration_id`');
        }

        // Drop the new unique constraint if it exists
        $uniqueExists = DB::selectOne("SHOW INDEX FROM `form_submission_syncs` WHERE Key_name = 'unique_submission_configuration'");
        if ($uniqueExists) {
            DB::statement('ALTER TABLE `form_submission_syncs` DROP INDEX `unique_submission_configuration`');
        }

        // Check if the column exists before trying to rename it
        $columnExists = DB::selectOne("SHOW COLUMNS FROM `form_submission_syncs` LIKE 'field_mapping_configuration_id'");
        if ($columnExists) {
            // Rename the column back
            DB::statement('ALTER TABLE `form_submission_syncs` CHANGE `field_mapping_configuration_id` `form_integration_setting_id` BIGINT UNSIGNED NULL');

            // Add back the old foreign key if the table exists
            $tableExists = DB::selectOne("SHOW TABLES LIKE 'form_integration_settings'");
            if ($tableExists) {
                DB::statement('ALTER TABLE `form_submission_syncs` ADD CONSTRAINT `form_submission_syncs_form_integration_setting_id_foreign`
                    FOREIGN KEY (`form_integration_setting_id`) REFERENCES `form_integration_settings` (`id`) ON DELETE CASCADE');
            }

            // Add back the old index
            $oldIndexExists = DB::selectOne("SHOW INDEX FROM `form_submission_syncs` WHERE Key_name = 'idx_form_submission_syncs_integration_id'");
            if (!$oldIndexExists) {
                DB::statement('CREATE INDEX `idx_form_submission_syncs_integration_id` ON `form_submission_syncs` (`form_integration_setting_id`)');
            }

            // Add back the old unique constraint if it doesn't exist
            $oldUniqueExists = DB::selectOne("SHOW INDEX FROM `form_submission_syncs` WHERE Key_name = 'unique_submission_integration'");
            if (!$oldUniqueExists) {
                DB::statement('ALTER TABLE `form_submission_syncs` ADD CONSTRAINT `unique_submission_integration`
                    UNIQUE (`form_submission_id`, `form_integration_setting_id`)');
            }
        }
    }
};
