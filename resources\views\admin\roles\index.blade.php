@extends('layouts.admin')
@section('pageTitle', trans('cruds.role.title'))

@section('content')

<div class="card card-default">

    <div class="card-header  separator">
        <div class="card-title mainheading"> <h4>{{ trans('cruds.role.title_singular') }} {{ trans('global.list') }}</h4>
        </div>
        <div class="card-controls">
            <ul>

                @can('role_create')
                <li>
                    <x-buttons.add 
                        :route="route('admin.roles.create')"
                        :text="trans('global.add') . ' ' . trans('cruds.role.title_singular')"
                    />
                </li>
                @endcan

            </ul>
        </div>
    </div>

    <div class="card-block">
        <div class="table-responsive">
            <table class=" table table-bordered table-striped table-hover datatable datatable-Role">
                <thead>
                    <tr>

                        <th>
                            {{ trans('cruds.role.fields.id') }}
                        </th>
                        <th>
                            {{ trans('cruds.role.fields.title') }}
                        </th>
                        {{-- <th>
                            {{ trans('cruds.role.fields.permissions') }}
                        </th> --}}
                        <th class="w-25">
                            &nbsp;
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($roles as $key => $role)
                    <tr data-entry-id="{{ $role->id }}">

                        <td>
                            {{ $role->id ?? '' }}
                        </td>
                        <td>
                            {{ $role->title ?? '' }}
                        </td>
                        {{-- <td>
                            @foreach($role->permissions as $key => $item)
                            <span class="badge badge-info">{{ $item->title }}</span>
                            @endforeach
                        </td> --}}
                        <td>
<!--                            @can('role_show')
                            <a class="btn btn-xs btn-primary" href="{{ route('admin.roles.show', $role->id) }}">
                                {{ trans('global.view') }}
                            </a>
                            @endcan-->

                            @can('role_edit')
                            <a class="btn btn-xs  btn-info" href="{{ route('admin.roles.edit', $role->id) }}">
                                {{ trans('global.edit') }}
                            </a>
                            @endcan

                            @can('role_delete')
                            <form class="mt-2" action="{{ route('admin.roles.destroy', $role->id) }}" method="POST" onsubmit="return confirm('{{ trans('global.areYouSure') }}');" style="display: inline-block;">
                                <input type="hidden" name="_method" value="DELETE">
                                <input type="hidden" name="_token" value="{{ csrf_token() }}">
                                <input type="submit" class="btn btn-block btn-xs btn-danger " value="{{ trans('global.delete') }}">
                            </form>
                            @endcan

                        </td>

                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
</div>



@endsection
@section('scripts')
@parent
<script>
    $(function () {
    let dtButtons = $.extend(true, [], $.fn.dataTable.defaults.buttons)
            @can('role_delete')
            let deleteButtonTrans = '{{ trans('global.datatables.delete') }}'
            let deleteButton = {
            text: deleteButtonTrans,
                    url: "{{ route('admin.roles.massDestroy') }}",
                    className: 'btn-danger',
                    action: function (e, dt, node, config) {
                    var ids = $.map(dt.rows({ selected: true }).nodes(), function (entry) {
                    return $(entry).data('entry-id')
                    });
                    if (ids.length === 0) {
                    alert('{{ trans('global.datatables.zero_selected') }}')

                            return
                    }

                    if (confirm('{{ trans('global.areYouSure') }}')) {
                    $.ajax({
                    headers: {'x-csrf-token': _token},
                            method: 'POST',
                            url: config.url,
                            data: { ids: ids, _method: 'DELETE' }})
                            .done(function () { location.reload() })
                    }
                    }
            }
    dtButtons.push(deleteButton)
            @endcan
           
//            dtButtons.splice(2, 6);

            $.extend(true, $.fn.dataTable.defaults, {
            orderCellsTop: true,
                    order: [[ 1, 'desc' ]],
                    pageLength: 100,
            });
    let table = $('.datatable-Role:not(.ajaxTable)').DataTable({ buttons: dtButtons });
    
            $('a[data-toggle="tab"]').on('shown.bs.tab click', function(e){
    $($.fn.dataTable.tables(true)).DataTable()
            .columns.adjust();
    });
    })

</script>
@endsection