@props([
    'route' => null,
    'text' => null,
    'size' => 'md',
    'class' => '',
    'icon' => 'arrow-left',
    'onclick' => null,
    'target' => '_self',
    'iconOnly' => false,
    'title' => null,
])

@php
    $attributes = $attributes->merge([
        'onclick' => $onclick ?: ($route ? null : 'window.history.back(); return false;'),
        'href' => $route ?: '#',
    ]);
@endphp

<x-buttons.button 
    variant="secondary" 
    :size="$size"
    :icon="$icon"
    :class="$class"
    :target="$target"
    :iconOnly="$iconOnly"
    :title="$title ?? __('global.back')"
    :attributes="$attributes"
    data-bs-toggle="tooltip"
>
    {{ $text ?? (!$iconOnly ? __('global.back') : '') }}
</x-buttons.button>
