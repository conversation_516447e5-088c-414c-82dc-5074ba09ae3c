
<?php $__env->startSection('pageTitle', __('cruds.fieldMappingConfiguration.title')); ?>

<?php $__env->startSection('content'); ?>
    <div class="card card-default">
        <div class="card-header separator">
            <div class="card-title mainheading">
                <h4><?php echo e(__('cruds.fieldMappingConfiguration.title')); ?> <?php echo e(trans('global.list')); ?></h4>
            </div>
            <div class="card-controls">
                <ul>
                    <?php if(auth()->user()->isAdmin() || auth()->user()->isSuperAdmin()): ?>
                        <li>
                            <?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.buttons.add','data' => ['route' => route('admin.field-mapping-configurations.create'),'title' => __('cruds.fieldMappingConfiguration.title_singular')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('buttons.add'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['route' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('admin.field-mapping-configurations.create')),'title' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(__('cruds.fieldMappingConfiguration.title_singular'))]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>

        <div class="card-block">
            <?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.datatable','data' => ['id' => 'field-mapping-configurations-table','columns' => $columns,'ajax' => ''.e(route('admin.field-mapping-configurations.index')).'','order' => [[9, 'desc']]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('datatable'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['id' => 'field-mapping-configurations-table','columns' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($columns),'ajax' => ''.e(route('admin.field-mapping-configurations.index')).'','order' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([[9, 'desc']])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('styles'); ?>
<style>
    .card-header h4 {
        margin: 0;
        color: #495057;
    }
    .table-responsive {
        overflow-x: auto;
        width: 100%;
        margin: 0;
    }
    .dataTables_wrapper .dataTables_paginate .paginate_button {
        padding: 0.25rem 0.5rem;
    }
    .table th {
        background-color: #f8f9fa;
        border-top: none;
    }
    .badge {
        font-size: 0.75em;
    }
    #field-mapping-configurations-table {
        width: 100% !important;
        margin: 0;
        table-layout: fixed;
    }
    .table {
        width: 100% !important;
        margin-bottom: 1rem;
        color: #212529;
    }
    @media (max-width: 768px) {
        .table-responsive {
            border: none;
        }
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
    <?php echo \Illuminate\View\Factory::parentPlaceholder('scripts'); ?>
    <script>
        $(function() {
            let dtButtons = $.extend(true, [], $.fn.dataTable.defaults.buttons);
            
            let deleteButton = {
                text: '<?php echo e(trans('global.datatables.delete')); ?>',
                url: "<?php echo e(route('admin.field-mapping-configurations.massDestroy')); ?>",
                className: 'btn-danger',
                action: function(e, dt, node, config) {
                    var ids = $.map(dt.rows({ selected: true }).data(), function(entry) {
                        return entry.id;
                    });

                    if (ids.length === 0) {
                        alert('<?php echo e(trans('global.datatables.zero_selected')); ?>');
                        return;
                    }

                    if (confirm('<?php echo e(trans('global.areYouSure')); ?>')) {
                        $.ajax({
                            headers: {'x-csrf-token': _token},
                            method: 'POST',
                            url: config.url,
                            data: { ids: ids, _method: 'DELETE' }
                        })
                        .done(function() {
                            location.reload();
                        });
                    }
                }
            };

            dtButtons.push(deleteButton);

            let dtOverrideGlobals = {
                buttons: dtButtons,
                processing: true,
                serverSide: true,
                retrieve: true,
                aaSorting: [],
                ajax: "<?php echo e(route('admin.field-mapping-configurations.index')); ?>",
                columns: [
                    { data: 'placeholder', name: 'placeholder' },
                    { data: 'name', name: 'name' },
                    { data: 'form.title', name: 'form.title' },
                    { data: 'endpoint_configuration', name: 'integrationConfiguration.name' },
                    { data: 'external_system', name: 'integrationConfiguration.external_system' },
                    { data: 'integration_method', name: 'integrationConfiguration.integration_method' },
                    { data: 'process_type', name: 'integrationConfiguration.process_type' },
                    { data: 'status', name: 'status' },
                    { data: 'updated_by', name: 'updatedBy.name' },
                    { data: 'updated_at', name: 'updated_at' },
                    { data: 'actions', name: 'actions', orderable: false, searchable: false }
                ],
                orderCellsTop: true,
                order: [[ 1, 'desc' ]],
                pageLength: 25,
            };

            // Initialize DataTable with error handling
            try {
                let table = $('#field-mapping-configurations-table').DataTable(dtOverrideGlobals);
                
                // Handle tab changes to adjust column widths
                $('a[data-toggle="tab"]').on('shown.bs.tab', function(e) {
                    table.columns.adjust().responsive.recalc();
                });
                
                // Log DataTable initialization
                console.log('DataTable initialized successfully', table);
            } catch (error) {
                console.error('Error initializing DataTable:', error);
            }
        });
    </script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Git Data Capture\application\resources\views/admin/field-mapping-configurations/index.blade.php ENDPATH**/ ?>