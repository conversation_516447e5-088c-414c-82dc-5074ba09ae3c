<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('audit_logs', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->unsignedBigInteger('form_submission_id')->nullable();
            $table->unsignedBigInteger('form_submission_sync_id')->nullable();
            $table->string('event_type', 50);
            $table->text('event_description');
            $table->enum('actor_type', ['user', 'system', 'admin']);
            $table->unsignedBigInteger('actor_id')->nullable();
            $table->json('old_values')->nullable();
            $table->json('new_values')->nullable();
            $table->json('metadata')->nullable();
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->timestamp('created_at')->useCurrent();

            // Foreign key constraints
            $table->foreign('form_submission_id')->references('id')->on('form_submissions')->onDelete('cascade');
            $table->foreign('form_submission_sync_id')->references('id')->on('form_submission_syncs')->onDelete('cascade');
            $table->foreign('actor_id')->references('id')->on('users')->onDelete('set null');

            // Indexes for performance
            $table->index('form_submission_id', 'idx_audit_logs_submission_id');
            $table->index('form_submission_sync_id', 'idx_audit_logs_sync_id');
            $table->index('event_type', 'idx_audit_logs_event_type');
            $table->index(['actor_type', 'actor_id'], 'idx_audit_logs_actor');
            $table->index('created_at', 'idx_audit_logs_created_at');
            $table->index(['form_submission_id', 'event_type'], 'idx_audit_logs_submission_event');
            $table->index(['event_type', 'created_at'], 'idx_audit_logs_event_date');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('audit_logs');
    }
};
