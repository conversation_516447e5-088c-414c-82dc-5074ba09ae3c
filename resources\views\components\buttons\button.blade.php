@push('styles')
<style>
.btn-icon-only {
    background: transparent !important;
    border: none !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 0.375rem !important;
}
</style>
@endpush

@props([
    'type' => 'button',
    'variant' => 'primary', // primary, secondary, success, danger, warning, info, light, dark, link
    'size' => 'md', // sm, md, lg
    'icon' => null,
    'iconPosition' => 'left', // left or right
    'disabled' => false,
    'class' => '',
    'title' => '',
    'href' => null,
    'target' => '_self',
    'data' => [],
    'confirm' => null, // For delete confirmation
    'form' => null, // Form ID for form submission
    'iconOnly' => false, // Show only icon without text
])

@php
    $baseClasses = 'btn';
    $variantClasses = [
        'primary' => 'btn-primary',
        'secondary' => 'btn-secondary',
        'success' => 'btn-success',
        'danger' => 'btn-danger',
        'warning' => 'btn-warning',
        'info' => 'btn-info',
        'light' => 'btn-light',
        'dark' => 'btn-dark',
        'link' => 'btn-link',
    ];
    
    $sizeClasses = [
        'sm' => 'btn-sm',
        'md' => '',
        'lg' => 'btn-lg',
    ];
    
    $iconOnlyClass = $iconOnly ? 'btn-icon-only' : '';
    
    if ($iconOnly) {
        $backgroundClass = 'bg-transparent border-0';
        $textColorClass = 'text-' . $variant;
        $classes = implode(' ', array_filter([
            $baseClasses,
            $backgroundClass,
            $textColorClass,
            $sizeClasses[$size] ?? '',
            $iconOnlyClass,
            $class
        ]));
    } else {
        $classes = implode(' ', array_filter([
            $baseClasses,
            $variantClasses[$variant] ?? $variantClasses['primary'],
            $sizeClasses[$size] ?? '',
            $class
        ]));
    }
    
    $tag = $href ? 'a' : 'button';
    $attributes = $attributes->merge([
        'class' => $classes,
        'type' => $tag === 'button' ? $type : null,
        'href' => $href,
        'target' => $href ? $target : null,
        'title' => $title,
        'disabled' => $disabled,
    ]);
    
    if ($confirm) {
        $attributes = $attributes->merge([
            'onclick' => "return confirm('" . addslashes($confirm) . "')"
        ]);
    }
    
    if ($form) {
        $attributes = $attributes->merge([
            'form' => $form,
            'type' => 'submit',
        ]);
    }
    
    foreach ($data as $key => $value) {
        $attributes = $attributes->merge(["data-{$key}" => $value]);
    }
    
    // Handle form method spoofing for delete/put/patch
    if (isset($data['method'])) {
        $method = strtoupper($data['method']);
        if (in_array($method, ['PUT', 'PATCH', 'DELETE'])) {
            $attributes = $attributes->merge([
                'data-method' => $method,
                'data-token' => csrf_token(),
            ]);
        }
    }
@endphp

<{{ $tag }} {{ $attributes }}>
    @if($icon && $iconPosition === 'left')
        <i class="fa fa-{{ $icon }}"></i>
    @endif
    
    @if(!$iconOnly)
        {{ $slot }}
    @endif
    
    @if($icon && $iconPosition === 'right')
        <i class="fa fa-{{ $icon }}"></i>
    @endif
</{{ $tag }}>

@if(isset($data['method']) && in_array(strtoupper($data['method']), ['PUT', 'PATCH', 'DELETE']))
    @once
        @push('scripts')
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    document.querySelectorAll('[data-method]').forEach(button => {
                        button.addEventListener('click', function(e) {
                            e.preventDefault();
                            
                            const form = document.createElement('form');
                            form.method = 'POST';
                            form.action = button.href || '#';
                            form.style.display = 'none';
                            
                            const methodInput = document.createElement('input');
                            methodInput.type = 'hidden';
                            methodInput.name = '_method';
                            methodInput.value = button.dataset.method;
                            form.appendChild(methodInput);
                            
                            const tokenInput = document.createElement('input');
                            tokenInput.type = 'hidden';
                            tokenInput.name = '_token';
                            tokenInput.value = button.dataset.token;
                            form.appendChild(tokenInput);
                            
                            document.body.appendChild(form);
                            form.submit();
                        });
                    });
                });
            </script>
        @endpush
    @endonce
@endif
