@props([
    'route' => '#',
    'text' => null,
    'size' => 'sm',
    'class' => '',
    'icon' => 'eye',
    'title' => null,
    'target' => '_self',
    'iconOnly' => true, // Default to icon only for listing views
])

<x-buttons.button
    :href="$route"
    variant="info"
    :size="$size"
    :icon="$icon"
    :class="$class"
    :title="$title ?? __('global.view')"
    :target="$target"
    :iconOnly="$iconOnly"
    data-bs-toggle="tooltip"
>
    @if($slot->isNotEmpty())
        {{ $slot }}
    @else
        {{ $text ?? (!$iconOnly ? __('global.view') : '') }}
    @endif
</x-buttons.button>
