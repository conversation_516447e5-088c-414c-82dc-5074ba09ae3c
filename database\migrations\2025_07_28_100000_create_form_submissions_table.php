<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('form_submissions', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->unsignedBigInteger('form_id');
            $table->unsignedBigInteger('user_id')->nullable();
            $table->json('submission_data');
            $table->enum('status', ['pending', 'processing', 'completed', 'failed', 'partial'])->default('pending');
            $table->timestamp('submitted_at');
            $table->timestamp('processed_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->integer('total_integrations')->default(0);
            $table->integer('successful_integrations')->default(0);
            $table->integer('failed_integrations')->default(0);
            $table->json('metadata')->nullable();
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('form_id')->references('id')->on('forms')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');

            // Indexes for performance
            $table->index('form_id', 'idx_form_submissions_form_id');
            $table->index('user_id', 'idx_form_submissions_user_id');
            $table->index('status', 'idx_form_submissions_status');
            $table->index('submitted_at', 'idx_form_submissions_submitted_at');
            $table->index(['form_id', 'status'], 'idx_form_submissions_form_status');
            $table->index(['user_id', 'submitted_at'], 'idx_form_submissions_user_date');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('form_submissions');
    }
};
