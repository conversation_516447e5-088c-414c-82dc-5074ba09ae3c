<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<!-- begin::Head -->

<head>
    <meta charset="utf-8" />
    <title>@yield('pageTitle') | {{ env('APP_NAME') }} </title>
    <meta name="description" content="Latest updates and statistic charts">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!--begin::Fonts -->
    <meta name="viewport"
        content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, shrink-to-fit=no" />
    <link rel="apple-touch-icon" href="/components/pages//ico/60.png">
    <link rel="apple-touch-icon" sizes="76x76" href="/components/pages//ico/76.png">
    <link rel="apple-touch-icon" sizes="120x120" href="/components/pages//ico/120.png">
    <link rel="apple-touch-icon" sizes="152x152" href="/components/pages//ico/152.png">
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-touch-fullscreen" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta content="" name="description" />
    <meta content="" name="author" />
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="-1">

    <link href="https://cdn.datatables.net/1.10.22/css/dataTables.bootstrap4.min.css" rel="stylesheet" type="text/css"
        media="screen" />
    <link href="https://cdn.datatables.net/responsive/2.2.6/css/responsive.dataTables.min.css" rel="stylesheet"
        type="text/css" media="screen" />
    <link href="https://cdn.datatables.net/fixedcolumns/3.3.1/css/fixedColumns.bootstrap4.min.css" rel="stylesheet"
        type="text/css" media="screen" />
    <link href="https://cdn.datatables.net/fixedheader/3.1.7/css/fixedHeader.bootstrap4.min.css" rel="stylesheet"
        type="text/css" media="screen" />
    <link href="https://cdn.datatables.net/select/1.3.1/css/select.dataTables.min.css" rel="stylesheet" type="text/css"
        media="screen" />
    {{-- <link rel="stylesheet" type="text/css"
        href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.dataTables.min.css"> --}}


    <link href="/components/assets/plugins/pace/pace-theme-flash.css" rel="stylesheet" type="text/css" />
    <link href="/components/assets/plugins/bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap/dist/css/bootstrap.min.css">
    {{-- <link href="/components/assets/plugins/font-awesome/css/font-awesome.css" rel="stylesheet" type="text/css" /> --}}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.css">

    <link href="/components/assets/plugins/jquery-scrollbar/jquery.scrollbar.css" rel="stylesheet" type="text/css"
        media="screen" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css">

    <link href="/components/assets/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css"
        media="screen" />
    <link href="/components/assets/plugins/switchery/css/switchery.min.css" rel="stylesheet" type="text/css"
        media="screen" />
    {{--
    <link href="/components/assets/plugins/jquery-metrojs/MetroJs.css" rel="stylesheet" type="text/css"
        media="screen" />
    <link href="/components/assets/plugins/codrops-dialogFx/dialog.css" rel="stylesheet" type="text/css"
        media="screen" />
    <link href="/components/assets/plugins/codrops-dialogFx/dialog-sandra.css" rel="stylesheet" type="text/css"
        media="screen" />
    <link href="/components/assets/plugins/owl-carousel/assets/owl.carousel.css" rel="stylesheet" type="text/css"
        media="screen" />
    <link href="/components/assets/plugins/jquery-nouislider/jquery.nouislider.css" rel="stylesheet" type="text/css"
        media="screen" /> --}}




    <link href="/components/assets/plugins/bootstrap-tag/bootstrap-tagsinput.css" rel="stylesheet" type="text/css" />
    <link href="/components/assets/plugins/dropzone/css/dropzone.css" rel="stylesheet" type="text/css" />
    <link href="/components/assets/plugins/bootstrap-datepicker/css/datepicker3.css" rel="stylesheet" type="text/css"
        media="screen">
    <!--<link href="/components/assets/plugins/summernote/css/summernote.css" rel="stylesheet" type="text/css" media="screen">-->
    {{-- <link href="https://cdn.jsdelivr.net/npm/summernote@0.8.15/dist/summernote.min.css" rel="stylesheet"
        type="text/css" media="screen"> --}}
    <link rel="stylesheet" href="/components/assets/plugins/gritter/jquery.gritter.css">

    <link href="/components/assets/plugins/bootstrap-daterangepicker/daterangepicker-bs3.css" rel="stylesheet"
        type="text/css" media="screen">
    <link href="/components/assets/plugins/bootstrap-timepicker/bootstrap-timepicker.min.css" rel="stylesheet"
        type="text/css" media="screen">

    <link href="/components/pages//css/pages-icons.css" rel="stylesheet" type="text/css">
    <link class="main-stylesheet" href="/components/pages//css/themes/corporate.css" rel="stylesheet"
        type="text/css" />
    <link href="/components/assets/css/style.css?ref=12" rel="stylesheet" type="text/css" />

    @yield('styles')
</head>

<!-- end::Head -->

<!-- begin::Body -->

<body class="fixed-header menu-pin menu-behind">
    <!-- BEGIN SIDEBPANEL-->
    @include('include.mobile.sidebar')
    <!-- END SIDEBAR -->
    <!-- END SIDEBPANEL-->
    <!-- START PAGE-CONTAINER -->
    <div class="page-container ">
        <!-- START HEADER -->
        @include('include.mobile.header')
        <!-- END HEADER -->
        <!-- START PAGE CONTENT WRAPPER -->
        <div class="page-content-wrapper ">
            <!-- START PAGE CONTENT -->
            <div class="content ">
                <!-- START JUMBOTRON -->
                <!--          <div class="jumbotron" data-pages="parallax">
                                <div class=" container-fluid   container-fixed-lg sm-p-l-0 sm-p-r-0">
                                  <div class="inner">
                                     START BREADCRUMB
                                    <ol class="breadcrumb">
                                      <li class="breadcrumb-item"><a href="#">Pages</a></li>
                                      <li class="breadcrumb-item active">Blank template</li>
                                    </ol>
                                     END BREADCRUMB
                                  </div>
                                </div>
                              </div>-->
                <!-- END JUMBOTRON -->
                <!-- START CONTAINER FLUID -->
                <div class=" container-fluid" style="z-index: 9;">
                    <!-- BEGIN PlACE PAGE CONTENT HERE -->
                    @yield('content')
                    <!-- END PLACE PAGE CONTENT HERE -->
                </div>
                <!-- END CONTAINER FLUID -->
            </div>
            <!-- END PAGE CONTENT -->
            <!-- START COPYRIGHT -->
            <!-- START CONTAINER FLUID -->
            <!-- START CONTAINER FLUID -->
            {{-- <div class=" container-fluid  container-fixed-lg footer">
                <div class="copyright sm-text-center">
                    <p class="small no-margin pull-left sm-pull-reset">
                        <span class="hint-text">Copyright &copy; 2020 </span>
                        <span class="font-montserrat">{{ env('APP_NAME') }}</span>.
                        <span class="hint-text">All rights reserved. </span>
                        <span class="sm-block"><a href="#" class="m-l-10 m-r-10">Terms of use</a> <span
                                class="muted">|</span> <a href="#" class="m-l-10">Privacy Policy</a></span>
                    </p>
                    <!--            <p class="small no-margin pull-right sm-pull-reset">
                          Hand-crafted <span class="hint-text">&amp; made with Love</span>
                        </p>-->
                    <div class="clearfix"></div>
                </div>
            </div> --}}

            <div class="pgn-wrapper" data-position="top-right" style="top: 59px;">
                <div class="pgn push-on-sidebar-open pgn-bar">
                    @include('partials.alerts')
                </div>

            </div>
            <!-- END COPYRIGHT -->
        </div>
        <!-- END PAGE CONTENT WRAPPER -->
    </div>
    <!-- END PAGE CONTAINER -->


    <!-- BEGIN VENDOR JS -->
    <script src="/components/assets/plugins/pace/pace.min.js" type="text/javascript"></script>
    <script src="/components/assets/plugins/jquery/jquery-1.11.1.min.js" type="text/javascript"></script>
    <script src="/components/assets/plugins/modernizr.custom.js" type="text/javascript"></script>
    <script src="/components/assets/plugins/jquery-ui/jquery-ui.min.js" type="text/javascript"></script>
    <script src="/components/assets/plugins/tether/js/tether.min.js" type="text/javascript"></script>
    <script src="/components/assets/plugins/bootstrap/js/bootstrap.min.js" type="text/javascript"></script>
    <script src="/components/assets/plugins/jquery/jquery-easy.js" type="text/javascript"></script>
    <script src="/components/assets/plugins/jquery-unveil/jquery.unveil.min.js" type="text/javascript"></script>
    {{-- <script src="/components/assets/plugins/jquery-ios-list/jquery.ioslist.min.js" type="text/javascript"></script> --}}
    {{-- <script src="/components/assets/plugins/jquery-actual/jquery.actual.min.js"></script> --}}
    <script src="/components/assets/plugins/jquery-scrollbar/jquery.scrollbar.min.js"></script>
    <script type="text/javascript" src="/components/assets/plugins/select2/js/select2.full.min.js"></script>
    {{-- <script type="text/javascript" src="/components/assets/plugins/classie/classie.js"></script> --}}
    <script src="/components/assets/plugins/switchery/js/switchery.min.js" type="text/javascript"></script>
    {{-- <script src="/components/assets/plugins/bootstrap3-wysihtml5/bootstrap3-wysihtml5.all.min.js"></script> --}}

    <script type="text/javascript" src="/components/assets/plugins/jquery-autonumeric/autoNumeric.js"></script>
    <script type="text/javascript" src="/components/assets/plugins/dropzone/dropzone.min.js"></script>
    <script type="text/javascript" src="/components/assets/plugins/bootstrap-tag/bootstrap-tagsinput.min.js"></script>
    <script type="text/javascript" src="/components/assets/plugins/jquery-inputmask/jquery.inputmask.min.js"></script>
    {{-- <script src="/components/assets/plugins/bootstrap-form-wizard/js/jquery.bootstrap.wizard.min.js" type="text/javascript"> --}}
    </script>
    <script src="/components/assets/plugins/jquery-validation/js/jquery.validate.min.js" type="text/javascript"></script>
    <script src="/components/assets/plugins/bootstrap-datepicker/js/bootstrap-datepicker.js" type="text/javascript">
    </script>
    <!--<script src="/components/assets/plugins/summernote/js/summernote.min.js" type="text/javascript"></script>-->
    <script src="https://cdn.jsdelivr.net/npm/summernote@0.8.15/dist/summernote.min.js"></script>

    <script src="/components/assets/plugins/moment/moment.min.js"></script>

    <script src="/components/assets/plugins/bootstrap-daterangepicker/daterangepicker.js"></script>
    <script src="/components/assets/plugins/bootstrap-timepicker/bootstrap-timepicker.min.js"></script>

    {{-- <script src="/components/assets/plugins/jquery-metrojs/MetroJs.min.js" type="text/javascript"></script> --}}
    {{-- <script src="/components/assets/plugins/imagesloaded/imagesloaded.pkgd.min.js"></script> --}}
    {{-- <script src="/components/assets/plugins/jquery-isotope/isotope.pkgd.min.js" type="text/javascript"></script> --}}
    {{-- <script src="/components/assets/plugins/codrops-dialogFx/dialogFx.js" type="text/javascript"></script> --}}
    {{-- <script src="/components/assets/plugins/owl-carousel/owl.carousel.min.js" type="text/javascript"></script> --}}
    {{-- <script src="/components/assets/plugins/jquery-nouislider/jquery.nouislider.min.js" type="text/javascript"></script> --}}
    {{-- <script src="/components/assets/plugins/jquery-nouislider/jquery.liblink.js" type="text/javascript"></script> --}}
    <script src="/components/assets/plugins/parsley/parsley.min.js"></script>

    <script src="/components/assets/plugins/bootstrap-typehead/typeahead.bundle.min.js"></script>
    <script src="/components/assets/plugins/bootstrap-typehead/typeahead.jquery.min.js"></script>
    <script src="/components/assets/plugins/handlebars/handlebars-v4.0.5.js"></script>
    <script src="/components/assets/plugins/sweetalert/sweetalert.min.js"></script>
    <script src="/components/assets/plugins/gritter/jquery.gritter.min.js"></script>
    <script src="/components/assets/plugins/gritter/gritter_alert.js"></script>
    <script src="/components/assets/plugins/ajaxSubmit/jquery.form.js"></script>
    <script src="/components/assets/plugins/jquery-confirmExit/jquery.confirmExit.js"></script>

    <script type="text/javascript" src="https://cdn.datatables.net/1.10.22/js/jquery.dataTables.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/1.10.22/js/dataTables.bootstrap4.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/fixedcolumns/3.3.2/js/dataTables.fixedColumns.min.js">
    </script>
    <script type="text/javascript" src="https://cdn.datatables.net/fixedheader/3.1.7/js/dataTables.fixedHeader.min.js">
    </script>
    <script type="text/javascript" src="https://cdn.datatables.net/responsive/2.2.6/js/dataTables.responsive.min.js">
    </script>
    <script type="text/javascript" src="https://cdn.datatables.net/select/1.3.1/js/dataTables.select.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    {{-- <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script> --}}
    {{-- <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script> --}}
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.print.min.js"></script>

    <!-- END VENDOR JS -->
    <!-- BEGIN CORE TEMPLATE JS -->
    <script src="/components/pages/js/pages.min.js"></script>
    <!-- END CORE TEMPLATE JS -->
    <!-- BEGIN PAGE LEVEL JS -->
    {{-- <script src="/components/assets/js/gallery.js" type="text/javascript"></script> --}}
    {{-- <script src="/components/assets/js/form_elements.js" type="text/javascript"></script> --}}

    <script src="/components/assets/js/scripts.js" type="text/javascript"></script>
    <script src="/components/assets/js/custom.js" type="text/javascript"></script>

    <!-- END PAGE LEVEL JS -->
    {{-- alerts --}}
    <script>
        var swalConfirm = function(msg) {
            return new Promise(function(resolve, reject) {
                swal({
                    title: "Are you sure?",
                    text: msg,
                    icon: "warning",
                    buttons: [
                        'No',
                        'Yes'

                    ],
                    dangerMode: false,
                }).then(function(isConfirm) {
                    if (isConfirm) {
                        resolve();
                    } else {
                        reject();
                    }
                })
            })
        };
        var showErrors = function(res) {
            // console.log(res)
            if (res.responseJSON.errors) {
                $.each(res.responseJSON.errors, function(key, value) {
                    $.each(res.responseJSON.errors[key], function(yek, value) {
                        GritterAlert.showErrorText(res.responseJSON.errors[key][yek]);
                    });
                });
            } else if (res.responseJSON.exception) {
                //console.log(res.responseJSON.errors);
                GritterAlert.showErrorText(res.responseJSON.message);

            } else if (res.error) {
                GritterAlert.showErrorText(res.error);
            } else {
                GritterAlert.showError();
            }
        }

        window.legacyAlert = window.alert;
        // types alert and confirm: "success", "error", "warning", "info", "question". Default: "warning"
        // overwrite default js alert
        window.alert = function(msg, title, type, params) {
            var title = (title == null) ? 'Warning' : title;
            var type = (type == null) ? 'warning' : type;
            swal($.extend({
                title: title,
                text: msg,
                type: type
            }, params || {}));
        };
        // keep default js alert to use in specific cases
        window.legacyConfirm = window.confirm;
        window.swal_confirm = function(msg, title, type, func_if_yes, func_if_cancel, params, e) {

            var title = (title == null) ? ' Are you sure?' : title;
            var type = (type == null) ? 'warning' : type;
            swal($.extend({
                title: title,
                text: msg,
                type: type,
                icon: "warning",
                buttons: [
                    'No',
                    'Yes'
                ],
                dangerMode: true,
                allowEscapeKey: false,
                allowOutsideClick: false
            }, params || {})).then(function(isConfirm) {
                if (isConfirm) {

                    return true;
                }
            }, function(dismiss) {
                // dismiss can be 'cancel', 'overlay', 'close', 'timer'
                if (dismiss === 'cancel' && func_if_cancel instanceof Function) {
                    func_if_cancel()
                    return false;
                }
            });
        };
        $(document).on('click', '.delete-confirm', function(e) {
            e.preventDefault();
            const id = $(this).data('id');
            const form = $(this).closest('form');
            // console.log(id, form);
            swal({
                title: "Are you sure?",
                text: "This will be permanently deleted!",
                icon: "warning",
                buttons: ["Cancel", "Yes, delete it!"],
                dangerMode: true,
            }).then((willDelete) => {
                if (willDelete) {
                    form.submit();
                }
            });
        });
    </script>
    <script>
        var buttonsDefault = [{
                extend: 'csv',
                className: 'btn-primary',
                text: "Export",
                exportOptions: {
                    modifier: {
                        search: 'applied',
                        order: 'applied'
                    },
                    columns: '.exportable',
                    format: {
                        body: function(data, column, row, node) {

                            if ($(node).find('._select2').length) {
                                var datad = $(node).find('._select2').select2('data')
                                //                                console.log(datad);
                                if (datad[0].id != "" && datad[0].id != "0") {
                                    return datad[0].text
                                }
                                return "";
                            }
                            if ($(node).find('.hidden').length) {
                                return "";
                            }
                            if ($(node).find('input').length) {
                                return $(node).find('input').val();
                            }
                            if ($(node).find('a').length) {
                                return $(node).find('a').text();
                            }
                            if ($(node).find('span').length) {
                                return $(node).find('span').text();
                            }
                            if ($(node).find('textarea').length) {
                                return $(node).find('textarea').val();
                            }



                            return data;
                            //                return intVal(data);
                        }
                    }
                }
            },
            //            {
            //                extend: 'selectAll',
            //                className: 'btn-primary',
            //                text: selectAllButtonTrans,
            //                exportOptions: {
            //                    columns: ':visible'
            //                },
            //                action: function (e, dt) {
            //                    e.preventDefault()
            //                    dt.rows().deselect();
            //                    dt.rows({search: 'applied'}).select();
            //                }
            //            },
            //            {
            //                extend: 'selectNone',
            //                className: 'btn-primary',
            //                text: selectNoneButtonTrans,
            //                exportOptions: {
            //                    columns: ':visible'
            //                }
            //            },
            //            {
            //                extend: 'copy',
            //                className: 'btn-default',
            //                text: copyButtonTrans,
            //                exportOptions: {
            //                    columns: '.exportable'
            //                }
            //            },

            //            {
            //                extend: 'excel',
            //                className: 'btn-default',
            //                text: excelButtonTrans,
            //                exportOptions: {
            //                    columns: '.exportable'
            //                }
            //            },
            //            {
            //                extend: 'pdf',
            //                className: 'btn-default',
            //                text: pdfButtonTrans,
            //                exportOptions: {
            //                    columns: '.exportable'
            //                }
            //            },
            //            {
            //                extend: 'print',
            //                className: 'btn-default',
            //                text: printButtonTrans,
            //                exportOptions: {
            //                    columns: '.exportable'
            //                }
            //            },
            //                {
            //                extend: 'colvis',
            //                        className: 'btn-default',
            //                        text: colvisButtonTrans,
            //                        exportOptions: {
            //                        columns: ':visible'
            //                        }
            //                }
        ];


        var print_css_url = "<?= url('/components/assets/css/print.css') ?>";
        var printDataTableBtn = {
            extend: 'print',
            className: 'btn-default print_btn',
            text: "<i class='fa fa-print'></i> Print",
            //     autoPrint: false,
            customize: function(win) {

                var css_link = $("<link>", {
                    rel: "stylesheet",
                    type: "text/css",
                    href: print_css_url
                });
                $(win.document.body).prepend(css_link)
                $(win.document.body).css('height', 'auto')
                //                .prepend("<link href='/components/assets/css/print.css?ref=183'></link>")
                console.log($(win.document.body));

            },
            exportOptions: {

                columns: '.exportable',

                format: {
                    body: function(data, column, row, node) {

                        if ($(node).find('.hidden').length) {
                            return "";
                        }
                        if ($(node).find('._select2').length) {
                            var datad = $(node).find('._select2').select2('data')
                            // console.log(datad);
                            if (datad[0].id != "" && datad[0].id != "0") {
                                return datad[0].text
                            }
                            return "";
                        }
                        if ($(node).find('a').length) {
                            return $(node).find('a').text();
                        }

                        if ($(node).find('textarea').length) {
                            return $(node).find('textarea').val();
                        }
                        if ($(node).find('.price_formate').length) {
                            //                    $(node).css('text-align','right');
                            return "<span class='text-right'>" + $(node).find('.price_formate').val() + "</span>";
                        }

                        if ($(node).find('input').length) {
                            return $(node).find('input').val();
                        }

                        if ($(node).find('span').length) {
                            return $(node).find('span').text();
                        }


                        return data;
                        //                return intVal(data);
                    }
                }
            },


        };


        let copyButtonTrans = '<?= trans('global.datatables.copy') ?>'
        let csvButtonTrans = 'Export'
        let excelButtonTrans = '<?= trans('global.datatables.excel') ?>'
        let pdfButtonTrans = '<?= trans('global.datatables.pdf') ?>'
        let printButtonTrans = '<?= trans('global.datatables.print') ?>'
        let colvisButtonTrans = '<?= trans('global.datatables.colvis') ?>'
        let selectAllButtonTrans = '<?= trans('global.select_all') ?>'
        let selectNoneButtonTrans = '<?= trans('global.deselect_all') ?>'

        let languages = {
            'en': 'https://cdn.datatables.net/plug-ins/1.10.19/i18n/English.json'
        };
        $.fn.dataTable.ext.errMode = function(settings, helpPage, message) {
            // console.log(message);
        };
        $.extend(true, $.fn.dataTable.Buttons.defaults.dom.button, {
            className: 'btn btn-primary'
        })
        // $.extend(true, $.fn.dataTable.defaults, {
        //     language: {
        //         url: languages['<?= app()->getLocale() ?>']
        //     },
        //     columnDefs: [{
        //             orderable: false,
        //             'checkboxes': {
        //                 'selectRow': true
        //             },
        //             "createdCell": function(td, cellData, rowData, row, col) {

        //                 if (rowData["DT_RowClass"] === "hide_dataTable_select") {
        //                     $(td).html("");
        //                 }
        //             },
        //             targets: 0,
        //         },
        //         {
        //             orderable: false,
        //             searchable: false,
        //             targets: -1,
        //         },
        //         {
        //             orderable: false,
        //             searchable: false,
        //             targets: "no_sort"
        //         },

        //     ],
        //     select: {
        //         style: 'multi',
        //         selector: 'tr:not(.hide_dataTable_select) td:first-child'
        //         //              selector: 'tr:not(.no-select) td'
        //     },
        //     "rowCallback": function(row, data, dataIndex) {

        //     },
        //     drawCallback: function() {
        //         // loadFormulaes();
        //         var tabled = this.api().table().node();
        //         $(tabled).find('.price_formate').blur();
        //     },
        //     "headerCallback": function(row, data, start, end, display) {
        //         var api = this.api();
        //         if ($(api.column(-1).header()).text() == "Action" || $(api.column(-1).header()).text() ==
        //             "Actions") {
        //             $(api.column(-1).header()).html("&nbsp;");
        //         }

        //     },
        //     order: [],
        //     scrollX: true,
        //     // scrollY: 600,
        //     pageLength: 50,
        //     //        fixedColumns: true,
        //     //        autoWidth: false,

        //     //                dom: 'lBfrtip<"actions">',
        //     dom: 'Bltip<"actions">',
        //     lengthMenu: [
        //         [50, 100, 500, 1000],
        //         [50, 100, 500, 1000]
        //     ],

        //     buttons: buttonsDefault,


        // });
        $.fn.dataTable.ext.classes.sPageButton = '';
        $.fn.dataTable.ext.order['dom-input'] = function(settings, col) {
            return this.api().column(col, {
                order: 'index'
            }).nodes().map(function(td, i) {
                return $('input', td).val();
            });
        }
    </script>
    <script>
        Number.prototype.toCurrencyString = function(prefix, suffix) {
            //    parseFloat(~~this);
            //    console.log(this);
            if (typeof prefix === 'undefined') {
                prefix = '';
            }
            if (typeof suffix === 'undefined') {
                suffix = '';
            }
            var _localeBug = new RegExp((1).toLocaleString().replace(/^1/, '').replace(/\./, '\\.') + "$");

            return prefix + (~~this).toLocaleString().replace(_localeBug, '') + (this % 1).toFixed(3).toLocaleString()
                .replace(/^[+-]?0+/, '') + suffix;
        }

        function parseCurrencyFormate(num) {
            //    console.log("input" + num);
            if (typeof num == "string") {

                if (num.indexOf("<input") >= 0) {
                    num = $(num).val();
                    //            console.log(num);
                } else {
                    num = num.replace(/[^\d\.\-]/g, "");
                }
            }
            num = parseMyNum(num);
            //    console.log("output" + num);
            //

            return parseFloat(num);
        }
        var intVal = function(i) {
            //    console.log(i);
            var to_return = 0;
            if (typeof i === "number") {
                to_return = i;
            } else if (typeof i === 'string') {
                if (i.indexOf("<input") == 0) {
                    //            console.log($(i))
                    i = $(i).val();

                }
                //        console.log(i);
                to_return = i.replace(/[\$,]/g, '') * 1
            }
            return to_return;

        };

        function parseMyNum(num) {

            var numReturn = 0;
            if (num == "") {
                num = 0;
            }

            //    console.log(numReturn);
            return num;
        }
    </script>
    @yield('scripts')
</body>
<!--end::Global App Bundle -->


<!-- end::Body -->

</html>
