@media print {
/*html, body { height: auto; }*/
    .table.compact td{
        padding: 5px !important;
    }
  
/*    .printFooter{
      
        position: fixed !important;
        bottom: 0px !important;
        height: 100px !important;
        width: 100%;
        color: red;
       break-before:always;
    }
    .table.datatable{
        break-inside: avoid;
    }*/
   
    
/*   body{
    padding: 5px;
    padding-bottom: 100px !important;
    margin-bottom: 100px !important;
  height: calc(100% - 100px);
  overflow: hidden;
}*/

   
/*    body{
        position: relative !important;
        display: block !important;
        background: #ccc !important;
        padding: 15px !important;
        
        padding-bottom: 200px !important;
    }*/
    .printFooter table tr td{
        color: red !important;
    }
    .datatable.table{
/*        height: 500px !important;*/
            /*display: list-item;*/
            /*page-break-inside:auto;*/
            /*overflow: hidden;*/
    }
}


.datatable.table{
/*        height: 500px !important;
            display: list-item;*/
    }
table{
    margin-top: 0px ;
}
thead td{
    background: #f5f5f5 !important;
    color: #000 !important; 
}
table.bordered{
    /*border-collapse:initial !important;*/

    /*border: 1px solid #ddd !important;*/
}
table.bordered, table.bordered th,table.bordered td {
    /*border: 1px solid black !important;*/
    /*border-style: inset;*/
    /*color:#000 !important;*/
}
table.bordered, table.bordered th,table.bordered td {
    border: 1px solid #ddd !important;
    /*border-style: inset;*/
    /*color:#000 !important;*/
    border: 1px solid #ddd !important;
    /*border-style: inset;*/
    /*color:#000 !important;*/

    border-collapse: separate;
    box-sizing: border-box;
    text-indent: initial;
    border-spacing: 2px;
    border-color: grey;
}
.table-borderless{
    border:0px !important;
}

.table.no-margin{
    margin: 0px !important;
}
.table.no-border ,.table.no-border td{
    border: 0px !important;
}
/*table.bordered,table.bordered td,table.bordered th {
    border: 1px solid black !important;
    border-spacing: 0px !important;
        border-collapse:unset !important;

}*/
.table.compact td{
    font-size: 10px;
    padding:5px !important;
}
/*.table{
    border: 1px solid #ddd !important;
    border-style: inset;
    color:#000 !important;
    border: 1px solid #ddd !important;
    border-style: inset;
    color:#000 !important;

    border-collapse: separate !important;
    ;
    box-sizing: border-box !important;
    text-indent: initial !important;
    ;
    border-spacing: 0px !important;
    border-color: grey  !important;
}*/

h2{
    font-size: 18px !important;
    line-height: 18px !important;
    font-weight: 500;
}
table thead th{
        padding: 4px !important;
         color: #000 !important; 
}
.printTableSubHeading{
     width: 40% !important;
}
.printTableSubHeadingText{
     width: 60% !important;
}
.printTableSubHeading,.printTableSubHeadingText,.printTableFooter td,.printTableFooter th{
   
    padding: 2px !important;
    font-size: 12px !important;
    line-height: 12px;
}