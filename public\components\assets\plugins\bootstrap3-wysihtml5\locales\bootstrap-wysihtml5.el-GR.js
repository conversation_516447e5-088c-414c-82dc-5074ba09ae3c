/**
 * Greek translation for bootstrap-wysihtml5
 */
(function($){
    $.fn.wysihtml5.locale["el-GR"] = {
        font_styles: {
              normal: "Απλό κείμενο",
              h1: "Κεφαλίδα 1",
              h2: "Κεφαλίδα 2",
        	  h3: "Κεφαλίδα 3"	
		},
        emphasis: {
              bold: "B",
              italic: "I",
              underline: "U"
        },
        lists: {
              unordered: "Λίστα με κουκκίδες",
              ordered: "Αριθμημένη λίστα",
              outdent: "Μείωση εσοχής",
              indent: "Αύξηση εσοχής"
        },
        link: {
              insert: "Εισαγωγή Συνδέσμου",
              cancel: "Άκυρο"
        },
        image: {
              insert: "Εισαγωγή Εικόνας",
              cancel: "Άκυρο"
        },
        html: {
            edit: "Επεξεργασία HTML"
        },
        colours: {
            black: "<PERSON>αύρ<PERSON>",
            silver: "<PERSON><PERSON>η<PERSON><PERSON>",
            gray: "Γ<PERSON><PERSON><PERSON>",
            maroon: "<PERSON>α<PERSON><PERSON>",
            red: "<PERSON>όκκιν<PERSON>",
            purple: "Μωβ",
            green: "Πράσινο",
            olive: "Λαδί",
            navy: "Βαθύ Μπλε",
            blue: "Μπλε",
            orange: "Πορτοκαλί"
        }
    };
}(jQuery));