<nav class="page-sidebar {{ 1 == 1 ? 'sp_admin' : '' }}" data-pages="sidebar">
    <!-- BEGIN SIDEBAR MENU TOP TRAY CONTENT-->

    <!-- <PERSON>ND SIDEBAR MENU TOP TRAY CONTENT-->
    <!-- BEGIN SIDEBAR MENU HEADER-->
    <div class="sidebar-header">
        <a href="/">
            <h4 style="color:#fff;display: inline;font-weight: 600;">{{ env('APP_NAME') }}</h4>
        </a>
        <!--<img src="/logo.png" alt="logo" class="brand" data-src="/logo.png" data-src-retina="/logo.png" width="78" height="22">-->
        <div class="sidebar-header-controls">
            <!--            <button type="button" class="btn btn-xs sidebar-slide-toggle btn-link m-l-20 hidden-md-down" data-pages-toggle="#appMenu"><i class="fa fa fa-angle-down fs-16"></i>
            </button>-->
            <button type="button" class="btn btn-link hidden-md-down" data-toggle-pin="sidebar"><i
                    class="fa fs-12"></i>
            </button>
        </div>
    </div>
    <!-- END SIDEBAR MENU HEADER-->
    <!-- START SIDEBAR MENU -->
    <div class="sidebar-menu">
        <!-- BEGIN SIDEBAR MENU ITEMS-->
        <ul class="menu-items">


            <li class="m-t-30 {{ request()->segment(1) == 'admin' && request()->segment(2) == '' ? 'active' : '' }}">
                <a href="/admin" class="">
                    <span class="title">Dashboard</span>
                    <!--<span class="details">12 New Updates</span>-->
                </a>
                <span class="icon-thumbnail">
                    <i class="fa fa-dashboard"></i>
                </span>
            </li>

            <hr style="margin: 5px;border-color: #cecece;">
            </hr>


            {{-- @can('user_management_access') --}}
            <?php
            $manageRequests = ['admin/permissions*', 'admin/roles*', 'admin/users*'];
            $managementClass = '';
            foreach ($manageRequests as $req) {
                if (request()->is($req)) {
                    $managementClass = 'open active';
                }
            }
            ?>
            <li class="sidebar-menu-bg1 {{ $managementClass }}">

                <a href="javascript:;"><span class="title"> {{ trans('cruds.userManagement.title') }}</span>
                    <span class=" arrow open active"></span></a>
                <span class="icon-thumbnail"><i class="fa fa-users"></i></span>

                <ul class="sub-menu">
                    {{-- @can('permission_access') --}}
                    {{-- <li
                                class="{{ request()->is('admin/permissions') || request()->is('admin/permissions/*') ? 'active' : '' }}">
                                <a href="{{ route('admin.permissions.index') }}">{{ trans('cruds.permission.title') }}</a>
                                <span class="icon-thumbnail">PM</span>

                            </li> --}}
                    {{-- @endcan --}}
                    {{-- @can('role_access') --}}
                    {{-- <li class="{{ request()->is('admin/roles') || request()->is('admin/roles/*') ? 'active' : '' }}">
                                <a href="{{ route('admin.roles.index') }}">{{ trans('cruds.role.title') }}</a>
                                <span class="icon-thumbnail">RL</span>
                            </li> --}}
                    {{-- @endcan --}}
                    {{-- @can('user_access') --}}
                    <li class="{{ request()->is('admin/users') || request()->is('admin/users/*') ? 'active' : '' }}">
                        <a href="{{ route('admin.users.index') }}">{{ trans('cruds.user.title') }}</a>
                        <span class="icon-thumbnail"><i class="fa fa-user-plus"></i></span>
                    </li>
                    {{-- @endcan --}}
                    <li
                        class="{{ request()->is('admin/usergroups') || request()->is('admin/usergroups/*') ? 'active' : '' }}">
                        <a href="{{ route('admin.usergroups.index') }}">{{ trans('cruds.usergroup.title') }}</a>
                        <span class="icon-thumbnail"><i class="fa fa-users"></i></span>
                    </li>
                    <li
                        class="{{ request()->is('admin/usergroups/forms') || request()->is('admin/usergroups/forms*') ? 'active' : '' }}">
                        <a href="{{ route('admin.usergroups.forms') }}">{{ trans('cruds.form.title') }}
                            {{ trans('cruds.usergroup.title') }}</a>
                        <span class="icon-thumbnail"><i class="fa fa-users"></i></span>
                    </li>



                </ul>
            </li>
            <li class="{{ request()->is('admin/forms*') ? 'active' : '' }}">
                <a href="javascript:;">
                    <span class="title">Form Builder</span>
                    <span class="arrow"></span>
                </a>
                <span class="icon-thumbnail">
                    <i class="fa fa-shield"></i>
                </span>
                <ul class="sub-menu">

                    <li class="{{ request()->is('admin/forms') ? 'active' : '' }}">
                        <a href="{{ route('admin.forms.index') }}">My Forms</a>
                        <span class="icon-thumbnail"><i class="fa fa-wpforms"></i></span>
                    </li>
                    <li
                        class="{{ request()->is('admin/forms/standard') || request()->is('admin/forms/standard*') ? 'active' : '' }}">
                        <a href="{{ route('admin.forms.getStandardForms') }}">Standard Forms</a>
                        <span class="icon-thumbnail"><i class="fa fa-wpforms"></i></span>
                    </li>

                </ul>
            </li>

            <li
                class="{{ request()->is('admin/form-field-templates') || request()->is('admin/form-field-templates/*') ? 'active' : '' }}">
                <a href="{{ route('admin.form-field-templates.index') }}">Form Field Templates</a>
                <span class="icon-thumbnail"><i class="fa fa-puzzle-piece"></i></span>
            </li>
            <li class="{{ request()->is('admin/field-mapping-configurations*') ? 'active' : '' }}">
                <a href="{{ route('admin.field-mapping-configurations.index') }}">Field Mapping Configurations</a>
                <span class="icon-thumbnail"><i class="fa fa-exchange"></i></span>
            </li>
            {{-- @endcan --}}

            {{-- Form Submissions Menu --}}
            {{-- @if (auth()->check())
                <li class="m-t-30 {{ request()->is('admin/form-submissions*') ? 'active' : '' }}">
                    <a href="{{ route('admin.form-submissions.index') }}" class="">
                        <span class="title">Form Submissions</span>
                    </a>
                    <span class="icon-thumbnail">
                        <i class="fa fa-paper-plane"></i>
                    </span>
                </li>
            @endif --}}
            <li class="{{ request()->is('admin/qr-decoder*') ? 'active' : '' }}">
                <a href="javascript:;">
                    <span class="title">QR Decoder</span>
                    <span class="arrow"></span>
                </a>
                <span class="icon-thumbnail">
                    <i class="fa fa-shield"></i>
                </span>
                <ul class="sub-menu">

                    <li class="{{ request()->is('admin/qr-decoder-profiles') ? 'active' : '' }}">
                        <a href="{{ route('admin.qr-decoder-profiles.index') }}">QR Decoder Profiles</a>
                        <span class="icon-thumbnail"><i class="fa fa-qrcode"></i></span>
                    </li>


                </ul>
            </li>
            {{-- Audit Logs Menu --}}
            @if (auth()->check())
                <li class="{{ request()->is('admin/audit-logs*') ? 'active' : '' }}">
                    <a href="{{ route('admin.audit-logs.index') }}" class="">
                        <span class="title">Audit Logs</span>
                    </a>
                    <span class="icon-thumbnail">
                        <i class="fa fa-history"></i>
                    </span>
                </li>
            @endif

            {{-- Error Logs Menu --}}
            @if (auth()->check())
                <li class="{{ request()->is('admin/error-logs*') ? 'active' : '' }}">
                    <a href="{{ route('admin.error-logs.index') }}" class="">
                        <span class="title">Error Logs</span>
                    </a>
                    <span class="icon-thumbnail">
                        <i class="fa fa-exclamation-triangle"></i>
                    </span>
                </li>
            @endif

            {{-- Super Admin Menu Section --}}
            @if ((auth()->check() && auth()->user()->type === 1) || 1 == 1)
                <li class="superadmin-section">
                    <a href="javascript:;">
                        <span class="title">Super Admin</span>
                        <span class="arrow"></span>
                    </a>
                    <span class="icon-thumbnail">
                        <i class="fa fa-shield"></i>
                    </span>
                    <ul class="sub-menu" style="background-color: #2c3e50;">
                        <li class="{{ request()->is('superadmin/integration-configurations*') ? 'active' : '' }}">
                            <a href="{{ route('admin.integration-configurations.index') }}">
                                <span class="title">Integration Configurations</span>
                            </a>
                            <span class="icon-thumbnail">
                                <i class="fa fa-plug"></i>
                            </span>
                        </li>
                        <li class="{{ request()->is('superadmin/data-capture-fields*') ? 'active' : '' }}">
                            <a href="{{ route('superadmin.data-capture-fields.index') }}">
                                <span class="title">DC Fields</span>
                            </a>
                            <span class="icon-thumbnail">
                                <i class="fa fa-plug"></i>
                            </span>
                        </li>
                    </ul>
                </li>
            @endif

            <li class="">
                <a href="{{ route('mobilehome') }}" class="">
                    <span class="title">{{ trans('global.menu.switch_to_mob') }}</span>
                </a>
                <span class="icon-thumbnail">
                    <i class="fa fa-mobile"></i>
                </span>
            </li>

        </ul>
        <div class="clearfix"></div>
    </div>

    <!-- END SIDEBAR MENU -->
</nav>
