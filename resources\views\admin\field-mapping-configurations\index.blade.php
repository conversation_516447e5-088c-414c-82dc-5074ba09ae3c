@extends('layouts.admin')
@section('pageTitle', __('cruds.fieldMappingConfiguration.title'))

@section('content')
    <div class="card card-default">
        <div class="card-header separator">
            <div class="card-title mainheading">
                <h4>{{ __('cruds.fieldMappingConfiguration.title') }} {{ trans('global.list') }}</h4>
            </div>
            <div class="card-controls">
                <ul>
                    @if (auth()->user()->isAdmin() || auth()->user()->isSuperAdmin())
                        <li>
                            <x-buttons.add :route="route('admin.field-mapping-configurations.create')" :title="__('cruds.fieldMappingConfiguration.title_singular')" />
                        </li>
                    @endif
                </ul>
            </div>
        </div>

        <div class="card-block">
            <x-datatable 
                id="field-mapping-configurations-table"
                :columns="$columns"
                ajax="{{ route('admin.field-mapping-configurations.index') }}"
                :order="[[9, 'desc']]"
            />
        </div>
    </div>
@endsection

@section('styles')
<style>
    .card-header h4 {
        margin: 0;
        color: #495057;
    }
    .table-responsive {
        overflow-x: auto;
        width: 100%;
        margin: 0;
    }
    .dataTables_wrapper .dataTables_paginate .paginate_button {
        padding: 0.25rem 0.5rem;
    }
    .table th {
        background-color: #f8f9fa;
        border-top: none;
    }
    .badge {
        font-size: 0.75em;
    }
    #field-mapping-configurations-table {
        width: 100% !important;
        margin: 0;
        table-layout: fixed;
    }
    .table {
        width: 100% !important;
        margin-bottom: 1rem;
        color: #212529;
    }
    @media (max-width: 768px) {
        .table-responsive {
            border: none;
        }
    }
</style>
@endsection

@section('scripts')
    @parent
    <script>
        $(function() {
            let dtButtons = $.extend(true, [], $.fn.dataTable.defaults.buttons);
            
            let deleteButton = {
                text: '{{ trans('global.datatables.delete') }}',
                url: "{{ route('admin.field-mapping-configurations.massDestroy') }}",
                className: 'btn-danger',
                action: function(e, dt, node, config) {
                    var ids = $.map(dt.rows({ selected: true }).data(), function(entry) {
                        return entry.id;
                    });

                    if (ids.length === 0) {
                        alert('{{ trans('global.datatables.zero_selected') }}');
                        return;
                    }

                    if (confirm('{{ trans('global.areYouSure') }}')) {
                        $.ajax({
                            headers: {'x-csrf-token': _token},
                            method: 'POST',
                            url: config.url,
                            data: { ids: ids, _method: 'DELETE' }
                        })
                        .done(function() {
                            location.reload();
                        });
                    }
                }
            };

            dtButtons.push(deleteButton);

            let dtOverrideGlobals = {
                buttons: dtButtons,
                processing: true,
                serverSide: true,
                retrieve: true,
                aaSorting: [],
                ajax: "{{ route('admin.field-mapping-configurations.index') }}",
                columns: [
                    { data: 'placeholder', name: 'placeholder' },
                    { data: 'name', name: 'name' },
                    { data: 'form.title', name: 'form.title' },
                    { data: 'endpoint_configuration', name: 'integrationConfiguration.name' },
                    { data: 'external_system', name: 'integrationConfiguration.external_system' },
                    { data: 'integration_method', name: 'integrationConfiguration.integration_method' },
                    { data: 'process_type', name: 'integrationConfiguration.process_type' },
                    { data: 'status', name: 'status' },
                    { data: 'updated_by', name: 'updatedBy.name' },
                    { data: 'updated_at', name: 'updated_at' },
                    { data: 'actions', name: 'actions', orderable: false, searchable: false }
                ],
                orderCellsTop: true,
                order: [[ 1, 'desc' ]],
                pageLength: 25,
            };

            // Initialize DataTable with error handling
            try {
                let table = $('#field-mapping-configurations-table').DataTable(dtOverrideGlobals);
                
                // Handle tab changes to adjust column widths
                $('a[data-toggle="tab"]').on('shown.bs.tab', function(e) {
                    table.columns.adjust().responsive.recalc();
                });
                
                // Log DataTable initialization
                console.log('DataTable initialized successfully', table);
            } catch (error) {
                console.error('Error initializing DataTable:', error);
            }
        });
    </script>
@endsection