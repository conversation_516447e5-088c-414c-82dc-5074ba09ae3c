<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Auth;

class BaseModel extends Model
{
    public function getCreatedAtAttribute($value)
    {
        return \Carbon\Carbon::parse($value)->format('m/d/Y');
    }
    public function getUpdatedAtAttribute($value)
    {
        return \Carbon\Carbon::parse($value)->format('m/d/Y');
    }
    protected static function booted()
    {
        static::creating(function ($model) {
            if (Auth::check()) {
                if ($model->isFillable('tenant_id') && empty($model->tenant_id)) {
                    $model->tenant_id = Auth::user()->tenant_id;
                }
                if ($model->isFillable('created_by') && empty($model->created_by)) {
                    $model->created_by = Auth::id();
                }
            }
        });

        // When updating via save()
        static::updating(function ($model) {
            if (Auth::check() && $model->isFillable('updated_by')) {
                $model->updated_by = Auth::id();
            }
        });
        static::updating(function ($model) {
            // dd(Auth::user()->id);
            if (Auth::check()) {
                $model->updated_by = Auth::user()->id;
            }
        });
        static::addGlobalScope('tenant', function ($builder) {
            if (Auth::check()) {
                // $builder->where('tenant_id', Auth::user()->tenant_id);
            }
        });
    }
    /**
     * Override update() so direct update([...]) also sets updated_by
     */
    // public function update(array $attributes = [], array $options = [])
    // {
    //     if (Auth::check() && $this->isFillable('updated_by')) {
    //         $attributes['updated_by'] = Auth::id();
    //     }

    //     return parent::update($attributes, $options);
    // }
}
