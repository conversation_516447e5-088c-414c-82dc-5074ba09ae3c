<?php

return [
    'system_fields' => [
        [
            "type" => "hidden",
            'key' => 'tenant_id',
            'label' => 'Tenant ID',
            'defaultValue' => '{{TENANT_ID}}', // placeholder, replaced in JS
            'persistent' => true,
            // 'hidden' => true,
        ],
        [
            "type" => "hidden",
            'key' => 'user_id',
            'label' => 'User ID',
            'defaultValue' => '{{USER_ID}}', // placeholder, replaced in JS
            'persistent' => true,
            // 'hidden' => true,
        ],
        [
            "type" => "hidden",
            'key' => 'timestamp',
            'label' => 'Timestamp',
            'defaultValue' => '{{TIMESTAMP}}',
            'persistent' => true,
            // 'hidden' => true,
        ],
        [
            'key' => 'device_id',
            'label' => 'Device ID / MAC Address',
            'type' => 'hidden',
            'persistent' => true,

            'defaultValue' => '', // frontend/mobile can provide this, or backend detects
        ],
        [
            'key' => 'form_id',
            'label' => 'Form ID',
            'type' => 'hidden',
            'persistent' => true,

            'defaultValue' => '{{FORM_ID}}',
        ],
        [
            'persistent' => true,
            'key' => 'transaction_id',
            'label' => 'Transaction ID',
            'type' => 'hidden',
            'defaultValue' => '{{UUID}}', // generate unique UUID per submission
        ],
        [
            'key' => 'process_code',
            'label' => 'Process Code',
            'type' => 'hidden',
            'persistent' => true,
            'defaultValue' => '{{PROCESS_CODE}}',
        ],
    ],
];
