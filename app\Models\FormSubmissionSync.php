<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class FormSubmissionSync extends Model
{
    use HasFactory;

    protected $fillable = [
        'uuid',
        'form_submission_id',
        'form_integration_setting_id',
        'status',
        'sync_data',
        'response_data',
        'error_message',
        'error_code',
        'attempted_at',
        'completed_at',
        'retry_count',
        'max_retries',
        'next_retry_at',
        'processing_time_ms',
    ];

    protected $casts = [
        'sync_data' => 'array',
        'response_data' => 'array',
        'attempted_at' => 'datetime',
        'completed_at' => 'datetime',
        'next_retry_at' => 'datetime',
        'retry_count' => 'integer',
        'max_retries' => 'integer',
        'processing_time_ms' => 'integer',
    ];

    protected $dates = [
        'attempted_at',
        'completed_at',
        'next_retry_at',
    ];

    // Status constants
    const STATUS_PENDING = 'pending';
    const STATUS_PROCESSING = 'processing';
    const STATUS_COMPLETED = 'completed';
    const STATUS_FAILED = 'failed';
    const STATUS_RETRYING = 'retrying';

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->uuid)) {
                $model->uuid = (string) Str::uuid();
            }
        });

        static::updated(function ($model) {
            // Update parent submission status when sync status changes
            if ($model->isDirty('status')) {
                $model->formSubmission->updateStatusFromSyncs();
            }
        });
    }

    /**
     * Get the form submission this sync belongs to
     */
    public function formSubmission(): BelongsTo
    {
        return $this->belongsTo(FormSubmission::class);
    }

    /**
     * Get the integration setting for this sync
     */
    public function formIntegrationSetting(): BelongsTo
    {
        return $this->belongsTo(FormIntegrationSetting::class);
    }

    /**
     * Get audit logs for this sync
     */
    public function auditLogs(): HasMany
    {
        return $this->hasMany(AuditLog::class);
    }

    /**
     * Get error logs for this sync
     */
    public function errorLogs(): HasMany
    {
        return $this->hasMany(ErrorLog::class);
    }

    /**
     * Scope to filter by status
     */
    public function scopeWithStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get failed syncs that can be retried
     */
    public function scopeRetryable($query)
    {
        return $query->where('status', self::STATUS_FAILED)
                    ->where('retry_count', '<', 'max_retries');
    }

    /**
     * Scope to get syncs ready for retry
     */
    public function scopeReadyForRetry($query)
    {
        return $query->where('status', self::STATUS_RETRYING)
                    ->where('next_retry_at', '<=', now());
    }

    /**
     * Check if sync is pending
     */
    public function isPending(): bool
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * Check if sync is processing
     */
    public function isProcessing(): bool
    {
        return $this->status === self::STATUS_PROCESSING;
    }

    /**
     * Check if sync is completed
     */
    public function isCompleted(): bool
    {
        return $this->status === self::STATUS_COMPLETED;
    }

    /**
     * Check if sync has failed
     */
    public function isFailed(): bool
    {
        return $this->status === self::STATUS_FAILED;
    }

    /**
     * Check if sync is retrying
     */
    public function isRetrying(): bool
    {
        return $this->status === self::STATUS_RETRYING;
    }

    /**
     * Check if sync can be retried
     */
    public function canRetry(): bool
    {
        return $this->retry_count < $this->max_retries &&
               ($this->isFailed() || $this->isRetrying());
    }

    /**
     * Mark sync as started
     */
    public function markAsStarted(): void
    {
        $this->update([
            'status' => self::STATUS_PROCESSING,
            'attempted_at' => now(),
        ]);
    }

    /**
     * Mark sync as completed
     */
    public function markAsCompleted(array $responseData = [], int $processingTimeMs = null): void
    {
        $this->update([
            'status' => self::STATUS_COMPLETED,
            'completed_at' => now(),
            'response_data' => $responseData,
            'processing_time_ms' => $processingTimeMs,
            'error_message' => null,
            'error_code' => null,
        ]);
    }

    /**
     * Mark sync as failed
     */
    public function markAsFailed(string $errorMessage, string $errorCode = null, int $processingTimeMs = null): void
    {
        $this->update([
            'status' => self::STATUS_FAILED,
            'error_message' => $errorMessage,
            'error_code' => $errorCode,
            'processing_time_ms' => $processingTimeMs,
        ]);
    }

    /**
     * Schedule retry
     */
    public function scheduleRetry(int $delayMinutes = null): void
    {
        if (!$this->canRetry()) {
            return;
        }

        // Calculate exponential backoff delay
        $delay = $delayMinutes ?? $this->calculateRetryDelay();

        $this->update([
            'status' => self::STATUS_RETRYING,
            'retry_count' => $this->retry_count + 1,
            'next_retry_at' => now()->addMinutes($delay),
        ]);
    }

    /**
     * Calculate retry delay using exponential backoff
     */
    private function calculateRetryDelay(): int
    {
        // Base delay of 5 minutes, exponentially increasing
        return min(5 * pow(2, $this->retry_count), 60); // Max 60 minutes
    }

    /**
     * Get human-readable status
     */
    public function getStatusLabel(): string
    {
        return match ($this->status) {
            self::STATUS_PENDING => 'Pending',
            self::STATUS_PROCESSING => 'Processing',
            self::STATUS_COMPLETED => 'Completed',
            self::STATUS_FAILED => 'Failed',
            self::STATUS_RETRYING => 'Retrying',
            default => 'Unknown',
        };
    }

    /**
     * Get status color for UI
     */
    public function getStatusColor(): string
    {
        return match ($this->status) {
            self::STATUS_PENDING => 'warning',
            self::STATUS_PROCESSING => 'info',
            self::STATUS_COMPLETED => 'success',
            self::STATUS_FAILED => 'danger',
            self::STATUS_RETRYING => 'warning',
            default => 'secondary',
        };
    }

    /**
     * Get integration name for display
     */
    public function getIntegrationName(): string
    {
        return $this->formIntegrationSetting->name ?? 'Unknown Integration';
    }

    /**
     * Get target system name
     */
    public function getTargetSystem(): string
    {
        return $this->formIntegrationSetting->endpointConfiguration->target_type ?? 'Unknown';
    }
}
