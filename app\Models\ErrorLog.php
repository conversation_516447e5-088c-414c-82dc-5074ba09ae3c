<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class ErrorLog extends Model
{
    use HasFactory;

    protected $table = 'error_logs';

    protected $fillable = [
        'uuid',
        'form_submission_id',
        'form_submission_sync_id',
        'error_level',
        'error_type',
        'error_message',
        'error_code',
        'stack_trace',
        'context_data',
        'resolved',
        'resolved_at',
        'resolved_by',
        'resolution_notes',
    ];

    protected $casts = [
        'context_data' => 'array',
        'resolved' => 'boolean',
        'resolved_at' => 'datetime',
        'created_at' => 'datetime',
    ];

    // Disable updated_at since this is an error log
    public $timestamps = false;
    protected $dates = ['created_at', 'resolved_at'];

    // Error level constants
    const LEVEL_INFO = 'info';
    const LEVEL_WARNING = 'warning';
    const LEVEL_ERROR = 'error';
    const LEVEL_CRITICAL = 'critical';

    // Error type constants
    const TYPE_VALIDATION_ERROR = 'validation_error';
    const TYPE_NETWORK_ERROR = 'network_error';
    const TYPE_API_ERROR = 'api_error';
    const TYPE_TIMEOUT_ERROR = 'timeout_error';
    const TYPE_AUTHENTICATION_ERROR = 'authentication_error';
    const TYPE_AUTHORIZATION_ERROR = 'authorization_error';
    const TYPE_DATA_TRANSFORMATION_ERROR = 'data_transformation_error';
    const TYPE_SYSTEM_ERROR = 'system_error';
    const TYPE_CONFIGURATION_ERROR = 'configuration_error';
    const TYPE_QUEUE_ERROR = 'queue_error';

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->uuid)) {
                $model->uuid = (string) Str::uuid();
            }
            if (empty($model->created_at)) {
                $model->created_at = now();
            }
        });
    }

    /**
     * Get the form submission this error log belongs to
     */
    public function formSubmission(): BelongsTo
    {
        return $this->belongsTo(FormSubmission::class);
    }

    /**
     * Get the form submission sync this error log belongs to
     */
    public function formSubmissionSync(): BelongsTo
    {
        return $this->belongsTo(FormSubmissionSync::class);
    }

    /**
     * Get the user who resolved this error
     */
    public function resolvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'resolved_by');
    }

    /**
     * Scope to filter by error level
     */
    public function scopeWithLevel($query, $level)
    {
        return $query->where('error_level', $level);
    }

    /**
     * Scope to filter by error type
     */
    public function scopeWithType($query, $type)
    {
        return $query->where('error_type', $type);
    }

    /**
     * Scope to filter by resolved status
     */
    public function scopeResolved($query, $resolved = true)
    {
        return $query->where('resolved', $resolved);
    }

    /**
     * Scope to filter by date range
     */
    public function scopeCreatedBetween($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Scope to get critical errors
     */
    public function scopeCritical($query)
    {
        return $query->where('error_level', self::LEVEL_CRITICAL);
    }

    /**
     * Scope to get unresolved errors
     */
    public function scopeUnresolved($query)
    {
        return $query->where('resolved', false);
    }

    /**
     * Scope to get recent errors
     */
    public function scopeRecent($query, $hours = 24)
    {
        return $query->where('created_at', '>=', now()->subHours($hours));
    }

    /**
     * Check if error is resolved
     */
    public function isResolved(): bool
    {
        return $this->resolved;
    }

    /**
     * Check if error is critical
     */
    public function isCritical(): bool
    {
        return $this->error_level === self::LEVEL_CRITICAL;
    }

    /**
     * Mark error as resolved
     */
    public function markAsResolved(User $user, string $notes = null): void
    {
        $this->update([
            'resolved' => true,
            'resolved_at' => now(),
            'resolved_by' => $user->id,
            'resolution_notes' => $notes,
        ]);

        // Create audit log entry
        AuditLog::createLog(
            AuditLog::EVENT_ERROR_RESOLVED,
            "Error resolved: {$this->error_message}",
            AuditLog::ACTOR_ADMIN,
            $user->id,
            $this->form_submission_id,
            $this->form_submission_sync_id,
            ['resolved' => false],
            ['resolved' => true, 'resolution_notes' => $notes],
            ['error_uuid' => $this->uuid]
        );
    }

    /**
     * Get human-readable error level
     */
    public function getErrorLevelLabel(): string
    {
        return match ($this->error_level) {
            self::LEVEL_INFO => 'Info',
            self::LEVEL_WARNING => 'Warning',
            self::LEVEL_ERROR => 'Error',
            self::LEVEL_CRITICAL => 'Critical',
            default => 'Unknown',
        };
    }

    /**
     * Get human-readable error type
     */
    public function getErrorTypeLabel(): string
    {
        return match ($this->error_type) {
            self::TYPE_VALIDATION_ERROR => 'Validation Error',
            self::TYPE_NETWORK_ERROR => 'Network Error',
            self::TYPE_API_ERROR => 'API Error',
            self::TYPE_TIMEOUT_ERROR => 'Timeout Error',
            self::TYPE_AUTHENTICATION_ERROR => 'Authentication Error',
            self::TYPE_AUTHORIZATION_ERROR => 'Authorization Error',
            self::TYPE_DATA_TRANSFORMATION_ERROR => 'Data Transformation Error',
            self::TYPE_SYSTEM_ERROR => 'System Error',
            self::TYPE_CONFIGURATION_ERROR => 'Configuration Error',
            self::TYPE_QUEUE_ERROR => 'Queue Error',
            default => ucwords(str_replace('_', ' ', $this->error_type)),
        };
    }

    /**
     * Get error level color for UI
     */
    public function getErrorLevelColor(): string
    {
        return match ($this->error_level) {
            self::LEVEL_INFO => 'info',
            self::LEVEL_WARNING => 'warning',
            self::LEVEL_ERROR => 'danger',
            self::LEVEL_CRITICAL => 'dark',
            default => 'secondary',
        };
    }

    /**
     * Get error priority (higher number = higher priority)
     */
    public function getPriority(): int
    {
        return match ($this->error_level) {
            self::LEVEL_CRITICAL => 4,
            self::LEVEL_ERROR => 3,
            self::LEVEL_WARNING => 2,
            self::LEVEL_INFO => 1,
            default => 0,
        };
    }

    /**
     * Get resolver name for display
     */
    public function getResolverName(): string
    {
        if ($this->resolvedBy) {
            return $this->resolvedBy->name ?? $this->resolvedBy->email ?? "User #{$this->resolved_by}";
        }

        return 'Unknown';
    }

    /**
     * Static method to create error log entry
     */
    public static function createError(
        string $level,
        string $type,
        string $message,
        ?int $formSubmissionId = null,
        ?int $formSubmissionSyncId = null,
        string $errorCode = null,
        string $stackTrace = null,
        array $contextData = null
    ): self {
        return self::create([
            'error_level' => $level,
            'error_type' => $type,
            'error_message' => $message,
            'error_code' => $errorCode,
            'stack_trace' => $stackTrace,
            'context_data' => $contextData,
            'form_submission_id' => $formSubmissionId,
            'form_submission_sync_id' => $formSubmissionSyncId,
        ]);
    }

    /**
     * Static method to create error from exception
     */
    public static function createFromException(
        \Exception $exception,
        string $level = self::LEVEL_ERROR,
        string $type = self::TYPE_SYSTEM_ERROR,
        ?int $formSubmissionId = null,
        ?int $formSubmissionSyncId = null,
        array $contextData = null
    ): self {
        return self::createError(
            $level,
            $type,
            $exception->getMessage(),
            $formSubmissionId,
            $formSubmissionSyncId,
            $exception->getCode() ? (string) $exception->getCode() : null,
            $exception->getTraceAsString(),
            array_merge($contextData ?? [], [
                'exception_class' => get_class($exception),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
            ])
        );
    }
}
