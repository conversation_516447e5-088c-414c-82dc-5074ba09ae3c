{"__meta": {"id": "01K4VAVEB0HJVV7HTYDCJAFNH6", "datetime": "2025-09-11 02:49:38", "utime": **********.913171, "method": "GET", "uri": "/admin/field-mapping-configurations/create", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 15, "start": **********.514879, "end": **********.913188, "duration": 0.39830899238586426, "duration_str": "398ms", "measures": [{"label": "Booting", "start": **********.514879, "relative_start": 0, "end": **********.683848, "relative_end": **********.683848, "duration": 0.*****************, "duration_str": "169ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.683859, "relative_start": 0.*****************, "end": **********.91319, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "229ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.703903, "relative_start": 0.****************, "end": **********.70934, "relative_end": **********.70934, "duration": 0.005437135696411133, "duration_str": "5.44ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: admin.field-mapping-configurations.create", "start": **********.771668, "relative_start": 0.***************, "end": **********.771668, "relative_end": **********.771668, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.back", "start": **********.901051, "relative_start": 0.****************, "end": **********.901051, "relative_end": **********.901051, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.button", "start": **********.901758, "relative_start": 0.*****************, "end": **********.901758, "relative_end": **********.901758, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.button", "start": **********.904106, "relative_start": 0.38922691345214844, "end": **********.904106, "relative_end": **********.904106, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.cancel", "start": **********.904901, "relative_start": 0.39002203941345215, "end": **********.904901, "relative_end": **********.904901, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.button", "start": **********.905902, "relative_start": 0.3910229206085205, "end": **********.905902, "relative_end": **********.905902, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.save", "start": **********.906738, "relative_start": 0.3918590545654297, "end": **********.906738, "relative_end": **********.906738, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.button", "start": **********.907379, "relative_start": 0.3924999237060547, "end": **********.907379, "relative_end": **********.907379, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.admin", "start": **********.90862, "relative_start": 0.3937411308288574, "end": **********.90862, "relative_end": **********.90862, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: include.sidebar", "start": **********.909207, "relative_start": 0.39432811737060547, "end": **********.909207, "relative_end": **********.909207, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: include.header", "start": **********.910287, "relative_start": 0.39540791511535645, "end": **********.910287, "relative_end": **********.910287, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.alerts", "start": **********.910762, "relative_start": 0.39588308334350586, "end": **********.910762, "relative_end": **********.910762, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 23515864, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "9.x", "tooltip": {"Laravel Version": "9.52.20", "PHP Version": "8.1.15", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 12, "nb_templates": 12, "templates": [{"name": "admin.field-mapping-configurations.create", "param_count": null, "params": [], "start": **********.771596, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/admin/field-mapping-configurations/create.blade.phpadmin.field-mapping-configurations.create", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fadmin%2Ffield-mapping-configurations%2Fcreate.blade.php:1", "ajax": false, "filename": "create.blade.php", "line": "?"}}, {"name": "components.buttons.back", "param_count": null, "params": [], "start": **********.901001, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/back.blade.phpcomponents.buttons.back", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fback.blade.php:1", "ajax": false, "filename": "back.blade.php", "line": "?"}}, {"name": "components.buttons.button", "param_count": null, "params": [], "start": **********.90171, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/button.blade.phpcomponents.buttons.button", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fbutton.blade.php:1", "ajax": false, "filename": "button.blade.php", "line": "?"}}, {"name": "components.buttons.button", "param_count": null, "params": [], "start": **********.904058, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/button.blade.phpcomponents.buttons.button", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fbutton.blade.php:1", "ajax": false, "filename": "button.blade.php", "line": "?"}}, {"name": "components.buttons.cancel", "param_count": null, "params": [], "start": **********.904825, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/cancel.blade.phpcomponents.buttons.cancel", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fcancel.blade.php:1", "ajax": false, "filename": "cancel.blade.php", "line": "?"}}, {"name": "components.buttons.button", "param_count": null, "params": [], "start": **********.90583, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/button.blade.phpcomponents.buttons.button", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fbutton.blade.php:1", "ajax": false, "filename": "button.blade.php", "line": "?"}}, {"name": "components.buttons.save", "param_count": null, "params": [], "start": **********.90669, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/save.blade.phpcomponents.buttons.save", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fsave.blade.php:1", "ajax": false, "filename": "save.blade.php", "line": "?"}}, {"name": "components.buttons.button", "param_count": null, "params": [], "start": **********.907332, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/button.blade.phpcomponents.buttons.button", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fbutton.blade.php:1", "ajax": false, "filename": "button.blade.php", "line": "?"}}, {"name": "layouts.admin", "param_count": null, "params": [], "start": **********.908573, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/layouts/admin.blade.phplayouts.admin", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Flayouts%2Fadmin.blade.php:1", "ajax": false, "filename": "admin.blade.php", "line": "?"}}, {"name": "include.sidebar", "param_count": null, "params": [], "start": **********.909159, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/include/sidebar.blade.phpinclude.sidebar", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Finclude%2Fsidebar.blade.php:1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}}, {"name": "include.header", "param_count": null, "params": [], "start": **********.91024, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/include/header.blade.phpinclude.header", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Finclude%2Fheader.blade.php:1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "partials.alerts", "param_count": null, "params": [], "start": **********.910717, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/partials/alerts.blade.phppartials.alerts", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fpartials%2Falerts.blade.php:1", "ajax": false, "filename": "alerts.blade.php", "line": "?"}}]}, "queries": {"count": 4, "nb_statements": 3, "nb_visible_statements": 4, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01636, "accumulated_duration_str": "16.36ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 15, "namespace": "middleware", "name": "auth", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}], "start": **********.728504, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php:168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "dc_local", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.735737, "duration": 0.01473, "duration_str": "14.73ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php:59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "dc_local", "explain": null, "start_percent": 0, "width_percent": 90.037}, {"sql": "select `id`, `title` from `forms` where `is_active` = 1 and `forms`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/FieldMappingConfigurationsController.php", "file": "D:\\Git Data Capture\\application\\app\\Http\\Controllers\\Admin\\FieldMappingConfigurationsController.php", "line": 111}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.7561688, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "FieldMappingConfigurationsController.php:111", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/FieldMappingConfigurationsController.php", "file": "D:\\Git Data Capture\\application\\app\\Http\\Controllers\\Admin\\FieldMappingConfigurationsController.php", "line": 111}, "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fapp%2FHttp%2FControllers%2FAdmin%2FFieldMappingConfigurationsController.php:111", "ajax": false, "filename": "FieldMappingConfigurationsController.php", "line": "111"}, "connection": "dc_local", "explain": null, "start_percent": 90.037, "width_percent": 5.868}, {"sql": "select `id`, `name`, `external_system`, `process_type`, `integration_method` from `integration_configurations` where `is_active` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/FieldMappingConfigurationsController.php", "file": "D:\\Git Data Capture\\application\\app\\Http\\Controllers\\Admin\\FieldMappingConfigurationsController.php", "line": 112}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.759563, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "FieldMappingConfigurationsController.php:112", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Admin/FieldMappingConfigurationsController.php", "file": "D:\\Git Data Capture\\application\\app\\Http\\Controllers\\Admin\\FieldMappingConfigurationsController.php", "line": 112}, "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fapp%2FHttp%2FControllers%2FAdmin%2FFieldMappingConfigurationsController.php:112", "ajax": false, "filename": "FieldMappingConfigurationsController.php", "line": "112"}, "connection": "dc_local", "explain": null, "start_percent": 95.905, "width_percent": 4.095}]}, "models": {"data": {"App\\Models\\Form": {"retrieved": 8, "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fapp%2FModels%2FForm.php:1", "ajax": false, "filename": "Form.php", "line": "?"}}, "App\\Models\\IntegrationConfiguration": {"retrieved": 4, "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fapp%2FModels%2FIntegrationConfiguration.php:1", "ajax": false, "filename": "IntegrationConfiguration.php", "line": "?"}}, "App\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fapp%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 13, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 13}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8001/admin/field-mapping-configurations/create", "action_name": "admin.field-mapping-configurations.create", "controller_action": "App\\Http\\Controllers\\Admin\\FieldMappingConfigurationsController@create", "uri": "GET admin/field-mapping-configurations/create", "controller": "App\\Http\\Controllers\\Admin\\FieldMappingConfigurationsController@create<a href=\"vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fapp%2FHttp%2FControllers%2FAdmin%2FFieldMappingConfigurationsController.php:106\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "/admin", "file": "<a href=\"vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fapp%2FHttp%2FControllers%2FAdmin%2FFieldMappingConfigurationsController.php:106\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/FieldMappingConfigurationsController.php:106-124</a>", "middleware": "web, auth, user_type:admin", "duration": "399ms", "peak_memory": "24MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1043766826 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1043766826\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1571469153 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1571469153\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-873794214 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko/20100101 Firefox/142.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"63 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">http://localhost:8001/admin/field-mapping-configurations</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IngyKzdMelFJakcrUk1xT2c2LzFOVEE9PSIsInZhbHVlIjoiSDhhZ0xqMFVHWkcvM050bE5LRHhCNlA2dVNySnJWOXloU3hKQ044dXlPK1loa1BXeUhILzRjRFdubzJkVzRMTVpINytrRm5VbzNRKzBodXlJa1Q3MDhENW5WLy95Z1pCcjI1TTdzaHBzQlk1bmw3NFNNajJSbnZrZmphU1ZYMGgiLCJtYWMiOiI0YTY0MDRkZjE0ZTRhODBmNjRlMGY5NjUzNzhlYmJkY2IwYzJhOWYzNDk0MzY3ODgxZDIwMmRmMjMxMGU1MzYyIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkI4U1h5UkR6QnBEZHpOcVJGeGdBb2c9PSIsInZhbHVlIjoib0pQdDR5cUVJNFZvbDNhWFlTOSsza3N1aUNPd0lHYnUwRXhEUjZQOUtjSDdOQVBDTFg1TFQ1c04vdm5VQnluR2Z4Y3cwSkxSZVRxRWUyZ2Z4M1h5dTJhbVl2YlEyQWFnNTVoVStJT0tDdFZRQm1Od2FqUFA4VmhOK0J2OUZ5YUciLCJtYWMiOiJmMzE0YTBlZDQyOTQ4ZDNhMWMwNjk1NTczYWZiYTAzYjFlM2ExZTdjMDgzNzBmNDFhNjEyMWE4NDFjZDExYmNmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=0, i</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-873794214\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1795456181 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4RbyFb2oU1pjc4UNp6CmU470XASLVRa4SNAgSVgT</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3qWAZMAIgZlFqvqcm0sdt0aBVP4ghvpst3qVJvce</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1795456181\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1873163164 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 11 Sep 2025 02:49:38 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1873163164\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1107330052 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4RbyFb2oU1pjc4UNp6CmU470XASLVRa4SNAgSVgT</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"56 characters\">http://localhost:8001/admin/field-mapping-configurations</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1757556567</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1107330052\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8001/admin/field-mapping-configurations/create", "action_name": "admin.field-mapping-configurations.create", "controller_action": "App\\Http\\Controllers\\Admin\\FieldMappingConfigurationsController@create"}, "badge": null}}