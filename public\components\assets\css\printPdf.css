body {
    font-family: Arial, Helvetica, sans-serif;
    margin: 0px;
    padding: 0px;
}

/*@page {
            margin: 0mm;
            margin-header: 0mm;
            margin-footer: 0mm;
            margin:"auto";
        }*/
table {
    width: 100%;
    border-collapse: collapse;
    /*table-layout: fixed;*/

    /*    border:1px solid #000;
        border-collapse: separate;
        height: 100%;*/
}

th,
td {
    padding: 5px;
}

/*@font-face {
    font-family: ArialBold;
     src: url(../../fonts/arial-bold-2.woff); 
    src: url(/fonts/arial-bold-2.woff);
}

@font-face {
    font-family: ArialRegular;
     src: url(../fonts/arial.woff); 
    src: url(/fonts/arial.woff);
}

@font-face {
    font-family: NarrowBold;
     src: url(../fonts/arial-narrow-bold.woff); 
    src: url(/fonts/arial-narrow-bold.woff);
}

@font-face {
    font-family: AbadiLightRegular;
     src: url(../fonts/abadi-mt-condensed-light-regular.woff); 
    src: url(/fonts/abadi-mt-condensed-light-regular.woff);
}*/

.t9-company-name {
    font-family: ArialBold;
    font-size: 12px;
}

.t9-company-address {
font-size:10px;
    font-family: Arial;
}
.t1-company-name,
.t2-company-name {
    font-family: ArialBold;
    font-size: 18px;
    /*font-family: Arial;*/
    /*font-weight: 600;*/

}

.t1-company-address {
    /*font-family: ArialRegular;*/
    font-size: 14px;
    font-family: Arial;
    /*font-weight: 6000;*/
}

.t2-company-address {
    /*font-family: ArialRegular;*/
    font-size: 12px;
    font-family: Arial;
    /*font-weight: 6000;*/
}

.t3.printHeader td,
.t3.printHeader td {
    font-size: 12px;
    font-family: Arial;
}

.t3_items_table {
    /*font-family: AbadiLightRegular;*/
    /*font-size: 14;*/
}

.t9.items-table td,
.t9.items-table th {
    font-family: AbadiLightRegular;
    font-size: 8px;
}

.t1-table-content,
.t2.items-table td,
.t5.items-table td,
.t5.items-table th,
.t3.items-table th,
.t3.items-table td {
    font-family: AbadiLightRegular;
    font-size: 12px;
    /*border:1px solid;*/
}

.t1-footer-content,
.t2.printFooter {
    font-family: NarrowBold;
    font-size: 13px;
}

.t4.printHeader h2 {
    font-family: NarrowBold;
    font-size: 13px;
}

.t4.printHeader td,
.t4.printHeader td {
    font-size: 12px;
    font-family: Arial;
}

.t3.printFooter {
    font-family: Arial;
    font-size: 10px;
}

.t5.printFooter {
    font-family: Arial;
    font-size: 11px;
}

/*.t3.printFooter td{
    font-family: NarrowBold;
    font-size: 11px;
}*/

.t2.printHeader h2 {
    /*    font-family: Arial !important;
        font-size: 12px !important;*/
}

.t2.printHeader p {
    /*    font-family: Arial !important;
        font-size: 8px !important;*/
}

/*.t2.printSubHeader h2 {
    font-family: Arial !important;
    font-style: bold;
    font-size: 14px !important;
}*/

.t9.printSubHeader td,
.t9.printSubHeader th {
    font-size: 8px;
}

.t2.printSubHeader td {
    font-family: Arial;
    font-size: 12px;
}

.t1-subheader {
    font-family: NarrowBold !important;
    font-size: 16px !important;
}

.bold {
    font-weight: 600;
}

/*.t2.items-table {
    font-family: Abadi MT !important;
    font-size: 10px !important;
}*/

/*.t2.printFooter {
    font-family: Arial !important;
    font-size: 10px !important;
}*/

.t2-bold {
    font-weight: bolder;
}

tr {
    height: 20%;
}

.center {
    text-align: center !important;
}

.left {
    text-align: left !important;
}

.right {
    text-align: right !important;
}

t1.items-table {
    display: grid;
    grid-template-columns: auto auto;
    table-layout: fixed;
}

/* .items-table td {
   text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      max-width: 1px; 
    } */

.text-right {
    text-align: right;
}

.text-center {
    text-align: center;
}

.border-horizontal {
    border-collapse: collapse;

    border-top: 1px solid;
    border-bottom: 1px solid;
    border-spacing: 0;
}

.border-vertical {
    border-collapse: collapse;

    border-right: 1px solid;
    border-left: 1px solid;
    border-spacing: 0;
}

.bordered-right {
    border-collapse: collapse;

    border-right: 1px solid;

    /*    border-right: 1px #000 dashed !important;
          border-top: 1px #000 dashed !important;*/

    border-spacing: 0;
}

.bordered-right-dashed {
    border-collapse: collapse;

    border-right: 1px dashed;

    /*    border-right: 1px #000 dashed !important;
          border-top: 1px #000 dashed !important;*/

    border-spacing: 0;

}

.t2 h2,
.t2 h4,
.t2 h5,
.t2 h6 {
    margin: 5px 0px;
}

.t4 h2,
.t4 h4,
.t4 h5,
.t4 h6 {
    margin: 5px 0px;
}

.bg-grey {
    background: #ccc !important;
}

.t2.printSubHeader th,
.t2.printSubHeader td,
.t2.items-table td,
.t2.items-table th {
    /*padding: 5px;*/

}

.padding-0 {
    padding: 0px !important;
}

.t5 td,
.t5 th {
    padding: 5px;
}

.border-top-0 {
    border-top: 0px !important;
}

.border-bottom-0 {
    border-bottom: 0px !important;
}

.border-left-0 {
    border-left: none !important;
}

.border-right-0 {
    border-right: none !important;
}

.t5_header {
    font-family: Arial;
    font-size: 12px;

}

.t5_sub_header {
    font-family: Arial;
    font-size: 11px;

}
.t9.printFooter td{
    font-size: 8px;
    width: 25%;
    /* box-sizing: content-box; */
}
.bd_top{
    /* border-top: 1px solid #000;
    width: 100%;
    display: table-cell !important; */
}
.t9.printFooter hr{
    margin: 1px !important;
}