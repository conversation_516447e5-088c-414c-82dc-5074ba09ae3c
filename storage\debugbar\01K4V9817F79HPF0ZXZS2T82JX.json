{"__meta": {"id": "01K4V9817F79HPF0ZXZS2T82JX", "datetime": "2025-09-11 02:21:34", "utime": **********.320629, "method": "GET", "uri": "/admin/integration-configurations/create", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 18, "start": 1757557293.868974, "end": **********.320642, "duration": 0.45166802406311035, "duration_str": "452ms", "measures": [{"label": "Booting", "start": 1757557293.868974, "relative_start": 0, "end": **********.04643, "relative_end": **********.04643, "duration": 0.*****************, "duration_str": "177ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.046441, "relative_start": 0.*****************, "end": **********.320644, "relative_end": 1.9073486328125e-06, "duration": 0.***************, "duration_str": "274ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.067087, "relative_start": 0.*****************, "end": **********.070757, "relative_end": **********.070757, "duration": 0.0036699771881103516, "duration_str": "3.67ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: admin.integration-configurations.create", "start": **********.118068, "relative_start": 0.*****************, "end": **********.118068, "relative_end": **********.118068, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.back", "start": **********.282717, "relative_start": 0.****************, "end": **********.282717, "relative_end": **********.282717, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.button", "start": **********.284757, "relative_start": 0.****************, "end": **********.284757, "relative_end": **********.284757, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.back", "start": **********.300339, "relative_start": 0.4313650131225586, "end": **********.300339, "relative_end": **********.300339, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.button", "start": **********.301766, "relative_start": 0.43279194831848145, "end": **********.301766, "relative_end": **********.301766, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.cancel", "start": **********.303279, "relative_start": 0.43430495262145996, "end": **********.303279, "relative_end": **********.303279, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.button", "start": **********.304202, "relative_start": 0.4352281093597412, "end": **********.304202, "relative_end": **********.304202, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.save", "start": **********.305414, "relative_start": 0.43643999099731445, "end": **********.305414, "relative_end": **********.305414, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.button", "start": **********.306447, "relative_start": 0.4374730587005615, "end": **********.306447, "relative_end": **********.306447, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.delete", "start": **********.311909, "relative_start": 0.4429349899291992, "end": **********.311909, "relative_end": **********.311909, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.button", "start": **********.31312, "relative_start": 0.44414591789245605, "end": **********.31312, "relative_end": **********.31312, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.admin", "start": **********.314596, "relative_start": 0.44562196731567383, "end": **********.314596, "relative_end": **********.314596, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: include.sidebar", "start": **********.315822, "relative_start": 0.44684791564941406, "end": **********.315822, "relative_end": **********.315822, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: include.header", "start": **********.317317, "relative_start": 0.44834303855895996, "end": **********.317317, "relative_end": **********.317317, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.alerts", "start": **********.317979, "relative_start": 0.449005126953125, "end": **********.317979, "relative_end": **********.317979, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 23681552, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "9.x", "tooltip": {"Laravel Version": "9.52.20", "PHP Version": "8.1.15", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 15, "nb_templates": 15, "templates": [{"name": "admin.integration-configurations.create", "param_count": null, "params": [], "start": **********.118014, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/admin/integration-configurations/create.blade.phpadmin.integration-configurations.create", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fadmin%2Fintegration-configurations%2Fcreate.blade.php:1", "ajax": false, "filename": "create.blade.php", "line": "?"}}, {"name": "components.buttons.back", "param_count": null, "params": [], "start": **********.282544, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/back.blade.phpcomponents.buttons.back", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fback.blade.php:1", "ajax": false, "filename": "back.blade.php", "line": "?"}}, {"name": "components.buttons.button", "param_count": null, "params": [], "start": **********.284626, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/button.blade.phpcomponents.buttons.button", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fbutton.blade.php:1", "ajax": false, "filename": "button.blade.php", "line": "?"}}, {"name": "components.buttons.back", "param_count": null, "params": [], "start": **********.300193, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/back.blade.phpcomponents.buttons.back", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fback.blade.php:1", "ajax": false, "filename": "back.blade.php", "line": "?"}}, {"name": "components.buttons.button", "param_count": null, "params": [], "start": **********.301689, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/button.blade.phpcomponents.buttons.button", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fbutton.blade.php:1", "ajax": false, "filename": "button.blade.php", "line": "?"}}, {"name": "components.buttons.cancel", "param_count": null, "params": [], "start": **********.303202, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/cancel.blade.phpcomponents.buttons.cancel", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fcancel.blade.php:1", "ajax": false, "filename": "cancel.blade.php", "line": "?"}}, {"name": "components.buttons.button", "param_count": null, "params": [], "start": **********.304126, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/button.blade.phpcomponents.buttons.button", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fbutton.blade.php:1", "ajax": false, "filename": "button.blade.php", "line": "?"}}, {"name": "components.buttons.save", "param_count": null, "params": [], "start": **********.305307, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/save.blade.phpcomponents.buttons.save", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fsave.blade.php:1", "ajax": false, "filename": "save.blade.php", "line": "?"}}, {"name": "components.buttons.button", "param_count": null, "params": [], "start": **********.306367, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/button.blade.phpcomponents.buttons.button", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fbutton.blade.php:1", "ajax": false, "filename": "button.blade.php", "line": "?"}}, {"name": "components.buttons.delete", "param_count": null, "params": [], "start": **********.311824, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/delete.blade.phpcomponents.buttons.delete", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fdelete.blade.php:1", "ajax": false, "filename": "delete.blade.php", "line": "?"}}, {"name": "components.buttons.button", "param_count": null, "params": [], "start": **********.313037, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/button.blade.phpcomponents.buttons.button", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fbutton.blade.php:1", "ajax": false, "filename": "button.blade.php", "line": "?"}}, {"name": "layouts.admin", "param_count": null, "params": [], "start": **********.314511, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/layouts/admin.blade.phplayouts.admin", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Flayouts%2Fadmin.blade.php:1", "ajax": false, "filename": "admin.blade.php", "line": "?"}}, {"name": "include.sidebar", "param_count": null, "params": [], "start": **********.315727, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/include/sidebar.blade.phpinclude.sidebar", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Finclude%2Fsidebar.blade.php:1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}}, {"name": "include.header", "param_count": null, "params": [], "start": **********.317267, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/include/header.blade.phpinclude.header", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Finclude%2Fheader.blade.php:1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "partials.alerts", "param_count": null, "params": [], "start": **********.317931, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/partials/alerts.blade.phppartials.alerts", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fpartials%2Falerts.blade.php:1", "ajax": false, "filename": "alerts.blade.php", "line": "?"}}]}, "queries": {"count": 4, "nb_statements": 3, "nb_visible_statements": 4, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0074399999999999996, "accumulated_duration_str": "7.44ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 15, "namespace": "middleware", "name": "auth", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}], "start": **********.092968, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php:168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "dc_local", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.099735, "duration": 0.0044599999999999996, "duration_str": "4.46ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php:59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "dc_local", "explain": null, "start_percent": 0, "width_percent": 59.946}, {"sql": "select * from `field_definitions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "admin.integration-configurations.create", "file": "D:\\Git Data Capture\\application\\resources\\views/admin/integration-configurations/create.blade.php", "line": 283}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.2906961, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "admin.integration-configurations.create:283", "source": {"index": 15, "namespace": "view", "name": "admin.integration-configurations.create", "file": "D:\\Git Data Capture\\application\\resources\\views/admin/integration-configurations/create.blade.php", "line": 283}, "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fadmin%2Fintegration-configurations%2Fcreate.blade.php:283", "ajax": false, "filename": "create.blade.php", "line": "283"}, "connection": "dc_local", "explain": null, "start_percent": 59.946, "width_percent": 27.823}, {"sql": "select * from `field_definitions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "admin.integration-configurations.create", "file": "D:\\Git Data Capture\\application\\resources\\views/admin/integration-configurations/create.blade.php", "line": 559}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.307221, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "admin.integration-configurations.create:559", "source": {"index": 15, "namespace": "view", "name": "admin.integration-configurations.create", "file": "D:\\Git Data Capture\\application\\resources\\views/admin/integration-configurations/create.blade.php", "line": 559}, "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fadmin%2Fintegration-configurations%2Fcreate.blade.php:559", "ajax": false, "filename": "create.blade.php", "line": "559"}, "connection": "dc_local", "explain": null, "start_percent": 87.769, "width_percent": 12.231}]}, "models": {"data": {"App\\Models\\FieldDefinition": {"retrieved": 28, "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fapp%2FModels%2FFieldDefinition.php:1", "ajax": false, "filename": "FieldDefinition.php", "line": "?"}}, "App\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fapp%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 29, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 29}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8001/admin/integration-configurations/create", "action_name": "admin.integration-configurations.create", "controller_action": "App\\Http\\Controllers\\Admin\\IntegrationConfigurationController@create", "uri": "GET admin/integration-configurations/create", "controller": "App\\Http\\Controllers\\Admin\\IntegrationConfigurationController@create<a href=\"vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fapp%2FHttp%2FControllers%2FAdmin%2FIntegrationConfigurationController.php:36\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "/admin", "file": "<a href=\"vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fapp%2FHttp%2FControllers%2FAdmin%2FIntegrationConfigurationController.php:36\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/IntegrationConfigurationController.php:36-50</a>", "middleware": "web, auth, user_type:admin", "duration": "452ms", "peak_memory": "24MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1902927060 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1902927060\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-450367491 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-450367491\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1822203347 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko/20100101 Firefox/142.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"63 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IklNbEZmUkxQRGJDRlROWldSdy9CM1E9PSIsInZhbHVlIjoiOHR6R0JkdFBwdGpFRGtMcktqb2cydm9IaEVOM0FZdXZ2YkcvUWtDVzl2ZnkzUHhWSHdIZnFvUGFOZWRoUXRlVEcrbzJNRm9PbkNhU0F3WGRLWWpSbjNSYU1FTHppb0UxNGlNcWpqZWRLT25NZkFEWHIvMHc5cEg4UnoxTE5SdEIiLCJtYWMiOiI4NTkxN2Q2YjcxZTA5YjZmYmQyNTUxYzhlYjQ1NjI1M2VmOWRhNmY1OTM4YjQ4NDk3ZGYxMGZkOGZjZGUzOWZjIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IldvOFZpQ2RCZ2Y5NlJVczEzNW1qc2c9PSIsInZhbHVlIjoialJlcG1aVTNtRzZHZjhYMmh6T1J4emx0eE1CbERuQ09VQ1BadndTN3N3S3NxVHdnSkpIdERFMFB1S3Z0T1pBWDFTOHRYQ0daSGQySnJOaHkrY0FCS21qTTkvTnVBTVZocG1tR1VlZ0hSME9sWll0MGRaSjFDc0hYK0p3YTFTUE8iLCJtYWMiOiI3MWQyMTMzMzA0YWNkOTY3ODFkMmU2NjE2M2ViZmFjYmExMDY1ZWVjMzFiM2VmYmIyYTk0YTI0NjhiMjdjZDA5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=0, i</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1822203347\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1126092453 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4RbyFb2oU1pjc4UNp6CmU470XASLVRa4SNAgSVgT</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3qWAZMAIgZlFqvqcm0sdt0aBVP4ghvpst3qVJvce</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1126092453\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-273058516 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 11 Sep 2025 02:21:34 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-273058516\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1894146494 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4RbyFb2oU1pjc4UNp6CmU470XASLVRa4SNAgSVgT</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"61 characters\">http://localhost:8001/admin/integration-configurations/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1757556567</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1894146494\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8001/admin/integration-configurations/create", "action_name": "admin.integration-configurations.create", "controller_action": "App\\Http\\Controllers\\Admin\\IntegrationConfigurationController@create"}, "badge": null}}