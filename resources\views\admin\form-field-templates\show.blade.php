@extends('layouts.admin')
@section('pageTitle', 'Form Field Template Details')

@section('styles')
    @parent
    <style>
        .json-display {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
        }
        .icon-preview {
            font-size: 24px;
            margin-right: 10px;
        }
    </style>
@endsection

@section('content')
    <div class="card card-default">
        <div class="card-header separator">
            <div class="card-title mainheading">
                <h4>Form Field Template Details</h4>
            </div>
            <div class="card-controls">
                <ul>
                    <li>
                        <a class="btn btn-secondary" href="{{ route('admin.form-field-templates.index') }}">
                            <i class="fa fa-arrow-left"></i> Back to List
                        </a>
                    </li>
                    <li>
                        <a class="btn btn-primary" href="{{ route('admin.form-field-templates.edit', $formFieldTemplate) }}">
                            <i class="fa fa-edit"></i> Edit
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <div class="card-block">
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-bordered">
                        <tr>
                            <th width="30%">Title</th>
                            <td>{{ $formFieldTemplate->title }}</td>
                        </tr>
                        <tr>
                            <th>Key</th>
                            <td><code>{{ $formFieldTemplate->key }}</code></td>
                        </tr>
                        <tr>
                            <th>Icon</th>
                            <td>
                                <i class="fa fa-{{ $formFieldTemplate->icon }} icon-preview"></i>
                                {{ $formFieldTemplate->icon }}
                            </td>
                        </tr>
                        <tr>
                            <th>Created At</th>
                            <td>{{ $formFieldTemplate->created_at->format('Y-m-d H:i:s') }}</td>
                        </tr>
                        <tr>
                            <th>Updated At</th>
                            <td>{{ $formFieldTemplate->updated_at->format('Y-m-d H:i:s') }}</td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    @php
                        $schema = is_string($formFieldTemplate->schema) ? json_decode($formFieldTemplate->schema, true) : $formFieldTemplate->schema;
                    @endphp

                    <h5>Schema Details</h5>
                    <table class="table table-bordered">
                        <tr>
                            <th width="30%">Label</th>
                            <td>{{ $schema['label'] ?? 'N/A' }}</td>
                        </tr>
                        <tr>
                            <th>Type</th>
                            <td><span class="badge badge-info">{{ $schema['type'] ?? 'N/A' }}</span></td>
                        </tr>
                        <tr>
                            <th>Key</th>
                            <td><code>{{ $schema['key'] ?? 'N/A' }}</code></td>
                        </tr>
                        <tr>
                            <th>Input Field</th>
                            <td>
                                @if($schema['input'] ?? false)
                                    <span class="badge badge-success">Yes</span>
                                @else
                                    <span class="badge badge-secondary">No</span>
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <th>Required</th>
                            <td>
                                @if($schema['validate']['required'] ?? false)
                                    <span class="badge badge-warning">Required</span>
                                @else
                                    <span class="badge badge-secondary">Optional</span>
                                @endif
                            </td>
                        </tr>
                    </table>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <h5>Complete JSON Schema</h5>
                    <div class="json-display">{{ json_encode($schema, JSON_PRETTY_PRINT) }}</div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <h5>Usage Example</h5>
                    <div class="json-display">{{ json_encode([
                        $formFieldTemplate->key => [
                            'title' => $formFieldTemplate->title,
                            'key' => $formFieldTemplate->key,
                            'icon' => $formFieldTemplate->icon,
                            'schema' => $schema
                        ]
                    ], JSON_PRETTY_PRINT) }}</div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="btn-group">
                        <a href="{{ route('admin.form-field-templates.edit', $formFieldTemplate) }}" class="btn btn-primary">
                            <i class="fa fa-edit"></i> Edit Template
                        </a>
                        <form method="POST" action="{{ route('admin.form-field-templates.destroy', $formFieldTemplate) }}" style="display: inline-block;" onsubmit="return confirm('Are you sure you want to delete this template?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger">
                                <i class="fa fa-trash"></i> Delete Template
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
