/* ============================================================
 * Form Elements
 * This file applies various jQuery plugins to form elements
 * For DEMO purposes only. Extract what you need.
 * ============================================================ */
(function ($) {

    'use strict';

    var getBaseURL = function () {
        var url = document.URL;
        return url.substr(0, url.lastIndexOf('/'));
    }

    $(document).ready(function () {

        //Multiselect - Select2 plug-in
        // $("#multi").val(["Jim", "Lucy"]).select2();
        //Date Pickers
        $('#datepicker-range, #datepicker-component, #datepicker-component2').datepicker();
        $('#datepicker-embeded').datepicker({
            daysOfWeekDisabled: "0,1"
        });
        //Typehead Sample Code

        // Basic Sample using Bloodhound
        // constructs the suggestion engine

//        var countries = new Bloodhound({
//            datumTokenizer: Bloodhound.tokenizers.whitespace,
//            queryTokenizer: Bloodhound.tokenizers.whitespace,
//            prefetch: 'http://pages.revox.io/json/countries-list.json'
//        });
//
//        var bestPictures = new Bloodhound({
//            datumTokenizer: Bloodhound.tokenizers.obj.whitespace('value'),
//            queryTokenizer: Bloodhound.tokenizers.whitespace,
//            prefetch: 'http://pages.revox.io/json/drop-countries.json',
//            remote: {
//                url: 'http://pages.revox.io/json/drop-countries.json',
//                wildcard: '%QUERY'
//            }
//        });
//        // passing in `null` for the `options` arguments will result in the default
//        // options being used
//        $('.sample-typehead').typeahead(null, {
//            name: 'countries',
//            source: countries
//        });
//
//        //Custom Template
//        $('#custom-templates .typeahead').typeahead(null, {
//            name: 'best-pictures',
//            display: 'value',
//            source: bestPictures,
//            templates: {
//                empty: [
//                    '<div class="empty-message">',
//                    'unable to find any Best Picture winners that match the current query',
//                    '</div>'
//                ].join('\n'),
//                suggestion: Handlebars.compile('<div>{{value}}– {{year}}</div>')
//            }
//        });

        $('#daterangepicker').daterangepicker({
            timePicker: true,
            timePickerIncrement: 30,
            format: 'MM/DD/YYYY h:mm A'
        }, function (start, end, label) {
            console.log(start.toISOString(), end.toISOString(), label);
        });
        /* Time picker
         * https://github.com/m3wolf/bootstrap3-timepicker
         */
        $('#timepicker').timepicker().on('show.timepicker', function (e) {
            var widget = $('.bootstrap-timepicker-widget');
            widget.find('.glyphicon-chevron-up').removeClass().addClass('pg-arrow_maximize');
            widget.find('.glyphicon-chevron-down').removeClass().addClass('pg-arrow_minimize');
        });
        $('.timepicker').timepicker().on('show.timepicker', function (e) {
            var widget = $('.bootstrap-timepicker-widget');
            widget.find('.glyphicon-chevron-up').removeClass().addClass('pg-arrow_maximize');
            widget.find('.glyphicon-chevron-down').removeClass().addClass('pg-arrow_minimize');
        });
        // disabling dates
        var nowTemp = new Date();
        var now = new Date(nowTemp.getFullYear(), nowTemp.getMonth(), nowTemp.getDate(), 0, 0, 0, 0);
        //Input mask - Input helper
        $(function ($) {
            $("#date").mask("99/99/9999");
            $("#phone").mask("(*************");
            $("#tin").mask("999-999-999-999");
            $("#ssn").mask("***********");
        });
        //Autonumeric plug-in - automatic addition of dollar signs,etc controlled by tag attributes
        $('.autonumeric').autoNumeric('init');
        //Drag n Drop up-loader
        // $("div#myId").dropzone({
        //     url: "/file/post"
        // });
        // //Single instance of tag inputs - can be initiated with simply using data-role="tagsinput" attribute in any input field
        // $('.custom-tag-input').tagsinput({

        // });
        var myCustomTemplates = {
            "font-styles": function (locale) {
                return '<li class="dropdown">' + '<a data-toggle="dropdown" class="btn btn-default dropdown-toggle ">' + '<span class="editor-icon editor-icon-headline"></span>' + '<span class="current-font">Normal</span>' + '<b class="caret"></b>' + '</a>' + '<ul class="dropdown-menu">' + '<li><a tabindex="-1" data-wysihtml5-command-value="p" data-wysihtml5-command="formatBlock" href="javascript:;" unselectable="on">Normal</a></li>' + '<li><a tabindex="-1" data-wysihtml5-command-value="h1" data-wysihtml5-command="formatBlock" href="javascript:;" unselectable="on">1</a></li>' + '<li><a tabindex="-1" data-wysihtml5-command-value="h2" data-wysihtml5-command="formatBlock" href="javascript:;" unselectable="on">2</a></li>' + '<li><a tabindex="-1" data-wysihtml5-command-value="h3" data-wysihtml5-command="formatBlock" href="javascript:;" unselectable="on">3</a></li>' + '<li><a tabindex="-1" data-wysihtml5-command-value="h4" data-wysihtml5-command="formatBlock" href="javascript:;" unselectable="on">4</a></li>' + '<li><a tabindex="-1" data-wysihtml5-command-value="h5" data-wysihtml5-command="formatBlock" href="javascript:;" unselectable="on">5</a></li>' + '<li><a tabindex="-1" data-wysihtml5-command-value="h6" data-wysihtml5-command="formatBlock" href="javascript:;" unselectable="on">6</a></li>' + '</ul>' + '</li>';
            },
            emphasis: function (locale) {
                return '<li>' + '<div class="btn-group">' + '<a tabindex="-1" title="CTRL+B" data-wysihtml5-command="bold" class="btn  btn-default" href="javascript:;" unselectable="on"><i class="editor-icon editor-icon-bold"></i></a>' + '<a tabindex="-1" title="CTRL+I" data-wysihtml5-command="italic" class="btn  btn-default" href="javascript:;" unselectable="on"><i class="editor-icon editor-icon-italic"></i></a>' + '<a tabindex="-1" title="CTRL+U" data-wysihtml5-command="underline" class="btn  btn-default" href="javascript:;" unselectable="on"><i class="editor-icon editor-icon-underline"></i></a>' + '</div>' + '</li>';
            },
            blockquote: function (locale) {
                return '<li>' + '<a tabindex="-1" data-wysihtml5-display-format-name="false" data-wysihtml5-command-value="blockquote" data-wysihtml5-command="formatBlock" class="btn  btn-default" href="javascript:;" unselectable="on">' + '<i class="editor-icon editor-icon-quote"></i>' + '</a>' + '</li>'
            },
            lists: function (locale) {
                return '<li>' + '<div class="btn-group">' + '<a tabindex="-1" title="Unordered list" data-wysihtml5-command="insertUnorderedList" class="btn  btn-default" href="javascript:;" unselectable="on"><i class="editor-icon editor-icon-ul"></i></a>' + '<a tabindex="-1" title="Ordered list" data-wysihtml5-command="insertOrderedList" class="btn  btn-default" href="javascript:;" unselectable="on"><i class="editor-icon editor-icon-ol"></i></a>' + '<a tabindex="-1" title="Outdent" data-wysihtml5-command="Outdent" class="btn  btn-default" href="javascript:;" unselectable="on"><i class="editor-icon editor-icon-outdent"></i></a>' + '<a tabindex="-1" title="Indent" data-wysihtml5-command="Indent" class="btn  btn-default" href="javascript:;" unselectable="on"><i class="editor-icon editor-icon-indent"></i></a>' + '</div>' + '</li>'
            },
            image: function (locale) {
                return '<li>' + '<div class="bootstrap-wysihtml5-insert-image-modal modal fade">' + '<div class="modal-dialog ">' + '<div class="modal-content">' + '<div class="modal-header">' + '<a data-dismiss="modal" class="close">×</a>' + '<h3>Insert image</h3>' + '</div>' + '<div class="modal-body">' + '<input class="bootstrap-wysihtml5-insert-image-url form-control" value="http://">' + '</div>' + '<div class="modal-footer">' + '<a data-dismiss="modal" class="btn btn-default">Cancel</a>' + '<a data-dismiss="modal" class="btn btn-primary">Insert image</a>' + '</div>' + '</div>' + '</div>' + '</div>' + '<a tabindex="-1" title="Insert image" data-wysihtml5-command="insertImage" class="btn  btn-default" href="javascript:;" unselectable="on">' + '<i class="editor-icon editor-icon-image"></i>' + '</a>' + '</li>'
            },
            link: function (locale) {
                return '<li>' + '<div class="bootstrap-wysihtml5-insert-link-modal modal fade">' + '<div class="modal-dialog ">' + '<div class="modal-content">' + '<div class="modal-header">' + '<a data-dismiss="modal" class="close">×</a>' + '<h3>Insert link</h3>' + '</div>' + '<div class="modal-body">' + '<input class="bootstrap-wysihtml5-insert-link-url form-control" value="http://">' + '<label class="checkbox"> <input type="checkbox" checked="" class="bootstrap-wysihtml5-insert-link-target">Open link in new window</label>' + '</div>' + '<div class="modal-footer">' + '<a data-dismiss="modal" class="btn btn-default">Cancel</a>' + '<a data-dismiss="modal" class="btn btn-primary" href="#">Insert link</a>' + '</div>' + '</div>' + '</div>' + '</div>' + '<a tabindex="-1" title="Insert link" data-wysihtml5-command="createLink" class="btn  btn-default" href="javascript:;" unselectable="on">' + '<i class="editor-icon editor-icon-link"></i>' + '</a>' + '</li>'
            },
            html: function (locale) {
                return '<li>' + '<div class="btn-group">' + '<a tabindex="-1" title="Edit HTML" data-wysihtml5-action="change_view" class="btn  btn-default" href="javascript:;" unselectable="on">' + '<i class="editor-icon editor-icon-html"></i>' + '</a>' + '</div>' + '</li>'
            }
        }
        //TODO: chrome doesn't apply the plugin on load
        // setTimeout(function () {
        //     $('#wysiwyg5').wysihtml5({
        //         html: true,
        //         stylesheets: ["pages/css/editor.css"],
        //         customTemplates: myCustomTemplates
        //     });
        // }, 500);
//        $('.summernote').wrap("<div class='card-block no-scroll card-toolbar'><div class='summernote-wrapper'></div></div>");
        var trap = false;
//        $('.summernote').each(function () {

//            console.log($(this).attr('placeholder'))
        var clearAllButton = function (context)
        {
            var ui = $.summernote.ui;
            // create button
            var button = ui.button({
                contents: '<b> Clear </b>',
                click: function ()
                {
                    context.invoke('code', '');
                    var named = $(context.$note[0]).attr('name');
//                    
                    if (named != "description" && named != "desc_itinerary" && named != "desc_itinerary_edit") {

                        $(context.$note[0]).summernote('editor.insertUnorderedList');
                    }

                }
            });
            return button.render();
        };
        
        
        $(".summernote_min").each(function () {
            var name = $(this).attr('name');
            var toolbard = [
                ['para', ['ul', 'bold', 'italic', 'underline']],
                ['insert', []],
                ['misc', ['clearAll']],
            ];
            
            $(this).summernote({
                placeholder: $(this).attr('placeholder'),
                disableDragAndDrop: true,
                spellCheck: true,
                disableGrammar: false,
                toolbar: toolbard,
                height: 200,
                buttons: {
                    clearAll: clearAllButton
                },
                onfocus: function (e) {
                    $('body').addClass('overlay-disabled');
                },
                onblur: function (e) {
                    $('body').removeClass('overlay-disabled');
                },
                callbacks: {
                    onPaste: function (e) {
                        if (document.queryCommandSupported("insertText")) {
                            var text = $(e.currentTarget).html();
                            var bufferText = ((e.originalEvent || e).clipboardData || window.clipboardData).getData('Text');
                            setTimeout(function () {
                                document.execCommand('insertText', false, bufferText);
                            }, 10);
                            e.preventDefault();
                        } else { //IE
                            var text = window.clipboardData.getData("text")
                            if (trap) {
                                trap = false;
                            } else {
                                trap = true;
                                setTimeout(function () {
                                    document.execCommand('paste', false, text);
                                }, 10);
                                e.preventDefault();
                            }
                        }

                    },
                    onImageUpload: function (data) {
                        data.pop();
                    }

                }
            });
        })
         $(".summernote").each(function () {
            var name = $(this).attr('name');
            var toolbard = [
                ['para', ['ul', 'bold', 'italic', 'underline']],
                ['insert', ['link']],
                ['misc', ['clearAll']],
            ];
            if (name == "description" || name == "desc_itinerary") {
                toolbard = [
                    ['para', ['ul', 'bold', 'italic', 'underline']],
                    ['misc', ['clearAll']]
                ];
            }
            $(this).summernote({
                placeholder: $(this).attr('placeholder'),
                disableDragAndDrop: true,
                spellCheck: true,
                disableGrammar: false,
                toolbar: toolbard,
                height: 200,
                buttons: {
                    clearAll: clearAllButton
                },
                onfocus: function (e) {
                    $('body').addClass('overlay-disabled');
                },
                onblur: function (e) {
                    $('body').removeClass('overlay-disabled');
                },
                callbacks: {
                    onPaste: function (e) {
                        if (document.queryCommandSupported("insertText")) {
                            var text = $(e.currentTarget).html();
                            var bufferText = ((e.originalEvent || e).clipboardData || window.clipboardData).getData('Text');
                            setTimeout(function () {
                                document.execCommand('insertText', false, bufferText);
                            }, 10);
                            e.preventDefault();
                        } else { //IE
                            var text = window.clipboardData.getData("text")
                            if (trap) {
                                trap = false;
                            } else {
                                trap = true;
                                setTimeout(function () {
                                    document.execCommand('paste', false, text);
                                }, 10);
                                e.preventDefault();
                            }
                        }

                    },
                    onImageUpload: function (data) {
                        data.pop();
                    }

                }
            });
        })
        var uploadImageContent = function (image, editor) {
            var data = new FormData();
            var token = $('meta[name="csrf-token"]').attr('content');
            data.append('_token', token);

            data.append('file', image);
            $.ajax({
                url: '/admin/blogs/upload_image',
                cache: false,
                contentType: false,
                processData: false,
                data: data,
                type: "post",
                success: function (url) {
                    var image = $('<img>').attr('src', url);
                    $(editor).summernote('insertNode', image[0]);
                },
                error: function (data) {
                    console.log(data);
                }});
        }

        $(".summernote_full").each(function () {
            var name = $(this).attr('name');
            var toolbard = [
                ['style', ['style']],
                ['font', ['']],
                ['fontsize', ['fontsize']],
                ['color', ['color']],
                ['para', ['ul', 'ol', 'paragraph']],
                ['height', ['height']],
                ['Insert', ['picture', 'link', 'table']],
                ['misc', ['clearAll', 'undo', 'redo']],
            ];
            $(this).summernote({
                placeholder: $(this).attr('placeholder'),
                disableDragAndDrop: true,
                spellCheck: true,
                disableGrammar: false,
                toolbar: toolbard,
                height: 300,
                buttons: {
                    clearAll: clearAllButton
                },
                onfocus: function (e) {
                    $('body').addClass('overlay-disabled');
                },
                onblur: function (e) {
                    $('body').removeClass('overlay-disabled');
                },
                callbacks: {
                    onImageUpload: function (image) {
                        var editor = $(this);
                        uploadImageContent(image[0], editor);
                    },
//                    onPaste: function (e) {
//                        if (document.queryCommandSupported("insertText")) {
//                            var text = $(e.currentTarget).html();
//                            var bufferText = ((e.originalEvent || e).clipboardData || window.clipboardData).getData('Text');
//
//                            setTimeout(function () {
//                                document.execCommand('insertText', false, bufferText);
//                            }, 10);
//                            e.preventDefault();
//                        } else { //IE
//                            var text = window.clipboardData.getData("text")
//                            if (trap) {
//                                trap = false;
//                            } else {
//                                trap = true;
//                                setTimeout(function () {
//                                    document.execCommand('paste', false, text);
//                                }, 10);
//                                e.preventDefault();
//                            }
//                        }
//
//                    },
//                    onImageUpload: function (data) {
//                        data.pop();
//                    }
//
                }
            });
        })

//            setTimeout(function () {
//                
//            }, 1000)

//        })



    });
})(window.jQuery);