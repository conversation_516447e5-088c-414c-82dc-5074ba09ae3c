<?php

namespace App\Services;

class HelperService
{
    /**
     * Format a date into a standardized display format.
     *
     * @param \DateTimeInterface|string $date
     * @param string $format
     * @return string
     */
    public function formatDate($date, $format = 'Y-m-d H:i:s'): string
    {
        if (!$date instanceof \DateTimeInterface) {
            $date = new \DateTime($date);
        }

        return $date->format($format);
    }
}
