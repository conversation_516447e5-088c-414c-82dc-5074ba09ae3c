@extends('layouts.mobile')
@section('pageTitle', $form->title)

@section('styles')
    @parent
    <link rel="stylesheet" href="https://cdn.form.io/js/formio.full.min.css">
    {{-- <link rel="stylesheet" href="https://cdn.form.io/js/formio.form.min.css"> --}}
    <style>
        .form-preview-container {
            /* border: 1px solid #ddd; */
            border-radius: 4px;
            /* padding: 20px; */
            /* margin-bottom: 20px; */
            background-color: #fff;
        }

        /* .lineEntryCont .editgrid-lineEntry-addRow { */
        .lineEntryCont [ref=editgrid-lineEntry-addRow] {
            display: none;
        }

        .btn-sm,
        .btn-group-sm>.btn {
            padding-left: 5px;
            padding-right: 5px;

        }

        .form-preview-container.mobile .editgrid-table-container .table-responsive {
            overflow-x: hidden;
        }

        .form-preview-container.mobile .editgrid-table-container .table-responsive table {
            table-layout: auto !important;
        }

        .form-preview-container.mobile .editgrid-table-body tr {
            position: relative;
            transition: transform 0.3s ease;
            overflow: hidden
        }

        .form-preview-container.mobile .editgrid-table-head tr .editgrid-table-column:last-child {
            display: none;

        }

        .form-preview-container.mobile .editgrid-table-body tr .editgrid-table-column:last-child {
            position: absolute;
            top: 0;
            bottom: 0;
            right: 0;
            /* pin to right side of the row */
            display: flex;
            align-items: center;
            background: #f8f8f8;
            padding: 0 10px;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            white-space: nowrap;

        }

        .form-preview-container.mobile .editgrid-table-body tr.show-actions .editgrid-table-column:last-child {
            /* display: flex; */
            transform: translateX(0);

        }
    </style>
@endsection

@section('content')
    <div class="card card-default">
        {{-- <div class="card-header separator">
            <div class="card-title mainheading">
                <h4>{{ $form->title }}</h4>
            </div>
            <div class="card-controls">
                <ul>
                    <li>
                        <a class="btn btn-secondary" href="{{ $backUrl }}">
                            {{ trans('global.back') }}
                        </a>
                    </li>

                </ul>
            </div>
        </div> --}}

        <div class="card-block">
            <div class="form-preview-container {{ $form->device_view }}">
                {{-- <form action="{{ route('forms.submit', $form->id) }}" method="POST" id="datacapture_form"> --}}
                @csrf
                {{-- <input type="hidden" name="submit_json" id="submit_json" value="" /> --}}
                <div id="formPreview">

                </div>
                {{-- <a id="cancelBtn" class="btn btn-secondary" href="{{ $backUrl }}">
                    {{ trans('global.cancel') }}
                </a> --}}
                {{-- </form> --}}
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    @parent
    {{-- <script src="https://cdn.form.io/js/formio.form.min.js"></script> --}}
    <script src="https://cdn.form.io/js/formio.full.min.js"></script>

    <script>
        var formDefinition = {!! $form->content !!};
        var redirect_to = "{{ $form->redirect_to ? route('forms.render', $form->redirect_to) : null }}";
        // console.log(redirect_to);
        var formId = "{{ $form->id }}";
        const FORM_KEY = `form_data_${formId}`; // current form's data
        const BACKUP_KEY = `form_backup_${formId}`;
        const NEXT_FORM_DATA_KEY = 'form_forward_data';

        var cancelBtn =
            "<a id='cancelBtn' class='btn btn-secondary' href='javascript:window.history.back()'>{{ trans('global.cancel') }}</a>";
        // console.log(formDefinition);
        Formio.createForm(document.getElementById('formPreview'), formDefinition)
            .then(function(form) {
                // const form1Data = sessionStorage.getItem('form1Data');
                // if (form1Data) {
                //     form.submission = {
                //         data: JSON.parse(form1Data)
                //     };
                //     sessionStorage.setItem('form1Data', null);

                // }
                const restoreData = (isBackNav = false) => {
                    let data = null;

                    // If we have forward data (from previous form), use it once and clear it
                    if (sessionStorage.getItem(NEXT_FORM_DATA_KEY)) {
                        data = JSON.parse(sessionStorage.getItem(NEXT_FORM_DATA_KEY));
                        sessionStorage.removeItem(NEXT_FORM_DATA_KEY); // clear after use
                    }
                    // Else check if backup exists (for back navigation)
                    else if (!isBackNav && sessionStorage.getItem(BACKUP_KEY)) {
                        // Back flow
                        data = JSON.parse(sessionStorage.getItem(BACKUP_KEY));
                        sessionStorage.removeItem(BACKUP_KEY); // clear after using
                    }

                    if (data) {
                        form.submission = {
                            data
                        };
                    }
                };
                restoreData(false);
                window.addEventListener('beforeunload', () => {
                    sessionStorage.setItem(FORM_KEY, JSON.stringify(form.submission.data));
                });
                form.on('change', () => {
                    applyTruncateMiddle();
                });
                $('.formio-component-submit').prepend(cancelBtn);

                // Handle form submission for preview
                form.on('submit', function(submission) {
                    // console.log('Form submission (preview mode):', submission);

                    // Show submission data in an alert for preview
                    var submissionData = JSON.stringify(submission.data, null, 2);
                    // console.log(submissionData);
                    // $("#submit_json").val(submissionData);
                    // $("#datacapture_form").submit();

                    sessionStorage.setItem(NEXT_FORM_DATA_KEY, JSON.stringify(submission.data));

                    // For back navigation
                    sessionStorage.setItem(BACKUP_KEY, JSON.stringify(submission.data));
                    if (redirect_to) {
                        // sessionStorage.setItem('form1Data', submissionData);
                        window.location.href = redirect_to;
                    } else {
                        alert('Form Submitted (Preview Mode)\n\nSubmission Data:\n' + submissionData);

                    }


                    // http://127.0.0.1:8000/forms/10/render
                    // Reset the form
                    // form.submission = {
                    //     data: {}
                    // };
                });
                window.addEventListener('pageshow', (event) => {
                    // `pageshow` fires even from bfcache (browser back cache)
                    if (event.persisted || performance.getEntriesByType("navigation")[0]?.type ===
                        'back_forward') {
                        restoreData(true);
                    }
                });
                // Track this form as last visited
                // Handle form change events
                // form.on('change', function(changed) {
                //     console.log('Form changed:', changed);
                // });
            })
            .catch(function(error) {
                console.error('Error rendering form:', error);
                document.getElementById('formPreview').innerHTML =
                    '<div class="alert alert-danger">Error loading form preview. Please check the form definition.</div>';
            });

        function getTextWidth(text, el) {
            const canvas = getTextWidth.canvas || (getTextWidth.canvas = document.createElement("canvas"));
            const ctx = canvas.getContext("2d");
            const style = window.getComputedStyle(el);
            ctx.font = `${style.fontWeight} ${style.fontSize} ${style.fontFamily}`;
            return ctx.measureText(text).width;
        }

        function truncateMiddleToFit(el) {
            const originalText = el.getAttribute('data-full-text') || el.textContent.trim();
            el.setAttribute('data-full-text', originalText);
            el.textContent = originalText;

            const availableWidth = el.offsetWidth;

            // If full text fits, do nothing
            if (getTextWidth(originalText, el) <= availableWidth) return;

            let frontCount = Math.ceil(originalText.length / 2);
            let backCount = originalText.length - frontCount;

            while (frontCount > 1 && backCount > 1) {
                const candidate = originalText.slice(0, frontCount) + '...' + originalText.slice(-backCount);
                if (getTextWidth(candidate, el) <= availableWidth) {
                    el.textContent = candidate;
                    return;
                }
                frontCount--;
                backCount--;
            }

            // Fallback: minimal characters on each side
            el.textContent = originalText.slice(0, 1) + '...' + originalText.slice(-1);
        }
        // Example: Apply to all TDs with .truncate-middle
        function applyTruncateMiddle() {
            // console.log("a");
            document.querySelectorAll('tbody td.editgrid-table-column:not(:last-child)').forEach(truncateMiddleToFit);
        }

        window.addEventListener('resize', applyTruncateMiddle);
        document.addEventListener('DOMContentLoaded', applyTruncateMiddle);

        let startX = 0;
        let threshold = 50; // px for swipe to trigger

        $(document)
            .on('touchstart', '.editgrid-table-body tr', function(e) {
                startX = e.originalEvent.touches[0].clientX;
            })
            .on('touchend', '.editgrid-table-body tr', function(e) {
                let endX = e.originalEvent.changedTouches[0].clientX;
                let diffX = endX - startX;

                let $row = $(this).closest('tr');

                if (diffX < -threshold) {
                    // Swipe left → show actions
                    $('.editgrid-table-body tr').removeClass('show-actions'); // close others
                    $row.addClass('show-actions');
                } else if (diffX > threshold) {
                    // Swipe right → hide actions
                    $row.removeClass('show-actions');
                }
            });
    </script>
@endsection
