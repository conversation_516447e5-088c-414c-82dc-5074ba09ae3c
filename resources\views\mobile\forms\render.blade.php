@extends('layouts.mobile')
@section('pageTitle', $form->title)

@section('styles')
    @parent
    <link rel="stylesheet" href="https://cdn.form.io/js/formio.full.min.css">
    {{-- <link rel="stylesheet" href="https://cdn.form.io/js/formio.form.min.css"> --}}
    <style>
        .form-preview-container {
            /* border: 1px solid #ddd; */
            border-radius: 4px;
            /* padding: 20px; */
            /* margin-bottom: 20px; */
            background-color: #fff;
        }

        /* .lineEntryCont .editgrid-lineEntry-addRow { */
        .lineEntryCont [ref=editgrid-lineEntry-addRow] {
            display: none;
        }

        .btn-sm,
        .btn-group-sm>.btn {
            padding-left: 5px;
            padding-right: 5px;

        }

        .form-preview-container.mobile .editgrid-table-container .table-responsive {
            overflow-x: hidden;
        }

        .form-preview-container.mobile .editgrid-table-container .table-responsive table {
            table-layout: auto !important;
        }

        .form-preview-container.mobile .editgrid-table-body tr {
            position: relative;
            transition: transform 0.3s ease;
            overflow: hidden
        }

        .form-preview-container.mobile .editgrid-table-head tr .editgrid-table-column:last-child {
            display: none;

        }

        .form-preview-container.mobile .editgrid-table-body tr .editgrid-table-column:last-child {
            position: absolute;
            top: 0;
            bottom: 0;
            right: 0;
            /* pin to right side of the row */
            display: flex;
            align-items: center;
            background: #f8f8f8;
            padding: 0 10px;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            white-space: nowrap;

        }

        .form-preview-container.mobile .editgrid-table-body tr.show-actions .editgrid-table-column:last-child {
            /* display: flex; */
            transform: translateX(0);

        }
    </style>
@endsection

@section('content')
    <div class="card card-default">
        {{-- <div class="card-header separator">
            <div class="card-title mainheading">
                <h4>{{ $form->title }}</h4>
            </div>
            <div class="card-controls">
                <ul>
                    <li>
                        <a class="btn btn-secondary" href="{{ $backUrl }}">
                            {{ trans('global.back') }}
                        </a>
                    </li>

                </ul>
            </div>
        </div> --}}

        <div class="card-block">
            <div class="form-preview-container {{ $form->device_view }}">
                {{-- <form action="{{ route('forms.submit', $form->id) }}" method="POST" id="datacapture_form"> --}}
                @csrf
                {{-- <input type="hidden" name="submit_json" id="submit_json" value="" /> --}}
                <div id="formPreview">

                </div>
                {{-- <a id="cancelBtn" class="btn btn-secondary" href="{{ $backUrl }}">
                    {{ trans('global.cancel') }}
                </a> --}}
                {{-- </form> --}}
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    @parent
    {{-- <script src="https://cdn.form.io/js/formio.form.min.js"></script> --}}
    <script src="https://cdn.form.io/js/formio.full.min.js"></script>

    <script>
        var formDefinition = {!! $form->content !!};
        var redirect_to = "{{ $form->redirect_to ? route('forms.render', $form->redirect_to) : null }}";
        // console.log(redirect_to);
        var formId = "{{ $form->id }}";
        const FORM_KEY = `form_data_${formId}`; // current form's data
        const BACKUP_KEY = `form_backup_${formId}`;
        const NEXT_FORM_DATA_KEY = 'form_forward_data';

        var cancelBtn =
            "<a id='cancelBtn' class='btn btn-secondary' href='javascript:window.history.back()'>{{ trans('global.cancel') }}</a>";
        // console.log(formDefinition);
        // Minimal integration helper - only checks the changed field
        // Minimal integration helper with form events
        // Proper Form.io event handling
        window.callInboundIntegrations = function(form, integrationAlias, paramMappings, targetField = null) {
            // console.log("called");

            // Create a unique identifier for this integration setup
            const integrationKey = `${integrationAlias}_${JSON.stringify(paramMappings)}_${targetField}`;

            // Check if this integration has already been setup
            if (!window._integrationRegistry) {
                window._integrationRegistry = new Set();
            }

            if (window._integrationRegistry.has(integrationKey)) {
                console.log('Integration already setup, skipping:', integrationKey);
                return; // Already setup, avoid duplicate listeners
            }

            // Mark this integration as setup
            window._integrationRegistry.add(integrationKey);

            const debounceTimers = {};
            const fieldsToWatch = Object.values(paramMappings);

            // Use form-level change listening (most reliable)
            form.on('change', (event) => {
                const changedField = event.changed?.component?.key;

                // Check if the changed field is one we're watching
                if (changedField && fieldsToWatch.includes(changedField)) {
                    // Only check if the changed field has value
                    // console.log(changedField, form);
                    const changedValue = event.data[changedField];

                    if (changedValue && changedValue.toString().trim().length > 0) {
                        // Debounce the API call
                        // console.log('change');
                        clearTimeout(debounceTimers[integrationAlias]);
                        debounceTimers[integrationAlias] = setTimeout(async () => {
                            try {
                                // Prepare all parameters
                                const params = {};
                                Object.keys(paramMappings).forEach(paramName => {
                                    const fieldName = paramMappings[paramName];
                                    params[paramName] = event.data[fieldName] || '';
                                });

                                // Emit start event
                                if (form.emit) {
                                    form.emit('integrationStart', {
                                        integrationAlias,
                                        params,
                                        targetField
                                    });
                                }

                                // Make the API call
                                const url = "{{ route('forms.callIntegeration', '') }}";
                                const response = await fetch(
                                    `${url}/${integrationAlias}`, {
                                        method: "POST",
                                        headers: {
                                            "Content-Type": "application/json",
                                            "X-CSRF-TOKEN": "{{ csrf_token() }}"
                                        },
                                        body: JSON.stringify({
                                            params
                                        })
                                    });

                                // Check if response is OK
                                if (!response.ok) {
                                    const errorText = await response.text();
                                    throw new Error(`API error (${response.status}): ${errorText}`);
                                }

                                const result = await response.json();
                                // console.log(result);

                                // Update target field if specified
                                const targetComponent = form.getComponent(targetField);

                                const isEditGrid = targetComponent && targetComponent.component
                                    ?.type === 'editgrid';
                                if (targetField && result[targetField] !== undefined) {

                                    if (isEditGrid) {
                                        // Handle EditGrid data
                                        handleEditGridData(targetComponent, result, targetField);
                                    } else {
                                        // Handle regular field data
                                        handleRegularFieldData(form, result, targetField);
                                    }
                                }
                                // if (targetField && result[targetField] !== undefined) {
                                //     if (form.data) {
                                //         form.data[targetField] = result[targetField];
                                //     } else if (form.submission && form.submission.data) {
                                //         form.submission.data[targetField] = result[targetField];
                                //     }
                                //     form.redraw();
                                // }

                                // Emit success event
                                if (form.emit) {
                                    form.emit('integrationSuccess', {
                                        integrationAlias,
                                        params,
                                        result,
                                        targetField
                                    });
                                }

                            } catch (error) {
                                console.error('Integration failed:', error);

                                // Emit error event
                                if (form.emit) {
                                    form.emit('integrationError', {
                                        integrationAlias,
                                        error: error.message,
                                        targetField
                                    });
                                }

                                // Optional: Clear target field on error
                                // if (targetField) {
                                //     if (form.data) {
                                //         form.data[targetField] = 'Error';
                                //     } else if (form.submission && form.submission.data) {
                                //         form.submission.data[targetField] = 'Error';
                                //     }
                                //     form.redraw();
                                // }
                                // Optional: Clear target field on error
                                if (targetField) {
                                    handleFieldClear(form, targetField);
                                }
                            }
                        }, 500);
                    } else if (targetField) {
                        // Clear target field if the changed field becomes empty
                        // if (form.data) {
                        //     form.data[targetField] = '';
                        // } else if (form.submission && form.submission.data) {
                        //     form.submission.data[targetField] = '';
                        // }
                        // form.redraw();
                        handleFieldClear(form, targetField);

                    }
                }
            });

            console.log('Integration setup complete:', integrationKey);
        };

        // Helper function to handle EditGrid data
        function handleEditGridData(editGridComponent, result, targetField) {
            try {
                // Check if result contains data for the EditGrid
                const editGridData = result[targetField] || result.data || result.rows || result;
                // let tableData = editGridComponent.dataValue || [];
                let tableData = [];
                // console.log(editGridData, tableData);
                // return;
                if (Array.isArray(editGridData)) {

                    editGridData.forEach((rowData, index) => {
                        // editGridComponent.addRow(rowData);
                        tableData.push(rowData);
                    });

                    console.log(`Added ${editGridData.length} rows to EditGrid: ${targetField}`);
                } else if (editGridData && typeof editGridData === 'object') {

                    tableData.push(editGridData);

                    console.log(`Added single row to EditGrid: ${targetField}`);
                } else {
                    console.warn('Invalid EditGrid data format:', editGridData);
                }
                // tableData.push(newRow);
                // console.log(tableData);
                // Update the edit grid's value
                editGridComponent.setValue(tableData);
                // editGridComponent.redraw();
            } catch (error) {
                console.error('Error updating EditGrid:', error);
            }
        }

        // Helper function to handle regular field data
        function handleRegularFieldData(form, result, targetField) {
            try {
                const fieldValue = result[targetField];
                if (fieldValue !== undefined) {
                    if (form.data) {
                        form.data[targetField] = fieldValue;
                    } else if (form.submission && form.submission.data) {
                        form.submission.data[targetField] = fieldValue;
                    }
                    form.redraw();
                }
            } catch (error) {
                console.error('Error updating regular field:', error);
            }
        }

        // Helper function to clear field data
        function handleFieldClear(form, targetField) {
            try {
                const targetComponent = form.getComponent(targetField);
                const isEditGrid = targetComponent && targetComponent.component?.type === 'editgrid';

                if (isEditGrid) {
                    // Clear EditGrid
                    targetComponent.setValue([]);
                    targetComponent.redraw();
                } else {
                    // Clear regular field
                    if (form.data) {
                        form.data[targetField] = '';
                    } else if (form.submission && form.submission.data) {
                        form.submission.data[targetField] = '';
                    }
                    form.redraw();
                }
            } catch (error) {
                console.error('Error clearing field:', error);
            }
        }


        Formio.createForm(document.getElementById('formPreview'), formDefinition)
            .then(function(form) {
                //   console.log(form);
                // const form1Data = sessionStorage.getItem('form1Data');
                // if (form1Data) {
                //     form.submission = {
                //         data: JSON.parse(form1Data)
                //     };
                //     sessionStorage.setItem('form1Data', null);

                // }
                const restoreData = (isBackNav = false) => {
                    let data = null;

                    // If we have forward data (from previous form), use it once and clear it
                    if (sessionStorage.getItem(NEXT_FORM_DATA_KEY)) {
                        data = JSON.parse(sessionStorage.getItem(NEXT_FORM_DATA_KEY));
                        sessionStorage.removeItem(NEXT_FORM_DATA_KEY); // clear after use
                    }
                    // Else check if backup exists (for back navigation)
                    else if (!isBackNav && sessionStorage.getItem(BACKUP_KEY)) {
                        // Back flow
                        data = JSON.parse(sessionStorage.getItem(BACKUP_KEY));
                        sessionStorage.removeItem(BACKUP_KEY); // clear after using
                    }

                    if (data) {
                        form.submission = {
                            data
                        };
                    }
                };
                restoreData(false);
                window.addEventListener('beforeunload', () => {
                    sessionStorage.setItem(FORM_KEY, JSON.stringify(form.submission.data));
                });
                form.on('change', () => {
                    applyTruncateMiddle();
                });
                $('.formio-component-submit').prepend(cancelBtn);

                // Handle form submission for preview
                form.on('submit', function(submission) {
                    // console.log('Form submission (preview mode):', submission);

                    // Show submission data in an alert for preview
                    var submissionData = JSON.stringify(submission.data, null, 2);
                    // console.log(submissionData);
                    // $("#submit_json").val(submissionData);
                    // $("#datacapture_form").submit();

                    sessionStorage.setItem(NEXT_FORM_DATA_KEY, JSON.stringify(submission.data));

                    // For back navigation
                    sessionStorage.setItem(BACKUP_KEY, JSON.stringify(submission.data));
                    if (redirect_to) {
                        // sessionStorage.setItem('form1Data', submissionData);
                        window.location.href = redirect_to;
                    } else {
                        alert('Form Submitted (Preview Mode)\n\nSubmission Data:\n' + submissionData);

                    }


                    // http://127.0.0.1:8000/forms/10/render
                    // Reset the form
                    // form.submission = {
                    //     data: {}
                    // };
                });
                window.addEventListener('pageshow', (event) => {
                    // `pageshow` fires even from bfcache (browser back cache)
                    if (event.persisted || performance.getEntriesByType("navigation")[0]?.type ===
                        'back_forward') {
                        restoreData(true);
                    }
                });
                form.on('integrationStart', (event) => {
                    console.log('API call started:', event.integrationAlias);
                });

                form.on('integrationSuccess', (event) => {
                    console.log('API success:', event.integrationAlias, event.result);
                });

                form.on('integrationError', (event) => {
                    alert(`Error in ${event.integrationAlias}: ${event.error}`);
                });
            })
            .catch(function(error) {
                console.error('Error rendering form:', error);
                document.getElementById('formPreview').innerHTML =
                    '<div class="alert alert-danger">Error loading form preview. Please check the form definition.</div>';
            });

        function getTextWidth(text, el) {
            const canvas = getTextWidth.canvas || (getTextWidth.canvas = document.createElement("canvas"));
            const ctx = canvas.getContext("2d");
            const style = window.getComputedStyle(el);
            ctx.font = `${style.fontWeight} ${style.fontSize} ${style.fontFamily}`;
            return ctx.measureText(text).width;
        }

        function truncateMiddleToFit(el) {
            const originalText = el.getAttribute('data-full-text') || el.textContent.trim();
            el.setAttribute('data-full-text', originalText);
            el.textContent = originalText;

            const availableWidth = el.offsetWidth;

            // If full text fits, do nothing
            if (getTextWidth(originalText, el) <= availableWidth) return;

            let frontCount = Math.ceil(originalText.length / 2);
            let backCount = originalText.length - frontCount;

            while (frontCount > 1 && backCount > 1) {
                const candidate = originalText.slice(0, frontCount) + '...' + originalText.slice(-backCount);
                if (getTextWidth(candidate, el) <= availableWidth) {
                    el.textContent = candidate;
                    return;
                }
                frontCount--;
                backCount--;
            }

            // Fallback: minimal characters on each side
            el.textContent = originalText.slice(0, 1) + '...' + originalText.slice(-1);
        }
        // Example: Apply to all TDs with .truncate-middle
        function applyTruncateMiddle() {
            // console.log("a");
            document.querySelectorAll('tbody td.editgrid-table-column:not(:last-child)').forEach(truncateMiddleToFit);
        }

        window.addEventListener('resize', applyTruncateMiddle);
        document.addEventListener('DOMContentLoaded', applyTruncateMiddle);

        let startX = 0;
        let threshold = 50; // px for swipe to trigger

        $(document)
            .on('touchstart', '.editgrid-table-body tr', function(e) {
                startX = e.originalEvent.touches[0].clientX;
            })
            .on('touchend', '.editgrid-table-body tr', function(e) {
                let endX = e.originalEvent.changedTouches[0].clientX;
                let diffX = endX - startX;

                let $row = $(this).closest('tr');

                if (diffX < -threshold) {
                    // Swipe left → show actions
                    $('.editgrid-table-body tr').removeClass('show-actions'); // close others
                    $row.addClass('show-actions');
                } else if (diffX > threshold) {
                    // Swipe right → hide actions
                    $row.removeClass('show-actions');
                }
            });
    </script>
@endsection
