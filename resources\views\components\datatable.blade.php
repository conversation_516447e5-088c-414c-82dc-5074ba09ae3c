<div class="table-responsive">
    <table class="table table-bordered table-striped table-hover datatable" id="datatable_1">
        <thead>
            <tr>
                @foreach ($columns as $col)
                    <th @if ($col['filter'] === false) rowspan="2" @endif class="{{ $col['class'] ?? '' }}"
                        width="{{ $col['width'] ?? '' }}">
                        {{ $col['title'] }}
                    </th>
                @endforeach
            </tr>
            <tr>
                @foreach ($columns as $col)
                    @if ($col['filter'] !== false)
                        <th data-column="{{ $loop->index }}">
                            @if ($col['filter'] === 'text' || $col['filter'] === 'relation')
                                <input type="text" class="form-control form-control-sm column-search"
                                    placeholder="Search {{ $col['title'] }}">
                            @elseif($col['filter'] === 'select' && isset($col['options']))
                                <select class="form-control form-control-sm column-search">
                                    <option value="">All</option>
                                    @foreach ($col['options'] as $opt)
                                        <option value="{{ $opt }}">{{ $opt }}</option>
                                    @endforeach
                                </select>
                            @elseif($col['filter'] === 'boolean')
                                <select class="form-control form-control-sm column-search">
                                    <option value="">-- {{ __('All') }} --</option>
                                    @foreach ($col['options'] ?? ['1' => 'Active', 0 => 'Inactive'] as $val => $label)
                                        <option value="{{ $val }}">{{ $label }}</option>
                                    @endforeach
                                </select>
                            @elseif($col['filter'] === 'date_range')
                                <input type="text" class="form-control form-control-sm ddaterange column-search"
                                    placeholder="{{ __('Select range') }}">
                            @else
                                {{-- no filter --}}
                            @endif
                        </th>
                    @endif
                @endforeach
            </tr>
        </thead>
    </table>
</div>

@section('scripts')
    @parent
    <script>
        $(function() {

            // console.log('a');
            let table = $('#datatable_1').DataTable({
                processing: true,
                serverSide: true,
                ajax: "{{ $ajax }}",
                columns: @json($columns),
                order: @json($order ?? [[0, 'desc']]),
                pageLength: {{ $pageLength ?? 25 }},
                dom: '<"top d-flex justify-content-between"l f B>rt<"bottom d-flex justify-content-between"i p><"clear">',
                language: {
                    search: "",
                    searchPlaceholder: "Search..."
                },
                orderCellsTop: true,
                buttons: buttonsDefault,
            });

            $('#datatable_1 thead').on('keyup change', '.column-search', function() {
                let colIndex = $(this).closest('th').data('column');
                table.column(colIndex).search(this.value).draw();
            });
            // Optional: init date range picker
            $('.ddaterange').daterangepicker({
                autoUpdateInput: false,
                locale: {
                    cancelLabel: 'Clear'
                }
            });
            $('.ddaterange').on('apply.daterangepicker', function(ev, picker) {
                $(this).val(picker.startDate.format('MM/DD/YYYY') + '|' + picker.endDate.format(
                    'MM/DD/YYYY')).trigger('change');
            });
            $('.ddaterange').on('cancel.daterangepicker', function(ev, picker) {
                $(this).val('').trigger('change');
            });
            setTimeout(() => {
                table.buttons().container().prependTo('.card-controls');
                // console.log(table.buttons().container());
            }, 50);
        });
    </script>
@endsection
