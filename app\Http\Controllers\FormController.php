<?php

namespace App\Http\Controllers;

use App\Models\Form;
use App\Models\FormSubmission;
use App\Services\FormSubmissionService;
use App\Services\AuditLoggerService;
use App\Services\ErrorLoggerService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;

class FormController extends Controller
{
    protected FormSubmissionService $formSubmissionService;
    protected AuditLoggerService $auditLogger;
    protected ErrorLoggerService $errorLogger;

    public function __construct(
        FormSubmissionService $formSubmissionService,
        AuditLoggerService $auditLogger,
        ErrorLoggerService $errorLogger
    ) {
        $this->formSubmissionService = $formSubmissionService;
        $this->auditLogger = $auditLogger;
        $this->errorLogger = $errorLogger;
    }

    /**
     * Display a listing of available forms
     */
    public function index()
    {
        $user = Auth::user();

        $query = Form::where('is_active', true)
            ->select(['id', 'title', 'created_at']);

        // If user is authenticated, filter by user groups
        if ($user) {
            $query->where(function ($q) use ($user) {
                $q->whereHas('userGroups', function ($userGroupQuery) use ($user) {
                    $userGroupQuery->where('user_group_id', $user->user_group_id);
                })->orWhereNull('user_group_id');
            });
        } else {
            // For unauthenticated users, only show forms without user group restrictions
            $query->whereNull('user_group_id');
        }

        $forms = $query->orderBy('title')->get();

        return view('forms.index', compact('forms'));
    }

    public function getMobileForms(Request $request, $module)
    {
        $menuName = $module;

        $user = Auth::user();
        $query = Form::with(['userGroup', 'creator'])
            ->whereDoesntHave('formsThatRedirectToThis')
            ->where('module', $module)
            ->accessibleByUser($user)
            ->orderBy('created_at', 'desc');
        $forms = $query->get();

        return view('mobile.forms.index')->with(compact('menuName', 'forms'));
    }
    /**
     * Display the specified form for submission
     */
    public function show(int $formId)
    {
        $form = Form::where('id', $formId)
            ->where('is_active', true)
            ->firstOrFail();

        // Check user access if authenticated
        $user = Auth::user();
        if ($user && $form->user_group_id && $form->user_group_id !== $user->user_group_id) {
            abort(403, 'Access denied to this form');
        }

        return view('forms.show', compact('form'));
    }

    /**
     * Handle form submission from web interface
     */
    public function submit(Request $request, int $formId)
    {
        try {
            // Find the form
            $form = Form::where('id', $formId)
                ->where('is_active', true)
                ->firstOrFail();

            // Check user access if authenticated
            $user = Auth::user();
            if ($user && $form->user_group_id && $form->user_group_id !== $user->user_group_id) {
                abort(403, 'Access denied to this form');
            }

            // Validate request
            $request->validate([
                'form_data' => 'required|array',
            ]);

            // Prepare metadata
            $metadata = [
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'source' => 'web',
                'submitted_via' => 'form_web_interface',
                'referrer' => $request->header('referer'),
            ];

            // Submit the form
            $submission = $this->formSubmissionService->submitForm(
                $request->input('form_data'),
                $form,
                $user,
                $metadata
            );

            // Redirect to status page
            return redirect()->route('forms.status', $submission->uuid)
                ->with('success', 'Form submitted successfully! You can track the progress below.');
        } catch (ValidationException $e) {
            return back()
                ->withErrors($e->errors())
                ->withInput()
                ->with('error', 'Please correct the errors below and try again.');
        } catch (\Exception $e) {
            $this->errorLogger->logError(
                \App\Models\ErrorLog::LEVEL_ERROR,
                \App\Models\ErrorLog::TYPE_SYSTEM_ERROR,
                $e->getMessage(),
                [
                    'form_id' => $formId,
                    'user_id' => Auth::id(),
                    'exception_class' => get_class($e),
                    'request_data' => $request->all(),
                ]
            );

            return back()
                ->withInput()
                ->with('error', 'An error occurred while submitting the form. Please try again.');
        }
    }

    /**
     * Display submission status page
     */
    public function status(string $uuid)
    {
        try {
            $status = $this->formSubmissionService->getSubmissionStatus($uuid);

            return view('forms.status', compact('status'));
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            abort(404, 'Submission not found');
        }
    }

    /**
     * Get submission status as JSON for AJAX requests
     */
    public function statusJson(string $uuid)
    {
        try {
            $status = $this->formSubmissionService->getSubmissionStatus($uuid);

            return response()->json([
                'success' => true,
                'data' => $status,
            ]);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Submission not found',
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving submission status',
            ], 500);
        }
    }

    /**
     * Display user's submission history
     */
    public function history(Request $request)
    {
        $user = Auth::user();
        if (!$user) {
            return redirect()->route('login')
                ->with('error', 'Please log in to view your submission history.');
        }

        $query = FormSubmission::where('user_id', $user->id)
            ->with(['form'])
            ->orderBy('submitted_at', 'desc');

        // Apply filters
        if ($request->has('status') && $request->input('status') !== '') {
            $query->where('status', $request->input('status'));
        }

        if ($request->has('form_id') && $request->input('form_id') !== '') {
            $query->where('form_id', $request->input('form_id'));
        }

        $submissions = $query->paginate(15);

        // Get user's forms for filter dropdown
        $userForms = Form::whereHas('userGroups', function ($q) use ($user) {
            $q->where('user_group_id', $user->user_group_id);
        })->orWhereNull('user_group_id')
            ->where('is_active', true)
            ->orderBy('title')
            ->get(['id', 'title']);

        return view('forms.history', compact('submissions', 'userForms'));
    }

    /**
     * Retry failed submission (for authenticated users)
     */
    public function retry(Request $request, string $uuid)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'Authentication required',
                ], 401);
            }

            $submission = FormSubmission::where('uuid', $uuid)
                ->where('user_id', $user->id)
                ->firstOrFail();

            // Log the user retry action
            $this->auditLogger->logUserAction(
                $user,
                \App\Models\AuditLog::EVENT_MANUAL_RETRY,
                [
                    'reason' => 'User-initiated retry from web interface',
                    'submission_uuid' => $uuid,
                ],
                $submission
            );

            // Retry failed syncs
            $this->formSubmissionService->retryFailedSyncs($submission);

            return response()->json([
                'success' => true,
                'message' => 'Retry initiated successfully',
            ]);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Submission not found or access denied',
            ], 404);
        } catch (\Exception $e) {
            $this->errorLogger->logError(
                \App\Models\ErrorLog::LEVEL_ERROR,
                \App\Models\ErrorLog::TYPE_SYSTEM_ERROR,
                $e->getMessage(),
                [
                    'submission_uuid' => $uuid,
                    'user_id' => $user->id ?? null,
                    'action' => 'user_retry',
                ]
            );

            return response()->json([
                'success' => false,
                'message' => 'Failed to initiate retry',
            ], 500);
        }
    }
}
