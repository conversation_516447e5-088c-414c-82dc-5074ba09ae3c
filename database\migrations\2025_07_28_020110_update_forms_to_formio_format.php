<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Update existing forms to use FormIO format
        // $this->updateFormToFormIOFormat();
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // This migration is not reversible as it's a data transformation
        // The old format would need to be stored separately to reverse
    }

    /**
     * Update forms to use proper FormIO format
     */
    private function updateFormToFormIOFormat()
    {
        // Sample FormIO format for Purchase Order form
        $poFormContent = [
            "components" => [
                [
                    "label" => "Purchase Order Header",
                    "columns" => [
                        [
                            "components" => [
                                [
                                    "label" => "Purchase Order Number",
                                    "placeholder" => "Enter PO Number",
                                    "description" => "Unique purchase order identifier",
                                    "validate" => [
                                        "required" => true,
                                        "maxLength" => 20
                                    ],
                                    "key" => "PurchaseOrderNumber",
                                    "type" => "textfield",
                                    "input" => true
                                ],
                                [
                                    "label" => "Vendor Code",
                                    "placeholder" => "Enter Vendor Code",
                                    "description" => "Supplier/Vendor identification code",
                                    "validate" => [
                                        "required" => true,
                                        "maxLength" => 15
                                    ],
                                    "key" => "VendorCode",
                                    "type" => "textfield",
                                    "input" => true
                                ],
                                [
                                    "label" => "Order Date",
                                    "format" => "yyyy-MM-dd",
                                    "description" => "Date when the order was placed",
                                    "validate" => [
                                        "required" => true
                                    ],
                                    "key" => "OrderDate",
                                    "type" => "datetime",
                                    "input" => true,
                                    "widget" => [
                                        "type" => "calendar",
                                        "format" => "yyyy-MM-dd",
                                        "enableTime" => false
                                    ]
                                ]
                            ],
                            "width" => 6,
                            "size" => "md"
                        ],
                        [
                            "components" => [
                                [
                                    "label" => "Expected Delivery Date",
                                    "format" => "yyyy-MM-dd",
                                    "description" => "Expected delivery date",
                                    "validate" => [
                                        "required" => true
                                    ],
                                    "key" => "ExpectedDeliveryDate",
                                    "type" => "datetime",
                                    "input" => true,
                                    "widget" => [
                                        "type" => "calendar",
                                        "format" => "yyyy-MM-dd",
                                        "enableTime" => false
                                    ]
                                ],
                                [
                                    "label" => "Currency Code",
                                    "placeholder" => "USD",
                                    "description" => "Currency for the purchase order",
                                    "validate" => [
                                        "required" => true,
                                        "maxLength" => 3
                                    ],
                                    "key" => "CurrencyCode",
                                    "type" => "textfield",
                                    "input" => true
                                ],
                                [
                                    "label" => "Payment Terms",
                                    "placeholder" => "NET30",
                                    "description" => "Payment terms for the order",
                                    "key" => "PaymentTerms",
                                    "type" => "textfield",
                                    "input" => true
                                ]
                            ],
                            "width" => 6,
                            "size" => "md"
                        ]
                    ],
                    "key" => "po_header",
                    "type" => "columns",
                    "input" => false,
                    "tableView" => false
                ],
                [
                    "label" => "Purchase Order Line Items",
                    "columns" => [
                        [
                            "components" => [
                                [
                                    "label" => "Line Number",
                                    "placeholder" => "10",
                                    "description" => "Line item sequence number",
                                    "validate" => [
                                        "required" => true,
                                        "min" => 1
                                    ],
                                    "key" => "LineNumber",
                                    "type" => "number",
                                    "input" => true
                                ],
                                [
                                    "label" => "Item Code",
                                    "placeholder" => "Enter Item Code",
                                    "description" => "Product/Item identification code",
                                    "validate" => [
                                        "required" => true,
                                        "maxLength" => 30
                                    ],
                                    "key" => "ItemCode",
                                    "type" => "textfield",
                                    "input" => true
                                ],
                                [
                                    "label" => "Quantity Ordered",
                                    "placeholder" => "0",
                                    "description" => "Quantity to be ordered",
                                    "validate" => [
                                        "required" => true,
                                        "min" => 0.01
                                    ],
                                    "key" => "QuantityOrdered",
                                    "type" => "number",
                                    "input" => true,
                                    "decimalLimit" => 3
                                ]
                            ],
                            "width" => 6,
                            "size" => "md"
                        ],
                        [
                            "components" => [
                                [
                                    "label" => "Unit Price",
                                    "placeholder" => "0.00",
                                    "description" => "Price per unit",
                                    "validate" => [
                                        "required" => true,
                                        "min" => 0
                                    ],
                                    "key" => "UnitPrice",
                                    "type" => "number",
                                    "input" => true,
                                    "decimalLimit" => 4
                                ],
                                [
                                    "label" => "Unit of Measure",
                                    "placeholder" => "EA",
                                    "description" => "Unit of measurement (EA, KG, LB, etc.)",
                                    "validate" => [
                                        "required" => true,
                                        "maxLength" => 10
                                    ],
                                    "key" => "UnitOfMeasure",
                                    "type" => "textfield",
                                    "input" => true
                                ],
                                [
                                    "label" => "Warehouse Code",
                                    "placeholder" => "WH01",
                                    "description" => "Destination warehouse code",
                                    "key" => "WarehouseCode",
                                    "type" => "textfield",
                                    "input" => true
                                ]
                            ],
                            "width" => 6,
                            "size" => "md"
                        ]
                    ],
                    "key" => "po_lines",
                    "type" => "columns",
                    "input" => false,
                    "tableView" => false
                ],
                [
                    "type" => "button",
                    "label" => "Submit Purchase Order",
                    "key" => "submit",
                    "disableOnInvalid" => true,
                    "input" => true,
                    "tableView" => false
                ]
            ]
        ];

        // Sample FormIO format for Inventory form
        $inventoryFormContent = [
            "components" => [
                [
                    "label" => "Inventory Transaction Header",
                    "columns" => [
                        [
                            "components" => [
                                [
                                    "label" => "Transaction Type",
                                    "description" => "Type of inventory transaction",
                                    "validate" => [
                                        "required" => true
                                    ],
                                    "key" => "TransactionType",
                                    "type" => "select",
                                    "input" => true,
                                    "data" => [
                                        "values" => [
                                            ["label" => "Goods Receipt", "value" => "GR"],
                                            ["label" => "Goods Issue", "value" => "GI"],
                                            ["label" => "Stock Transfer", "value" => "ST"],
                                            ["label" => "Physical Count", "value" => "PC"],
                                            ["label" => "Adjustment", "value" => "ADJ"]
                                        ]
                                    ]
                                ],
                                [
                                    "label" => "Document Number",
                                    "placeholder" => "Enter Document Number",
                                    "description" => "Reference document number",
                                    "validate" => [
                                        "required" => true,
                                        "maxLength" => 20
                                    ],
                                    "key" => "DocumentNumber",
                                    "type" => "textfield",
                                    "input" => true
                                ],
                                [
                                    "label" => "Transaction Date",
                                    "format" => "yyyy-MM-dd",
                                    "description" => "Date of the inventory transaction",
                                    "validate" => [
                                        "required" => true
                                    ],
                                    "key" => "TransactionDate",
                                    "type" => "datetime",
                                    "input" => true,
                                    "widget" => [
                                        "type" => "calendar",
                                        "format" => "yyyy-MM-dd",
                                        "enableTime" => false
                                    ]
                                ]
                            ],
                            "width" => 6,
                            "size" => "md"
                        ],
                        [
                            "components" => [
                                [
                                    "label" => "Posting Date",
                                    "format" => "yyyy-MM-dd",
                                    "description" => "Date when transaction should be posted",
                                    "validate" => [
                                        "required" => true
                                    ],
                                    "key" => "PostingDate",
                                    "type" => "datetime",
                                    "input" => true,
                                    "widget" => [
                                        "type" => "calendar",
                                        "format" => "yyyy-MM-dd",
                                        "enableTime" => false
                                    ]
                                ],
                                [
                                    "label" => "Reference Number",
                                    "placeholder" => "Enter Reference",
                                    "description" => "External reference number (PO, SO, etc.)",
                                    "key" => "ReferenceNumber",
                                    "type" => "textfield",
                                    "input" => true
                                ],
                                [
                                    "label" => "Comments",
                                    "placeholder" => "Enter comments",
                                    "description" => "Additional comments or notes",
                                    "key" => "Comments",
                                    "type" => "textarea",
                                    "input" => true,
                                    "rows" => 2
                                ]
                            ],
                            "width" => 6,
                            "size" => "md"
                        ]
                    ],
                    "key" => "inventory_header",
                    "type" => "columns",
                    "input" => false,
                    "tableView" => false
                ],
                [
                    "label" => "Inventory Line Items",
                    "columns" => [
                        [
                            "components" => [
                                [
                                    "label" => "Item Code",
                                    "placeholder" => "Enter Item Code",
                                    "description" => "Product/Item identification code",
                                    "validate" => [
                                        "required" => true,
                                        "maxLength" => 30
                                    ],
                                    "key" => "ItemCode",
                                    "type" => "textfield",
                                    "input" => true
                                ],
                                [
                                    "label" => "Item Description",
                                    "placeholder" => "Item description",
                                    "description" => "Description of the item",
                                    "key" => "ItemDescription",
                                    "type" => "textfield",
                                    "input" => true
                                ],
                                [
                                    "label" => "Quantity",
                                    "placeholder" => "0",
                                    "description" => "Transaction quantity",
                                    "validate" => [
                                        "required" => true,
                                        "min" => 0.001
                                    ],
                                    "key" => "Quantity",
                                    "type" => "number",
                                    "input" => true,
                                    "decimalLimit" => 3
                                ],
                                [
                                    "label" => "Unit of Measure",
                                    "placeholder" => "EA",
                                    "description" => "Unit of measurement",
                                    "validate" => [
                                        "required" => true,
                                        "maxLength" => 10
                                    ],
                                    "key" => "UnitOfMeasure",
                                    "type" => "textfield",
                                    "input" => true
                                ]
                            ],
                            "width" => 6,
                            "size" => "md"
                        ],
                        [
                            "components" => [
                                [
                                    "label" => "Warehouse Code",
                                    "placeholder" => "WH01",
                                    "description" => "Source/Destination warehouse",
                                    "validate" => [
                                        "required" => true,
                                        "maxLength" => 10
                                    ],
                                    "key" => "WarehouseCode",
                                    "type" => "textfield",
                                    "input" => true
                                ],
                                [
                                    "label" => "Bin Location",
                                    "placeholder" => "A-01-01",
                                    "description" => "Specific bin/location within warehouse",
                                    "key" => "BinLocation",
                                    "type" => "textfield",
                                    "input" => true
                                ],
                                [
                                    "label" => "Batch Number",
                                    "placeholder" => "Enter Batch",
                                    "description" => "Batch or lot number",
                                    "key" => "BatchNumber",
                                    "type" => "textfield",
                                    "input" => true
                                ],
                                [
                                    "label" => "Serial Number",
                                    "placeholder" => "Enter Serial",
                                    "description" => "Serial number for serialized items",
                                    "key" => "SerialNumber",
                                    "type" => "textfield",
                                    "input" => true
                                ]
                            ],
                            "width" => 6,
                            "size" => "md"
                        ]
                    ],
                    "key" => "inventory_lines",
                    "type" => "columns",
                    "input" => false,
                    "tableView" => false
                ],
                [
                    "label" => "Cost and Valuation",
                    "columns" => [
                        [
                            "components" => [
                                [
                                    "label" => "Unit Cost",
                                    "placeholder" => "0.00",
                                    "description" => "Cost per unit",
                                    "key" => "UnitCost",
                                    "type" => "number",
                                    "input" => true,
                                    "decimalLimit" => 4
                                ],
                                [
                                    "label" => "Total Value",
                                    "placeholder" => "0.00",
                                    "description" => "Total transaction value",
                                    "key" => "TotalValue",
                                    "type" => "number",
                                    "input" => true,
                                    "decimalLimit" => 2
                                ]
                            ],
                            "width" => 6,
                            "size" => "md"
                        ],
                        [
                            "components" => [
                                [
                                    "label" => "Cost Center",
                                    "placeholder" => "CC001",
                                    "description" => "Cost center for accounting",
                                    "key" => "CostCenter",
                                    "type" => "textfield",
                                    "input" => true
                                ],
                                [
                                    "label" => "GL Account",
                                    "placeholder" => "140000",
                                    "description" => "General ledger account",
                                    "key" => "GLAccount",
                                    "type" => "textfield",
                                    "input" => true
                                ]
                            ],
                            "width" => 6,
                            "size" => "md"
                        ]
                    ],
                    "key" => "cost_valuation",
                    "type" => "columns",
                    "input" => false,
                    "tableView" => false
                ],
                [
                    "type" => "button",
                    "label" => "Submit Inventory Transaction",
                    "key" => "submit",
                    "disableOnInvalid" => true,
                    "input" => true,
                    "tableView" => false
                ]
            ]
        ];

        // Update forms with proper FormIO format
        DB::table('forms')->where('title', 'LIKE', '%Purchase%')->update([
            'content' => json_encode($poFormContent)
        ]);

        DB::table('forms')->where('title', 'LIKE', '%Inventory%')->update([
            'content' => json_encode($inventoryFormContent)
        ]);

        // For any other forms, create a generic business form structure
        $forms = DB::table('forms')->whereNotIn('title', ['Purchase Order Form', 'Inventory Form'])->get();

        foreach ($forms as $form) {
            $genericFormContent = [
                "components" => [
                    [
                        "label" => "Document Information",
                        "columns" => [
                            [
                                "components" => [
                                    [
                                        "label" => "Document Number",
                                        "placeholder" => "Enter document number",
                                        "description" => "Unique document identifier",
                                        "validate" => [
                                            "required" => true,
                                            "maxLength" => 20
                                        ],
                                        "key" => "DocumentNumber",
                                        "type" => "textfield",
                                        "input" => true
                                    ],
                                    [
                                        "label" => "Document Date",
                                        "format" => "yyyy-MM-dd",
                                        "description" => "Date of the document",
                                        "validate" => [
                                            "required" => true
                                        ],
                                        "key" => "DocumentDate",
                                        "type" => "datetime",
                                        "input" => true,
                                        "widget" => [
                                            "type" => "calendar",
                                            "format" => "yyyy-MM-dd",
                                            "enableTime" => false
                                        ]
                                    ],
                                    [
                                        "label" => "Business Partner Code",
                                        "placeholder" => "Enter BP code",
                                        "description" => "Customer or vendor code",
                                        "key" => "BusinessPartnerCode",
                                        "type" => "textfield",
                                        "input" => true
                                    ]
                                ],
                                "width" => 6,
                                "size" => "md"
                            ],
                            [
                                "components" => [
                                    [
                                        "label" => "Reference Number",
                                        "placeholder" => "Enter reference",
                                        "description" => "External reference number",
                                        "key" => "ReferenceNumber",
                                        "type" => "textfield",
                                        "input" => true
                                    ],
                                    [
                                        "label" => "Status",
                                        "description" => "Document status",
                                        "key" => "Status",
                                        "type" => "select",
                                        "input" => true,
                                        "data" => [
                                            "values" => [
                                                ["label" => "Draft", "value" => "DRAFT"],
                                                ["label" => "Pending", "value" => "PENDING"],
                                                ["label" => "Approved", "value" => "APPROVED"],
                                                ["label" => "Rejected", "value" => "REJECTED"]
                                            ]
                                        ]
                                    ],
                                    [
                                        "label" => "Comments",
                                        "placeholder" => "Enter comments",
                                        "description" => "Additional notes or comments",
                                        "key" => "Comments",
                                        "type" => "textarea",
                                        "input" => true,
                                        "rows" => 3
                                    ]
                                ],
                                "width" => 6,
                                "size" => "md"
                            ]
                        ],
                        "key" => "document_header",
                        "type" => "columns",
                        "input" => false,
                        "tableView" => false
                    ],
                    [
                        "label" => "Line Items",
                        "columns" => [
                            [
                                "components" => [
                                    [
                                        "label" => "Line Number",
                                        "placeholder" => "10",
                                        "description" => "Line item sequence",
                                        "validate" => [
                                            "min" => 1
                                        ],
                                        "key" => "LineNumber",
                                        "type" => "number",
                                        "input" => true
                                    ],
                                    [
                                        "label" => "Item Code",
                                        "placeholder" => "Enter item code",
                                        "description" => "Product or service code",
                                        "key" => "ItemCode",
                                        "type" => "textfield",
                                        "input" => true
                                    ],
                                    [
                                        "label" => "Description",
                                        "placeholder" => "Enter description",
                                        "description" => "Item description",
                                        "key" => "Description",
                                        "type" => "textfield",
                                        "input" => true
                                    ]
                                ],
                                "width" => 6,
                                "size" => "md"
                            ],
                            [
                                "components" => [
                                    [
                                        "label" => "Quantity",
                                        "placeholder" => "0",
                                        "description" => "Item quantity",
                                        "validate" => [
                                            "min" => 0
                                        ],
                                        "key" => "Quantity",
                                        "type" => "number",
                                        "input" => true,
                                        "decimalLimit" => 3
                                    ],
                                    [
                                        "label" => "Unit Price",
                                        "placeholder" => "0.00",
                                        "description" => "Price per unit",
                                        "key" => "UnitPrice",
                                        "type" => "number",
                                        "input" => true,
                                        "decimalLimit" => 4
                                    ],
                                    [
                                        "label" => "Total Amount",
                                        "placeholder" => "0.00",
                                        "description" => "Total line amount",
                                        "key" => "TotalAmount",
                                        "type" => "number",
                                        "input" => true,
                                        "decimalLimit" => 2
                                    ]
                                ],
                                "width" => 6,
                                "size" => "md"
                            ]
                        ],
                        "key" => "line_items",
                        "type" => "columns",
                        "input" => false,
                        "tableView" => false
                    ],
                    [
                        "type" => "button",
                        "label" => "Submit Document",
                        "key" => "submit",
                        "disableOnInvalid" => true,
                        "input" => true,
                        "tableView" => false
                    ]
                ]
            ];

            DB::table('forms')->where('id', $form->id)->update([
                'content' => json_encode($genericFormContent)
            ]);
        }
    }
};
