/**
 * Select2 Hungarian translation
 */
(function ($) {
    "use strict";

    $.fn.select2.locales['hu'] = {
        formatNoMatches: function () { return "<PERSON>nc<PERSON> tal<PERSON>lat."; },
        formatInputTooShort: function (input, min) { var n = min - input.length; return "T<PERSON> rövid. Még " + n + " karakter <PERSON>."; },
        formatInputTooLong: function (input, max) { var n = input.length - max; return "T<PERSON> hossz<PERSON>. " + n + " karak<PERSON><PERSON> több, mint kellene."; },
        formatSelectionTooBig: function (limit) { return "Csak " + limit + " elemet lehet k<PERSON>."; },
        formatLoadMore: function (pageNumber) { return "Töltés…"; },
        formatSearching: function () { return "Keresés…"; }
    };

    $.extend($.fn.select2.defaults, $.fn.select2.locales['hu']);
})(jQuery);
