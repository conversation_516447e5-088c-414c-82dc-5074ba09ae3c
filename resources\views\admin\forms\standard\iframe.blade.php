@extends('layouts.iframe')
@section('pageTitle', trans('cruds.form.title'))

@section('content')
    <style>
        table {
            width: 100% !important;
        }

        .dt-buttons {
            display: none !important;
        }

        .card .card-block {
            padding: 0px !important;
        }

        .container-fluid {
            padding: 0px !important;
        }

        .card.card-default {
            margin-top: 0px !important;
            padding: 15px 0px !important;
            border: 0px;
        }

        .card-header {
            position: relative !important;
            top: 0px !important;
            background: #fff !important;
            padding: 0px !important;
            margin-bottom: 15px !important;

        }
    </style>
    <div class="card card-default">
        <div class="card-header separator">
            <div class="card-title mainheading">
                <h4>{{ trans('cruds.fields.standard') }} {{ trans('cruds.form.title') }} {{ trans('global.list') }}</h4>
            </div>
            <div class="card-controls">
                {{-- <ul>
                    @if (auth()->user()->isAdmin() || auth()->user()->isSuperAdmin())
                        <li>

                            <x-add-button :url="route('admin.forms.create')" :text="__('cruds.form.title_singular')" />

                        </li>
                    @endif
                </ul> --}}
            </div>
        </div>

        <div class="card-block">


            <x-datatable id="forms-table" :columns="$columns" ajax="{{ route('admin.forms.getStandardForms') }}"
                :order="[[4, 'desc']]" />
        </div>
    </div>
@endsection
