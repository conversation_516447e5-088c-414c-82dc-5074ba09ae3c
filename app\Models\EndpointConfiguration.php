<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EndpointConfiguration extends BaseModel
{
    use HasFactory;

    // Target type constants
    public const TARGET_CSI = 'csi';
    public const TARGET_SAP = 'sap';
    public const TARGET_INFOR_WMS = 'infor_wms';
    // public const TARGET_SALESFORCE = 'salesforce';
    // public const TARGET_DYNAMICS = 'microsoft_dynamics';
    // public const TARGET_ORACLE = 'oracle';
    // public const TARGET_CUSTOM_API = 'custom_api';

    // Process selection constants
    public const PROCESS_MISC_ISSUE = 'misc_issue';
    public const PROCESS_MISC_RECEIPT = 'misc_receipt';
    public const PROCESS_QUANTITY_MOVE = 'quantity_move';
    public const PROCESS_PO_RECEIPT = 'po_receipt';

    // Endpoint type constants
    public const TYPE_API = 'api';
    public const TYPE_STORED_PROCEDURE = 'stored_procedure';

    protected $fillable = [
        'name',
        'url',
        'body_data_field',
        'target_type',
        'process_selection',
        'endpoint_type',
        'is_active',
        'created_by',
        'updated_by',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = [
        'target_type_name',
        'process_selection_name',
        'endpoint_type_name',
    ];

    /**
     * Get the display name for the target type.
     *
     * @return string
     */
    public function getTargetTypeNameAttribute()
    {
        return self::getTargetOptions()[$this->target_type] ?? $this->target_type;
    }

    /**
     * Get the display name for the process selection.
     *
     * @return string
     */
    public function getProcessSelectionNameAttribute()
    {
        return self::getProcessOptions()[$this->process_selection] ?? $this->process_selection;
    }

    /**
     * Get the display name for the endpoint type.
     *
     * @return string
     */
    public function getEndpointTypeNameAttribute()
    {
        return self::getEndpointTypeOptions()[$this->endpoint_type] ?? $this->endpoint_type;
    }

    protected $casts = [
        'is_active' => 'boolean',
        'body_data_field' => 'array',
    ];

    // Relationships
    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    // Available options for dropdowns
    public static function getTargetOptions()
    {
        return [
            self::TARGET_CSI => 'CSI',
            self::TARGET_SAP => 'SAP',
            self::TARGET_INFOR_WMS => 'Infor WMS',
            // self::TARGET_SALESFORCE => 'Salesforce',
            // self::TARGET_DYNAMICS => 'Microsoft Dynamics',
            // self::TARGET_ORACLE => 'Oracle',
            // self::TARGET_CUSTOM_API => 'Custom API',
        ];
    }

    public static function getProcessOptions()
    {
        return [
            self::PROCESS_MISC_ISSUE => 'Misc Issue',
            self::PROCESS_MISC_RECEIPT => 'Misc Receipt',
            self::PROCESS_QUANTITY_MOVE => 'Quantity Move',
            self::PROCESS_PO_RECEIPT => 'PO Receipt',
        ];
    }

    public static function getEndpointTypeOptions()
    {
        return [
            self::TYPE_API => 'API',
            self::TYPE_STORED_PROCEDURE => 'Stored Procedure',
        ];
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByTarget($query, $target)
    {
        return $query->where('target_type', $target);
    }

    public function scopeByProcess($query, $process)
    {
        return $query->where('process_selection', $process);
    }

    public function scopeByEndpointType($query, $type)
    {
        return $query->where('endpoint_type', $type);
    }

    /**
     * Get body data field as JSON string for forms
     */
    public function getBodyDataFieldJsonAttribute()
    {
        return is_array($this->body_data_field)
            ? json_encode($this->body_data_field)
            : $this->body_data_field;
    }
}
