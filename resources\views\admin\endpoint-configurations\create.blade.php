@extends('layouts.admin')
@section('pageTitle', 'Create Endpoint Configuration')

@push('styles')
<style>
    .required:after {
        content: " *";
        color: red;
    }
    .help-block {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 0.25rem;
    }
    .form-group {
        margin-bottom: 1.5rem;
    }
    .card-header h4 {
        margin: 0;
        color: #495057;
    }
</style>
@endpush

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card card-default">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h4><i class="fa fa-plus-circle"></i> {{ trans('global.create') }} {{ trans('cruds.endpointConfiguration.title_singular') }}</h4>
                        </div>
                        <div class="col-auto">
                            <a href="{{ route('admin.endpoint-configurations.index') }}" class="btn btn-secondary">
                                <i class="fa fa-arrow-left"></i> {{ trans('global.back_to_list') }}
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('admin.endpoint-configurations.store') }}" enctype="multipart/form-data">
                        @csrf

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="required" for="name">{{ trans('cruds.endpointConfiguration.fields.name') }}</label>
                                    <input class="form-control {{ $errors->has('name') ? 'is-invalid' : '' }}" type="text" name="name" id="name" value="{{ old('name', '') }}" required placeholder="Enter configuration name">
                                    @if($errors->has('name'))
                                        <div class="invalid-feedback">
                                            {{ $errors->first('name') }}
                                        </div>
                                    @endif
                                    <small class="help-block">{{ trans('cruds.endpointConfiguration.fields.name_helper') }}</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="required" for="target_type">Target System</label>
                                    <select class="form-control select2 {{ $errors->has('target_type') ? 'is-invalid' : '' }}" name="target_type" id="target_type" required>
                                        @foreach($targetOptions as $key => $label)
                                            <option value="{{ $key }}" {{ old('target_type', 'CSI') == $key ? 'selected' : '' }}>{{ $label }}</option>
                                        @endforeach
                                    </select>
                                    @if($errors->has('target_type'))
                                        <div class="invalid-feedback">
                                            {{ $errors->first('target_type') }}
                                        </div>
                                    @endif
                                    <small class="help-block">Select the target system for this endpoint</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="required" for="process_selection">{{ trans('cruds.endpointConfiguration.fields.process_selection') }}</label>
                                    <select class="form-control select2 {{ $errors->has('process_selection') ? 'is-invalid' : '' }}" name="process_selection" id="process_selection" required>
                                        <option value="">{{ trans('global.pleaseSelect') }}</option>
                                        @foreach($processOptions as $key => $label)
                                            <option value="{{ $key }}" {{ old('process_selection', '') == $key ? 'selected' : '' }}>{{ $label }}</option>
                                        @endforeach
                                    </select>
                                    @if($errors->has('process_selection'))
                                        <div class="invalid-feedback">
                                            {{ $errors->first('process_selection') }}
                                        </div>
                                    @endif
                                    <small class="help-block">{{ trans('cruds.endpointConfiguration.fields.process_selection_helper') }}</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="required" for="endpoint_type">{{ trans('cruds.endpointConfiguration.fields.endpoint_type') }}</label>
                                    <select class="form-control select2 {{ $errors->has('endpoint_type') ? 'is-invalid' : '' }}" name="endpoint_type" id="endpoint_type" required>
                                        @foreach($endpointTypeOptions as $key => $label)
                                            <option value="{{ $key }}" {{ old('endpoint_type', 'API') == $key ? 'selected' : '' }}>{{ $label }}</option>
                                        @endforeach
                                    </select>
                                    @if($errors->has('endpoint_type'))
                                        <div class="invalid-feedback">
                                            {{ $errors->first('endpoint_type') }}
                                        </div>
                                    @endif
                                    <small class="help-block">{{ trans('cruds.endpointConfiguration.fields.endpoint_type_helper') }}</small>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="required" for="url">{{ trans('cruds.endpointConfiguration.fields.url') }}</label>
                            <textarea class="form-control {{ $errors->has('url') ? 'is-invalid' : '' }}" name="url" id="url" rows="3" required placeholder="IDORequestService/ido/invoke/SLStockActItems?method=ItemMiscReceiptSp">{{ old('url', '') }}</textarea>
                            @if($errors->has('url'))
                                <div class="invalid-feedback">
                                    {{ $errors->first('url') }}
                                </div>
                            @endif
                            <small class="help-block">{{ trans('cruds.endpointConfiguration.fields.url_helper') }}</small>
                        </div>

                        <div class="form-group">
                            <label for="body_data_field">{{ trans('cruds.endpointConfiguration.fields.body_data_field') }}</label>
                            <div class="card">
                                <div class="card-header">
                                    <div class="row align-items-center">
                                        <div class="col">
                                            <h6 class="mb-0">Field Mapping Configuration</h6>
                                        </div>
                                        <div class="col-auto">
                                            <button type="button" class="btn btn-sm btn-outline-primary mr-2" id="importJsonBtn" title="Import fields from JSON">
                                                <i class="fa fa-file-import"></i> Import JSON
                                            </button>
                                            <button type="button" class="btn btn-sm btn-success" id="addFieldBtn">
                                                <i class="fa fa-plus"></i> Add Field
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div id="fieldMappingContainer">
                                        <!-- Dynamic fields will be added here -->
                                    </div>
                                    <div id="noFieldsMessage" class="text-center text-muted py-3">
                                        <i class="fa fa-info-circle"></i> No fields added yet. Click "Add Field" to start.
                                    </div>
                                </div>
                            </div>
                            <input type="hidden" name="body_data_field" id="body_data_field_hidden" value="{{ old('body_data_field', '') }}">
                            @if($errors->has('body_data_field'))
                                <div class="invalid-feedback d-block">
                                    {{ $errors->first('body_data_field') }}
                                </div>
                            @endif
                            <small class="help-block">{{ trans('cruds.endpointConfiguration.fields.body_data_field_helper') }}</small>
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-checkbox">
                                <input type="hidden" name="is_active" value="0">
                                <input class="custom-control-input" type="checkbox" name="is_active" id="is_active" value="1" {{ old('is_active', 1) == 1 ? 'checked' : '' }}>
                                <label class="custom-control-label" for="is_active">
                                    {{ trans('cruds.endpointConfiguration.fields.is_active') }}
                                </label>
                                @if($errors->has('is_active'))
                                    <div class="invalid-feedback d-block">
                                        {{ $errors->first('is_active') }}
                                    </div>
                                @endif
                                <small class="help-block d-block">{{ trans('cruds.endpointConfiguration.fields.is_active_helper') }}</small>
                            </div>
                        </div>

                        <hr>

                        <div class="form-group mb-0">
                            <button class="btn btn-primary" type="submit">
                                <i class="fa fa-save"></i> {{ trans('global.save') }}
                            </button>
                            <a class="btn btn-secondary ml-2" href="{{ route('admin.endpoint-configurations.index') }}">
                                <i class="fa fa-times"></i> {{ trans('global.cancel') }}
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    let fieldCounter = 0;

    // Data types options
    const dataTypes = [
        'string', 'integer', 'float', 'boolean', 'date', 'datetime', 'email', 'url', 'text', 'json'
    ];

    // Load existing data if editing
    const existingData = $('#body_data_field_hidden').val();
    if (existingData) {
        try {
            const fields = JSON.parse(existingData);
            if (Array.isArray(fields)) {
                fields.forEach(field => addFieldRow(field));
                updateNoFieldsMessage();
            }
        } catch (e) {
            console.log('Error parsing JSON data:', e);
        }
    }

    // Add field button click
    $('#addFieldBtn').on('click', function(e) {
        e.preventDefault();
        addFieldRow();
    });

    // Function to add a new field row
    function addFieldRow(fieldData = {}) {
        fieldCounter++;

        const fieldRow = `
            <div class="field-row border rounded p-3 mb-3" data-field-id="${fieldCounter}">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group mb-2">
                            <label class="small font-weight-bold">Field Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control form-control-sm field-name"
                                   placeholder="e.g., item_code" value="${fieldData.name || ''}" required>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group mb-2">
                            <label class="small font-weight-bold">Data Type</label>
                            <select class="form-control form-control-sm field-datatype">
                                ${dataTypes.map(type =>
                                    `<option value="${type}" ${fieldData.datatype === type ? 'selected' : ''}>${type}</option>`
                                ).join('')}
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group mb-2">
                            <label class="small font-weight-bold">Max Length</label>
                            <input type="number" class="form-control form-control-sm field-maxlength"
                                   placeholder="255" value="${fieldData.maxlength !== null && fieldData.maxlength !== undefined ? fieldData.maxlength : ''}" min="1">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group mb-2">
                            <label class="small font-weight-bold">Required</label>
                            <select class="form-control form-control-sm field-required">
                                <option value="false" ${fieldData.required === false ? 'selected' : ''}>No</option>
                                <option value="true" ${fieldData.required === true ? 'selected' : ''}>Yes</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group mb-2">
                            <label class="small font-weight-bold">Default Value</label>
                            <input type="text" class="form-control form-control-sm field-default"
                                   placeholder="Optional" value="${fieldData.default !== null && fieldData.default !== undefined ? fieldData.default : ''}">
                        </div>
                    </div>
                    <div class="col-md-1">
                        <div class="form-group mb-2">
                            <label class="small font-weight-bold">&nbsp;</label>
                            <button type="button" class="btn btn-sm btn-danger btn-block remove-field">
                                <i class="fa fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-11">
                        <div class="form-group mb-0">
                            <label class="small font-weight-bold">Description</label>
                            <input type="text" class="form-control form-control-sm field-description"
                                   placeholder="Optional field description" value="${fieldData.description || ''}">
                        </div>
                    </div>
                </div>
            </div>
        `;

        $('#fieldMappingContainer').append(fieldRow);
        updateNoFieldsMessage();
        updateHiddenField();
    }

    // Remove field
    $(document).on('click', '.remove-field', function() {
        $(this).closest('.field-row').remove();
        updateNoFieldsMessage();
        updateHiddenField();
    });

    // Update hidden field when any input changes
    $(document).on('input change', '.field-row input, .field-row select', function() {
        updateHiddenField();
    });

    // Function to update the hidden field with JSON data
    function updateHiddenField() {
        const fields = [];

        $('.field-row').each(function() {
            const row = $(this);
            const field = {
                name: row.find('.field-name').val().trim(),
                datatype: row.find('.field-datatype').val(),
                maxlength: row.find('.field-maxlength').val() ? parseInt(row.find('.field-maxlength').val()) : null,
                required: row.find('.field-required').val() === 'true',
                default: row.find('.field-default').val().trim() || null,
                description: row.find('.field-description').val().trim() || null
            };

            if (field.name) {
                fields.push(field);
            }
        });

        $('#body_data_field_hidden').val(JSON.stringify(fields));
    }

    // Function to show/hide no fields message
    function updateNoFieldsMessage() {
        if ($('.field-row').length === 0) {
            $('#noFieldsMessage').show();
        } else {
            $('#noFieldsMessage').hide();
        }
    }

    // Function to determine data type from value
    function determineDataType(value) {
        if (value === null || value === undefined) return 'string';
        if (Array.isArray(value)) return 'array';
        if (typeof value === 'object') return 'object';
        if (typeof value === 'number') {
            return Number.isInteger(value) ? 'integer' : 'float';
        }
        if (typeof value === 'boolean') return 'boolean';
        if (typeof value === 'string') {
            // Check for date/datetime
            const date = new Date(value);
            if (!isNaN(date.getTime())) {
                return value.includes('T') || value.includes(' ') ? 'datetime' : 'date';
            }
            // Check for email
            if (value.includes('@') && value.includes('.')) {
                return 'email';
            }
            // Check for URL
            try {
                new URL(value);
                return 'url';
            } catch (e) {}
        }
        return 'string';
    }

    // Function to flatten object and create field mappings
    function generateFieldMappingsFromSample(data, prefix = '', flatten = true, isRoot = true, depth = 0) {
        console.log(`generateFieldMappingsFromSample called with prefix: '${prefix}', isRoot: ${isRoot}, depth: ${depth}, data type:`, typeof data, 'data:', data);
        const fields = [];
        const indent = '  '.repeat(depth);
        console.log(`${indent}Processing:`, { data, prefix, flatten, isRoot });
        
        if (data === null || data === undefined) {
            console.log(`${indent}Data is null/undefined, returning empty fields`);
            return fields;
        }
        
        // Handle arrays - process each item and merge unique fields
        if (Array.isArray(data)) {
            console.log(`${indent}Processing array with ${data.length} items`);
            if (data.length === 0) {
                console.log(`${indent}Empty array, returning empty fields`);
                return fields;
            }
            
            // For arrays of objects, collect all unique fields from all objects
            if (typeof data[0] === 'object' && data[0] !== null) {
                console.log(`${indent}Processing array of objects`);
                
                // Collect all unique field names
                const allFieldNames = new Set();
                data.forEach(item => {
                    if (item && typeof item === 'object') {
                        Object.keys(item).forEach(key => allFieldNames.add(key));
                    }
                });
                
                // Create a merged object with all possible fields
                const mergedObject = {};
                allFieldNames.forEach(fieldName => {
                    // Find the first non-null value for this field
                    const value = data.find(item => item && item[fieldName] !== undefined);
                    if (value) {
                        mergedObject[fieldName] = value[fieldName];
                    }
                });
                
                console.log(`${indent}Merged object with ${Object.keys(mergedObject).length} fields:`, mergedObject);
                return generateFieldMappingsFromSample(mergedObject, prefix, flatten, isRoot, depth + 1);
            } 
            // For arrays of primitives, treat as a single field
            else if (data.length > 0) {
                console.log(`${indent}Processing array of primitives`);
                const field = {
                    name: prefix || 'array_field',
                    datatype: 'array',
                    required: false,
                    description: `Array field ${prefix || ''}`,
                    default: JSON.stringify(data)
                };
                console.log(`${indent}Created array field:`, field);
                fields.push(field);
                return fields;
            }
            console.log(`${indent}No items in array, returning empty fields`);
            return fields;
        }
        
        // Handle objects
        if (typeof data === 'object') {
            console.log(`${indent}Processing object with keys:`, Object.keys(data));
            Object.entries(data).forEach(([key, value]) => {
                const fieldName = prefix ? `${prefix}_${key}` : key;
                console.log(`${indent}Processing key '${key}' with value:`, value);
                
                // Handle nested objects
                if (value !== null && typeof value === 'object' && !Array.isArray(value)) {
                    console.log(`${indent}Key '${key}' is an object`);
                    if (flatten) {
                        console.log(`${indent}Flattening nested object '${key}'`);
                        const nestedFields = generateFieldMappingsFromSample(value, fieldName, flatten, false, depth + 1);
                        console.log(`${indent}Got ${nestedFields.length} fields from nested object '${key}'`);
                        fields.push(...nestedFields);
                    } else if (isRoot || !flatten) {
                        console.log(`${indent}Creating object field for '${key}'`);
                        const field = {
                            name: fieldName,
                            datatype: 'object',
                            required: false,
                            description: `Object field ${fieldName}`,
                            default: '{}'
                        };
                        fields.push(field);
                    }
                } 
                // Handle arrays
                else if (Array.isArray(value)) {
                    console.log(`${indent}Key '${key}' is an array`);
                    if (flatten) {
                        console.log(`${indent}Processing array items for '${key}'`);
                        const arrayFields = generateFieldMappingsFromSample(value, fieldName, flatten, false, depth + 1);
                        console.log(`${indent}Got ${arrayFields.length} fields from array '${key}'`);
                        fields.push(...arrayFields);
                    } else {
                        console.log(`${indent}Creating array field for '${key}'`);
                        const field = {
                            name: fieldName,
                            datatype: 'array',
                            required: false,
                            description: `Array field ${fieldName}`,
                            default: '[]'
                        };
                        fields.push(field);
                    }
                }
                // Handle primitive values
                else {
                    console.log(`${indent}Key '${key}' is a primitive (${typeof value}):`, value);
                    const field = {
                        name: fieldName,
                        datatype: determineDataType(value),
                        required: false,
                        description: `Field ${fieldName}`
                    };
                    
                    // Set reasonable defaults based on data type
                    if (field.datatype === 'string') {
                        field.maxlength = 255;
                        field.default = value !== null && value !== undefined ? String(value) : '';
                    } else if (field.datatype === 'integer' || field.datatype === 'float') {
                        field.maxlength = 20;
                        field.default = value !== null && value !== undefined ? String(value) : '0';
                    } else if (field.datatype === 'boolean') {
                        field.default = value ? 'true' : 'false';
                    }
                    
                    console.log(`${indent}Created field:`, field);
                    fields.push(field);
                }
            });
        } else {
            console.log(`${indent}Data is not an object or array:`, typeof data);
        }
        
        console.log(`${indent}Returning ${fields.length} fields`);
        return fields;
    }

    // Initial state
    updateNoFieldsMessage();

    // JSON Import Modal
    const importModal = `
    <div class="modal fade" id="importJsonModal" tabindex="-1" role="dialog" aria-labelledby="importJsonModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="importJsonModalLabel">Import Field Mappings</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs mb-3" id="importTabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="json-tab" data-toggle="tab" href="#jsonTab" role="tab" aria-controls="jsonTab" aria-selected="true">Field Mappings</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="sample-tab" data-toggle="tab" href="#sampleTab" role="tab" aria-controls="sampleTab" aria-selected="false">From Sample Data</a>
                        </li>
                    </ul>
                    
                    <div class="tab-content" id="importTabsContent">
                        <!-- Field Mappings Tab -->
                        <div class="tab-pane fade show active" id="jsonTab" role="tabpanel" aria-labelledby="json-tab">
                            <div class="form-group">
                                <label for="jsonInput">Paste your JSON array of field mappings:</label>
                                <textarea class="form-control" id="jsonInput" rows="10" placeholder='[
  {
    "name": "field1",
    "datatype": "string",
    "maxlength": 255,
    "required": true,
    "default": "",
    "description": "Field description"
  },
  {
    "name": "field2",
    "datatype": "integer",
    "required": false,
    "description": "Numeric field"
  }
]'></textarea>
                                <small class="form-text text-muted">
                                    Format: An array of objects with field properties (name, datatype, maxlength, required, default, description)
                                </small>
                            </div>
                        </div>
                        
                        <!-- Sample Data Tab -->
                        <div class="tab-pane fade" id="sampleTab" role="tabpanel" aria-labelledby="sample-tab">
                            <div class="form-group">
                                <label for="sampleDataInput">Paste sample JSON response from your endpoint:</label>
                                <textarea class="form-control" id="sampleDataInput" rows="10" placeholder='{
  "id": 123,
  "name": "Example Product",
  "price": 99.99,
  "in_stock": true,
  "tags": ["electronics", "new"],
  "details": {
    "weight": 2.5,
    "dimensions": "10x15x5cm"
  },
  "created_at": "2023-01-01T12:00:00Z"
}'></textarea>
                                <small class="form-text text-muted">
                                    Paste a sample JSON response to automatically generate field mappings
                                </small>
                            </div>
                            <div class="form-group">
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" class="custom-control-input" id="flattenNested" checked>
                                    <label class="custom-control-label" for="flattenNested">Flatten nested objects (e.g., details.weight → details_weight)</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div id="jsonError" class="alert alert-danger mt-3" style="display: none;"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="importJsonSubmit">Import Fields</button>
                </div>
            </div>
        </div>
    </div>`;

    // Debug: Log when script loads
    console.log('Endpoint configuration form script loaded');

    // Add modal to DOM
    $('body').append(importModal);
    console.log('Import modal added to DOM');

    // Debug: Check if button exists
    const $importButton = $('#importJsonBtn');
    console.log('Import button found:', $importButton.length > 0);

    // Open import modal
    $importButton.on('click', function(e) {
        console.log('Import button clicked', e);
        $('#jsonInput').val('');
        $('#jsonError').hide();
        console.log('Showing import modal');
        $('#importJsonModal').modal('show');
    });
    
    // Debug: Check event binding
    console.log('Event listeners for import button:', $._data($importButton[0], 'events'));

    // Debug: Check tab initialization
    console.log('Initial active tab:', $('.nav-tabs .nav-link.active').attr('id'));
    
    // Debug: Tab switching
    $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
        const activeTab = $(e.target).attr('href'); // This will be like '#jsonTab' or '#sampleTab'
        console.log('Tab switched to:', activeTab);
    });

    // Handle JSON import
    $('#importJsonSubmit').on('click', function(e) {
        console.log('Import submit button clicked', e);
        
        // Prevent form submission
        e.preventDefault();
        e.stopPropagation();
        
        // Get the active tab by checking which tab link has the 'active' class
        const activeTabId = $('.nav-tabs .nav-link.active').attr('href'); // Returns '#jsonTab' or '#sampleTab'
        console.log('Active tab ID:', activeTabId);
        
        // Remove the '#' to get the actual ID
        const activeTab = activeTabId ? activeTabId.substring(1) : '';
        console.log('Active tab:', activeTab);
        
        try {
            let fields = [];
            
            if (activeTab === 'jsonTab') {
                console.log('Processing direct field mappings');
                const jsonInput = $('#jsonInput').val().trim();
                if (!jsonInput) {
                    showJsonError('Please enter JSON data');
                    return;
                }
                
                const parsed = JSON.parse(jsonInput);
                console.log('Parsed JSON:', parsed);
                
                if (!Array.isArray(parsed)) {
                    throw new Error('Invalid format. Expected an array of field objects.');
                }
                fields = parsed;
                console.log('Using direct field mappings:', fields);
            } 
            else if (activeTab === 'sampleTab') {
                console.log('Processing sample data import');
                const sampleDataInput = $('#sampleDataInput').val().trim();
                console.log('Raw sample data input:', sampleDataInput);
                
                if (!sampleDataInput) {
                    showJsonError('Please paste sample JSON data');
                    return;
                }
                
                console.log('Attempting to parse sample data...');
                let sampleData;
                try {
                    sampleData = JSON.parse(sampleDataInput);
                    console.log('Successfully parsed sample data:', sampleData);
                } catch (e) {
                    console.error('Error parsing sample data:', e);
                    throw new Error('Invalid JSON format: ' + e.message);
                }
                
                const flattenNested = $('#flattenNested').is(':checked');
                console.log('Flatten nested objects:', flattenNested);
                
                console.log('Generating field mappings...');
                fields = generateFieldMappingsFromSample(sampleData, '', flattenNested, true);
                console.log('Generated fields:', fields);
                
                if (fields.length === 0) {
                    console.warn('No fields were generated from the sample data');
                    throw new Error('Could not generate any fields from the sample data');
                }
                
                console.log(`Successfully generated ${fields.length} fields from sample data`);
            } else {
                console.warn('Unknown active tab:', activeTab);
                throw new Error('Unknown import type');
            }

            // Clear existing fields
            $('.field-row').remove();
            
            // Add each field from the generated fields
            fields.forEach(field => {
                if (field && typeof field === 'object' && field.name) {
                    addFieldRow(field);
                }
            });
            
            updateNoFieldsMessage();
            updateHiddenField();
            
            $('#importJsonModal').modal('hide');
            showToast('success', `Successfully imported ${fields.length} fields`);
            
        } catch (error) {
            showJsonError('Error: ' + error.message);
        }
    });
    
    // Show JSON error message
    function showJsonError(message) {
        const $errorDiv = $('#jsonError');
        $errorDiv.text(message).show();
        $('html, body').animate({
            scrollTop: $errorDiv.offset().top - 100
        }, 500);
    }
    
    // Show toast notification
    function showToast(type, message) {
        // You can implement a toast notification here or use alert for now
        alert(message);
    }
});
</script>

<style>
    .field-row {
        background-color: #f8f9fa;
        border-radius: 4px;
        margin-bottom: 15px;
        transition: all 0.2s;
    }
    .field-row:hover {
        background-color: #f1f3f5;
    }
    .form-control-sm {
        height: calc(1.5em + 0.5rem + 2px);
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        line-height: 1.5;
        border-radius: 0.2rem;
    }
    .modal-body textarea {
        font-family: monospace;
        font-size: 0.9em;
    }
</style>
@endsection
