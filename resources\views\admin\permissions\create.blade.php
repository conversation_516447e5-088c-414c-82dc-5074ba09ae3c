@extends('layouts.admin')
@section('pageTitle',trans('global.create')." ". trans('cruds.permission.title_singular'))

@section('content')
<div class="card card-default">
    <div class="card-header  separator">
        <div class="card-title mainheading"> 
            <h4>      
                {{ trans('global.create') }} {{ trans('cruds.permission.title_singular') }}
            </h4>
        </div>
        <div class="card-controls">
            <ul>

            </ul>
        </div>
    </div>


    <div class="card-block">
        <form method="POST" id="form-work" class="form-horizontal" action="{{ route("admin.permissions.store") }}" enctype="multipart/form-data">
            @csrf
            <div class="form-group row">
                <div class="col-md-3"><h5 class="bold inputlabel required" for="title">{{ trans('cruds.permission.fields.title') }}</h5></div>
                <div class="col-md-9">



                    <input class="form-control {{ $errors->has('title') ? 'is-invalid' : '' }}" type="text" name="title" id="title" value="{{ old('title', '') }}" required>
                    @if($errors->has('title'))
                    <span class="text-danger">{{ $errors->first('title') }}</span>
                    @endif
                    <span class="help-block">{{ trans('cruds.permission.fields.title_helper') }}</span>
                </div>
            </div>
            <div class="form-group row">
                <div class="col-md-3"></div>
                <div class="col-md-9 text-right">
                    <button class="btn btn-lg btn-success" type="submit">
                        {{ trans('global.save') }}
                    </button>
                </div>

            </div>
        </form>
    </div>
</div>




@endsection