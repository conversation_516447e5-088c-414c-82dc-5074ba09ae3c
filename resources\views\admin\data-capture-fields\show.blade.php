@extends('layouts.admin')
@section('pageTitle', 'Data Capture Field Details')

@section('styles')
    @parent
    <style>
        .json-display {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
        }
        .icon-preview {
            font-size: 24px;
            margin-right: 10px;
        }
    </style>
@endsection

@section('content')
    <div class="card card-default">
        <div class="card-header separator">
            <div class="card-title mainheading">
                <h4>Data Capture Field Details</h4>
            </div>
            <div class="card-controls">
                <ul>
                    <li>
                        <a class="btn btn-secondary" href="{{ route('admin.data-capture-fields.index') }}">
                            <i class="fa fa-arrow-left"></i> Back to List
                        </a>
                    </li>
                    <li>
                        <a class="btn btn-primary" href="{{ route('admin.data-capture-fields.edit', $dataCaptureField) }}">
                            <i class="fa fa-edit"></i> Edit
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <div class="card-block">
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-bordered">
                        <tr>
                            <th width="30%">Title</th>
                            <td>{{ $dataCaptureField->title }}</td>
                        </tr>
                        <tr>
                            <th>Key</th>
                            <td><code>{{ $dataCaptureField->key }}</code></td>
                        </tr>
                        <tr>
                            <th>Process Type</th>
                            <td>{{ $dataCaptureField->process_type }}</td>
                        </tr>
                        <tr>
                            <th>Icon</th>
                            <td>
                                <i class="fa fa-{{ $dataCaptureField->icon }} icon-preview"></i>
                                {{ $dataCaptureField->icon }}
                            </td>
                        </tr>
                        <tr>
                            <th>Created At</th>
                            <td>{{ $dataCaptureField->created_at->format('Y-m-d H:i:s') }}</td>
                        </tr>
                        <tr>
                            <th>Updated At</th>
                            <td>{{ $dataCaptureField->updated_at->format('Y-m-d H:i:s') }}</td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    @php
                        $schema = is_string($dataCaptureField->schema) ? json_decode($dataCaptureField->schema, true) : $dataCaptureField->schema;
                    @endphp

                    <h5>Schema Details</h5>
                    <table class="table table-bordered">
                        <tr>
                            <th width="30%">Label</th>
                            <td>{{ $schema['label'] ?? 'N/A' }}</td>
                        </tr>
                        <tr>
                            <th>Type</th>
                            <td><span class="badge badge-info">{{ $schema['type'] ?? 'N/A' }}</span></td>
                        </tr>
                        <tr>
                            <th>Key</th>
                            <td><code>{{ $schema['key'] ?? 'N/A' }}</code></td>
                        </tr>
                        <tr>
                            <th>Input Field</th>
                            <td>
                                @if($schema['input'] ?? false)
                                    <span class="badge badge-success">Yes</span>
                                @else
                                    <span class="badge badge-secondary">No</span>
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <th>Required</th>
                            <td>
                                @if($schema['validate']['required'] ?? false)
                                    <span class="badge badge-warning">Required</span>
                                @else
                                    <span class="badge badge-secondary">Optional</span>
                                @endif
                            </td>
                        </tr>
                    </table>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <h5>Complete JSON Schema</h5>
                    <div class="json-display">{{ json_encode($schema, JSON_PRETTY_PRINT) }}</div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <h5>Usage Example</h5>
                    <div class="json-display">{{ json_encode([
                        $dataCaptureField->key => [
                            'title' => $dataCaptureField->title,
                            'key' => $dataCaptureField->key,
                            'icon' => $dataCaptureField->icon,
                            'schema' => $schema
                        ]
                    ], JSON_PRETTY_PRINT) }}</div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="btn-group">
                        <a href="{{ route('admin.data-capture-fields.edit', $dataCaptureField) }}" class="btn btn-primary">
                            <i class="fa fa-edit"></i> Edit Field
                        </a>
                        <form method="POST" action="{{ route('admin.data-capture-fields.destroy', $dataCaptureField) }}" style="display: inline-block;" onsubmit="return confirm('Are you sure you want to delete this field?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger">
                                <i class="fa fa-trash"></i> Delete Field
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
