<?php

namespace Database\Factories;

use App\Models\EndpointConfiguration;
use Illuminate\Database\Eloquent\Factories\Factory;

class EndpointConfigurationFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = EndpointConfiguration::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'name' => $this->faker->unique()->words(3, true),
            'external_system' => $this->faker->randomElement(array_keys(EndpointConfiguration::EXTERNAL_SYSTEMS)),
            'integration_method' => $this->faker->randomElement(EndpointConfiguration::INTEGRATION_METHODS),
            'process_type' => $this->faker->randomElement(EndpointConfiguration::PROCESS_TYPES),
            'endpoint_url' => $this->faker->url,
            'request_fields' => [
                [
                    'field_name' => 'field1',
                    'field_type' => 'string',
                    'is_required' => true,
                    'example' => 'example1',
                    'description' => 'Test field 1',
                ],
                [
                    'field_name' => 'field2',
                    'field_type' => 'number',
                    'is_required' => false,
                    'example' => '123',
                    'description' => 'Test field 2',
                ],
            ],
            'is_active' => $this->faker->boolean,
            'created_by' => function () {
                return \App\Models\User::factory()->create()->id;
            },
            'updated_by' => function (array $attributes) {
                return $attributes['created_by'];
            },
        ];
    }

    /**
     * Indicate that the configuration is active.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function active()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_active' => true,
            ];
        });
    }

    /**
     * Indicate that the configuration is inactive.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function inactive()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_active' => false,
            ];
        });
    }
}
