<div class=" btn-toolbar">
    <div class="btn-group sm-m-t-10" role="group">
        @if (isset($actions['view']))
            <x-buttons.view :route="$actions['view']" size="sm" :title="__('global.view')" :iconOnly="true" class="me-1" />
            {{-- <a class="btn btn-sm btn-default" title="View" href="{{ $actions['view'] }}">
                <i class="fa fa-eye"></i>
            </a> --}}
        @endif
        @if (isset($actions['edit']))
            <x-buttons.edit :route="$actions['edit']" size="sm" :title="__('global.edit')" :iconOnly="true" class="me-1" />

            {{-- <a class="btn btn-sm btn-default" title="Edit" href="{{ $actions['edit'] }}">
                <i class="fa fa-edit"></i>
            </a> --}}
        @endif
        @if (isset($actions['clone']))
            <x-buttons.clone :route="$actions['clone']" size="sm" :title="__('global.clone')" :iconOnly="true" class="me-1" />

            {{-- <a class="btn btn-sm btn-default" title="Clone" href="{{ $actions['clone'] }}">
                <i class="fa fa-copy"></i>
            </a> --}}
        @endif
        @if (isset($actions['delete']))
            @php
                $itemName = $actions['name'] ?? '';
                $confirmMessage = __('global.are_you_sure_delete');
                if ($itemName) {
                    $confirmMessage = __('global.are_you_sure_delete', ['name' => $itemName]);
                }
            @endphp
            <x-buttons.delete 
                :route="$actions['delete']" 
                size="sm" 
                :title="__('global.delete')" 
                :confirm="$confirmMessage"
                :iconOnly="true" />
            {{-- <form class="d-inline" action="{{ $actions['delete'] }}" method="POST">
                @csrf
                @method('DELETE')
                <button type="button" title="Delete" class="btn btn-sm btn-default  btn-danger delete-confirm"
                    data-id="{{ $row->id }}">
                    <i class="fa fa-trash"></i>
                </button>
            </form> --}}
        @endif
    </div>
</div>

