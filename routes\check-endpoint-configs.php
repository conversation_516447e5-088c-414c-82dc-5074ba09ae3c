<?php

use Illuminate\Support\Facades\Route;
use App\Models\IntegrationConfiguration;

Route::get('/check-endpoint-configs', function () {
    // Get all endpoint configurations
    $configs = \App\Models\IntegrationConfiguration::all();
    
    // Get the available process options from the model
    $processOptions = IntegrationConfiguration::getProcessOptions();
    
    echo "<h1>Endpoint Configurations</h1>";
    echo "<table border='1' cellpadding='5' cellspacing='0'>";
    echo "<tr><th>ID</th><th>Name</th><th>Target Type</th><th>Process Selection</th><th>Endpoint Type</th><th>Active</th></tr>";
    
    foreach ($configs as $config) {
        echo "<tr>";
        echo "<td>{$config->id}</td>";
        echo "<td>{$config->name}</td>";
        echo "<td>{$config->target_type} (" . ($config->target_type_name ?? 'N/A') . ")</td>";
        echo "<td>{$config->process_selection} (" . ($config->process_selection_name ?? 'N/A') . ")</td>";
        echo "<td>{$config->endpoint_type} (" . ($config->endpoint_type_name ?? 'N/A') . ")</td>";
        echo "<td>" . ($config->is_active ? 'Yes' : 'No') . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    echo "<h2>Available Process Options</h2>";
    echo "<pre>";
    print_r($processOptions);
    echo "</pre>";
    
    return '';
});
