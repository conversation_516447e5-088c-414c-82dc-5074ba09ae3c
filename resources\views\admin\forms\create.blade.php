@extends('layouts.admin')
@section('pageTitle', trans('global.create') . ' ' . trans('cruds.form.title_singular'))
{{-- <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap/dist/css/bootstrap.min.css"> --}}
{{-- <link rel="stylesheet" href="https://cdn.form.io/js/formio.full.min.css"> --}}
<link rel="stylesheet" href="https://cdn.form.io/js/formio.form.min.css">
@section('styles')
    @parent

    <style>
        .builder {
            width: auto !important;
            right: 0px !important;
            min-height: 500px;
        }

        .form-builder-container {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
    </style>
@endsection

@section('content')

    <div class="card card-default">
        <div class="card-header separator">
            <div class="card-title mainheading">
                <h4>{{ trans('global.create') }} {{ trans('cruds.form.title_singular') }}</h4>
            </div>
            <div class="card-controls">
                <ul>
                    <li>
                        <x-cancel-button :url="route('admin.forms.index')" :text="trans('global.back_to_list')" />
                    </li>
                </ul>
            </div>
        </div>

        <div class="card-block">
            @if ($errors->any())
                <div class="alert alert-danger">
                    <ul>
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <form method="POST" action="{{ route('admin.forms.store') }}" id="formData">
                @csrf

                <div class="form-group row">
                    <label for="title" class="col-md-2 col-form-label">{{ trans('cruds.fields.form_title') }} <span
                            class="text-danger">*</span></label>
                    <div class="col-md-4">
                        <input type="text" class="form-control" id="title" name="title" value="{{ old('title') }}"
                            required>
                    </div>
                    <label for="is_active" class="col-md-2 col-form-label">{{ trans('cruds.fields.status') }}</label>
                    <div class="col-md-4">
                        <x-status-select :row="null" />

                        {{-- <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="is_active" name="is_active" value="1"
                                {{ old('is_active', true) ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_active">
                                {{ trans('cruds.fields.active') }}
                            </label>
                        </div> --}}
                    </div>
                </div>

                <div class="form-group row">
                    <label for="user_group_id"
                        class="col-md-2 col-form-label">{{ trans('cruds.fields.user_group') }}</label>
                    <div class="col-md-4">
                        <select class="form-control" id="user_group_id" name="user_group_id">
                            <option value="">{{ trans('cruds.fields.all_groups') }}</option>
                            @foreach ($userGroups as $id => $name)
                                <option value="{{ $id }}" {{ old('user_group_id') == $id ? 'selected' : '' }}>
                                    {{ $name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <label for="redirect_to"
                        class="col-md-2 col-form-label">{{ trans('cruds.fields.redirect_to') }}</label>
                    <div class="col-md-4">
                        <select class="form-control" id="redirect_to" name="redirect_to">
                            <option value="">{{ trans('cruds.fields.no_redirect') }}</option>
                            @foreach ($forms as $id => $title)
                                <option value="{{ $id }}" {{ old('redirect_to') == $id ? 'selected' : '' }}>
                                    {{ $title }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>

                <div class="form-group row">
                    <label for="type"
                        class="col-md-2 col-form-label required">{{ trans('cruds.fields.module') }}</label>
                    <div class="col-md-4">
                        <select class="form-control {{ $errors->has('module') ? 'is-invalid' : '' }}" name="module"
                            id="module" required>
                            <option value="">{{ trans('global.pleaseSelect') }}</option>
                            @foreach (\App\Models\Form::$form_modules as $key => $label)
                                <option value="{{ $key }}" {{ old('module', '') == $key ? 'selected' : '' }}>
                                    {{ $label }}
                                </option>
                            @endforeach
                        </select>
                        @if ($errors->has('module'))
                            <div class="invalid-feedback">
                                {{ $errors->first('module') }}
                            </div>
                        @endif

                    </div>
                    <label for="type" class="col-md-2 col-form-label">{{ trans('cruds.fields.process_type') }}</label>
                    <div class="col-md-4">
                        <select class="form-control {{ $errors->has('process_type') ? 'is-invalid' : '' }}"
                            name="process_type" id="process_type">
                            <option value="">{{ trans('global.pleaseSelect') }}</option>
                            @foreach (\App\Models\IntegrationConfiguration::getProcessTypeOptions() as $type)
                                <option value="{{ $type }}">{{ $type }}</option>
                            @endforeach

                        </select>
                        @if ($errors->has('process_type'))
                            <div class="invalid-feedback">
                                {{ $errors->first('process_type') }}
                            </div>
                        @endif

                    </div>
                    {{-- <label for="type" class="col-md-2 col-form-label required">{{ trans('cruds.fields.device_view') }}</label>
                    <div class="col-md-4">
                        <select class="form-control {{ $errors->has('device_view') ? 'is-invalid' : '' }}"
                            name="device_view" id="device_view" required>
                            <option value="">{{ trans('global.pleaseSelect') }}</option>
                            @foreach (\App\Models\Form::$form_views as $key => $label)
                                <option value="{{ $key }}"
                                    {{ old('device_view', '') == $key ? 'selected' : '' }}>
                                    {{ $label }}
                                </option>
                            @endforeach
                        </select>
                        @if ($errors->has('device_view'))
                            <div class="invalid-feedback">
                                {{ $errors->first('device_view') }}
                            </div>
                        @endif

                    </div> --}}
                </div>



                <div class="form-group row">
                    {{-- <label class="col-md-3 col-form-label">Form Builder</label> --}}
                    <div class="col-md-12">
                        <div class="form-builder-container">
                            <div id="builder"></div>
                        </div>
                    </div>
                </div>

                <input type="hidden" name="content" id="content">

                <div class="form-group row">
                    <div class="col-md-10 offset-md-2 text-right">
                        <x-save-button />
                        <x-cancel-button :url="route('admin.forms.index')" :text="trans('global.cancel')" />
                    </div>
                </div>
            </form>

        </div>
    </div>
@endsection

@section('scripts')
    @parent
    @include('admin.forms.scripts')
@endsection
