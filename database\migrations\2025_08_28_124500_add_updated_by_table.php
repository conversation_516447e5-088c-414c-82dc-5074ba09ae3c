<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('forms', function (Blueprint $table) {
            $table->unsignedBigInteger('updated_by')->default(0)->comment('User who last updated this setting');
        });
        Schema::table('form_field_templates', function (Blueprint $table) {
            $table->unsignedBigInteger('updated_by')->default(0)->comment('User who last updated this setting');
        });
        Schema::table('users', function (Blueprint $table) {
            $table->unsignedBigInteger('updated_by')->default(0)->comment('User who last updated this setting');
        });
        Schema::table('user_groups', function (Blueprint $table) {
            $table->unsignedBigInteger('updated_by')->default(0)->comment('User who last updated this setting');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('forms', function (Blueprint $table) {
            // $table->dropForeign(['redirect_to']);
            $table->dropColumn('updated_by');
        });
        Schema::table('form_field_templates', function (Blueprint $table) {
            // $table->dropForeign(['redirect_to']);
            $table->dropColumn('updated_by');
        });
        Schema::table('users', function (Blueprint $table) {
            // $table->dropForeign(['redirect_to']);
            $table->dropColumn('updated_by');
        });
        Schema::table('user_groups', function (Blueprint $table) {
            // $table->dropForeign(['redirect_to']);
            $table->dropColumn('updated_by');
        });
    }
};
