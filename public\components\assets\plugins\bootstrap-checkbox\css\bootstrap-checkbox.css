.bootstrap-checkbox {
    display:inline-block;
    position:relative;
    width:15px;
    height:15px;
    border:1px solid #c2c6cb;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
	-webkit-border-radius: 3px;
-moz-border-radius: 3px;
border-radius: 3px;
transition: border 0.2s linear 0s;
}
.check-circle .bootstrap-checkbox{
	-webkit-border-radius: 99px;
	-moz-border-radius: 99px;
	border-radius: 99px;
}
.bootstrap-checkbox.checked{
	  border:1px solid #999999;
}
.,.check-success,.check-primary, .check-info, .check-warning, .check-danger {
	display:inline-block;
}
. label,.check-success label,.check-primary label, .check-info label, .check-warning label, .check-danger label {
	display:inline-block;
	position:relative;
	top:-4px;
	margin-left:5px;
	font-size:12px;
}
.check-success .bootstrap-checkbox.checked{
	background-color:#0090D9;
	border:1px solid #0090D9;
}
.check-primary .bootstrap-checkbox.checked{
	background-color:#0AA699;
	border:1px solid #0AA699;
}
.check-info .bootstrap-checkbox.checked{
	background-color:#1F3853;
	border:1px solid #1F3853;
}
.check-warning .bootstrap-checkbox.checked{
	background-color:#FDD01C;
	border:1px solid #FDD01C;
}
.check-danger .bootstrap-checkbox.checked{
	background-color:#F35958;
	border:1px solid #F35958;
}
.check-success .bootstrap-checkbox i ,.check-primary .bootstrap-checkbox i,.check-info .bootstrap-checkbox i,.check-warning .bootstrap-checkbox i,.check-danger .bootstrap-checkbox i{
	color:#fff;
}
.bootstrap-checkbox i{
    position:absolute;
    left : 2px;
    top : 2px;
	font-size:11px
}


.radio label {
	display: inline-block;
	cursor: pointer;
	position: relative;
	padding-left: 25px;
	margin-right: 15px;
	font-size: 13px;
	margin-bottom:6px;
	color: #777a80;
}

.radio input[type=radio] {
	display: none;
}
.radio label:before {
	content: "";
	display: inline-block;
	width: 16px;
	height: 16px;
	margin-right: 10px;
	position: absolute;
	left: 0;
	bottom: 1px;
	background-color: #fff;
	 border:1px solid #c2c6cb;
	border-radius: 99px;
}


.radio label:after {
  font-size: 13px;
  content: "";
  text-align: center;
  display: inline-block;
  width: 16px;
  height: 16px;
  position: absolute;
  -o-transform: scale(0);
  -ms-transform: scale(0);
  -moz-transform: scale(0);
  -webkit-transform: scale(0);
  transform: scale(0);
  border-radius: 99px;
  left:0px;
  top:1px;   
  background-color: #6f7b8a;  
  border:1px solid #6f7b8a;
}
.radio input[type=radio][disabled]+ label{	
	 opacity: 0.65;
}
.checkbox input[type=radio][disabled]+ label:after {
	background-color:#eceff3;
}

.radio.radio-success label:after { 
	background-color: #0090D9;  
	 border:1px solid #0090D9;
}

.radio.radio-primary label:after { 
	background-color: #0AA699;  
	border:1px solid #0AA699;
}

.radio.radio-info label:after { 
	background-color: #1F3853;  
	 border:1px solid #1F3853;
}

.radio.radio-warning label:after { 
	background-color: #FDD01C;  
	 border:1px solid #FDD01C;
}

.radio.radio-danger label:after { 
	background-color: #F35958;  
	 border:1px solid #F35958;
}

.radio input[type=radio]:checked + label{	
	color: #4d5056;
}
.radio input[type=radio]:checked + label:after { 
  -o-transform: scale(0.63);
  -ms-transform: scale(0.63);
  -moz-transform: scale(0.63);
  -webkit-transform: scale(0.63);
  transform:scale(0.63);
  line-height: 18px;  
  -o-transition: -o-transform .1s 0s cubic-bezier(0.455, 0.030, 0.215, 1.330);
  -ms-transition: -ms-transform .1s 0s cubic-bezier(0.455, 0.030, 0.215, 1.330);
  -moz-transition: -moz-transform .1s 0s cubic-bezier(0.455, 0.030, 0.215, 1.330);
  -webkit-transition: -webkit-transform .1s 0s cubic-bezier(0.455, 0.030, 0.215, 1.330);
  -o-transition: transform .1s 0s cubic-bezier(0.455, 0.030, 0.215, 1.330);
  -ms-transition: transform .1s 0s cubic-bezier(0.455, 0.030, 0.215, 1.330);
  -moz-transition: transform .1s 0s cubic-bezier(0.455, 0.030, 0.215, 1.330);
  -webkit-transition: transform .1s 0s cubic-bezier(0.455, 0.030, 0.215, 1.330);
  transition: transform .1s 0s cubic-bezier(0.455, 0.030, 0.215, 1.330);
}
.radio input[type=radio]:checked + label:before {  
	border:1px solid #8e9095;
}
.radio.radio-success input[type=radio]:checked + label:before {  
	border:1px solid #0090D9;
}
.radio.radio-primary input[type=radio]:checked + label:before {  
	border:1px solid #0AA699;
}
.radio.radio-info label input[type=radio]:checked + :before {  
	border:1px solid #1F3853;
}
.radio.radio-warning input[type=radio]:checked + label:before {  
	border:1px solid #FDD01C;
}
.radio.radio-danger input[type=radio]:checked + label:before {  
	border:1px solid #F35958;
}

.checkbox label {
	display: inline-block;
	cursor: pointer;
	position: relative;
	padding-left: 25px;
	margin-right: 15px;
	font-size: 13px;
	margin-bottom:6px;
	color: #777a80;
	transition: border 0.2s linear 0s,color 0.2s linear 0s;
}
table td .checkbox label {
	padding-left: 0px;
}
table th .checkbox label {
	padding-left: 0px;
}
.star label {
	display: inline-block;
	cursor: pointer;
	position: relative;
	font-size: 18px;
	transition: border 0.2s linear 0s,color 0.2s linear 0s;
}

.star label:before {
	font-family: 'FontAwesome';	
	content: "\F005";
	display: inline-block;
	margin-right: 10px;
	position: absolute;
	left: 0px;
	top: -4px;
		font-size: 18px;
	color:#97a3b3;
	transition: border 0.2s linear 0s,color 0.2s linear 0s;
}

.star label {
	margin-bottom: 10px;
}
.star  input[type=checkbox] {
	display: none;
}

.star input[type=checkbox]:checked + label:before  {
	color:#ffd200;
}

.checkbox label:before {
	content: "";
	display: inline-block;
	width: 16px;
	height: 16px;
	margin-right: 10px;
	position: absolute;
	left: 0;
	top: 1.5px;
	background-color: #fff;
	border:1px solid #c2c6cb;
	border-radius: 3px;
	transition: border 0.2s linear 0s,color 0.2s linear 0s;
}


.checkbox label::after {
  display: inline-block;
  width: 16px;
  height: 16px;
  position: absolute;
  left:3.8px;
  top:0px;  
  font-size:11px;  
  transition: border 0.2s linear 0s,color 0.2s linear 0s;
}

.checkbox  input[type=checkbox] {
	display: none;
}
.checkbox label {
	margin-bottom: 10px;
}
.checkbox label:after {
    border-radius: 3px;
}
.checkbox.checkbox-circle label:after {
    border-radius: 99px;
}
.checkbox.checkbox-circle label:before {
    border-radius: 99px;
}

.checkbox input[type=checkbox]:checked + label::after  {
	font-family: 'FontAwesome';
	content: "\F00C";
}
.checkbox input[type=checkbox]:checked + label{
	color: #4d5056;
}

.checkbox input[type=checkbox][disabled]+ label{	
	 opacity: 0.65;
}
.checkbox input[type=checkbox][disabled]+ label:before {
	background-color:#eceff3;
}

.checkbox.check-success input[type=checkbox]:checked + label:before {
	background-color:#0090D9;
	border:1px solid #0090D9;
}
.checkbox.check-success input[type=checkbox]:checked + label::after {
	color:#fff;
}

.checkbox.check-primary input[type=checkbox]:checked + label:before {
		background-color:#0AA699;
	border:1px solid #0AA699;
}
.checkbox.check-primary input[type=checkbox]:checked + label::after {
	color:#fff;
}

.checkbox.check-info input[type=checkbox]:checked + label:before {
	background-color:#1F3853;
	border:1px solid #1F3853;
}
.checkbox.check-info input[type=checkbox]:checked + label::after {
	color:#fff;
}

.checkbox.check-warning input[type=checkbox]:checked + label:before {
	background-color:#FDD01C;
	border:1px solid #FDD01C;
}
.checkbox.check-warning input[type=checkbox]:checked + label::after {
	color:#fff;
}

.checkbox.check-danger input[type=checkbox]:checked + label:before {
	background-color:#F35958;
	border:1px solid #F35958;
}
.checkbox.check-danger input[type=checkbox]:checked + label::after {
	color:#fff;
}


