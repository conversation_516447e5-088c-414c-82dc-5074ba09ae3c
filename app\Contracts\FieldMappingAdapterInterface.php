<?php

namespace App\Contracts;

use App\Models\FieldMappingConfiguration;

interface FieldMappingAdapterInterface
{
    /**
     * Transform form data according to the field mapping configuration
     *
     * @param array $formData The submitted form data
     * @param FieldMappingConfiguration $configuration The field mapping configuration
     * @return array The transformed data ready for target system submission
     */
    public function transformFormData(array $formData, FieldMappingConfiguration $configuration): array;

    /**
     * Validate the form data against the field mapping configuration
     *
     * @param array $formData The submitted form data
     * @param FieldMappingConfiguration $configuration The field mapping configuration
     * @return array Array of validation errors (empty if valid)
     */
    public function validateFormData(array $formData, FieldMappingConfiguration $configuration): array;

    /**
     * Get the target system this adapter supports
     *
     * @return string The target system name (e.g., 'CSI', 'SAP')
     */
    public function getSupportedTarget(): string;

    /**
     * Get supported process types for this target system
     *
     * @return array Array of supported process types
     */
    public function getSupportedProcesses(): array;

    /**
     * Prepare data for API submission
     *
     * @param array $transformedData The transformed form data
     * @param FieldMappingConfiguration $configuration The field mapping configuration
     * @return array The final data structure for API submission
     */
    public function prepareApiData(array $transformedData, FieldMappingConfiguration $configuration): array;

    /**
     * Get field mapping configuration schema
     * 
     * @return array The schema definition for the field mapping configuration
     */
    public function getFieldMappingSchema(): array;
}
