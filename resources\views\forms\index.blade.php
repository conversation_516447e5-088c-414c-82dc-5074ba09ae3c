@extends('layouts.app')

@section('title', 'Available Forms')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Available Forms</h3>
                    <div class="card-tools">
                        @auth
                            <a href="{{ route('forms.history') }}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-history"></i> My Submissions
                            </a>
                        @endauth
                    </div>
                </div>
                <div class="card-body">
                    @if($forms->isEmpty())
                        <div class="text-center py-5">
                            <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">No Forms Available</h4>
                            <p class="text-muted">There are currently no forms available for submission.</p>
                            @guest
                                <p class="text-muted">
                                    <a href="{{ route('login') }}">Log in</a> to access additional forms.
                                </p>
                            @endguest
                        </div>
                    @else
                        <div class="row">
                            @foreach($forms as $form)
                                <div class="col-md-6 col-lg-4 mb-4">
                                    <div class="card h-100 shadow-sm">
                                        <div class="card-body d-flex flex-column">
                                            <h5 class="card-title">
                                                <i class="fas fa-file-alt text-primary"></i>
                                                {{ $form->title }}
                                            </h5>
                                            <p class="card-text text-muted small flex-grow-1">
                                                Created: {{ $form->created_at->format('M d, Y') }}
                                            </p>
                                            <div class="mt-auto">
                                                <a href="{{ route('forms.show', $form->id) }}" 
                                                   class="btn btn-primary btn-block">
                                                    <i class="fas fa-edit"></i> Fill Out Form
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@if(session('success'))
    <div class="toast-container position-fixed top-0 end-0 p-3">
        <div class="toast show" role="alert">
            <div class="toast-header bg-success text-white">
                <i class="fas fa-check-circle me-2"></i>
                <strong class="me-auto">Success</strong>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">
                {{ session('success') }}
            </div>
        </div>
    </div>
@endif

@if(session('error'))
    <div class="toast-container position-fixed top-0 end-0 p-3">
        <div class="toast show" role="alert">
            <div class="toast-header bg-danger text-white">
                <i class="fas fa-exclamation-circle me-2"></i>
                <strong class="me-auto">Error</strong>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">
                {{ session('error') }}
            </div>
        </div>
    </div>
@endif
@endsection

@push('styles')
<style>
.card {
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

.toast-container {
    z-index: 1055;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-hide toasts after 5 seconds
    setTimeout(function() {
        var toasts = document.querySelectorAll('.toast');
        toasts.forEach(function(toast) {
            var bsToast = new bootstrap.Toast(toast);
            bsToast.hide();
        });
    }, 5000);
});
</script>
@endpush
