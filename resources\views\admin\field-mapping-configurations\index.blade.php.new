@extends('layouts.admin')
@section('pageTitle', __('cruds.fieldMappingConfiguration.title'))

@section('content')
    <div class="card card-default">
        <div class="card-header separator">
            <div class="card-title mainheading">
                <h4>{{ __('cruds.fieldMappingConfiguration.title') }} {{ trans('global.list') }}</h4>
            </div>
            <div class="card-controls">
                <ul>
                    @if (auth()->user()->isAdmin() || auth()->user()->isSuperAdmin())
                        <li>
                            <x-buttons.add :url="route('admin.field-mapping-configurations.create')" :text="__('cruds.fieldMappingConfiguration.title_singular')" />
                        </li>
                    @endif
                </ul>
            </div>
        </div>

        <div class="card-block">
            <x-datatable 
                id="field-mapping-configurations-table"
                :columns="$columns"
                ajax="{{ route('admin.field-mapping-configurations.index') }}"
                :order="[[0, 'desc']]"
            />
        </div>
    </div>
@endsection

@section('styles')
<style>
    .card {
        box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 3px rgba(0,0,0,.2);
        margin-bottom: 1rem;
        width: 100%;
    }
    .card-header h4 {
        margin: 0;
        color: #495057;
    }
    .table-responsive {
        overflow-x: auto;
    }
    .dataTables_wrapper .dataTables_paginate .paginate_button {
        padding: 0.25rem 0.5rem;
    }
    #field-mapping-configurations-table {
        width: 100% !important;
    }
    @media (max-width: 768px) {
        .table-responsive {
            border: none;
        }
    }
</style>
@endsection

@section('scripts')
    @parent
    <script>
        $(function() {
            let dtButtons = $.extend(true, [], $.fn.dataTable.defaults.buttons);
            
            let deleteButton = {
                text: '{{ trans('global.datatables.delete') }}',
                url: "{{ route('admin.field-mapping-configurations.massDestroy') }}",
                className: 'btn-danger',
                action: function(e, dt, node, config) {
                    var ids = $.map(dt.rows({ selected: true }).data(), function(entry) {
                        return entry.id;
                    });

                    if (ids.length === 0) {
                        alert('{{ trans('global.datatables.zero_selected') }}');
                        return;
                    }

                    if (confirm('{{ trans('global.areYouSure') }}')) {
                        $.ajax({
                            headers: {'x-csrf-token': _token},
                            method: 'POST',
                            url: config.url,
                            data: { ids: ids, _method: 'DELETE' }
                        })
                        .done(function() {
                            location.reload();
                        });
                    }
                }
            };

            dtButtons.push(deleteButton);

            let dtOverrideGlobals = {
                buttons: dtButtons,
                processing: true,
                serverSide: true,
                retrieve: true,
                aaSorting: [],
                ajax: "{{ route('admin.field-mapping-configurations.index') }}",
                columns: [
                    { data: 'placeholder', name: 'placeholder' },
                    { data: 'name', name: 'name' },
                    { data: 'form.title', name: 'form.title' },
                    { data: 'endpoint_configuration', name: 'integrationConfiguration.name' },
                    { data: 'external_system', name: 'integrationConfiguration.external_system' },
                    { data: 'integration_method', name: 'integrationConfiguration.integration_method' },
                    { data: 'process_type', name: 'integrationConfiguration.process_type' },
                    { data: 'status', name: 'status' },
                    { data: 'created_by', name: 'createdBy.name' },
                    { 
                        data: 'actions', 
                        name: 'actions', 
                        orderable: false, 
                        searchable: false,
                        className: 'text-center'
                    }
                ],
                orderCellsTop: true,
                order: [[ 1, 'desc' ]],
                pageLength: 25,
                responsive: true
            };

            let table = $('.datatable-FieldMappingConfiguration').DataTable(dtOverrideGlobals);
            $('a[data-toggle="tab"]').on('shown.bs.tab click', function(e) {
                $($.fn.dataTable.tables(true)).DataTable()
                    .columns.adjust();
            });
        });
    </script>
@endsection
