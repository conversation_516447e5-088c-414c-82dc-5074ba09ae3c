<div class="btn-group" role="group">
    <a class="btn btn-sm btn-primary" title="View" href="{{ route('admin.endpoint-configurations.show', $row->id) }}">
        <i class="fa fa-eye"></i>
    </a>
    <a class="btn btn-sm btn-info" title="Edit" href="{{ route('admin.endpoint-configurations.edit', $row->id) }}">
        <i class="fa fa-edit"></i>
    </a>
    <form class="d-inline" action="{{ route('admin.endpoint-configurations.destroy', $row->id) }}" method="POST">
        @csrf
        @method('DELETE')
        <button type="button" title="Delete" class="btn btn-sm btn-danger delete-confirm" data-id="{{ $row->id }}">
            <i class="fa fa-trash"></i>
        </button>
    </form>
</div>

@push('scripts')
@parent
<script>
    $(document).ready(function() {
        $('.delete-confirm').on('click', function(e) {
            e.preventDefault();
            const id = $(this).data('id');
            const form = $(this).closest('form');
            
            swal({
                title: "Are you sure?",
                text: "This endpoint configuration will be permanently deleted!",
                icon: "warning",
                buttons: ["Cancel", "Yes, delete it!"],
                dangerMode: true,
            }).then((willDelete) => {
                if (willDelete) {
                    form.submit();
                }
            });
        });
    });
</script>
@endpush
