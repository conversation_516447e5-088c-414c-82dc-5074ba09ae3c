<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class AuditLog extends Model
{
    use HasFactory;

    protected $table = 'audit_logs';

    protected $fillable = [
        'uuid',
        'form_submission_id',
        'form_submission_sync_id',
        'event_type',
        'event_description',
        'actor_type',
        'actor_id',
        'old_values',
        'new_values',
        'metadata',
        'ip_address',
        'user_agent',
    ];

    protected $casts = [
        'old_values' => 'array',
        'new_values' => 'array',
        'metadata' => 'array',
        'created_at' => 'datetime',
    ];

    // Disable updated_at since this is an audit log
    public $timestamps = false;
    protected $dates = ['created_at'];

    // Actor type constants
    const ACTOR_USER = 'user';
    const ACTOR_SYSTEM = 'system';
    const ACTOR_ADMIN = 'admin';

    // Event type constants
    const EVENT_SUBMISSION_CREATED = 'submission_created';
    const EVENT_SUBMISSION_VALIDATED = 'submission_validated';
    const EVENT_SYNC_STARTED = 'sync_started';
    const EVENT_SYNC_COMPLETED = 'sync_completed';
    const EVENT_SYNC_FAILED = 'sync_failed';
    const EVENT_SYNC_RETRIED = 'sync_retried';
    const EVENT_STATUS_UPDATED = 'status_updated';
    const EVENT_ERROR_RESOLVED = 'error_resolved';
    const EVENT_MANUAL_RETRY = 'manual_retry';
    const EVENT_DATA_EXPORTED = 'data_exported';

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->uuid)) {
                $model->uuid = (string) Str::uuid();
            }
            if (empty($model->created_at)) {
                $model->created_at = now();
            }
        });
    }

    /**
     * Get the form submission this audit log belongs to
     */
    public function formSubmission(): BelongsTo
    {
        return $this->belongsTo(FormSubmission::class);
    }

    /**
     * Get the form submission sync this audit log belongs to
     */
    public function formSubmissionSync(): BelongsTo
    {
        return $this->belongsTo(FormSubmissionSync::class);
    }

    /**
     * Get the actor (user) who performed the action
     */
    public function actor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'actor_id');
    }

    /**
     * Scope to filter by event type
     */
    public function scopeWithEventType($query, $eventType)
    {
        return $query->where('event_type', $eventType);
    }

    /**
     * Scope to filter by actor type
     */
    public function scopeWithActorType($query, $actorType)
    {
        return $query->where('actor_type', $actorType);
    }

    /**
     * Scope to filter by date range
     */
    public function scopeCreatedBetween($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Scope to get recent logs
     */
    public function scopeRecent($query, $hours = 24)
    {
        return $query->where('created_at', '>=', now()->subHours($hours));
    }

    /**
     * Get human-readable event type
     */
    public function getEventTypeLabel(): string
    {
        return match ($this->event_type) {
            self::EVENT_SUBMISSION_CREATED => 'Submission Created',
            self::EVENT_SUBMISSION_VALIDATED => 'Submission Validated',
            self::EVENT_SYNC_STARTED => 'Sync Started',
            self::EVENT_SYNC_COMPLETED => 'Sync Completed',
            self::EVENT_SYNC_FAILED => 'Sync Failed',
            self::EVENT_SYNC_RETRIED => 'Sync Retried',
            self::EVENT_STATUS_UPDATED => 'Status Updated',
            self::EVENT_ERROR_RESOLVED => 'Error Resolved',
            self::EVENT_MANUAL_RETRY => 'Manual Retry',
            self::EVENT_DATA_EXPORTED => 'Data Exported',
            default => ucwords(str_replace('_', ' ', $this->event_type)),
        };
    }

    /**
     * Get human-readable actor type
     */
    public function getActorTypeLabel(): string
    {
        return match ($this->actor_type) {
            self::ACTOR_USER => 'User',
            self::ACTOR_SYSTEM => 'System',
            self::ACTOR_ADMIN => 'Admin',
            default => 'Unknown',
        };
    }

    /**
     * Get actor name for display
     */
    public function getActorName(): string
    {
        if ($this->actor_type === self::ACTOR_SYSTEM) {
            return 'System';
        }

        if ($this->actor && $this->actor_id) {
            return $this->actor->name ?? $this->actor->email ?? "User #{$this->actor_id}";
        }

        return 'Unknown';
    }

    /**
     * Get event color for UI
     */
    public function getEventColor(): string
    {
        return match ($this->event_type) {
            self::EVENT_SUBMISSION_CREATED => 'primary',
            self::EVENT_SUBMISSION_VALIDATED => 'info',
            self::EVENT_SYNC_STARTED => 'info',
            self::EVENT_SYNC_COMPLETED => 'success',
            self::EVENT_SYNC_FAILED => 'danger',
            self::EVENT_SYNC_RETRIED => 'warning',
            self::EVENT_STATUS_UPDATED => 'info',
            self::EVENT_ERROR_RESOLVED => 'success',
            self::EVENT_MANUAL_RETRY => 'warning',
            self::EVENT_DATA_EXPORTED => 'secondary',
            default => 'secondary',
        };
    }

    /**
     * Check if this is a system-generated event
     */
    public function isSystemEvent(): bool
    {
        return $this->actor_type === self::ACTOR_SYSTEM;
    }

    /**
     * Check if this is a user-generated event
     */
    public function isUserEvent(): bool
    {
        return $this->actor_type === self::ACTOR_USER;
    }

    /**
     * Check if this is an admin-generated event
     */
    public function isAdminEvent(): bool
    {
        return $this->actor_type === self::ACTOR_ADMIN;
    }

    /**
     * Get formatted changes for display
     */
    public function getFormattedChanges(): array
    {
        $changes = [];

        if ($this->old_values && $this->new_values) {
            foreach ($this->new_values as $key => $newValue) {
                $oldValue = $this->old_values[$key] ?? null;
                if ($oldValue !== $newValue) {
                    $changes[$key] = [
                        'old' => $oldValue,
                        'new' => $newValue,
                    ];
                }
            }
        }

        return $changes;
    }

    /**
     * Static method to create audit log entry
     */
    public static function createLog(
        string $eventType,
        string $eventDescription,
        string $actorType = self::ACTOR_SYSTEM,
        ?int $actorId = null,
        ?int $formSubmissionId = null,
        ?int $formSubmissionSyncId = null,
        array $oldValues = null,
        array $newValues = null,
        array $metadata = null,
        string $ipAddress = null,
        string $userAgent = null
    ): self {
        return self::create([
            'event_type' => $eventType,
            'event_description' => $eventDescription,
            'actor_type' => $actorType,
            'actor_id' => $actorId,
            'form_submission_id' => $formSubmissionId,
            'form_submission_sync_id' => $formSubmissionSyncId,
            'old_values' => $oldValues,
            'new_values' => $newValues,
            'metadata' => $metadata,
            'ip_address' => $ipAddress,
            'user_agent' => $userAgent,
        ]);
    }
}
