<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps([
    'route' => '#',
    'text' => null,
    'size' => 'md',
    'class' => '',
    'icon' => 'plus',
    'title' => null,
    'target' => '_self',
    'iconOnly' => false,
]) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps([
    'route' => '#',
    'text' => null,
    'size' => 'md',
    'class' => '',
    'icon' => 'plus',
    'title' => null,
    'target' => '_self',
    'iconOnly' => false,
]); ?>
<?php foreach (array_filter(([
    'route' => '#',
    'text' => null,
    'size' => 'md',
    'class' => '',
    'icon' => 'plus',
    'title' => null,
    'target' => '_self',
    'iconOnly' => false,
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.buttons.button','data' => ['href' => $route,'variant' => 'primary','size' => $size,'icon' => $icon,'class' => $class,'title' => __('global.add') . ' ' . $title ?? '','target' => $target,'iconOnly' => $iconOnly,'dataBsToggle' => 'tooltip']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('buttons.button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($route),'variant' => 'primary','size' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($size),'icon' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($icon),'class' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($class),'title' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(__('global.add') . ' ' . $title ?? ''),'target' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($target),'iconOnly' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($iconOnly),'data-bs-toggle' => 'tooltip']); ?>
    <?php echo e($text ?? (!$iconOnly ? __('global.add') : '')); ?>

 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>
<?php /**PATH D:\Git Data Capture\application\resources\views/components/buttons/add.blade.php ENDPATH**/ ?>