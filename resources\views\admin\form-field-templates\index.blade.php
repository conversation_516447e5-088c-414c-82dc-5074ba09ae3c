@extends('layouts.admin')
@section('pageTitle', __('cruds.form_field_template.title'))

@section('content')
    <div class="card card-default">
        <div class="card-header separator">
            <div class="card-title mainheading">
                <h4>{{ __('cruds.form_field_template.title') }}</h4>
            </div>
            <div class="card-controls">
                <ul>
                    <li>
                        <x-add-button :url="route('admin.form-field-templates.create')"
                                      :text="__('cruds.form_field_template.title_singular')" />
                    </li>
                </ul>
            </div>
        </div>

        <div class="card-block">
            @php
                $columns = \App\Models\FormFieldTemplate::dataTableColumns();
            @endphp

            <x-datatable
                :columns="$columns"
                :ajax="route('admin.form-field-templates.index')"
                :order="[[3, 'desc']]"
            />
        </div>
    </div>
@endsection
