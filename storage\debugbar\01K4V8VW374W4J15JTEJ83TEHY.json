{"__meta": {"id": "01K4V8VW374W4J15JTEJ83TEHY", "datetime": "2025-09-11 02:14:55", "utime": **********.849754, "method": "GET", "uri": "/admin/integration-configurations/create", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 18, "start": 1757556894.926313, "end": **********.849803, "duration": 0.923490047454834, "duration_str": "923ms", "measures": [{"label": "Booting", "start": 1757556894.926313, "relative_start": 0, "end": **********.113158, "relative_end": **********.113158, "duration": 0.***************, "duration_str": "187ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.113168, "relative_start": 0.*****************, "end": **********.849808, "relative_end": 5.0067901611328125e-06, "duration": 0.****************, "duration_str": "737ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.136638, "relative_start": 0.*****************, "end": **********.140957, "relative_end": **********.140957, "duration": 0.004319190979003906, "duration_str": "4.32ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: admin.integration-configurations.create", "start": **********.188726, "relative_start": 0.*****************, "end": **********.188726, "relative_end": **********.188726, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.back", "start": **********.688011, "relative_start": 0.****************, "end": **********.688011, "relative_end": **********.688011, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.button", "start": **********.699706, "relative_start": 0.****************, "end": **********.699706, "relative_end": **********.699706, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.back", "start": **********.753222, "relative_start": 0.826909065246582, "end": **********.753222, "relative_end": **********.753222, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.button", "start": **********.756235, "relative_start": 0.8299219608306885, "end": **********.756235, "relative_end": **********.756235, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.cancel", "start": **********.760889, "relative_start": 0.8345761299133301, "end": **********.760889, "relative_end": **********.760889, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.button", "start": **********.784332, "relative_start": 0.8580191135406494, "end": **********.784332, "relative_end": **********.784332, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.save", "start": **********.786965, "relative_start": 0.860651969909668, "end": **********.786965, "relative_end": **********.786965, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.button", "start": **********.797981, "relative_start": 0.8716681003570557, "end": **********.797981, "relative_end": **********.797981, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.delete", "start": **********.811068, "relative_start": 0.8847551345825195, "end": **********.811068, "relative_end": **********.811068, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.button", "start": **********.828577, "relative_start": 0.9022641181945801, "end": **********.828577, "relative_end": **********.828577, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.admin", "start": **********.831902, "relative_start": 0.9055891036987305, "end": **********.831902, "relative_end": **********.831902, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: include.sidebar", "start": **********.834496, "relative_start": 0.9081830978393555, "end": **********.834496, "relative_end": **********.834496, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: include.header", "start": **********.838706, "relative_start": 0.9123930931091309, "end": **********.838706, "relative_end": **********.838706, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.alerts", "start": **********.840878, "relative_start": 0.9145650863647461, "end": **********.840878, "relative_end": **********.840878, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 23655096, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "9.x", "tooltip": {"Laravel Version": "9.52.20", "PHP Version": "8.1.15", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 15, "nb_templates": 15, "templates": [{"name": "admin.integration-configurations.create", "param_count": null, "params": [], "start": **********.188672, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/admin/integration-configurations/create.blade.phpadmin.integration-configurations.create", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fadmin%2Fintegration-configurations%2Fcreate.blade.php:1", "ajax": false, "filename": "create.blade.php", "line": "?"}}, {"name": "components.buttons.back", "param_count": null, "params": [], "start": **********.687877, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/back.blade.phpcomponents.buttons.back", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fback.blade.php:1", "ajax": false, "filename": "back.blade.php", "line": "?"}}, {"name": "components.buttons.button", "param_count": null, "params": [], "start": **********.699625, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/button.blade.phpcomponents.buttons.button", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fbutton.blade.php:1", "ajax": false, "filename": "button.blade.php", "line": "?"}}, {"name": "components.buttons.back", "param_count": null, "params": [], "start": **********.752886, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/back.blade.phpcomponents.buttons.back", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fback.blade.php:1", "ajax": false, "filename": "back.blade.php", "line": "?"}}, {"name": "components.buttons.button", "param_count": null, "params": [], "start": **********.755957, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/button.blade.phpcomponents.buttons.button", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fbutton.blade.php:1", "ajax": false, "filename": "button.blade.php", "line": "?"}}, {"name": "components.buttons.cancel", "param_count": null, "params": [], "start": **********.760712, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/cancel.blade.phpcomponents.buttons.cancel", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fcancel.blade.php:1", "ajax": false, "filename": "cancel.blade.php", "line": "?"}}, {"name": "components.buttons.button", "param_count": null, "params": [], "start": **********.784158, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/button.blade.phpcomponents.buttons.button", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fbutton.blade.php:1", "ajax": false, "filename": "button.blade.php", "line": "?"}}, {"name": "components.buttons.save", "param_count": null, "params": [], "start": **********.786789, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/save.blade.phpcomponents.buttons.save", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fsave.blade.php:1", "ajax": false, "filename": "save.blade.php", "line": "?"}}, {"name": "components.buttons.button", "param_count": null, "params": [], "start": **********.797802, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/button.blade.phpcomponents.buttons.button", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fbutton.blade.php:1", "ajax": false, "filename": "button.blade.php", "line": "?"}}, {"name": "components.buttons.delete", "param_count": null, "params": [], "start": **********.810894, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/delete.blade.phpcomponents.buttons.delete", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fdelete.blade.php:1", "ajax": false, "filename": "delete.blade.php", "line": "?"}}, {"name": "components.buttons.button", "param_count": null, "params": [], "start": **********.828375, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/button.blade.phpcomponents.buttons.button", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fbutton.blade.php:1", "ajax": false, "filename": "button.blade.php", "line": "?"}}, {"name": "layouts.admin", "param_count": null, "params": [], "start": **********.831729, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/layouts/admin.blade.phplayouts.admin", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Flayouts%2Fadmin.blade.php:1", "ajax": false, "filename": "admin.blade.php", "line": "?"}}, {"name": "include.sidebar", "param_count": null, "params": [], "start": **********.834328, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/include/sidebar.blade.phpinclude.sidebar", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Finclude%2Fsidebar.blade.php:1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}}, {"name": "include.header", "param_count": null, "params": [], "start": **********.838536, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/include/header.blade.phpinclude.header", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Finclude%2Fheader.blade.php:1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "partials.alerts", "param_count": null, "params": [], "start": **********.840711, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/partials/alerts.blade.phppartials.alerts", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fpartials%2Falerts.blade.php:1", "ajax": false, "filename": "alerts.blade.php", "line": "?"}}]}, "queries": {"count": 4, "nb_statements": 3, "nb_visible_statements": 4, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.029990000000000003, "accumulated_duration_str": "29.99ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 15, "namespace": "middleware", "name": "auth", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}], "start": **********.163447, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php:168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "dc_local", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.1709368, "duration": 0.00409, "duration_str": "4.09ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php:59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "dc_local", "explain": null, "start_percent": 0, "width_percent": 13.638}, {"sql": "select * from `field_definitions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "admin.integration-configurations.create", "file": "D:\\Git Data Capture\\application\\resources\\views/admin/integration-configurations/create.blade.php", "line": 272}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.7190752, "duration": 0.0238, "duration_str": "23.8ms", "memory": 0, "memory_str": null, "filename": "admin.integration-configurations.create:272", "source": {"index": 15, "namespace": "view", "name": "admin.integration-configurations.create", "file": "D:\\Git Data Capture\\application\\resources\\views/admin/integration-configurations/create.blade.php", "line": 272}, "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fadmin%2Fintegration-configurations%2Fcreate.blade.php:272", "ajax": false, "filename": "create.blade.php", "line": "272"}, "connection": "dc_local", "explain": null, "start_percent": 13.638, "width_percent": 79.36}, {"sql": "select * from `field_definitions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "admin.integration-configurations.create", "file": "D:\\Git Data Capture\\application\\resources\\views/admin/integration-configurations/create.blade.php", "line": 548}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.799917, "duration": 0.0021000000000000003, "duration_str": "2.1ms", "memory": 0, "memory_str": null, "filename": "admin.integration-configurations.create:548", "source": {"index": 15, "namespace": "view", "name": "admin.integration-configurations.create", "file": "D:\\Git Data Capture\\application\\resources\\views/admin/integration-configurations/create.blade.php", "line": 548}, "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fadmin%2Fintegration-configurations%2Fcreate.blade.php:548", "ajax": false, "filename": "create.blade.php", "line": "548"}, "connection": "dc_local", "explain": null, "start_percent": 92.998, "width_percent": 7.002}]}, "models": {"data": {"App\\Models\\FieldDefinition": {"retrieved": 28, "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fapp%2FModels%2FFieldDefinition.php:1", "ajax": false, "filename": "FieldDefinition.php", "line": "?"}}, "App\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fapp%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 29, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 29}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8001/admin/integration-configurations/create", "action_name": "admin.integration-configurations.create", "controller_action": "App\\Http\\Controllers\\Admin\\IntegrationConfigurationController@create", "uri": "GET admin/integration-configurations/create", "controller": "App\\Http\\Controllers\\Admin\\IntegrationConfigurationController@create<a href=\"vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fapp%2FHttp%2FControllers%2FAdmin%2FIntegrationConfigurationController.php:36\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "/admin", "file": "<a href=\"vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fapp%2FHttp%2FControllers%2FAdmin%2FIntegrationConfigurationController.php:36\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/IntegrationConfigurationController.php:36-50</a>", "middleware": "web, auth, user_type:admin", "duration": "926ms", "peak_memory": "24MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1484202534 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1484202534\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2125839279 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2125839279\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1210037900 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko/20100101 Firefox/142.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"63 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlllMjlzQUoyTVhEbXMrOUR2R0JWTWc9PSIsInZhbHVlIjoic3l0VFF5VHN3aFVoWmxTaWNQbTU0N21PVWpPT3dHRXN4Ukl0Q01GbGlqNm1GVm1RNjNvMGE1WEladWdZcHRiMVF4OXQ0eDdIdnFUZzkrelNyZG40ajlLL2E5bXJtejVCNEIxQ1U2MDE0dUwrYktVdGUyYUlOZ2lkVS9XZHNkUmQiLCJtYWMiOiJjNDNjY2I2OTkzMmExMDk5OTMwZjc0OWJmYTZhNThlNWQzZGJiZWRhNDYyY2ViODdjZDhiOTY4Nzg2NjcxZTMxIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ik9sbmViQjVUUHhkYWtmdFlVbnZqUUE9PSIsInZhbHVlIjoiY2RWQzJpZXVTNExNblJxbWpVZ3ZJWEp1ZGFzRDJqY3U0d29GbWRsUE9tTHpXVlhXc1BwOFdxYVZQK3BLN0NqOVM3ejlXRWFET254TFdKUGhLUWpVWWRpSHpremoxOTFnWW84SHkybEYwSVZUVkg3dWJaQkhxWlNBS2sweDI3MkMiLCJtYWMiOiI1YTA1YjNkZThmZWUxZTQyMWNiOWFkNjAxMzRlODA0MGRmYzI4NWNiNWU2NGIzZjY1N2Q3OGY3MmRhYzkwNDFmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=0, i</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1210037900\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1801107053 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4RbyFb2oU1pjc4UNp6CmU470XASLVRa4SNAgSVgT</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3qWAZMAIgZlFqvqcm0sdt0aBVP4ghvpst3qVJvce</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1801107053\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2070472292 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 11 Sep 2025 02:14:55 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2070472292\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1228323520 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4RbyFb2oU1pjc4UNp6CmU470XASLVRa4SNAgSVgT</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost:8001/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1757556567</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1228323520\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8001/admin/integration-configurations/create", "action_name": "admin.integration-configurations.create", "controller_action": "App\\Http\\Controllers\\Admin\\IntegrationConfigurationController@create"}, "badge": null}}