<?php

namespace App\Models;

// use App\Enums\QrDecoderMode;

use App\Traits\DataTableFilter;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class QrDecoderProfile extends BaseModel
{
    use HasFactory, DataTableFilter;

    protected $fillable = [
        'name',
        'process_type',
        'mode',
        'delimiter',
        'sample_qr',
        'vendor_num',
        'enabled'
    ];

    protected $casts = [
        'enabled' => 'boolean',
    ];
    public static $modes = [
        'delimiter' => 'Delimiter',
        'fixed-length' => 'Fixed Length',
        'gs1' => 'GS1'
    ];
    public static function dataTableColumns(): array
    {
        return [
            [
                'data' => 'DT_RowIndex',
                'name' => 'DT_RowIndex',
                'title' => __('#'),
                'filter' => false,
                'searchable' => false,
            ],
            [
                'data' => 'name',
                'name' => 'name',
                'title' => __('cruds.fields.profile_name'),
                'filter' => 'text'
            ],
            [
                'data' => 'process_type',
                'name' => 'process_type',
                'title' => __('cruds.fields.process_type'),
                'filter' => 'text'
            ],
            // [
            //     'data' => 'device_view',
            //     'name' => 'device_view',
            //     'title' => __('Device View'),
            //     'filter' => 'select',
            //     'options' => ['Mobile', 'Tablet', 'Desktop']
            // ],
            [
                'data' => 'mode',
                'name' => 'mode',
                'title' => __('cruds.fields.mode'),
                'filter' => 'text'
            ],
            // ✅ relationship filter

            [
                'data' => 'vendor_num',
                'name' => 'vendor_num',
                'title' =>  __('cruds.fields.vendor_num'),
                'filter' => 'text',
                // 'options' => ['draft' => 'Draft', 'published' => 'Published']
            ],
            [
                'data' => 'updater.name',
                'name' => 'updater.name',
                'title' =>  __('cruds.fields.updated_by'),
                'filter' => 'relation',
                'filter_callback' => function ($query, $value) {
                    return $query->whereHas('updater', function ($q) use ($value) {
                        $q->whereRaw("LOWER(name) like ?", ['%' . strtolower($value) . '%']);
                    });
                },
            ],
            [
                'data' => 'updated_at',
                'name' => 'updated_at',
                'title' =>  __('cruds.fields.updated_at'),
                'filter' => 'date_range',
                'className' => 'text-right',
            ],
            [
                'data' => 'action',
                'name' => 'action',
                'title' => __('global.actions'),
                'orderable' => false,
                'searchable' => false,
                'filter' => false,
            ],
        ];
    }
    public function fields()
    {
        return $this->hasMany(QrDecoderField::class, 'profile_id')->orderBy('order');
    }
}
