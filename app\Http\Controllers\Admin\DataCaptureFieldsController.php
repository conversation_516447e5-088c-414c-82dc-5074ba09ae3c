<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreDataCaptureFieldRequest;
use App\Http\Requests\UpdateDataCaptureFieldRequest;
use App\Models\DataCaptureField;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Log;

class DataCaptureFieldsController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $query = DataCaptureField::orderBy('created_at', 'desc');
            $query = $query->applyDataTableFilters($request);

            return DataTables::of($query)
                ->addIndexColumn()
                ->addColumn('action', function ($row) {
                    $actions = [];
                    // $actions['show'] = route('admin.data-capture-fields.show', $row->id);
                    $actions['edit'] = route('superadmin.data-capture-fields.edit', $row->id);
                    $actions['delete'] = route('superadmin.data-capture-fields.destroy', $row->id);

                    $html = view('partials.datatablesActions', compact('actions', 'row'))->render();
                    return $html;
                })
                ->addColumn('process_type', function ($row) {
                    return $row->process_type;
                })
                ->rawColumns(['action'])
                ->make(true);
        }

        return view('admin.data-capture-fields.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('admin.data-capture-fields.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \App\Http\Requests\StoreDataCaptureFieldRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreDataCaptureFieldRequest $request)
    {
        $inputs = $request->validated();
        // $inputs['tenant_id'] = 1;
        DataCaptureField::create($inputs);

        return redirect()->route('superadmin.data-capture-fields.index')
            ->with('success', 'Data Capture Field created successfully.');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\DataCaptureField  $dataCaptureField
     * @return \Illuminate\Http\Response
     */
    public function show(DataCaptureField $dataCaptureField)
    {
        return view('admin.data-capture-fields.show', compact('dataCaptureField'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\DataCaptureField  $dataCaptureField
     * @return \Illuminate\Http\Response
     */
    public function edit(DataCaptureField $dataCaptureField)
    {
        return view('admin.data-capture-fields.edit', compact('dataCaptureField'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \App\Http\Requests\UpdateDataCaptureFieldRequest  $request
     * @param  \App\Models\DataCaptureField  $dataCaptureField
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateDataCaptureFieldRequest $request, DataCaptureField $dataCaptureField)
    {
        $validatedData = $request->validated();

        // Debug: Log the validated data
        Log::info('Update Data Capture Field - Validated Data:', $validatedData);

        $dataCaptureField->update($validatedData);

        return redirect()->route('superadmin.data-capture-fields.index')
            ->with('success', 'Data Capture Field updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\DataCaptureField  $dataCaptureField
     * @return \Illuminate\Http\Response
     */
    public function destroy(DataCaptureField $dataCaptureField)
    {
        $dataCaptureField->delete();

        return redirect()->route('superadmin.data-capture-fields.index')
            ->with('success', 'Data Capture Field deleted successfully.');
    }

    /**
     * Mass destroy data capture fields.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function massDestroy(Request $request)
    {
        DataCaptureField::whereIn('id', $request->ids)->delete();
        return response(null, Response::HTTP_NO_CONTENT);
    }
}
