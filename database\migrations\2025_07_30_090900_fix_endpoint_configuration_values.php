<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use App\Models\EndpointConfiguration;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Map old values to new constant values
        $mappings = [
            'target_type' => [
                'CSI' => EndpointConfiguration::TARGET_CSI,
                'SAP' => EndpointConfiguration::TARGET_SAP,
                'Infor WMS' => EndpointConfiguration::TARGET_INFOR_WMS,
            ],
            'process_selection' => [
                'Misc Issue' => EndpointConfiguration::PROCESS_MISC_ISSUE,
                'Misc Receipt' => EndpointConfiguration::PROCESS_MISC_RECEIPT,
                'Quantity Move' => EndpointConfiguration::PROCESS_QUANTITY_MOVE,
                'PO Receipt' => EndpointConfiguration::PROCESS_PO_RECEIPT,
            ],
            'endpoint_type' => [
                'API' => EndpointConfiguration::TYPE_API,
                'Stored Procedure' => EndpointConfiguration::TYPE_STORED_PROCEDURE,
            ]
        ];

        // Update existing records with new constant values
        foreach ($mappings['target_type'] as $oldValue => $newValue) {
            DB::table('endpoint_configurations')
                ->where('target_type', $oldValue)
                ->update(['target_type' => $newValue]);
        }

        foreach ($mappings['process_selection'] as $oldValue => $newValue) {
            DB::table('endpoint_configurations')
                ->where('process_selection', $oldValue)
                ->update(['process_selection' => $newValue]);
        }

        foreach ($mappings['endpoint_type'] as $oldValue => $newValue) {
            DB::table('endpoint_configurations')
                ->where('endpoint_type', $oldValue)
                ->update(['endpoint_type' => $newValue]);
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Note: This is a one-way migration
        // Reverting would require mapping back to the old values
        // which might not be possible if new values were added
        throw new \RuntimeException('Cannot reverse this migration. Please restore from backup if needed.');
    }
};
