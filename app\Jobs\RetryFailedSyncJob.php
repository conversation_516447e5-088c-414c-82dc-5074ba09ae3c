<?php

namespace App\Jobs;

use App\Models\FormSubmissionSync;
use App\Services\FormSyncEngineService;
use App\Services\ErrorLoggerService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class RetryFailedSyncJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected FormSubmissionSync $sync;

    /**
     * The number of times the job may be attempted.
     */
    public int $tries = 1; // We handle retries manually through the sync engine

    /**
     * The maximum number of seconds the job can run.
     */
    public int $timeout = 60;

    /**
     * Create a new job instance.
     */
    public function __construct(FormSubmissionSync $sync)
    {
        $this->sync = $sync;
        $this->onQueue('form_submissions');
    }

    /**
     * Execute the job.
     */
    public function handle(FormSyncEngineService $syncEngine): void
    {
        // Refresh the sync to get the latest status
        $this->sync->refresh();

        // Check if the sync is still eligible for retry
        if (!$this->sync->canRetry()) {
            Log::info('Sync retry job skipped - sync cannot be retried', [
                'sync_uuid' => $this->sync->uuid,
                'status' => $this->sync->status,
                'retry_count' => $this->sync->retry_count,
                'max_retries' => $this->sync->max_retries,
            ]);
            return;
        }

        // Check if it's time to retry
        if ($this->sync->next_retry_at && $this->sync->next_retry_at->isFuture()) {
            Log::info('Sync retry job skipped - not yet time to retry', [
                'sync_uuid' => $this->sync->uuid,
                'next_retry_at' => $this->sync->next_retry_at,
                'current_time' => now(),
            ]);

            // Re-schedule the job for the correct time
            self::dispatch($this->sync)->delay($this->sync->next_retry_at);
            return;
        }

        Log::info('Retry failed sync job started', [
            'sync_uuid' => $this->sync->uuid,
            'submission_uuid' => $this->sync->formSubmission->uuid,
            'integration_name' => $this->sync->getIntegrationName(),
            'retry_count' => $this->sync->retry_count,
            'job_id' => $this->job->getJobId(),
        ]);

        try {
            $syncEngine->retrySync($this->sync);

            Log::info('Retry failed sync job completed', [
                'sync_uuid' => $this->sync->uuid,
                'submission_uuid' => $this->sync->formSubmission->uuid,
                'integration_name' => $this->sync->getIntegrationName(),
                'final_status' => $this->sync->fresh()->status,
                'job_id' => $this->job->getJobId(),
            ]);

        } catch (\Exception $e) {
            Log::error('Retry failed sync job failed', [
                'sync_uuid' => $this->sync->uuid,
                'submission_uuid' => $this->sync->formSubmission->uuid,
                'integration_name' => $this->sync->getIntegrationName(),
                'retry_count' => $this->sync->retry_count,
                'error' => $e->getMessage(),
                'job_id' => $this->job->getJobId(),
            ]);

            // The sync engine handles error logging and retry logic
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Retry failed sync job failed permanently', [
            'sync_uuid' => $this->sync->uuid,
            'submission_uuid' => $this->sync->formSubmission->uuid,
            'integration_name' => $this->sync->getIntegrationName(),
            'retry_count' => $this->sync->retry_count,
            'error' => $exception->getMessage(),
        ]);

        // Mark sync as failed if it's not already handled
        $currentSync = $this->sync->fresh();
        if ($currentSync->status === \App\Models\FormSubmissionSync::STATUS_RETRYING) {
            $currentSync->markAsFailed(
                'Retry job failed: ' . $exception->getMessage(),
                (string) $exception->getCode()
            );
        }

        // Log the permanent failure
        app(ErrorLoggerService::class)->logSyncError(
            $this->sync,
            $exception,
            \App\Models\ErrorLog::LEVEL_CRITICAL,
            \App\Models\ErrorLog::TYPE_QUEUE_ERROR,
            [
                'permanent_failure' => true,
                'retry_job_failure' => true,
                'retry_count' => $this->sync->retry_count,
            ]
        );
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        return [
            'form_sync_retry',
            'sync:' . $this->sync->uuid,
            'submission:' . $this->sync->formSubmission->uuid,
            'integration:' . $this->sync->form_integration_setting_id,
            'retry:' . $this->sync->retry_count,
        ];
    }
}
