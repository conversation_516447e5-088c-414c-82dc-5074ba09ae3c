<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        // 1. Rename columns if they exist (from 113600_update_field_definitions_structure.php)
        if (Schema::hasColumn('field_definitions', 'name') && !Schema::hasColumn('field_definitions', 'field_key')) {
            DB::statement('ALTER TABLE field_definitions CHANGE name field_key VARCHAR(255)');
        }
        
        if (Schema::hasColumn('field_definitions', 'type') && !Schema::hasColumn('field_definitions', 'data_type')) {
            DB::statement('ALTER TABLE field_definitions CHANGE type data_type VARCHAR(255)');
        }

        // 2. Add new columns if they don't exist (from 113600_update_field_definitions_structure.php)
        if (!Schema::hasColumn('field_definitions', 'field_name')) {
            DB::statement('ALTER TABLE field_definitions ADD COLUMN field_name VARCHAR(255) AFTER field_key');
        }

        if (!Schema::hasColumn('field_definitions', 'max_length')) {
            DB::statement('ALTER TABLE field_definitions ADD COLUMN max_length INT NULL AFTER data_type');
        }

        if (!Schema::hasColumn('field_definitions', 'default_value')) {
            DB::statement('ALTER TABLE field_definitions ADD COLUMN default_value TEXT NULL AFTER max_length');
        }

        if (!Schema::hasColumn('field_definitions', 'field_group')) {
            DB::statement('ALTER TABLE field_definitions ADD COLUMN field_group VARCHAR(255) NULL AFTER description');
        }

        if (!Schema::hasColumn('field_definitions', 'metadata')) {
            DB::statement('ALTER TABLE field_definitions ADD COLUMN metadata JSON NULL AFTER field_group');
        }

        // 3. Update existing columns to be non-nullable with defaults (from 113600_update_field_definitions_structure.php)
        DB::statement('ALTER TABLE field_definitions MODIFY COLUMN field_key VARCHAR(255) NOT NULL');
        DB::statement("ALTER TABLE field_definitions MODIFY COLUMN data_type VARCHAR(50) NOT NULL DEFAULT 'string'");
        DB::statement('ALTER TABLE field_definitions MODIFY COLUMN is_required TINYINT(1) NOT NULL DEFAULT 0');
        DB::statement('ALTER TABLE field_definitions MODIFY COLUMN is_active TINYINT(1) NOT NULL DEFAULT 1');
        DB::statement('ALTER TABLE field_definitions MODIFY COLUMN sort_order INT NOT NULL DEFAULT 0');

        // 4. Add indexes if they don't exist (from 114100_finalize_field_definitions_structure.php)
        $indexes = DB::select("SHOW INDEX FROM field_definitions");
        $indexNames = array_column($indexes, 'Key_name');
        
        if (!in_array('field_definitions_field_key_unique', $indexNames)) {
            DB::statement('ALTER TABLE field_definitions ADD UNIQUE field_definitions_field_key_unique (field_key)');
        }

        if (!in_array('field_definitions_tenant_id_index', $indexNames)) {
            DB::statement('ALTER TABLE field_definitions ADD INDEX field_definitions_tenant_id_index (tenant_id)');
        }

        // 5. Fix validation_rules column type if needed (from 112400_fix_validation_rules_column_type.php)
        if (Schema::hasColumn('field_definitions', 'validation_rules')) {
            if (DB::getDriverName() === 'mysql') {
                DB::statement("ALTER TABLE field_definitions MODIFY COLUMN validation_rules TEXT NULL");
            } else {
                DB::statement('ALTER TABLE field_definitions ALTER COLUMN validation_rules TYPE TEXT');
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        // We won't implement the down method to prevent data loss
        // If needed, these changes should be reverted manually
    }
};
