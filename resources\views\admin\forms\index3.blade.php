@extends('layouts.admin')
@section('pageTitle', trans('cruds.form.title'))

@section('content')

    <div class="card card-default">
        <div class="card-header separator">
            <div class="card-title mainheading">
                <h4>{{ trans('cruds.form.title') }} {{ trans('global.list') }}</h4>
            </div>
            <div class="card-controls">
                <ul>
                    @if (auth()->user()->isAdmin() || auth()->user()->isSuperAdmin())
                        <li>

                            <x-add-button :url="route('admin.forms.create')" :text="__('cruds.form.title_singular')" />

                        </li>
                    @endif
                </ul>
            </div>
        </div>

        <div class="card-block">
            <?php
            // Define columns dynamically here
            $columns = [
                ['data' => 'DT_RowIndex', 'name' => 'DT_RowIndex', 'title' => '#', 'orderable' => false, 'searchable' => false, 'filter' => false, 'width' => '10'],
                ['data' => 'title', 'name' => 'title', 'title' => trans('cruds.fields.title'), 'filter' => 'text'],
                ['data' => 'device_view', 'name' => 'device_view', 'title' => trans('cruds.fields.device_view'), 'filter' => 'select', 'options' => ['Mobile', 'Tablet', 'Desktop']],
                ['data' => 'module', 'name' => 'module', 'title' => trans('cruds.fields.module'), 'filter' => 'text'],
                ['data' => 'redirect_to', 'name' => 'redirectToForm.title', 'title' => trans('cruds.fields.redirect_to'), 'orderable' => false, 'searchable' => false, 'filter' => false],
                ['data' => 'status', 'name' => 'is_active', 'title' => trans('cruds.fields.status'), 'filter' => 'select', 'options' => ['Active', 'Inactive']],
                ['data' => 'creator_name', 'name' => 'creator.name', 'title' => trans('cruds.fields.created_by'), 'filter' => 'text'],
                ['data' => 'created_at', 'name' => 'created_at', 'title' => trans('cruds.fields.created_at'), 'class' => 'text-right', 'filter' => 'text'],
                ['data' => 'action', 'name' => 'action', 'title' => trans('global.actions'), 'orderable' => false, 'searchable' => false, 'filter' => false, 'width' => '150'],
            ];
            ?>
            <div class="table-responsive">
                <table class="table compact table-bordered table-striped table-hover datatable" id="forms-table">
                    <thead>
                        <tr>
                            @foreach ($columns as $col)
                                <th class="{{ $col['class'] ?? '' }}" width="{{ $col['width'] ?? '' }}"
                                    @if ($col['filter'] === false) rowspan="2" @endif>
                                    {{ $col['title'] }}
                                </th>
                            @endforeach
                        </tr>
                        <tr>
                            @foreach ($columns as $col)
                                @if ($col['filter'] !== false)
                                    <th data-column="{{ $loop->index }}">
                                        @if ($col['filter'] === 'text')
                                            <input type="text" placeholder="Search {{ $col['title'] }}"
                                                class="form-control form-control-sm column-search" />
                                        @elseif($col['filter'] === 'select' && isset($col['options']))
                                            <select class="form-control form-control-sm column-search">
                                                <option value="">All</option>
                                                @foreach ($col['options'] as $opt)
                                                    <option value="{{ $opt }}">{{ $opt }}</option>
                                                @endforeach
                                            </select>
                                        @endif
                                    </th>
                                @endif
                            @endforeach
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    @parent
    <script>
        $(function() {
            var columns = @json($columns);

            var table = $('#forms-table').DataTable({
                processing: true,
                serverSide: true,
                ajax: "{{ route('admin.forms.index') }}",
                columns: columns,
                order: [
                    [7, 'desc']
                ],
                pageLength: 25,
                responsive: true,
                dom: 'Btip',
                orderCellsTop: true,
                buttons: buttonsDefault
            });
            setTimeout(() => {
                table.buttons().container().prependTo('.card-controls');
                // console.log(table.buttons().container());
            }, 50);
            // Text & dropdown filter logic
            $('#forms-table thead').on('keyup change', '.column-search', function() {
                let colIndex = $(this).closest('th').data('column');
                table.column(colIndex).search(this.value).draw();
            });
        });
    </script>
@endsection
