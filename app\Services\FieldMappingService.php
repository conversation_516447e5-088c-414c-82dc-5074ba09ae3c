<?php

namespace App\Services;

use App\Contracts\FieldMappingAdapterInterface;
use App\Models\FieldMappingConfiguration;
use App\Models\Form;
use App\Models\IntegrationConfiguration;
use App\Models\IntegrationEndpoint;
use Illuminate\Support\Facades\Log;

class FieldMappingService
{
    private array $adapters = [];

    public function __construct()
    {
        $this->registerAdapters();
    }
    
    /**
     * Clean JSON string by fixing common issues
     */
    protected function cleanJsonString(string $json): string
    {
        // Remove any trailing commas before closing brackets/braces
        $json = preg_replace('/,\s*([}\]])/m', '$1', $json);
        
        // Fix unescaped control characters
        $json = str_replace(["\n", "\r"], ['\\n', '\\r'], $json);
        
        // Fix single quotes to double quotes
        $json = preg_replace(
            "/'([^']+)'\s*:/", 
            '"$1":', 
            $json
        );
        
        // Fix unquoted property names
        $json = preg_replace(
            '/([\{\[]\s*)([a-zA-Z0-9_]+)(\s*:)/', 
            '$1"$2"$3', 
            $json
        );
        
        // Fix trailing commas
        $json = preg_replace('/,(\s*[}\]])/', '$1', $json);
        
        return $json;
    }
    
    /**
     * Make a human-readable label from a key
     */
    protected function makeLabel(string $key): string
    {
        return ucwords(str_replace(['_', '-', '.'], ' ', $key));
    }
    
    /**
     * Determine field type based on value
     */
    protected function determineFieldType($value): string
    {
        if (is_bool($value)) return 'checkbox';
        if (is_numeric($value)) return 'number';
        if (is_string($value) && strtotime($value) !== false) return 'datetime';
        if (is_string($value)) return 'text';
        if (is_array($value)) return 'array';
        
        return 'text';
    }
    
    /**
     * Extract fields from a flat structure (non-nested)
     */
    protected function extractFieldsFromFlatStructure(array $data, array &$fields, string $prefix = ''): void
    {
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                // If the value is an array, recurse into it
                $this->extractFieldsFromFlatStructure($value, $fields, $prefix . $key . '.');
            } elseif (is_string($key) && !empty($key)) {
                // Skip numeric keys and empty keys
                if (is_numeric($key)) continue;
                
                // Skip common metadata fields
                if (in_array($key, ['_id', '_rev', 'created_at', 'updated_at', 'deleted_at'])) continue;
                
                // Add the field with a cleaned key
                $cleanKey = rtrim($prefix . $key, '.');
                $fields[$cleanKey] = [
                    'key' => $cleanKey,
                    'label' => $this->makeLabel($key),
                    'type' => $this->determineFieldType($value)
                ];
            }
        }
    }

    /**
     * Register all available adapters
     */
    private function registerAdapters(): void
    {
        // Register adapters here
        // Example: $this->adapters['SYSTEM_NAME'] = new SystemNameAdapter();
    }

    /**
     * Get adapter for specific target system
     *
     * @param string $targetSystem
     * @return FieldMappingAdapterInterface
     * @throws \InvalidArgumentException
     */
    public function getAdapter(string $targetSystem): FieldMappingAdapterInterface
    {
        if (!isset($this->adapters[$targetSystem])) {
            throw new \InvalidArgumentException("No adapter found for target system: {$targetSystem}");
        }

        return $this->adapters[$targetSystem];
    }

    /**
     * Get all available target systems
     *
     * @return array
     */
    public function getAvailableTargets(): array
    {
        return array_keys($this->adapters);
    }

    /**
     * Process form submission through field mapping configuration
     *
     * @param array $formData
     * @param FieldMappingConfiguration $configuration
     * @return array
     */
    public function processFormSubmission(array $formData, FieldMappingConfiguration $configuration): array
    {
        try {
            $targetSystem = $configuration->integrationConfiguration->external_system;
            $adapter = $this->getAdapter($targetSystem);

            // Validate form data
            $validationErrors = $adapter->validateFormData($formData, $configuration);
            if (!empty($validationErrors)) {
                return [
                    'success' => false,
                    'errors' => $validationErrors,
                    'data' => null
                ];
            }

            // Transform form data
            $transformedData = $adapter->transformFormData($formData, $configuration);

            // Prepare API data
            $apiData = $adapter->prepareApiData($transformedData, $configuration);

            Log::info('Field mapping configuration processing completed', [
                'configuration_id' => $configuration->id,
                'target_system' => $targetSystem,
                'form_id' => $configuration->form_id,
                'integration_id' => $configuration->integration_configuration_id
            ]);

            return [
                'success' => true,
                'errors' => [],
                'data' => $apiData
            ];

        } catch (\Exception $e) {
            Log::error('Field mapping configuration processing failed', [
                'configuration_id' => $configuration->id ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'errors' => ['An error occurred while processing the form submission: ' . $e->getMessage()],
                'data' => null
            ];
        }
    }

    /**
     * Get form fields for field mapping
     *
     * @param Form $form
     * @return array
     */
    public function getFormFields(Form $form): array
    {
        try {
            // Parse form fields from form structure
            $fields = [];
            
            // Check if form_structure is empty or null
            if (empty($form->content)) {
                Log::warning('Form structure is empty', ['form_id' => $form->id]);
                return [];
            }
            
            // Try to decode the form structure
            $formStructure = json_decode($form->content, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                // If JSON is invalid, try to fix common issues
                $cleanedJson = $this->cleanJsonString($form->content);
                $formStructure = json_decode($cleanedJson, true);
                
                if (json_last_error() !== JSON_ERROR_NONE) {
                    throw new \RuntimeException('Invalid JSON in form structure: ' . json_last_error_msg());
                }
            }
            
            if (!$formStructure || !is_array($formStructure)) {
                throw new \RuntimeException('Form structure is empty or invalid');
            }
            
            Log::debug('Processing form structure', [
                'form_id' => $form->id,
                'form_title' => $form->title,
                'structure_type' => gettype($formStructure),
                'structure_keys' => array_keys($formStructure),
                'structure_sample' => array_slice($formStructure, 0, 3, true) // First 3 elements for debugging
            ]);
            
            // Check for different possible structures
            $foundComponents = false;
            
            // Check for direct components
            if (isset($formStructure['components']) && is_array($formStructure['components'])) {
                $this->extractFieldsFromComponents($formStructure['components'], $fields);
                $foundComponents = true;
            } 
            // Check for form.io structure
            elseif (isset($formStructure['components']) && is_array($formStructure['components'])) {
                $this->extractFieldsFromComponents($formStructure['components'], $fields);
                $foundComponents = true;
            }
            // Handle case where components are at the root level
            elseif (isset($formStructure[0]) && is_array($formStructure[0])) {
                $this->extractFieldsFromComponents($formStructure, $fields);
                $foundComponents = true;
            }
            // Try to find components in other possible locations
            else {
                $possibleKeys = ['components', 'data', 'form', 'pages', 'sections', 'schema', 'form'];
                
                foreach ($possibleKeys as $key) {
                    if (isset($formStructure[$key]) && is_array($formStructure[$key])) {
                        $components = is_array($formStructure[$key][0] ?? null) ? $formStructure[$key] : [$formStructure[$key]];
                        $this->extractFieldsFromComponents($components, $fields);
                        $foundComponents = true;
                        break;
                    }
                }
            }
            
            if (!$foundComponents) {
                // As a last resort, try to process the entire structure
                Log::warning('No standard component structure found, attempting to process entire structure', [
                    'form_id' => $form->id,
                    'available_keys' => array_keys($formStructure)
                ]);
                $this->extractFieldsFromComponents([$formStructure], $fields);
            }
            
            // If still no fields, try to extract fields directly from the structure
            if (empty($fields)) {
                $this->extractFieldsFromFlatStructure($formStructure, $fields);
            }
            
            Log::debug('Extracted form fields', [
                'form_id' => $form->id,
                'form_title' => $form->title,
                'field_count' => count($fields),
                'field_keys' => array_keys($fields)
            ]);
            
            return $fields;
            
        } catch (\Exception $e) {
            Log::error('Failed to extract form fields', [
                'form_id' => $form->id,
                'form_title' => $form->title ?? 'N/A',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'form_structure_sample' => substr($form->form_structure ?? '', 0, 200) . '...' // First 200 chars for debugging
            ]);
            
            // Return a helpful error message in development
            if (config('app.debug')) {
                return [
                    '_error' => [
                        'message' => 'Failed to extract form fields: ' . $e->getMessage(),
                        'form_id' => $form->id,
                        'form_title' => $form->title ?? 'N/A',
                        'exception' => get_class($e)
                    ]
                ];
            }
            
            return [];
        }
    }

    /**
     * Add a field to the fields array if it's a valid input field
     */
    private function addFieldIfValid(array $component, array &$fields, string $fullKey): void
    {
        $inputTypes = [
            'textfield', 'textarea', 'number', 'password', 'checkbox', 
            'select', 'radio', 'selectboxes', 'email', 'phoneNumber', 
            'address', 'datetime', 'date', 'time', 'hidden', 'file',
            'button', 'submit', 'signature', 'currency', 'phoneNumber',
            'editgrid', 'panel', 'datagrid', 'datamap', 'container'
        ];
        
        $isInputField = !empty($component['key']) && 
                       (empty($component['type']) || in_array($component['type'], $inputTypes));
        
        if ($isInputField) {
            $fieldKey = trim($fullKey, '.');
            if (!empty($fieldKey)) {
                $fields[$fieldKey] = [
                    'label' => $component['label'] ?? $component['key'] ?? 'Untitled',
                    'type' => $component['type'] ?? 'text',
                    'required' => isset($component['validate']['required']) ? (bool)$component['validate']['required'] : false,
                    'placeholder' => $component['placeholder'] ?? '',
                    'description' => $component['description'] ?? ''
                ];
                
                // Handle select/radio/checkbox options
                if (in_array($component['type'] ?? '', ['select', 'radio', 'selectboxes', 'checkbox'])) {
                    $fields[$fieldKey]['options'] = [];
                    if (isset($component['data']['values']) && is_array($component['data']['values'])) {
                        foreach ($component['data']['values'] as $option) {
                            if (isset($option['value'], $option['label'])) {
                                $fields[$fieldKey]['options'][$option['value']] = $option['label'];
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * Recursively extract fields from form components
     *
     * @param array $components
     * @param array $fields
     * @param string $parentKey
     * @return void
     */
    private function extractFieldsFromComponents(array $components, array &$fields, string $parentKey = ''): void
    {
        if (!is_array($components)) {
            return;
        }

        foreach ($components as $component) {
            if (!is_array($component)) {
                continue;
            }

            $key = $component['key'] ?? '';
            $fullKey = $parentKey ? $parentKey . '.' . $key : $key;
            $componentType = $component['type'] ?? '';
            
            // Handle nested components recursively
            if (isset($component['components']) && is_array($component['components'])) {
                $this->extractFieldsFromComponents($component['components'], $fields, $fullKey);
            } 
            
            // Handle columns layout
            if (isset($component['columns']) && is_array($component['columns'])) {
                foreach ($component['columns'] as $column) {
                    if (isset($column['components']) && is_array($column['components'])) {
                        $this->extractFieldsFromComponents($column['components'], $fields, $fullKey);
                    }
                }
            }
            
            // Handle tabs
            if (isset($component['tabs']) && is_array($component['tabs'])) {
                foreach ($component['tabs'] as $tab) {
                    if (isset($tab['components']) && is_array($tab['components'])) {
                        $this->extractFieldsFromComponents($tab['components'], $fields, $fullKey);
                    }
                }
            }
            
            // Handle panels and containers
            if (in_array($componentType, ['panel', 'container']) && isset($component['components']) && is_array($component['components'])) {
                $this->extractFieldsFromComponents($component['components'], $fields, $fullKey);
            }
            
            // Handle edit grid components
            if ($componentType === 'editgrid' && isset($component['components']) && is_array($component['components'])) {
                // Add the edit grid itself as a field
                $this->addFieldIfValid($component, $fields, $fullKey);
                
                // Process the components inside the edit grid with array notation
                $gridComponents = $component['components'];
                foreach ($gridComponents as $gridComponent) {
                    $gridComponentKey = $fullKey . '.[].' . ($gridComponent['key'] ?? '');
                    $this->addFieldIfValid($gridComponent, $fields, $gridComponentKey);
                }
            }
            
            // Add the field itself if it has a key and is an input component
            $this->addFieldIfValid($component, $fields, $fullKey);
        }
    }

    /**
     * Get integration fields for field mapping
     *
     * @param IntegrationConfiguration $integration
     * @return array
     */
    public function getIntegrationFields(IntegrationConfiguration $integration): array
    {
        try {
            $fields = [];
            
            // First check request_fields attribute (cast to array)
            if (!empty($integration->request_fields) && is_array($integration->request_fields)) {
                $fields = $integration->request_fields;
            }
            
            // Fallback to configuration array if no fields found in request_fields
            if (empty($fields)) {
                $config = $integration->configuration ?? [];
                if (isset($config['fields']) && is_array($config['fields'])) {
                    $fields = $config['fields'];
                }
            }
            
            return $fields;
        } catch (\Exception $e) {
            Log::error('Failed to get integration fields', [
                'integration_id' => $integration->id,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Get endpoint fields for field mapping
     *
     * @param IntegrationEndpoint $endpoint
     * @return array
     */
    public function getEndpointFields(IntegrationEndpoint $endpoint): array
    {
        try {
            $fields = [];
            
            // Get fields from endpoint configuration
            $config = $endpoint->configuration ?? [];
            
            if (isset($config['fields']) && is_array($config['fields'])) {
                $fields = $config['fields'];
            }
            
            return $fields;
        } catch (\Exception $e) {
            Log::error('Failed to get endpoint fields', [
                'endpoint_id' => $endpoint->id,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Get field mapping suggestions based on field names
     *
     * @param int $formId
     * @param int $integrationId
     * @return array
     */
    public function getFieldMappingSuggestions(int $formId, int $integrationId): array
    {
        try {
            $form = Form::findOrFail($formId);
            $integration = IntegrationConfiguration::findOrFail($integrationId);
            
            $formFields = $this->getFormFields($form);
            $integrationFields = $this->getIntegrationFields($integration);
            
            $suggestions = [];
            
            // Simple matching logic - can be enhanced based on specific requirements
            foreach ($integrationFields as $fieldKey => $fieldConfig) {
                $fieldName = strtolower($fieldConfig['label'] ?? $fieldKey);
                
                foreach ($formFields as $formKey => $formField) {
                    $formFieldName = strtolower($formField['label'] ?? $formKey);
                    
                    // Check for direct match or partial match
                    if ($formFieldName === $fieldName || 
                        str_contains($formFieldName, $fieldName) || 
                        str_contains($fieldName, $formFieldName)) {
                        $suggestions[$fieldKey] = $formKey;
                        break;
                    }
                }
            }
            
            return $suggestions;
            
        } catch (\Exception $e) {
            Log::error('Failed to generate field mapping suggestions', [
                'form_id' => $formId,
                'integration_id' => $integrationId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }
}
