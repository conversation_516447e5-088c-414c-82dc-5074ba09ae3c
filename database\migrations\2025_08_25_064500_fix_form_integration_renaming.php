<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Skip if the table is already renamed
        if (Schema::hasTable('field_mapping_configurations') && !Schema::hasTable('form_integration_settings')) {
            // Mark the original migration as completed
            DB::table('migrations')
                ->where('migration', '2025_08_14_062600_update_form_integration_settings_to_field_mapping_configurations')
                ->update(['batch' => DB::table('migrations')->max('batch') + 1]);
            return;
        }

        // If we get here, we need to run the migration
        if (Schema::hasTable('form_integration_settings') && !Schema::hasTable('field_mapping_configurations')) {
            Schema::rename('form_integration_settings', 'field_mapping_configurations');
        }

        // Update the column in form_submission_syncs if it exists
        if (Schema::hasColumn('form_submission_syncs', 'form_integration_setting_id')) {
            // Drop foreign key if it exists
            $constraint = DB::selectOne("SELECT CONSTRAINT_NAME 
                FROM information_schema.KEY_COLUMN_USAGE 
                WHERE TABLE_NAME = 'form_submission_syncs' 
                AND COLUMN_NAME = 'form_integration_setting_id' 
                AND CONSTRAINT_NAME <> 'PRIMARY'");
                
            if ($constraint) {
                DB::statement("ALTER TABLE `form_submission_syncs` DROP FOREIGN KEY `{$constraint->CONSTRAINT_NAME}`");
            }

            // Rename the column
            DB::statement('ALTER TABLE `form_submission_syncs` CHANGE `form_integration_setting_id` `field_mapping_configuration_id` BIGINT UNSIGNED NULL');

            // Add foreign key
            DB::statement('ALTER TABLE `form_submission_syncs` ADD CONSTRAINT `form_submission_syncs_field_mapping_configuration_id_foreign` 
                FOREIGN KEY (`field_mapping_configuration_id`) REFERENCES `field_mapping_configurations` (`id`) ON DELETE CASCADE');
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // This is a one-way migration
    }
};
