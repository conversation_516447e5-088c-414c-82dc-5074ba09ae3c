@extends('layouts.admin')
@section('pageTitle', 'Edit Form Integration Setting')

@push('styles')
<style>
    .required:after {
        content: " *";
        color: red;
    }
    .help-block {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 0.25rem;
    }
    .form-group {
        margin-bottom: 1.5rem;
    }
    .card-header h4 {
        margin: 0;
        color: #495057;
    }
</style>
@endpush

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card card-default">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h4><i class="fa fa-edit"></i> Edit Form Integration Setting</h4>
                        </div>
                        <div class="col-auto">
                            <a href="{{ route('admin.form-integration-settings.index') }}" class="btn btn-secondary">
                                <i class="fa fa-arrow-left"></i> Back to List
                            </a>
                        </div>
                    </div>
                </div>

                <form action="{{ route('admin.form-integration-settings.update', $formIntegrationSetting->id) }}" method="POST" id="integration-form">
                    @csrf
                    @method('PUT')
                    <div class="card-body">
                        <!-- Basic Information -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="name" class="required">Integration Name</label>
                                    <input type="text"
                                           class="form-control @error('name') is-invalid @enderror"
                                           id="name"
                                           name="name"
                                           value="{{ old('name', $formIntegrationSetting->name) }}"
                                           required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="is_active">Status</label>
                                    <select class="form-control @error('is_active') is-invalid @enderror"
                                            id="is_active"
                                            name="is_active">
                                        <option value="1" {{ old('is_active', $formIntegrationSetting->is_active) == '1' ? 'selected' : '' }}>Active</option>
                                        <option value="0" {{ old('is_active', $formIntegrationSetting->is_active) == '0' ? 'selected' : '' }}>Inactive</option>
                                    </select>
                                    @error('is_active')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="description">Description</label>
                            <textarea class="form-control @error('description') is-invalid @enderror"
                                      id="description"
                                      name="description"
                                      rows="3">{{ old('description', $formIntegrationSetting->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Form Selection -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="form_id" class="required">Select Form</label>
                                    <select class="form-control @error('form_id') is-invalid @enderror"
                                            id="form_id"
                                            name="form_id"
                                            required>
                                        <option value="">-- Select Form --</option>
                                        @foreach($forms as $form)
                                            <option value="{{ $form->id }}" {{ old('form_id', $formIntegrationSetting->form_id) == $form->id ? 'selected' : '' }}>
                                                {{ $form->title }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('form_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Endpoint Configuration Filters -->
                        <div class="card mt-4">
                            <div class="card-header">
                                <h5>Endpoint Configuration Selection</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="external_system_filter">Filter by External System</label>
                                            <select class="form-control" id="external_system_filter">
                                                <option value="">All External Systems</option>
                                                @foreach(\App\Models\IntegrationConfiguration::EXTERNAL_SYSTEMS as $key => $value)
                                                    <option value="{{ $key }}">{{ $value }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="integration_method_filter">Filter by Integration Method</label>
                                            <select class="form-control" id="integration_method_filter">
                                                <option value="">All Integration Methods</option>
                                                @foreach(\App\Models\IntegrationConfiguration::INTEGRATION_METHODS as $method)
                                                    <option value="{{ $method }}">{{ $method }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="process_type_filter">Filter by Process Type</label>
                                            <select class="form-control" id="process_type_filter">
                                                <option value="">All Process Types</option>
                                                @foreach(\App\Models\IntegrationConfiguration::PROCESS_TYPES as $type)
                                                    <option value="{{ $type }}">{{ $type }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="endpoint_configuration_id" class="required">Select Endpoint Configuration</label>
                                    <select class="form-control @error('endpoint_configuration_id') is-invalid @enderror"
                                            id="endpoint_configuration_id"
                                            name="endpoint_configuration_id"
                                            required>
                                        <option value="">-- Select Endpoint Configuration --</option>
                                        @foreach($endpointConfigurations as $endpoint)
                                            <option value="{{ $endpoint->id }}"
                                                    data-external-system="{{ $endpoint->external_system }}"
                                                    data-integration-method="{{ $endpoint->integration_method }}"
                                                    data-process-type="{{ $endpoint->process_type }}"
                                                    {{ old('endpoint_configuration_id', $formIntegrationSetting->endpoint_configuration_id) == $endpoint->id ? 'selected' : '' }}>
                                                {{ $endpoint->name }} ({{ $endpoint->external_system }} - {{ $endpoint->process_type }})
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('endpoint_configuration_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Field Mappings Section -->
                        <div class="mt-4">
                            <div class="card">
                                <div class="card-header">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0"><i class="fa fa-exchange-alt"></i> Field Mappings</h6>
                                        <div>
                                            <button type="button" class="btn btn-sm btn-outline-secondary mr-2" id="import-json-btn" style="display: none;" title="Import field mappings from JSON">
                                                <i class="fa fa-file-import"></i> Import JSON
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-primary" id="suggest-mappings-btn" style="display: none;" title="AI-powered intelligent field matching">
                                                <i class="fa fa-magic"></i> Auto-Suggest
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div id="field-mappings-container">
                                        <div class="text-center text-muted py-4">
                                            <i class="fa fa-info-circle fa-2x mb-2"></i>
                                            <p>Select both form and endpoint configuration to configure field mappings</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            @error('field_mappings')
                                <div class="alert alert-danger mt-3">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fa fa-save"></i> Update Integration Setting
                        </button>
                        <a href="{{ route('admin.form-integration-settings.index') }}" class="btn btn-secondary">
                            Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    let formFields = [];
    let endpointFields = [];
    let fieldMappings = {};
    
    // Initialize fieldMappings from the server data
    const serverFieldMappings = @json(old('field_mappings', $formIntegrationSetting->field_mappings ?? []));
    console.log('Server field mappings:', serverFieldMappings);
    
    // Convert array format to object format if needed
    if (Array.isArray(serverFieldMappings)) {
        // Handle array of objects with form_field and endpoint_field properties
        serverFieldMappings.forEach(mapping => {
            if (mapping && mapping.form_field && mapping.endpoint_field) {
                fieldMappings[mapping.form_field] = mapping.endpoint_field;
            }
        });
    } else if (typeof serverFieldMappings === 'object' && serverFieldMappings !== null) {
        // Handle object format where keys are form fields and values are endpoint fields
        fieldMappings = { ...serverFieldMappings };
    }

    // Initialize with existing data
    let initPromises = [];
    const formId = $('#form_id').val();
    const endpointId = $('#endpoint_configuration_id').val();

    console.log('Initial form ID:', formId, 'Endpoint ID:', endpointId);

    if (formId) {
        console.log('Loading form fields for form ID:', formId);
        initPromises.push(loadFormFields(formId));
    }
    if (endpointId) {
        console.log('Loading endpoint fields for endpoint ID:', endpointId);
        initPromises.push(loadEndpointFields(endpointId));
    }

    // Wait for both to complete, then update field mapping section
    if (initPromises.length > 0) {
        console.log('Waiting for', initPromises.length, 'promises to resolve...');
        Promise.all(initPromises)
            .then(() => {
                console.log('All promises resolved, updating field mapping section');
                updateFieldMappingSection();
                // Apply existing field mappings after the interface is built
                applyExistingMappings();
            })
            .catch(error => {
                console.error('Error initializing form:', error);
                showError('Failed to initialize form: ' + error.message);
            });
    } else {
        console.log('No promises to wait for, showing placeholder');
        showFieldMappingPlaceholder();
    }

    // Form selection change
    $('#form_id').change(function() {
        const formId = $(this).val();
        if (formId) {
            loadFormFields(formId).then(() => {
                updateFieldMappingSection();
            });
        } else {
            clearFormFields();
            updateFieldMappingSection();
        }
    });

    // Endpoint configuration selection change
    $('#endpoint_configuration_id').change(function() {
        const endpointId = $(this).val();
        if (endpointId) {
            loadEndpointFields(endpointId).then(() => {
                updateFieldMappingSection();
            });
        } else {
            clearEndpointFields();
            updateFieldMappingSection();
        }
    });

    // Filter functionality
    $('#external_system_filter, #integration_method_filter, #process_type_filter').change(function() {
        filterEndpointOptions();
    });

    // Suggest mappings button
    $('#suggest-mappings-btn').click(function() {
        suggestFieldMappings();
    });



    // Load form fields via AJAX
    function loadFormFields(formId) {
        return $.ajax({
            url: '{{ route("admin.form-integration-settings.get-form-fields") }}',
            method: 'GET',
            data: { form_id: formId },
            success: function(response) {
                console.log('Form fields response:', response);
                formFields = response.fields || response.data || [];
            },
            error: function(xhr, status, error) {
                console.error('Failed to load form fields:', xhr.responseText);
                showError('Failed to load form fields');
                clearFormFields();
            }
        });
    }

    // Load endpoint fields via AJAX
    function loadEndpointFields(endpointId) {
        return $.ajax({
            url: '{{ route("admin.form-integration-settings.get-endpoint-fields") }}',
            method: 'GET',
            data: { endpoint_id: endpointId },
            success: function(response) {
                console.log('Endpoint fields response:', response);
                endpointFields = response.fields || response.data || [];
            },
            error: function(xhr, status, error) {
                console.error('Failed to load endpoint fields:', xhr.responseText);
                showError('Failed to load endpoint fields');
                clearEndpointFields();
            }
        });
    }



    // Update field mapping section visibility and content
    function updateFieldMappingSection() {
        if (formFields.length > 0 && endpointFields.length > 0) {
            // $('#suggest-mappings-btn').show();
            displayEndpointFieldMappings();
        } else {
            $('#suggest-mappings-btn').hide();
            showFieldMappingPlaceholder();
        }
    }

    // Show placeholder when no fields are loaded
    function showFieldMappingPlaceholder() {
        const container = $('#field-mappings-container');
        container.html(`
            <div class="text-center text-muted py-4">
                <i class="fa fa-info-circle fa-2x mb-2"></i>
                <p>Select both form and endpoint configuration to configure field mappings</p>
            </div>
        `);
    }

    // Display endpoint fields with form field mapping options
    function displayEndpointFieldMappings() {
        const container = $('#field-mappings-container');

        if (endpointFields.length === 0) {
            showFieldMappingPlaceholder();
            return;
        }

        let html = '<div class="table-responsive"><table class="table table-sm table-bordered">';
        html += `
            <thead class="thead-light">
                <tr>
                    <th width="35%">Endpoint Field</th>
                    <th width="20%">Type</th>
                    <th width="30%">Form Field</th>
                    <th width="15%">Required</th>
                </tr>
            </thead>
            <tbody>
        `;

        endpointFields.forEach(function(field, index) {
            html += createEndpointFieldRow(field, index);
        });

        html += '</tbody></table></div>';
        container.html(html);
    }

    // Create endpoint field row with form field mapping
    function createEndpointFieldRow(endpointField, index) {
        const currentMapping = Object.keys(fieldMappings).find(key => fieldMappings[key] === endpointField.name);

        const formFieldOptions = formFields.map(field =>
            `<option value="${field.key}" ${field.key === currentMapping ? 'selected' : ''}>${field.label || field.key}</option>`
        ).join('');

        const requiredBadge = endpointField.required
            ? '<span class="badge badge-danger">Required</span>'
            : '<span class="badge badge-secondary">Optional</span>';

        return `
            <tr data-endpoint-field="${endpointField.name}">
                <td>
                    <strong>${endpointField.name}</strong>
                    ${endpointField.description ? `<br><small class="text-muted">${endpointField.description}</small>` : ''}
                </td>
                <td>
                    <span class="badge badge-info">${endpointField.datatype || 'string'}</span>
                </td>
                <td>
                    <select class="form-control form-control-sm form-field-select" data-endpoint-field="${endpointField.name}">
                        <option value="">-- Select Form Field --</option>
                        ${formFieldOptions}
                    </select>
                </td>
                <td class="text-center">
                    ${requiredBadge}
                </td>
            </tr>
        `;
    }

    // Handle form field selection changes
    $(document).on('change', '.form-field-select', function() {
        const endpointField = $(this).data('endpoint-field');
        const formField = $(this).val();

        if (formField) {
            fieldMappings[formField] = endpointField;
            // Create hidden input for form submission
            updateHiddenFieldMappings();
        } else {
            // Remove mapping if form field is deselected
            Object.keys(fieldMappings).forEach(key => {
                if (fieldMappings[key] === endpointField) {
                    delete fieldMappings[key];
                }
            });
            updateHiddenFieldMappings();
        }
    });



    // Update hidden inputs for form submission
    function updateHiddenFieldMappings() {
        // Remove existing hidden inputs
        $('input[name^="field_mappings["]').remove();

        // Add hidden inputs for current mappings
        Object.keys(fieldMappings).forEach(formField => {
            const endpointField = fieldMappings[formField];
            $('<input>').attr({
                type: 'hidden',
                name: `field_mappings[${formField}]`,
                value: endpointField
            }).appendTo('#integration-form');
        });
    }

    // Apply existing field mappings to the interface
    function applyExistingMappings() {
        console.log('applyExistingMappings called. fieldMappings:', fieldMappings);
        
        if (!fieldMappings || Object.keys(fieldMappings).length === 0) {
            console.log('No field mappings to apply');
            return;
        }
        
        // First, ensure the field mapping UI is rendered
        if ($('.form-field-select').length === 0) {
            console.log('Form select elements not found, retrying in 100ms...');
            // If the select elements don't exist yet, try again after a short delay
            setTimeout(applyExistingMappings, 100);
            return;
        }
        
        // Clear any existing selections
        $('.form-field-select').val('');
        
        // Apply the mappings
        let appliedCount = 0;
        Object.keys(fieldMappings).forEach(formField => {
            const endpointField = fieldMappings[formField];
            console.log('Applying mapping:', formField, '->', endpointField);
            
            // Find the select element for this endpoint field and set its value
            const $select = $(`.form-field-select[data-endpoint-field="${endpointField}"]`);
            if ($select.length) {
                $select.val(formField);
                console.log('Set value for', endpointField, 'to', formField);
                appliedCount++;
            } else {
                console.warn('Could not find select for endpoint field:', endpointField);
                // Try to find by name attribute as fallback
                const $fallbackSelect = $(`select[name="field_mappings[${formField}]"]`);
                if ($fallbackSelect.length) {
                    $fallbackSelect.val(endpointField);
                    console.log('Applied fallback mapping for', formField);
                    appliedCount++;
                }
            }
        });
        
        console.log(`Applied ${appliedCount} field mappings`);
        
        // Update hidden inputs
        updateHiddenFieldMappings();
    }

    // Suggest field mappings with enhanced feedback
    function suggestFieldMappings() {
        const formId = $('#form_id').val();
        const endpointId = $('#endpoint_configuration_id').val();

        if (!formId || !endpointId) {
            showError('Please select both form and endpoint configuration first');
            return;
        }

        // Show loading state
        const $btn = $('#suggest-mappings-btn');
        const originalText = $btn.html();
        $btn.html('<i class="fa fa-spinner fa-spin"></i> Analyzing...').prop('disabled', true);

        $.ajax({
            url: '{{ route("admin.form-integration-settings.get-field-mapping-suggestions") }}',
            method: 'GET',
            data: {
                form_id: formId,
                endpoint_id: endpointId
            },
            success: function(response) {
                if (response.success && response.suggestions) {
                    let appliedCount = 0;

                    // Apply suggestions to the form field selects
                    Object.keys(response.suggestions).forEach(formField => {
                        const endpointField = response.suggestions[formField];
                        const $select = $(`.form-field-select[data-endpoint-field="${endpointField}"]`);

                        if ($select.length > 0) {
                            $select.val(formField).trigger('change');
                            appliedCount++;

                            // Add visual feedback
                            $select.closest('tr').addClass('table-success').delay(2000).queue(function() {
                                $(this).removeClass('table-success').dequeue();
                            });
                        }
                    });

                    // Show detailed results
                    const totalSuggestions = response.total_suggestions || 0;
                    const unmappedForm = response.unmapped_form_fields ? response.unmapped_form_fields.length : 0;
                    const unmappedEndpoint = response.unmapped_endpoint_fields ? response.unmapped_endpoint_fields.length : 0;

                    let message = `Applied ${appliedCount} intelligent field mappings`;
                    if (unmappedForm > 0 || unmappedEndpoint > 0) {
                        message += `\n${unmappedForm} form fields and ${unmappedEndpoint} endpoint fields remain unmapped`;
                    }

                    showSuccess(message);

                    // Log detailed suggestions for debugging
                    if (response.detailed_suggestions) {
                        console.log('Detailed mapping suggestions:', response.detailed_suggestions);
                    }
                } else {
                    showWarning('No field mapping suggestions found. Please map fields manually.');
                }
            },
            error: function(xhr) {
                console.error('Suggestion error:', xhr.responseText);
                showError('Failed to get field mapping suggestions. Please try again.');
            },
            complete: function() {
                // Restore button state
                $btn.html(originalText).prop('disabled', false);
            }
        });
    }

    // Filter endpoint options
    function filterEndpointOptions() {
        const externalSystemFilter = $('#external_system_filter').val();
        const integrationMethodFilter = $('#integration_method_filter').val();
        const processTypeFilter = $('#process_type_filter').val();

        $('#endpoint_configuration_id option').each(function() {
            if ($(this).val() === '') return; // Skip the default option

            const externalSystem = $(this).data('external-system');
            const integrationMethod = $(this).data('integration-method');
            const processType = $(this).data('process-type');

            let show = true;

            if (externalSystemFilter && externalSystem !== externalSystemFilter) show = false;
            if (integrationMethodFilter && integrationMethod !== integrationMethodFilter) show = false;
            if (processTypeFilter && processType !== processTypeFilter) show = false;

            $(this).toggle(show);
        });

    // Clear form fields
    function clearFormFields() {
        formFields = [];
        $('#form-fields-list').html('<p class="text-muted">Select a form to see its fields</p>');
    }

    // Clear endpoint fields
    function clearEndpointFields() {
        endpointFields = [];
        $('#endpoint-fields-list').html('<p class="text-muted">Select an endpoint configuration to see its fields</p>');
    }

    // Utility functions
    function showError(message) {
        alert('Error: ' + message);
    }

    function showSuccess(message) {
        alert('Success: ' + message);
    }

    function showWarning(message) {
        alert('Warning: ' + message);
    }
});
</script>
@endsection

@section('styles')
<style>
.required::after {
    content: " *";
    color: red;
}

.form-field-item, .endpoint-field-item {
    cursor: pointer;
    transition: background-color 0.2s;
}

.form-field-item:hover, .endpoint-field-item:hover {
    background-color: #f8f9fa;
}

.mapping-row {
    background-color: #f8f9fa;
}

.badge {
    font-size: 0.7em;
}

#field-mapping-section .card-body {
    background-color: #fff;
}

.field-mappings-list {
    max-height: 500px;
    overflow-y: auto;
}
</style>

<!-- JSON Import Modal -->
<div class="modal fade" id="importJsonModal" tabindex="-1" role="dialog" aria-labelledby="importJsonModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="importJsonModalLabel">Import Field Mappings from JSON</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="jsonInput">Paste your JSON object with field mappings:</label>
                    <textarea class="form-control" id="jsonInput" rows="10" placeholder='{"form_field1": "endpoint_field1", "form_field2": "endpoint_field2"}'></textarea>
                    <small class="form-text text-muted">
                        Format: <code>{"form_field_name": "endpoint_field_name", ...}</code>
                    </small>
                </div>
                <div id="jsonError" class="alert alert-danger" style="display: none;"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="applyJsonMappings">Apply Mappings</button>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Import JSON button click handler
    $('#import-json-btn').on('click', function() {
        $('#jsonInput').val('');
        $('#jsonError').hide();
        $('#importJsonModal').modal('show');
    });

    // Apply JSON mappings
    $('#applyJsonMappings').on('click', function() {
        const jsonInput = $('#jsonInput').val().trim();
        
        if (!jsonInput) {
            showJsonError('Please enter JSON data');
            return;
        }

        try {
            // Parse the JSON input
            const jsonMappings = JSON.parse(jsonInput);
            
            if (typeof jsonMappings !== 'object' || jsonMappings === null) {
                throw new Error('Invalid JSON format. Expected an object with field mappings.');
            }

            // Clear existing mappings
            fieldMappings = {};
            
            // Process each mapping
            let validMappings = 0;
            
            for (const [formField, endpointField] of Object.entries(jsonMappings)) {
                if (typeof formField === 'string' && typeof endpointField === 'string' && 
                    formField.trim() !== '' && endpointField.trim() !== '') {
                    
                    // Check if the endpoint field exists
                    const endpointExists = endpointFields.some(field => field.name === endpointField);
                    
                    if (endpointExists) {
                        fieldMappings[formField] = endpointField;
                        validMappings++;
                        
                        // Update the select element if it exists
                        const $select = $(`.form-field-select[data-endpoint-field="${endpointField}"]`);
                        if ($select.length) {
                            $select.val(formField).trigger('change');
                        }
                    }
                }
            }
            
            // Update hidden fields
            updateHiddenFieldMappings();
            
            // Show success message
            showSuccess(`Successfully imported ${validMappings} field mappings`);
            
            // Close the modal
            $('#importJsonModal').modal('hide');
            
        } catch (error) {
            showJsonError('Invalid JSON: ' + error.message);
        }
    });
    
    // Show JSON error message
    function showJsonError(message) {
        const $errorDiv = $('#jsonError');
        $errorDiv.text(message).show();
        $('html, body').animate({
            scrollTop: $errorDiv.offset().top - 100
        }, 500);
    }
});
</script>

@endsection
