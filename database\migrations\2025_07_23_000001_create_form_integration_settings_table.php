<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('form_integration_settings', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('Integration setting name for identification');
            $table->unsignedBigInteger('form_id')->comment('Reference to the form');
            $table->unsignedBigInteger('endpoint_configuration_id')->comment('Reference to the endpoint configuration');
            $table->json('field_mappings')->comment('JSON mapping of form fields to endpoint fields');
            $table->text('description')->nullable()->comment('Optional description of the integration');
            $table->boolean('is_active')->default(true)->comment('Whether this integration setting is active');
            $table->unsignedBigInteger('created_by')->nullable()->comment('User who created this setting');
            $table->unsignedBigInteger('updated_by')->nullable()->comment('User who last updated this setting');
            $table->timestamps();
            $table->softDeletes();

            // Foreign key constraints
            $table->foreign('form_id')->references('id')->on('forms')->onDelete('cascade');
            $table->foreign('endpoint_configuration_id')->references('id')->on('endpoint_configurations')->onDelete('cascade');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
            $table->foreign('updated_by')->references('id')->on('users')->onDelete('set null');

            // Indexes for better performance
            $table->index(['form_id', 'endpoint_configuration_id'], 'form_endpoint_idx');
            $table->index('is_active');
            $table->index('created_by');
            $table->index('updated_by');

            // Unique constraint to prevent duplicate form-endpoint combinations
            $table->unique(['form_id', 'endpoint_configuration_id'], 'unique_form_endpoint');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('form_integration_settings');
    }
};
