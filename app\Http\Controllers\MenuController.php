<?php

namespace App\Http\Controllers;

use DataTables;
use Carbon\Carbon;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Camroncade\Timezone\Facades\Timezone;
use Illuminate\Support\Facades\Redirect;
use App\helpers;
use App\Models\Form;

class MenuController extends Controller
{

    public function __construct()
    {
        // $this->middleware('auth');
        // $this->middleware('can:hasSysadmin');
    }



    public function webmenu(Request $request)
    {

        $getnow = Carbon::now()->toDateTimeString();
        $timezone = auth()->user()->timezone;
        //$timezone = auth()->user()->timezone;
        $date = Timezone::convertFromUTC($getnow, $timezone, 'Y-m-d');
        $time = Timezone::convertFromUTC($getnow, $timezone, 'H:i:s');
        $nowDateTime = $date . ' ' . $time;
        // if (Auth::check()) {
        //     if (auth()->user()->homepage == 'mobile')
        //         return redirect()->intended(route('mobile'));
        // }
        if ($request->yes == 1) {
            if (Session::get('password_session') != '') {
                if (session('yesClick') == 1) {
                    $password_auth = Session::get('password_session');
                    session()->forget('yesClick');
                    Auth::logoutOtherDevices($password_auth);

                    // Regenerate the Session and store new session
                    $request->session()->regenerate();
                    $previous_session = Auth::User()->session_id;

                    Session::getHandler()->destroy($previous_session);
                    Auth::user()->session_id = Session::getId();


                    // Auth::user()->modified_date = $nowDateTime;


                    Auth::user()->save();
                }
            } else {
                dd(Session::get('password_session'));
            }
        } else {





            Session::put('password_auth', '');
        }

        $plan = $this->getPlan();

        $sap_integration = $this->getSAPIntegration();


        // Auth::user()->modified_date = $nowDateTime;
        Auth::user()->save();


        return view('layout.menu', compact('plan', 'sap_integration'));
    }



    public function mobilehome()
    {

        // throw new \Exception("testing");
        $menuName =  __('global.home');

        $submenu=[];
        foreach (Form::$form_modules as $key=>$val)
        {
            $submenu[]=['title'=>__("global.menu.$key"),'route'=>route('mobile.forms',$key)];
        }
        // dd($submenu);
        // $submenu = array(
        //     // array('title' => 'Warehouse', 'route' => route('whse'), 'icon' => 'icon-industry'),
        //     array('title' => 'Incoming',  'route' => route('incoming'), 'icon' => 'fa-sign-in'),
        //     // array('title' => 'Outgoing',  'route' => route('outgoing'), 'icon' => 'icon-upload3'),
        //     // array('title' => 'Print Label', 'route' => route('printlabel'), 'icon' => 'icon-printer'),
        // );

        // dd($query->get());
        return view('mobile.home')->with(compact('submenu', 'menuName'));
    }


    public function incoming()
    {
        $menuName = 'incoming';

        $user = Auth::user();
        $query = Form::with(['userGroup', 'creator'])
            ->whereDoesntHave('formsThatRedirectToThis')
            ->where('module', $menuName)
            ->accessibleByUser($user)
            ->orderBy('created_at', 'desc');
        $forms = $query->get();

        return view('mobile.forms.index')->with(compact('menuName', 'forms'));
    }
    public function outgoing()
    {
        $menuName = 'outgoing';

        $user = Auth::user();
        $query = Form::with(['userGroup', 'creator'])
            ->whereDoesntHave('formsThatRedirectToThis')
            ->where('module', $menuName)
            ->accessibleByUser($user)
            ->orderBy('created_at', 'desc');
        $forms = $query->get();

        return view('mobile.forms.index')->with(compact('menuName', 'forms'));
    }





    public function stopPreviousSession(Request $request)
    {
        $request->session()->regenerate();
        $previous_session = auth()->user()->session_id;
        Session::getHandler()->destroy($previous_session);
        return 'success';
    }
}
