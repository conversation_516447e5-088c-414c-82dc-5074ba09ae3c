<?php

namespace Tests\Feature;

use App\Models\EndpointConfiguration;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class EndpointConfigurationControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user and authenticate
        $user = User::factory()->create([
            'type' => 1, // Super Admin
        ]);
        
        $this->actingAs($user);
    }

    /** @test */
    public function it_can_display_index_page()
    {
        $response = $this->get(route('superadmin.endpoint-configurations.index'));
        
        $response->assertStatus(200);
        $response->assertViewIs('admin.endpoint-configurations.index');
    }

    /** @test */
    public function it_can_display_create_page()
    {
        $response = $this->get(route('superadmin.endpoint-configurations.create'));
        
        $response->assertStatus(200);
        $response->assertViewIs('admin.endpoint-configurations.create');
        $response->assertViewHas('externalSystems');
        $response->assertViewHas('integrationMethods');
        $response->assertViewHas('processTypes');
    }

    /** @test */
    public function it_can_store_a_new_configuration()
    {
        $data = [
            'name' => 'Test Integration',
            'external_system' => 'CSI_10',
            'integration_method' => 'API',
            'process_type' => 'PO Receipt',
            'endpoint_url' => 'https://api.example.com/endpoint',
            'request_fields' => json_encode([
                ['field_name' => 'po_number', 'field_type' => 'string', 'is_required' => true],
                ['field_name' => 'item_code', 'field_type' => 'string', 'is_required' => true],
            ]),
            'is_active' => true,
        ];

        $response = $this->post(route('superadmin.endpoint-configurations.store'), $data);
        
        $response->assertRedirect(route('superadmin.endpoint-configurations.index'));
        $this->assertDatabaseHas('endpoint_configurations', [
            'name' => 'Test Integration',
            'external_system' => 'CSI_10',
            'integration_method' => 'API',
            'process_type' => 'PO Receipt',
            'endpoint_url' => 'https://api.example.com/endpoint',
            'is_active' => true,
        ]);
    }

    /** @test */
    public function it_validates_required_fields_when_creating()
    {
        $response = $this->post(route('superadmin.endpoint-configurations.store'), []);
        
        $response->assertSessionHasErrors([
            'name',
            'external_system',
            'integration_method',
            'process_type',
            'endpoint_url',
            'request_fields',
        ]);
    }

    /** @test */
    public function it_can_display_edit_page()
    {
        $config = EndpointConfiguration::factory()->create();
        
        $response = $this->get(route('superadmin.endpoint-configurations.edit', $config->id));
        
        $response->assertStatus(200);
        $response->assertViewIs('admin.endpoint-configurations.edit');
        $response->assertViewHas('endpointConfiguration', $config);
        $response->assertViewHas('externalSystems');
        $response->assertViewHas('integrationMethods');
        $response->assertViewHas('processTypes');
    }

    /** @test */
    public function it_can_update_a_configuration()
    {
        $config = EndpointConfiguration::factory()->create();
        
        $updateData = [
            'name' => 'Updated Integration',
            'external_system' => 'SAP_B1',
            'integration_method' => 'File Transfer',
            'process_type' => 'Misc Issue',
            'endpoint_url' => '/path/to/file',
            'request_fields' => json_encode([
                ['field_name' => 'doc_num', 'field_type' => 'string', 'is_required' => true],
                ['field_name' => 'item_code', 'field_type' => 'string', 'is_required' => true],
                ['field_name' => 'quantity', 'field_type' => 'number', 'is_required' => true],
            ]),
            'is_active' => false,
        ];

        $response = $this->put(route('superadmin.endpoint-configurations.update', $config->id), $updateData);
        
        $response->assertRedirect(route('superadmin.endpoint-configurations.index'));
        $this->assertDatabaseHas('endpoint_configurations', [
            'id' => $config->id,
            'name' => 'Updated Integration',
            'external_system' => 'SAP_B1',
            'integration_method' => 'File Transfer',
            'process_type' => 'Misc Issue',
            'endpoint_url' => '/path/to/file',
            'is_active' => false,
        ]);
    }

    /** @test */
    public function it_can_delete_a_configuration()
    {
        $config = EndpointConfiguration::factory()->create();
        
        $response = $this->delete(route('superadmin.endpoint-configurations.destroy', $config->id));
        
        $response->assertRedirect(route('superadmin.endpoint-configurations.index'));
        $this->assertSoftDeleted('endpoint_configurations', ['id' => $config->id]);
    }
}
