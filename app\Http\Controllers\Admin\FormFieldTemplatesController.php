<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreFormFieldTemplateRequest;
use App\Http\Requests\UpdateFormFieldTemplateRequest;
use App\Models\FormFieldTemplate;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Log;

class FormFieldTemplatesController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $query = FormFieldTemplate::orderBy('created_at', 'desc');
            $query = $query->applyDataTableFilters($request);

            return DataTables::of($query)
                ->addIndexColumn()
                ->addColumn('action', function ($row) {
                    $actions = [];
                    $actions['show'] = route('admin.form-field-templates.show', $row->id);
                    $actions['edit'] = route('admin.form-field-templates.edit', $row->id);
                    $actions['delete'] = route('admin.form-field-templates.destroy', $row->id);

                    $html = view('partials.datatablesActions', compact('actions', 'row'))->render();
                    return $html;
                })
                // ->editColumn('created_at', function ($row) {
                //     return $row->created_at->format('Y-m-d H:i:s');
                // })
                ->rawColumns(['action'])
                ->make(true);
        }

        return view('admin.form-field-templates.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('admin.form-field-templates.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \App\Http\Requests\StoreFormFieldTemplateRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreFormFieldTemplateRequest $request)
    {
        // $request->tenant_id=1;
        $inputs = $request->validated();
        $inputs['tenant_id'] = 1;
        FormFieldTemplate::create($inputs);

        return redirect()->route('admin.form-field-templates.index')
            ->with('success', 'Form Field Template created successfully.');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\FormFieldTemplate  $formFieldTemplate
     * @return \Illuminate\Http\Response
     */
    public function show(FormFieldTemplate $formFieldTemplate)
    {
        return view('admin.form-field-templates.show', compact('formFieldTemplate'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\FormFieldTemplate  $formFieldTemplate
     * @return \Illuminate\Http\Response
     */
    public function edit(FormFieldTemplate $formFieldTemplate)
    {
        return view('admin.form-field-templates.edit', compact('formFieldTemplate'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \App\Http\Requests\UpdateFormFieldTemplateRequest  $request
     * @param  \App\Models\FormFieldTemplate  $formFieldTemplate
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateFormFieldTemplateRequest $request, FormFieldTemplate $formFieldTemplate)
    {
        $validatedData = $request->validated();
        // dd($validatedData);
        // Debug: Log the validated data
        Log::info('Update Form Field Template - Validated Data:', $validatedData);

        $formFieldTemplate->update($validatedData);
        // dd($formFieldTemplate->schema['type']);
        return redirect()->route('admin.form-field-templates.index')
            ->with('success', 'Form Field Template updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\FormFieldTemplate  $formFieldTemplate
     * @return \Illuminate\Http\Response
     */
    public function destroy(FormFieldTemplate $formFieldTemplate)
    {
        $formFieldTemplate->delete();

        return redirect()->route('admin.form-field-templates.index')
            ->with('success', 'Form Field Template deleted successfully.');
    }

    /**
     * Mass destroy form field templates.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function massDestroy(Request $request)
    {
        FormFieldTemplate::whereIn('id', $request->ids)->delete();
        return response(null, Response::HTTP_NO_CONTENT);
    }
}
