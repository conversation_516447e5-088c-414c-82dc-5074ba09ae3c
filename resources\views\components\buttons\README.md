# Button Components

This directory contains reusable button components for the application. Each button is styled according to its purpose and includes support for icon-only display.

## Available Buttons

| Action  | Component | Variant  | Icon | Default Text |
|---------|-----------|----------|------|--------------|
| Add | `<x-buttons.add>` | `success` | `plus` | `Add` |
| View | `<x-buttons.view>` | `info` | `eye` | `View` |
| Edit | `<x-buttons.edit>` | `primary` | `pencil` | `Edit` |
| Delete | `<x-buttons.delete>` | `danger` | `trash-can` | `Delete` |
| Clone | `<x-buttons.clone>` | `complete` | `copy` | `Clone` |
| Cancel | `<x-buttons.cancel>` | `secondary` | `times` | `Cancel` |
| Back | `<x-buttons.back>` | `secondary` | `arrow-left` | `Back` |
| Submit | `<x-buttons.submit>` | `primary` | `paper-plane` | `Submit` |
| Save | `<x-buttons.save>` | `success` | `save` | `Save` |
| Import | `<x-buttons.import>` | `primary` | `file-import` | `Import` |
| Export | `<x-buttons.export>` | `primary` | `file-export` | `Export` |
| Upload | `<x-buttons.upload>` | `primary` | `cloud-upload-alt` | `Upload` |

## Common Props

All button components accept the following props:

- `route` (string): The URL for the button's link (default: '#')
- `text` (string): The button text (default: action-specific text)
- `size` (string): Button size - 'sm', 'md', or 'lg' (default: 'md')
- `class` (string): Additional CSS classes
- `icon` (string): Icon name from Font Awesome (default: action-specific icon)
- `title` (string): Tooltip text (default: action-specific text)
- `target` (string): Link target attribute (default: '_self')
- `iconOnly` (bool): Whether to show only the icon (default: false)
- `disabled` (bool): Whether the button is disabled (default: false)

## Usage Examples

### Basic Usage

```blade
<x-buttons.add />
<x-buttons.edit :route="route('items.edit', $item->id)" />
<x-buttons.delete :route="route('items.destroy', $item->id)" 
                 :confirm="__('Are you sure you want to delete this item?')" />
```

### Icon-Only Buttons

```blade
<x-buttons.add :iconOnly="true" />
<x-buttons.edit :route="route('items.edit', $item->id)" :iconOnly="true" />
```

### Custom Text and Icons

```blade
<x-buttons.add text="New Item" icon="plus-circle" />
<x-buttons.export text="Download Report" />
```

## Styling

- Buttons have a transparent background by default
- Only the icon is colored based on the button's variant
- Hover effects include a subtle background color change
- Tooltips are shown on hover for icon-only buttons
