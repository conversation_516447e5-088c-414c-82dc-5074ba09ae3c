<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use App\Models\EndpointConfiguration;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Map old values to new constant values
        $mappings = [
            'target_type' => [
                'CSI' => EndpointConfiguration::TARGET_CSI,
                'SAP' => EndpointConfiguration::TARGET_SAP,
                'Infor WMS' => EndpointConfiguration::TARGET_INFOR_WMS,
            ],
            'process_selection' => [
                'Misc Issue' => EndpointConfiguration::PROCESS_MISC_ISSUE,
                'Misc Receipt' => EndpointConfiguration::PROCESS_MISC_RECEIPT,
                'Quantity Move' => EndpointConfiguration::PROCESS_QUANTITY_MOVE,
                'PO Receipt' => EndpointConfiguration::PROCESS_PO_RECEIPT,
            ],
            'endpoint_type' => [
                'API' => EndpointConfiguration::TYPE_API,
                'Stored Procedure' => EndpointConfiguration::TYPE_STORED_PROCEDURE,
            ]
        ];

        // Use raw SQL to modify the columns
        DB::statement('ALTER TABLE endpoint_configurations MODIFY target_type VARCHAR(50) NOT NULL');
        DB::statement('ALTER TABLE endpoint_configurations MODIFY process_selection VARCHAR(50) NOT NULL');
        DB::statement('ALTER TABLE endpoint_configurations MODIFY endpoint_type VARCHAR(50) NOT NULL DEFAULT "' . EndpointConfiguration::TYPE_API . '"');

        // Update existing records with new constant values
        foreach ($mappings['target_type'] as $oldValue => $newValue) {
            DB::table('endpoint_configurations')
                ->where('target_type', $oldValue)
                ->update(['target_type' => $newValue]);
        }

        foreach ($mappings['process_selection'] as $oldValue => $newValue) {
            DB::table('endpoint_configurations')
                ->where('process_selection', $oldValue)
                ->update(['process_selection' => $newValue]);
        }

        foreach ($mappings['endpoint_type'] as $oldValue => $newValue) {
            DB::table('endpoint_configurations')
                ->where('endpoint_type', $oldValue)
                ->update(['endpoint_type' => $newValue]);
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Note: This is a one-way migration as we're moving from ENUM to constants
        // Reverting would require mapping back to the old ENUM values
        // which might not be possible if new values were added
        
        // If you need to reverse this, you would need to:
        // 1. Map the constant values back to their original ENUM values
        // 2. Alter the columns back to ENUM type with the original values
        // 3. Update the records with the original values
        
        throw new \RuntimeException('Cannot reverse this migration. Please restore from backup if needed.');
    }
};
