/*

* Override Pages default styles or create your own styles here

*/

.pgn-wrapper {
    min-width: 300px;
}

.summernote-wrapper {
    margin: -20px;
}

.sticky_card_primary>.card-header {
    position: sticky;
    top: 60px;

    /*border: 1px solid #ccc;*/
    /*border-bottom: 0px;*/
    z-index: 99;
    /*background: #fff;*/

}

.card .card-header {
    /* background: #323237; */
    /* color: #fff; */
}

.card-header h3 {
    color: #fff;
}

.sticky_card_primary>.card-header h3 {}

.sticky_card_primary>.card-header .card-controls .fa {
    margin-right: 5px;
}

th {
    /* color: black !important;
    background-color: #f0f0f0; */
}

.card .card-header+.card-block {
    padding-top: 20px;
}

.page-sidebar .sidebar-menu .menu-items li>a {
    width: 78%;
}

.sub_section {
    border: 1px solid #ccc;
    border-radius: 5px;
    margin-top: 15px;
    padding: 15px;
}

.sub_section h5 {
    text-align: center;
    border-bottom: 1px solid #ccc;
    line-height: 2;
    margin-top: -15px;

}

.item_row {
    border: 1px solid #cecece;
    border-radius: 5px;
    margin-top: 15px;
    padding: 15px;
}

.form-horizontal .form-group .control-label {
    font-weight: bold !important;
    color: #000;
    font-size: 12px !important;
    opacity: 1 !important;
    text-transform: capitalize !important;
}

.windows h5,
.windows h6 {

    font-weight: 500;
}

.item_row>h6 {

    background: #323237;
    padding: 10px;
    color: #fff;
    margin: -15px;
    margin-bottom: 15px;
    border-top-right-radius: 5px;
    border-top-left-radius: 5px;

}

.select2 {
    max-width: 100%;
}

thead th {
    /* color: black !important;
    background-color: #949494; */

}



table.dataTable>thead>tr>th:not(.sorting_disabled),
table.dataTable>thead>tr>td:not(.sorting_disabled) {
    padding-right: 5px !important;
    padding-left: 5px !important;
}

.table-bordered td,
.table-bordered th {
    border: 1px solid #bfc0c1;
}

table.dataTable td,
table.dataTable th {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: #e6e6e6;
}

.form-horizontal .form-group .control-label {
    opacity: 0.9;
}

td,
th {
    padding: 0px 10px;
    /* text-align: center; */
}

.v-align-middled.inputs {
    /*min-width: 100px;*/
}

label {
    /* font-weight: bold; */
}

.danger {
    background: #ce4242;
    color: #ffffff;
}

.warning {
    background: #daca9e;
    color: #fff;
}

.success {
    background: #0eaf91;
    color: #fff;
}

.table.dataTable.no-footer {
    border: auto;
}

/* th{
    padding-top: 5px;
    padding-bottom: 5px;

}
td{
    padding-top: 5px;
    padding-bottom: 5px;

} */
.w_30 {
    max-width: 30px !important;
    width: 30px !important;
    /*padding-right: 5px !important;*/
}

.w_50 {
    width: 50px;
    max-width: 50px;
}

.w_70 {
    width: 70px;
    max-width: 70px;
}

input:disabled {
    /* border: 0px;
    color: #000;
    cursor: default;
    background-color: #fff0;
    text-align: center; */
}

table.dataTable {
    width: 100%;
}

.card .card-header {

    padding: 5px 20px 5px 20px;

    min-height: 38px;
}

.windows .card-title h3 {

    font-size: 18px;
    line-height: 1;
    margin: 5px 0px 1px 0px;
    font-weight: 600;
}

.page-container .page-content-wrapper .footer {
    /*position: relative;*/


}

table.dataTable>tbody>tr.child td {
    text-align: left;

}

table.dataTable>tbody>tr.child,
table.dataTable>tbody>tr.child:hover {
    background: #f3ebeb !important;
}

table.dataTable>tbody>tr.child .dtr-details {
    width: 100%;
}

/* table.dataTable>thead .sorting:before, table.dataTable>thead .sorting_asc:before, table.dataTable>thead .sorting_desc:before, table.dataTable>thead .sorting_asc_disabled:before, table.dataTable>thead .sorting_desc_disabled:before{
    display: none;
}
table.dataTable>thead .sorting:after, table.dataTable>thead .sorting_asc:after, table.dataTable>thead .sorting_desc:after, table.dataTable>thead .sorting_asc_disabled:after, table.dataTable>thead .sorting_desc_disabled:after {
    display: none;
} */
.page-item.active .page-link {
    z-index: 2;
    color: #fff;
    background-color: #0275d8;
    border-color: #0275d8;
}

.form-control {

    border: 1px solid rgb(0 0 0 / 32%);
}

tr.validation_error {
    background: #f19f9f !important;
}

.datatable tr .parsley-errors-list.filled {
    display: inline;
    list-style: none;
}

.datepicker table tr td.today,
.datepicker table tr td.today:hover,
.datepicker table tr td.today.disabled,
.datepicker table tr td.today.disabled:hover {
    color: #000000;
    background-color: #ffdb99;
    border-color: #ffb733;
}

.card .card-header .card-controls {
    margin-top: 11px;
}

.card.card-default {
    margin-top: 10px;
}

.table-responsive {
    overflow-x: hidden;
}

/* Icon-only buttons with transparent background */
.btn.btn-icon-only {
    padding: 0.25rem 0.5rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    background: transparent !important;
    border: 1px solid transparent;
    color: #6c757d;
    /* Default icon color */
    box-shadow: none !important;
    transition: all 0.2s ease-in-out;
}

.btn.btn-icon-only i {
    margin: 0 !important;
    font-size: 16px;
}

/* Colored icons */
/* Info (View) */
.btn-info.btn-icon-only {
    color: #1f3953;
}

/* Warning (Edit) */
.btn-warning.btn-icon-only {
    color: #ffc107;
}

/* Danger (Delete) */
.btn-danger.btn-icon-only {
    color: #dc3545;
}

/* Success (Add, Save) */
.btn-success.btn-icon-only {
    color: #198754;
}

/* Primary (Edit, Submit, Import, Export, Upload) */
.btn-primary.btn-icon-only {
    color: #6d5eac;
    /* Updated primary color */
}

/* Complete (Clone) */
.btn-complete.btn-icon-only {
    color: #37b0e9;
    /* Updated complete color */
}

/* Secondary (Cancel, Back) */
.btn-secondary.btn-icon-only {
    color: #575757;
    border: 1px solid #dee2e6;
}

/* Hover effects for icon buttons */
.btn.btn-icon-only:hover {
    background-color: rgba(0, 0, 0, 0.05) !important;
    border-color: rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

/* Active/focus states */
.btn.btn-icon-only:active,
.btn.btn-icon-only:focus {
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Button group spacing */
.btn-group .btn-icon-only {
    margin-right: 0.25rem;
}

.btn-group .btn-icon-only:last-child {
    margin-right: 0;
}

/* Remove default button focus styles */
.btn:focus,
.btn:active:focus,
.btn.active:focus,
.btn.focus,
.btn:active.focus,
.btn.active.focus {
    outline: none;
    box-shadow: none;
}

/* Ensure consistent spacing in button groups */
.btn-group .btn {
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

.dataTables_wrapper .dataTables_paginate ul>li.active>a {
    color: #ffffff;
}

.builder-group-button {
    /* background: #6d5eac !important; */
    background: #323237 !important;
    color: #fff !important;
}

/* .formio-component-tabs .tab-component-tabs .nav-item:nth-last-child(-n+3), */
/* .formio-component-tabs .tab-component-tabs .nav-item:nth-child(-2), */
/* .formio-component-tabs .tab-component-tabs .nav-item:nth-child(-3), */
.formio-component-customDefaultValuePanel,
.formio-component-calculateValuePanel,
.formio-component-calculateServer,
.formio-component-allowCalculateOverride,
.formio-component-tags,
.formio-component-properties {
    /* display: none; */

}

.component-settings .nav>li>a {

    font-weight: 600;
    opacity: 1 !important;
}

.table thead tr th {
    text-transform: capitalize;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    text-transform: capitalize;
}

.card {
    --bs-card-spacer-y: 0.5rem;
    --bs-card-spacer-x: 0.5rem;
}

.table thead tr th

/* .table tbody tr td { */
    {
    padding: 10px !important;
}

td {
    padding: 0px 10px;
    text-align: left;
}

/* dataTables_filter {
    margin-bottom: 10px;
    align-items: center;
    display: flex;
} */

.form-group label:not(.error) {
    text-transform: capitalize;
}

label.required:after {
    content: " *";
    color: red;
}







.table tbody tr td {

    padding: 5px;
    /* font-size: 13.5px; */
    line-height: 1;
    vertical-align: middle;
    text-align: start;
}

.table thead tr th {
    text-transform: none;
    font-size: 12px;
    text-align: start;
    /* padding-right: 15px; */
    /* background: #cecece; */
    /* color: rgb(44 44 44 / 66%); */
    font-weight: 600;

}

table.dataTable>thead .sorting:before,
table.dataTable>thead .sorting_asc:before,
table.dataTable>thead .sorting_desc:before,
table.dataTable>thead .sorting_asc_disabled:before,
table.dataTable>thead .sorting_desc_disabled:before {
    display: none;

}

table.dataTable thead .sorting:after,
table.dataTable thead .sorting_asc:after,
table.dataTable thead .sorting_desc:after,
table.dataTable thead .sorting_asc_disabled:after,
table.dataTable thead .sorting_desc_disabled:after {
    right: 0px;
}

table.dataTable>thead>tr>th:not(.sorting_disabled),
table.dataTable>thead>tr>td:not(.sorting_disabled) {
    /* padding-right: 15px !important; */
}

.card .card-header .card-title {
    text-transform: none;
}

.form-group label:not(.error) {

    text-transform: none;
    font-size: 12px;
}

.form-group {
    margin-bottom: 16px;
}

.card .card-header .card-controls ul,
.card .card-header .card-controls .dt-buttons {
    display: inline-block;
}

.btn-standard {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

@media (min-width: 768px) {
    .container-fluid {
        padding-right: 15px;
        padding-left: 15px;
    }

    .btn-standard {
        width: 120px;

    }
}

@media (max-width: 768px) {
    .card .card-block {
        padding: 10px;
    }

    .card .card-header {
        padding: 5px 10px 5px 10px;
    }

    .btn-standard {
        width: 90px;
    }
}

.form-preview-container {
    position: relative;
    height: calc(100vh - 75px);
    /* overflow-y: auto; */
}

.form-preview-container .formio-component .card {
    border: 0px !important;
}

.form-preview-container .formio-component .card-header {
    position: relative;
    text-align: left;
}

.form-preview-container .formio-component .card-title {
    position: relative;
    display: inline-block !important;
    padding: 0 5px !important;
    background: white;
    font-size: 12px !important;
    z-index: 1;
}

.form-preview-container .formio-component .card-header::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 1px;
    background: #ccc;
    z-index: 0;
    margin-top: -6px;
    /* behind the text */
}

.form-preview-container .formio-component .card-body {
    padding: 0px !important;
}

.form-preview-container .formio-component-submit {
    /* margin-top: 10px; */
    text-align: center;
    position: sticky;
    bottom: 0vh;
    left: 0px;
    z-index: 99;
    /* top: 200px; */
    width: 100%;
    background: #ffffff;
    padding: 10px;
    margin-bottom: 0px !important;
    /* border: 1px solid #ccc; */
}

.form-preview-container .editgrid-actions .btn {
    color: inherit !important;
    background: inherit !important;
    border-color: #51585e !important;
}

.form-preview-container .editRow {
    padding-left: 0px;
    padding-right: 0px;
    background: none;
    color: #6d5eac;
    border: none;
    font-size: 12px;
    line-height: 1;
    margin-right: 10px;
}

.form-preview-container .removeRow {
    padding-left: 0px;
    padding-right: 0px;
    background: none;
    color: #f35958;
    border: none;
    font-size: 12px;
    line-height: 1;
    margin-right: 5px;
}

.form-preview-container .table-responsive {
    border: none;
}

.form-preview-container .editgrid-table-container {
    max-width: none;

}

.form-preview-container .editgrid-table-container table {
    table-layout: fixed;
}

.form-preview-container .editgrid-table-container thead .editgrid-table-column {
    font-weight: 600;
}

.form-preview-container .editgrid-table-container .editgrid-table-column {
    border: none;
    white-space: nowrap;
    /* overflow-x: unset; */
    /* white-space: nowrap; */
    /* overflow: hidden; */
    /* text-overflow: ellipsis; */
    /* padding: 5px; */
    padding: 10px 5px;
}

.form-preview-container .formio-dialog.formio-dialog-theme-default .formio-dialog-content {
    width: 90%;
}

.form-preview-container .col-form-label {
    text-align: right !important;
    float: right;
}

.form-preview-container .formio-component-label-hidden {
    position: relative;
    margin-bottom: 0px !important;
}

.formio-component .col-sm-6 {
    flex: 0 0 50% !important;
    max-width: 50% !important;
}

.formio-component .col-sm-8 {
    flex: 0 0 66.66% !important;
    max-width: 66.66% !important;
}

.formio-component-columns {
    /* background-color: #000000; */
}

.formio-component-columns>div:nth-child(2) {
    padding-left: 0px !important;
}

.formio-component .col-sm-4 {
    flex: 0 0 33.33% !important;
    max-width: 33.33% !important;
}

label.required::after {
    position: relative;
    z-index: 10;

    content: " *";
    color: #eb0000;
}

.col-form-label {
    text-align: right;
}

.system-field {
    /* background-color: #cecece; */
}

.system-field .formio-component-hidden {
    /* color: #000 !important; */
}

.form-control {
    display: block;
    width: 100%;
    padding: 0.375rem 0.375rem 0.375rem 0.375rem;
    -moz-padding-start: calc(0.75rem - 3px);
    /* font-size: 1rem; */
    font-size: 14px;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    background-color: #fff;
    background-image: url("data:image/svg+xml,...");
    /* bootstrap arrow */
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 16px 12px;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    appearance: none;
}

form .cancel_btn,
form .save_btn {
    display: none;
}

.container-fluid>.card>.card-header {
    position: sticky !important;
    top: 60px !important;
    background: #fff !important;
}

.card.card-default:last-child {
    margin-top: 10px;
    margin-bottom: 0px;
}

.card .card-block:last-child {
    padding-bottom: 0px !important;
}

.modal-large .modal-dialog {
    max-width: none !important;
    width: 90% !important;
}

.modal .close {
    position: absolute;
    right: 50px;
    top: 15px;
    z-index: 9;
}

@media (max-width: 767px) {
    .page-container .page-content-wrapper .content {
        /* padding-top: 48px; */
        padding-bottom: 0px;
    }
}
