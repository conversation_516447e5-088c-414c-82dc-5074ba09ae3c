@extends('layouts.admin')
@section('pageTitle', trans('global.edit') . ' ' . trans('cruds.data_capture_field.title_singular'))

@section('styles')
    @parent
    <style>
        .json-editor {
            border: 1px solid #ddd;
            border-radius: 4px;
            min-height: 200px;
        }

        .schema-preview {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
        }
    </style>
@endsection

@section('content')
    <div class="card card-default">
        <div class="card-header separator">
            <div class="card-title mainheading">
                <h4>{{ trans('global.edit') }} {{ trans('cruds.data_capture_field.title_singular') }}</h4>
            </div>
            <div class="card-controls">
                <ul>
                    <li>

                        <x-cancel-button :url="route('superadmin.data-capture-fields.index')" :text="trans('global.back_to_list')" />
                    </li>

                </ul>
            </div>
        </div>

        <div class="card-block">
            @if ($errors->any())
                <div class="alert alert-danger">
                    <ul>
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <form method="POST" action="{{ route('superadmin.data-capture-fields.update', $dataCaptureField) }}">
                @csrf
                @method('PUT')

                <div class="form-group row">
                    <label for="title" class="col-md-2 col-form-label">{{ trans('cruds.fields.field_title') }} <span
                            class="text-danger">*</span></label>
                    <div class="col-md-4">
                        <input type="text" class="form-control" id="title" name="title"
                            value="{{ old('title', $dataCaptureField->title) }}" required>
                    </div>
                </div>

                <div class="form-group row">
                    <label for="key" class="col-md-2 col-form-label">{{ trans('cruds.fields.field_key') }} <span
                            class="text-danger">*</span></label>
                    <div class="col-md-4">
                        <input type="text" class="form-control" id="key" name="key"
                            value="{{ old('key', $dataCaptureField->key) }}" required>
                    </div>
                </div>

                <div class="form-group row">
                    <label for="process_type" class="col-md-2 col-form-label">{{ trans('cruds.fields.process_type') }} <span
                            class="text-danger">*</span></label>
                    <div class="col-md-4">
                        <select class="form-control form-control-sm " name="process_type" id="process_type">
                            <option value="">{{ __('global.all') }}</option>
                            @foreach (\App\Models\IntegrationConfiguration::getProcessTypeOptions() as $type)
                                <option {{ $type == $dataCaptureField->process_type ? 'selected' : '' }}
                                    value="{{ $type }}">{{ $type }}</option>
                            @endforeach
                        </select>
                        {{-- <input type="text" class="form-control" id="process_type" name="process_type"
                            value="{{ old('process_type', $dataCaptureField->process_type) }}" required> --}}

                    </div>
                </div>

                <input type="hidden" name="icon" value="terminal" />

                <div class="form-group row">
                    <label for="schema" class="col-md-2 col-form-label">{{ trans('cruds.fields.field_defination') }}
                        <span class="text-danger">*</span></label>
                    <div class="col-md-4">
                        <div class="row">
                            <div class="col-md-12">
                                @php
                                    $schema = is_string($dataCaptureField->schema)
                                        ? json_decode($dataCaptureField->schema, true)
                                        : $dataCaptureField->schema;
                                @endphp

                                <label for="schema_label">{{ trans('cruds.fields.label') }} <span
                                        class="text-danger">*</span></label>
                                <input type="text" class="form-control mb-2" id="schema_label" name="schema_label"
                                    value="{{ old('schema_label', $schema['label'] ?? '') }}" required>

                                <label for="schema_type">{{ trans('cruds.fields.type') }} <span
                                        class="text-danger">*</span></label>
                                <select class="form-control mb-2" id="schema_type" name="schema_type" required>
                                    <option value="">Select Type</option>
                                    <option value="textfield"
                                        {{ old('schema_type', $schema['type'] ?? '') == 'textfield' ? 'selected' : '' }}>
                                        Text Field</option>
                                    <option value="textarea"
                                        {{ old('schema_type', $schema['type'] ?? '') == 'textarea' ? 'selected' : '' }}>
                                        Textarea</option>
                                    <option value="number"
                                        {{ old('schema_type', $schema['type'] ?? '') == 'number' ? 'selected' : '' }}>
                                        Number</option>
                                    <option value="email"
                                        {{ old('schema_type', $schema['type'] ?? '') == 'email' ? 'selected' : '' }}>Email
                                    </option>
                                    <option value="select"
                                        {{ old('schema_type', $schema['type'] ?? '') == 'select' ? 'selected' : '' }}>
                                        Select</option>
                                    <option value="checkbox"
                                        {{ old('schema_type', $schema['type'] ?? '') == 'checkbox' ? 'selected' : '' }}>
                                        Checkbox</option>
                                    <option value="radio"
                                        {{ old('schema_type', $schema['type'] ?? '') == 'radio' ? 'selected' : '' }}>Radio
                                    </option>
                                </select>

                                <!-- Options management for select, checkbox, and radio -->
                                <div id="options-container" class="mb-3" style="display: none;">
                                    <label class="mb-2">Options</label>
                                    <div id="options-list">
                                        <?php
                                        $vals = [];
                                        if ($schema['type'] == 'select') {
                                            $vals = isset($schema['data']['values']) && is_array($schema['data']['values']) ? $schema['data']['values'] : [];
                                        } elseif ($schema['type'] == 'radio') {
                                            $vals = isset($schema['values']) && is_array($schema['values']) ? $schema['values'] : [];
                                        }
                                        ?>
                                        <!-- Options will be dynamically added here -->
                                        @if (count($vals))
                                            @foreach ($vals as $option)
                                                <div class="input-group mb-2 option-item">
                                                    <input type="text" class="form-control option-label"
                                                        placeholder="Label" value="{{ $option['label'] }}" />
                                                    <input type="text" class="form-control option-value"
                                                        placeholder="Value" value="{{ $option['value'] }}" />
                                                    <div class="input-group-append">
                                                        <button class="btn btn-outline-danger remove-option"
                                                            type="button">&times;</button>
                                                    </div>
                                                </div>
                                            @endforeach
                                        @endif
                                    </div>
                                    <button type="button" id="add-option" class="btn btn-sm btn-secondary mt-2">
                                        Add Option
                                    </button>
                                </div>

                                <label for="schema_key">{{ trans('cruds.fields.key') }} <span
                                        class="text-danger">*</span></label>
                                <input type="text" class="form-control mb-2" id="schema_key" name="schema_key"
                                    value="{{ old('schema_key', $schema['key'] ?? '') }}" required>

                                <div class="form-check mb-2">
                                    <input type="checkbox" class="form-check-input" id="schema_required"
                                        name="schema_required" value="1"
                                        {{ old('schema_required', $schema['validate']['required'] ?? false) ? 'checked' : '' }}>
                                    <label class="form-check-label"
                                        for="schema_required">{{ trans('cruds.fields.required') }}</label>
                                </div>
                                <textarea class="form-control" id="schema" name="schema" rows="8" style="display: none;">{{ old('schema', is_string($dataCaptureField->schema) ? $dataCaptureField->schema : json_encode($dataCaptureField->schema)) }}</textarea>
                                <input type="hidden" id="options" name="options" value="{{ old('options') }}" />
                            </div>
                        </div>
                        <textarea class="form-control" id="schema" name="schema" rows="8" style="display: none;">{{ old('schema', is_string($dataCaptureField->schema) ? $dataCaptureField->schema : json_encode($dataCaptureField->schema)) }}</textarea>
                    </div>
                </div>

                <div class="form-group row">
                    <div class="col-md-9 offset-md-3 text-right">

                        <x-cancel-button :url="route('superadmin.data-capture-fields.index')" :text="trans('global.cancel')" />
                        <x-save-button />

                    </div>
                </div>
            </form>
        </div>
    </div>
@endsection

@section('scripts')
    @parent
    <script>
        function updateSchemaPreview() {
            const schema = {
                label: $('#schema_label').val() || '',
                type: $('#schema_type').val() || '',
                key: $('#schema_key').val() || '',
                input: true, // Always true for form components
                validate: {
                    required: $('#schema_required').is(':checked')
                }
            };

            // Add options for select, checkbox, and radio components
            const type = $('#schema_type').val();
            if (type === 'select' || type === 'radio') {
                const options = [];
                $('.option-item').each(function() {
                    const label = $(this).find('.option-label').val();
                    const value = $(this).find('.option-value').val();
                    if (label && value) {
                        options.push({
                            label: label,
                            value: value
                        });
                    }
                });

                if (options.length > 0) {
                    if (type === 'radio') {
                        schema.values = options;
                    } else {
                        schema.data = {
                            values: options
                        };
                    }
                }
            }

            $('#schema-preview').text(JSON.stringify(schema, null, 2));
            $('#schema').val(JSON.stringify(schema));

            // Update hidden options input
            const options = [];
            $('.option-item').each(function() {
                const label = $(this).find('.option-label').val();
                const value = $(this).find('.option-value').val();
                if (label && value) {
                    options.push({
                        label: label,
                        value: value
                    });
                }
            });
            $('#options').val(JSON.stringify(options));
        }

        $(document).ready(function() {
            // Update preview when any field changes
            $('#schema_label, #schema_type, #schema_key, #schema_input, #schema_required').on('input change',
                updateSchemaPreview);

            // Show/hide options container based on component type
            $('#schema_type').on('change', function() {
                const type = $(this).val();
                if (type === 'select' || type === 'radio') {
                    $('#options-container').show();
                    // Add initial option if none exist
                    if ($('#options-list').children().length === 0) {
                        addOption();
                    }
                } else {
                    $('#options-container').hide();
                }
                updateSchemaPreview();
            });

            // Add option button
            $('#add-option').on('click', function() {
                addOption();
            });

            // Function to add a new option
            function addOption() {
                const optionId = Date.now(); // Unique ID for each option
                const optionHtml = `
                    <div class="input-group mb-2 option-item" data-id="${optionId}">
                        <input type="text" class="form-control option-label" placeholder="Label" />
                        <input type="text" class="form-control option-value" placeholder="Value" />
                        <div class="input-group-append">
                            <button class="btn btn-outline-danger remove-option" type="button">&times;</button>
                        </div>
                    </div>
                `;
                $('#options-list').append(optionHtml);

                // Add event for remove button
                $('.remove-option').last().on('click', function() {
                    $(this).closest('.option-item').remove();
                    updateSchemaPreview();
                });

                // Add event for input changes
                $('.option-item').last().find('input').on('input', updateSchemaPreview);
            }

            // Initialize options container visibility
            const initialType = $('#schema_type').val();
            if (initialType === 'select' || initialType === 'radio') {
                $('#options-container').show();
            }

            // Add event for remove buttons on existing options
            $('.remove-option').on('click', function() {
                $(this).closest('.option-item').remove();
                updateSchemaPreview();
            });

            // Add event for input changes on existing options
            $('.option-item').find('input').on('input', updateSchemaPreview);

            // Initialize preview
            updateSchemaPreview();
        });
    </script>
@endsection
