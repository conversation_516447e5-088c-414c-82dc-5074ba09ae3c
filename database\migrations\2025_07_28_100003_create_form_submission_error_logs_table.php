<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('error_logs', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->unsignedBigInteger('form_submission_id')->nullable();
            $table->unsignedBigInteger('form_submission_sync_id')->nullable();
            $table->enum('error_level', ['info', 'warning', 'error', 'critical']);
            $table->string('error_type', 100);
            $table->text('error_message');
            $table->string('error_code', 50)->nullable();
            $table->longText('stack_trace')->nullable();
            $table->json('context_data')->nullable();
            $table->boolean('resolved')->default(false);
            $table->timestamp('resolved_at')->nullable();
            $table->unsignedBigInteger('resolved_by')->nullable();
            $table->text('resolution_notes')->nullable();
            $table->timestamp('created_at')->useCurrent();

            // Foreign key constraints
            $table->foreign('form_submission_id')->references('id')->on('form_submissions')->onDelete('cascade');
            $table->foreign('form_submission_sync_id')->references('id')->on('form_submission_syncs')->onDelete('cascade');
            $table->foreign('resolved_by')->references('id')->on('users')->onDelete('set null');

            // Indexes for performance
            $table->index('form_submission_id', 'idx_error_logs_submission_id');
            $table->index('form_submission_sync_id', 'idx_error_logs_sync_id');
            $table->index('error_level', 'idx_error_logs_level');
            $table->index('error_type', 'idx_error_logs_type');
            $table->index('resolved', 'idx_error_logs_resolved');
            $table->index('created_at', 'idx_error_logs_created_at');
            $table->index(['error_level', 'resolved'], 'idx_error_logs_level_resolved');
            $table->index(['error_type', 'created_at'], 'idx_error_logs_type_date');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('error_logs');
    }
};
