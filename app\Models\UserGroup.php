<?php

namespace App\Models;

use App\Traits\DataTableFilter;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class UserGroup extends BaseModel
{
    use HasFactory, SoftDeletes, DataTableFilter;

    protected $fillable = [
        'name',
        'description',
        'is_active',
        'tenant_id',
        'updated_by',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];
    public static function dataTableColumns(): array
    {
        return [
            // [
            //     'data' => 'checkbox',
            //     'name' => 'checkbox',
            //     'title' => '<input type="checkbox" id="select-all">',
            //     'orderable' => false,
            //     'searchable' => false,
            //     'filter' => false,
            //     'class' => 'text-center',
            //     'width' => '10',
            // ],
            [
                'data' => 'DT_RowIndex',
                'name' => 'DT_RowIndex',
                'title' => '#',
                'orderable' => false,
                'searchable' => false,
                'filter' => false,
                'width' => '10',
            ],
            [
                'data' => 'name',
                'name' => 'name',
                'title' => __('cruds.fields.name'),
                'filter' => 'text',
            ],
            [
                'data' => 'description',
                'name' => 'description',
                'title' => __('cruds.fields.description'),
                'filter' => 'text',
            ],
            // Uncomment if you want to show users_count
            // [
            //     'data' => 'users_count',
            //     'name' => 'users_count',
            //     'title' => __('cruds.fields.users_count'),
            //     'filter' => false,
            //     'class' => 'text-right w_50',
            // ],
            [
                'data' => 'status',
                'name' => 'is_active',
                'title' => __('cruds.fields.status'),
                'filter' => 'boolean',
                'options' => ['1' => __('Active'), '0' => __('Inactive')],
            ],
            [
                'data' => 'updated_at',
                'name' => 'updated_at',
                'title' => __('cruds.fields.updated_at'),
                'filter' => 'date_range',
                'class' => 'text-right',
            ],
            [
                'data' => 'action',
                'name' => 'action',
                'title' => __('global.actions'),
                'orderable' => false,
                'searchable' => false,
                'filter' => false,
                'width' => '150',
            ],
        ];
    }
    public function users()
    {
        return $this->hasMany(User::class, 'user_group_id');
    }

    public function forms()
    {
        return $this->belongsToMany(Form::class, 'form_user_group');
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
