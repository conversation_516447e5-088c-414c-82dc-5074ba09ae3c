/**
 * Hungarian translation for bootstrap-wysihtml5
 */
(function($){
  $.fn.wysihtml5.locale["hu-HU"] = {
    font_styles: {
      normal: "Szövegtörzs",
      h1: "<PERSON>őcím",
      h2: "<PERSON><PERSON><PERSON><PERSON>",
      h3: "Harmadrangú alcím",
      h4: "Negyedrangú alcím",
      h5: "Ötödrangú alcím",
      h6: "Hatodrangú alcím"
    },
    emphasis: {
      bold: "Vastag",
      italic: "Dölt",
      underline: "Aláh<PERSON>zott"
    },
    lists: {
      unordered: "Pontozott lista",
      ordered: "Számozott lista",
      outdent: "Behúzás növelése",
      indent: "<PERSON><PERSON><PERSON><PERSON><PERSON> csökkentése"
    },
    link: {
      insert: "Hivatkozás beszúrása",
      cancel: "Mégsem",
      target: "Hivatkozás megnyitása új ablakban"
    },
    image: {
      insert: "<PERSON><PERSON><PERSON> be<PERSON>ú<PERSON>",
      cancel: "<PERSON>égs<PERSON>"
    },
    html: {
      edit: "HTML szerkesztése"
    },
    colours: {
      black: "<PERSON>ket<PERSON>",
      silver: "<PERSON>züst",
      gray: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
      maroon: "Gesztenyebarna",
      red: "Piros",
      purple: "Lila",
      green: "Zöld",
      olive: "Olajzöld",
      navy: "Tengerkék",
      blue: "Kék",
      orange: "Narancs"
    }
  };
}(jQuery));
