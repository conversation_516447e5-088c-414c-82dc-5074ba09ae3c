@extends('layouts.admin')
@section('pageTitle', trans('cruds.form.title'))

@section('content')

    <div class="card card-default">
        <div class="card-header separator">
            <div class="card-title mainheading">
                <h4>{{ trans('cruds.form.title') }} {{ trans('global.list') }}</h4>
            </div>
            <div class="card-controls">
                <ul>
                    @if (auth()->user()->isAdmin() || auth()->user()->isSuperAdmin())
                        <li>

                            {{-- <x-secondary-button id="standard_form_trigger" :url="__('javascript:void(0)')" :text="__('cruds.fields.standard') . ' ' . __('cruds.form.title')" /> --}}

                            <x-add-button :url="route('admin.forms.create')" :text="__('cruds.form.title_singular')" />

                        </li>
                    @endif
                </ul>
            </div>
        </div>

        <div class="card-block">
            <x-datatable id="forms-table" :columns="$columns" ajax="{{ route('admin.forms.index') }}" :order="[[6, 'desc']]" />
        </div>

    </div>

@endsection
@section('scripts')
    <div class="modal modal-large fade " id="standard_form_modal" role="dialog">
        <div class="modal-dialog">
            <div class="modal-content-wrapper">
                <div class="modal-content">
                    {{-- <div class="modal-header clearfix text-left"> --}}
                        <button aria-label="" type="button" class="close" data-dismiss="modal" aria-hidden="true"><i
                                class="fa fa-close"></i>
                        </button>

                    {{-- </div> --}}
                    <div class="modal-body">

                        {{-- <h4>{{ trans('cruds.form.standard_title') }} {{ trans('global.list') }}</h4> --}}

                        <iframe width="100%" height="500" src="{{ route('admin.forms.getStandardForms') }}"
                            noborders=true></iframe>



                    </div>
                </div>
            </div>

        </div>
    </div>
    <script>
        $(function() {
            $("#standard_form_trigger").click(function() {
                $("#standard_form_modal").modal('show');
            })
        })
    </script>
@endsection
