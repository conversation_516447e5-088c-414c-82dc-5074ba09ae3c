@extends('layouts.admin')
@section('pageTitle', __('cruds.integrationConfiguration.title'))

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card card-default">
                <div class="card-header separator">
                    <div class="card-title mainheading">
                        <h4>{{ __('cruds.integrationConfiguration.title') }}</h4>
                    </div>
                    <div class="card-controls">
                        <ul>
                            <li>
                                <x-buttons.add :route="route('admin.integration-configurations.create')" />
                            </li>
                        </ul>
                    </div>
                </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-striped table-hover datatable datatable-IntegrationConfiguration">
                <thead>
                    <tr>
                        <th width="10">

                        </th>
                        <th>
                            {{ __('cruds.integrationConfiguration.fields.name') }}
                        </th>
                        <th>
                            {{ __('cruds.integrationConfiguration.fields.external_system') }}
                        </th>
                        <th>
                            {{ __('cruds.integrationConfiguration.fields.integration_method') }}
                        </th>
                        <th>
                            {{ __('cruds.integrationConfiguration.fields.process_type') }}
                        </th>
                        <th>
                            {{ __('cruds.integrationConfiguration.fields.endpoint_url') }}
                        </th>
                        <th>
                            {{ __('cruds.integrationConfiguration.fields.is_active') }}
                        </th>
                        <th>
                            {{ __('global.created_by') }}
                        </th>
                        <th class="text-end">
                            {{ __('global.created_at') }}
                        </th>
                        <th class="text-end">
                            {{ __('global.updated_at') }}
                        </th>
                        <th>
                            {{ __('global.actions') }}
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($integrations as $key => $integration)
                        <tr data-entry-id="{{ $integration->id }}">
                            <td>

                            </td>
                            <td>
                                {{ $integration->name ?? '' }}
                            </td>
                            <td>
                                {{ $integration->external_system_name }}
                            </td>
                            <td>
                                {{ $integration->integration_method_name }}
                            </td>
                            <td>
                                {{ $integration->process_type_name }}
                            </td>
                            <td>
                                <span title="{{ $integration->endpoint_url }}">
                                    {{ Str::limit($integration->endpoint_url, 50) }}
                                </span>
                            </td>
                            <td>
                                <span class="badge badge-{{ $integration->is_active ? 'success' : 'danger' }}">
                                    {{ $integration->is_active ? 'Active' : 'Inactive' }}
                                </span>
                            </td>
                            <td>
                                {{ $integration->createdBy->name ?? 'System' }}
                            </td>
                            <td class="text-end">
                                {{ $integration->created_at->format('M d, Y \a\t H:i') }}
                            </td>
                            <td class="text-end">
                                {{ $integration->updated_at->format('M d, Y \a\t H:i') }}
                            </td>
                            <td>
                                @include('admin.integration-configurations.partials.actions', ['row' => $integration])
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
@parent
<script>
    $(function () {
        let dtButtons = $.extend(true, [], $.fn.dataTable.defaults.buttons)

        $.extend($.fn.dataTable.defaults, {
            orderCellsTop: true,
            order: [[ 1, 'desc' ]],
            pageLength: 25,
            dom: "<'row'<'col-sm-6'l><'col-sm-6'f>>" +
                 "<'row'<'col-sm-12'tr>>" +
                 "<'row'<'col-sm-12'p>>",
            language: {
                search: "",
                searchPlaceholder: "Search..."
            }
        });
        // Custom DataTables initialization
        let table = $('.datatable-IntegrationConfiguration:not(.ajaxTable)').DataTable({
            dom: "<'row'<'col-sm-6'l><'col-sm-6'f>>" +
                 "<'row'<'col-sm-12'tr>>" +
                 "<'row'<'col-sm-12'p>>",
            buttons: dtButtons,
            columnDefs: [
                { targets: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9], searchable: true, orderable: true }
            ],
            language: {
                search: "",
                searchPlaceholder: "Search..."
            },
            initComplete: function() {
                // Ensure search box is on the right
                $('.dataTables_filter').addClass('text-right');
            }
        });
        
        // Force redraw to ensure layout is applied
        table.draw(false);
        $('a[data-toggle="tab"]').on('shown.bs.tab click', function(e){
            $($.fn.dataTable.tables(true)).DataTable()
                .columns.adjust();
        });

    })
</script>
@endsection
