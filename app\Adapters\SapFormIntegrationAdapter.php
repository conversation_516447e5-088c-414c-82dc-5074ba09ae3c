<?php

namespace App\Adapters;

use App\Models\FormIntegrationSetting;

class SapFormIntegrationAdapter extends BaseFormIntegrationAdapter
{
    /**
     * Get the target system this adapter supports
     */
    public function getSupportedTarget(): string
    {
        return 'SAP';
    }

    /**
     * Get supported process types for this target system
     */
    public function getSupportedProcesses(): array
    {
        return [
            \App\Models\EndpointConfiguration::PROCESS_MISC_ISSUE,
            \App\Models\EndpointConfiguration::PROCESS_MISC_RECEIPT,
            \App\Models\EndpointConfiguration::PROCESS_QUANTITY_MOVE,
            \App\Models\EndpointConfiguration::PROCESS_PO_RECEIPT
        ];
    }

    /**
     * Apply SAP-specific transformations
     */
    protected function applyTargetSpecificTransformations(array $transformedData, FormIntegrationSetting $integrationSetting): array
    {
        $processType = $integrationSetting->endpointConfiguration->process_selection;

        switch ($processType) {
            case \App\Models\EndpointConfiguration::PROCESS_MISC_ISSUE:
                return $this->transformForMiscIssue($transformedData, $integrationSetting);

            case \App\Models\EndpointConfiguration::PROCESS_MISC_RECEIPT:
                return $this->transformForMiscReceipt($transformedData, $integrationSetting);

            case \App\Models\EndpointConfiguration::PROCESS_QUANTITY_MOVE:
                return $this->transformForQuantityMove($transformedData, $integrationSetting);
                
            case \App\Models\EndpointConfiguration::PROCESS_PO_RECEIPT:
                return $this->transformForPOReceipt($transformedData, $integrationSetting);

            default:
                return $transformedData;
        }
    }

    /**
     * Apply SAP-specific validation rules
     */
    protected function validateTargetSpecificRules($value, array $fieldDefinition, string $formField): array
    {
        $errors = [];
        $fieldName = $fieldDefinition['name'] ?? 'unknown';

        // SAP-specific validation rules
        switch ($fieldName) {
            case 'material_number':
                if (!empty($value) && !preg_match('/^[A-Z0-9]{1,18}$/', $value)) {
                    $errors[] = "SAP Material Number '{$fieldName}' must be alphanumeric and max 18 characters.";
                }
                break;

            case 'plant_code':
                if (!empty($value) && !preg_match('/^[A-Z0-9]{4}$/', $value)) {
                    $errors[] = "SAP Plant Code '{$fieldName}' must be exactly 4 alphanumeric characters.";
                }
                break;

            case 'storage_location':
                if (!empty($value) && !preg_match('/^[A-Z0-9]{1,4}$/', $value)) {
                    $errors[] = "SAP Storage Location '{$fieldName}' must be 1-4 alphanumeric characters.";
                }
                break;

            case 'movement_type':
                $validMovementTypes = ['261', '262', '311', '312', '501', '502'];
                if (!empty($value) && !in_array($value, $validMovementTypes)) {
                    $errors[] = "SAP Movement Type '{$fieldName}' must be one of: " . implode(', ', $validMovementTypes);
                }
                break;

            case 'quantity':
                if (!empty($value) && $value <= 0) {
                    $errors[] = "SAP Quantity '{$fieldName}' must be greater than zero.";
                }
                break;
        }

        return $errors;
    }

    /**
     * Apply SAP-specific API preparation
     */
    protected function applyTargetSpecificApiPreparation(array $apiData, FormIntegrationSetting $integrationSetting): array
    {
        // Add SAP-specific headers or data structure modifications
        $apiData['headers'] = [
            'Content-Type' => 'application/json',
            'X-SAP-Version' => '2.0',
            'X-Integration-Source' => 'Form-Integration-System',
            'Accept' => 'application/json'
        ];

        // Add SAP-specific authentication
        $apiData['auth'] = [
            'type' => 'oauth2', // or whatever SAP uses
            'credentials' => config('sap.api_credentials', [])
        ];

        // Wrap data in SAP-specific envelope if needed
        $apiData['data'] = [
            'MessageHeader' => [
                'ID' => uniqid('SAP_'),
                'TimeStamp' => now()->toISOString(),
                'SenderID' => config('sap.sender_id', 'FORM_INTEGRATION')
            ],
            'MessageBody' => $apiData['data']
        ];

        return $apiData;
    }

    /**
     * Transform data for Misc Issue process
     */
    private function transformForMiscIssue(array $data, FormIntegrationSetting $integrationSetting): array
    {
        // SAP Misc Issue specific transformations
        if (isset($data['quantity'])) {
            $data['quantity'] = abs($data['quantity']); // Ensure positive
        }

        // Set SAP-specific movement type for misc issue
        $data['movement_type'] = $data['movement_type'] ?? '261'; // Goods issue
        $data['document_type'] = $data['document_type'] ?? 'WA'; // Goods movement

        // Add SAP-specific fields
        $data['posting_date'] = $data['posting_date'] ?? now()->format('Y-m-d');
        $data['document_date'] = $data['document_date'] ?? now()->format('Y-m-d');

        return $data;
    }

    /**
     * Transform data for Misc Receipt process
     */
    private function transformForMiscReceipt(array $data, FormIntegrationSetting $integrationSetting): array
    {
        // SAP Misc Receipt specific transformations
        if (isset($data['quantity'])) {
            $data['quantity'] = abs($data['quantity']); // Ensure positive
        }

        // Set SAP-specific movement type for misc receipt
        $data['movement_type'] = $data['movement_type'] ?? '501'; // Goods receipt without PO
        $data['document_type'] = $data['document_type'] ?? 'WE'; // Goods receipt

        // Add SAP-specific fields
        $data['posting_date'] = $data['posting_date'] ?? now()->format('Y-m-d');
        $data['document_date'] = $data['document_date'] ?? now()->format('Y-m-d');

        return $data;
    }

    /**
     * Transform data for Quantity Move process
     */
    private function transformForQuantityMove(array $data, FormIntegrationSetting $integrationSetting): array
    {
        // SAP Quantity Move specific transformations
        if (isset($data['quantity'])) {
            $data['quantity'] = abs($data['quantity']); // Ensure positive
        }

        // Set SAP-specific movement type for transfer
        $data['movement_type'] = $data['movement_type'] ?? '311'; // Transfer posting
        $data['document_type'] = $data['document_type'] ?? 'WL'; // Transfer

        // Add SAP-specific fields
        $data['posting_date'] = $data['posting_date'] ?? now()->format('Y-m-d');
        $data['document_date'] = $data['document_date'] ?? now()->format('Y-m-d');

        // Ensure receiving plant/location are set for transfers
        if (isset($data['to_location'])) {
            $data['receiving_storage_location'] = $data['to_location'];
        }
        if (isset($data['to_plant'])) {
            $data['receiving_plant'] = $data['to_plant'];
        }

        return $data;
    }
}
