<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;

class FieldMappingSuggestionService
{
    /**
     * Generate intelligent field mapping suggestions
     */
    public function generateSuggestions(array $formFields, array $endpointFields): array
    {
        $suggestions = [];
        $usedEndpointFields = [];

        // Sort form fields by priority (required fields first)
        $sortedFormFields = $this->sortFieldsByPriority($formFields);

        foreach ($sortedFormFields as $formField) {
            $bestMatch = $this->findBestMatch($formField, $endpointFields, $usedEndpointFields);
            
            if ($bestMatch) {
                $suggestions[$formField['key']] = $bestMatch['field'];
                $usedEndpointFields[] = $bestMatch['field'];
                
                Log::info('Field mapping suggestion', [
                    'form_field' => $formField['key'],
                    'endpoint_field' => $bestMatch['field'],
                    'confidence' => $bestMatch['confidence'],
                    'reason' => $bestMatch['reason']
                ]);
            }
        }

        return $suggestions;
    }

    /**
     * Find the best matching endpoint field for a form field
     */
    private function findBestMatch(array $formField, array $endpointFields, array $usedFields): ?array
    {
        $bestMatch = null;
        $highestConfidence = 0;

        foreach ($endpointFields as $endpointField) {
            $endpointName = $endpointField['name'] ?? '';
            
            // Skip already used fields
            if (in_array($endpointName, $usedFields)) {
                continue;
            }

            $match = $this->calculateMatchConfidence($formField, $endpointField);
            
            if ($match['confidence'] > $highestConfidence) {
                $highestConfidence = $match['confidence'];
                $bestMatch = [
                    'field' => $endpointName,
                    'confidence' => $match['confidence'],
                    'reason' => $match['reason']
                ];
            }
        }

        // Only return matches with confidence above threshold
        return ($highestConfidence >= 0.6) ? $bestMatch : null;
    }

    /**
     * Calculate match confidence between form and endpoint fields
     */
    private function calculateMatchConfidence(array $formField, array $endpointField): array
    {
        $formKey = strtolower($formField['key'] ?? '');
        $formLabel = strtolower($formField['label'] ?? '');
        $formType = strtolower($formField['type'] ?? '');
        
        $endpointName = strtolower($endpointField['name'] ?? '');
        $endpointDesc = strtolower($endpointField['description'] ?? '');
        $endpointType = strtolower($endpointField['datatype'] ?? '');

        $confidence = 0;
        $reasons = [];

        // 1. Exact name match (highest confidence)
        if ($formKey === $endpointName) {
            return ['confidence' => 1.0, 'reason' => 'Exact field name match'];
        }

        // 2. Exact label to name match
        if ($formLabel === $endpointName) {
            return ['confidence' => 0.95, 'reason' => 'Exact label to name match'];
        }

        // 3. Semantic field mapping (high confidence)
        $semanticMatch = $this->getSemanticMatch($formKey, $formLabel, $endpointName, $endpointDesc);
        if ($semanticMatch['confidence'] > 0) {
            $confidence = max($confidence, $semanticMatch['confidence']);
            $reasons[] = $semanticMatch['reason'];
        }

        // 4. Fuzzy string matching
        $fuzzyMatch = $this->getFuzzyMatch($formKey, $formLabel, $endpointName, $endpointDesc);
        if ($fuzzyMatch['confidence'] > 0) {
            $confidence = max($confidence, $fuzzyMatch['confidence']);
            $reasons[] = $fuzzyMatch['reason'];
        }

        // 5. Data type compatibility bonus
        $typeBonus = $this->getTypeCompatibilityBonus($formType, $endpointType);
        if ($typeBonus > 0) {
            $confidence += $typeBonus;
            $reasons[] = 'Compatible data types';
        }

        // 6. Business context matching
        $contextMatch = $this->getBusinessContextMatch($formField, $endpointField);
        if ($contextMatch['confidence'] > 0) {
            $confidence = max($confidence, $contextMatch['confidence']);
            $reasons[] = $contextMatch['reason'];
        }

        return [
            'confidence' => min($confidence, 1.0), // Cap at 1.0
            'reason' => implode(', ', $reasons) ?: 'No clear match'
        ];
    }

    /**
     * Get semantic field matching based on business meaning
     */
    private function getSemanticMatch(string $formKey, string $formLabel, string $endpointName, string $endpointDesc): array
    {
        $semanticMappings = [
            // Document identifiers
            'document_number' => ['docnum', 'documentnumber', 'document_id', 'doc_num'],
            'purchase_order_number' => ['purchaseordernumber', 'po_number', 'po_num', 'ponumber'],
            'reference_number' => ['referencenumber', 'ref_number', 'ref_num', 'reference'],
            
            // Dates
            'order_date' => ['orderdate', 'docdate', 'document_date'],
            'transaction_date' => ['transactiondate', 'posting_date', 'postingdate'],
            'due_date' => ['duedate', 'delivery_date', 'deliverydate'],
            'expected_delivery_date' => ['expecteddeliverydate', 'delivery_date', 'expected_date'],
            
            // Business partners
            'vendor_code' => ['vendorcode', 'supplier_code', 'cardcode', 'bp_code'],
            'customer_code' => ['customercode', 'client_code', 'cardcode', 'bp_code'],
            'business_partner_code' => ['businesspartnercode', 'cardcode', 'bp_code'],
            
            // Items and products
            'item_code' => ['itemcode', 'product_code', 'material_number', 'sku'],
            'item_description' => ['itemdescription', 'product_description', 'description'],
            
            // Quantities and measurements
            'quantity' => ['qty', 'amount', 'count', 'quantityordered'],
            'quantity_ordered' => ['quantityordered', 'order_qty', 'qty_ordered'],
            'unit_price' => ['unitprice', 'price', 'rate', 'cost'],
            'total_amount' => ['totalamount', 'total_value', 'line_total', 'amount'],
            'unit_of_measure' => ['unitofmeasure', 'uom', 'unit', 'measure_unit'],
            
            // Locations
            'warehouse_code' => ['warehousecode', 'whscode', 'location_code', 'storage_location'],
            'bin_location' => ['binlocation', 'bin_code', 'storage_bin'],
            
            // Financial
            'currency_code' => ['currencycode', 'currency', 'curr_code'],
            'payment_terms' => ['paymentterms', 'payment_method', 'terms'],
            'cost_center' => ['costcenter', 'cost_centre', 'cc_code'],
            'gl_account' => ['glaccount', 'account_code', 'ledger_account'],
            
            // Inventory specific
            'transaction_type' => ['transactiontype', 'trans_type', 'movement_type'],
            'batch_number' => ['batchnumber', 'batch_num', 'lot_number', 'lot_num'],
            'serial_number' => ['serialnumber', 'serial_num', 'serial'],
            
            // Line items
            'line_number' => ['linenumber', 'line_num', 'sequence', 'line_seq'],
            
            // Status and control
            'status' => ['doc_status', 'document_status', 'state'],
            'comments' => ['remarks', 'notes', 'comment', 'description']
        ];

        foreach ($semanticMappings as $concept => $variations) {
            if ($this->containsConcept($formKey, $formLabel, $concept)) {
                foreach ($variations as $variation) {
                    if ($this->containsConcept($endpointName, $endpointDesc, $variation)) {
                        return [
                            'confidence' => 0.9,
                            'reason' => "Semantic match: {$concept}"
                        ];
                    }
                }
            }
        }

        return ['confidence' => 0, 'reason' => ''];
    }

    /**
     * Check if a field contains a specific concept
     */
    private function containsConcept(string $field1, string $field2, string $concept): bool
    {
        $concept = strtolower($concept);
        $field1 = strtolower($field1);
        $field2 = strtolower($field2);
        
        return strpos($field1, $concept) !== false || 
               strpos($field2, $concept) !== false ||
               $this->normalizeFieldName($field1) === $this->normalizeFieldName($concept) ||
               $this->normalizeFieldName($field2) === $this->normalizeFieldName($concept);
    }

    /**
     * Normalize field names for comparison
     */
    private function normalizeFieldName(string $fieldName): string
    {
        // Remove common prefixes/suffixes and separators
        $normalized = strtolower($fieldName);
        $normalized = preg_replace('/[_\-\s]+/', '', $normalized);
        $normalized = preg_replace('/^(doc|document|field|item|line)/', '', $normalized);
        $normalized = preg_replace('/(code|number|num|id|date)$/', '', $normalized);
        
        return $normalized;
    }

    /**
     * Get fuzzy string matching confidence
     */
    private function getFuzzyMatch(string $formKey, string $formLabel, string $endpointName, string $endpointDesc): array
    {
        $similarities = [
            $this->calculateSimilarity($formKey, $endpointName),
            $this->calculateSimilarity($formLabel, $endpointName),
            $this->calculateSimilarity($formKey, $endpointDesc),
            $this->calculateSimilarity($formLabel, $endpointDesc)
        ];

        $maxSimilarity = max($similarities);
        
        if ($maxSimilarity >= 0.8) {
            return ['confidence' => $maxSimilarity * 0.8, 'reason' => 'High string similarity'];
        } elseif ($maxSimilarity >= 0.6) {
            return ['confidence' => $maxSimilarity * 0.6, 'reason' => 'Moderate string similarity'];
        }

        return ['confidence' => 0, 'reason' => ''];
    }

    /**
     * Calculate string similarity using Levenshtein distance
     */
    private function calculateSimilarity(string $str1, string $str2): float
    {
        if (empty($str1) || empty($str2)) {
            return 0;
        }

        $str1 = strtolower($str1);
        $str2 = strtolower($str2);

        $maxLen = max(strlen($str1), strlen($str2));
        if ($maxLen === 0) {
            return 1;
        }

        $distance = levenshtein($str1, $str2);
        return 1 - ($distance / $maxLen);
    }

    /**
     * Get data type compatibility bonus
     */
    private function getTypeCompatibilityBonus(string $formType, string $endpointType): float
    {
        $typeMapping = [
            'textfield' => ['string', 'text', 'varchar', 'char'],
            'number' => ['integer', 'int', 'decimal', 'float', 'double', 'numeric'],
            'datetime' => ['date', 'datetime', 'timestamp'],
            'select' => ['string', 'text', 'varchar', 'enum'],
            'textarea' => ['string', 'text', 'longtext']
        ];

        if (isset($typeMapping[$formType])) {
            foreach ($typeMapping[$formType] as $compatibleType) {
                if (strpos($endpointType, $compatibleType) !== false) {
                    return 0.1; // Small bonus for type compatibility
                }
            }
        }

        return 0;
    }

    /**
     * Get business context matching
     */
    private function getBusinessContextMatch(array $formField, array $endpointField): array
    {
        // Check if both fields are required (business critical)
        $formRequired = $formField['required'] ?? false;
        $endpointRequired = $endpointField['required'] ?? false;

        if ($formRequired && $endpointRequired) {
            // Both required fields get priority matching
            $contextBonus = 0.1;
        } else {
            $contextBonus = 0;
        }

        return ['confidence' => $contextBonus, 'reason' => 'Business context match'];
    }

    /**
     * Sort fields by priority (required fields first, then by name)
     */
    private function sortFieldsByPriority(array $formFields): array
    {
        usort($formFields, function ($a, $b) {
            $aRequired = $a['required'] ?? false;
            $bRequired = $b['required'] ?? false;

            if ($aRequired && !$bRequired) {
                return -1;
            } elseif (!$aRequired && $bRequired) {
                return 1;
            }

            return strcmp($a['key'] ?? '', $b['key'] ?? '');
        });

        return $formFields;
    }
}
