
<?php $__env->startSection('pageTitle', __('global.create') . ' ' . __('cruds.fieldMappingConfiguration.title_singular')); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .required:after {
        content: " *";
        color: red;
    }
    .help-block {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 0.25rem;
    }
    .form-group {
        margin-bottom: 1.5rem;
    }
    .card-header h4 {
        margin: 0;
        color: #495057;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <div class="card card-default">
        <div class="card-header separator">
            <div class="card-title mainheading">
                <h4><?php echo e(__('global.create')); ?> <?php echo e(__('cruds.fieldMappingConfiguration.title_singular')); ?></h4>
            </div>
            <div class="card-controls">
                <ul>
                    <li>
                        <?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.buttons.back','data' => ['route' => route('admin.field-mapping-configurations.index'),'text' => __('global.back_to_list')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('buttons.back'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['route' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('admin.field-mapping-configurations.index')),'text' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(__('global.back_to_list'))]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>
                    </li>
                </ul>
            </div>
        </div>
        <div class="card-block">
            <?php if($errors->any()): ?>
                <div class="alert alert-danger">
                    <ul>
                        <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li><?php echo e($error); ?></li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                </div>
            <?php endif; ?>

            <form method="POST" action="<?php echo e(route('admin.field-mapping-configurations.store')); ?>" id="field-mapping-form">
                <?php echo csrf_field(); ?>

                <div class="form-group row">
                    <label for="name" class="col-md-2 col-form-label required"><?php echo e(__('cruds.fieldMappingConfiguration.fields.name')); ?></label>
                    <div class="col-md-4">
                        <input type="text" class="form-control <?php echo e($errors->has('name') ? 'is-invalid' : ''); ?>" 
                               id="name" name="name" value="<?php echo e(old('name')); ?>" required>
                        <?php if($errors->has('name')): ?>
                            <div class="invalid-feedback">
                                <?php echo e($errors->first('name')); ?>

                            </div>
                        <?php endif; ?>
                    </div>
                    <label for="is_active" class="col-md-2 col-form-label required"><?php echo e(trans('cruds.fields.status')); ?></label>
                    <div class="col-md-4">
                        <div class="form-check mt-2">
                            <input type="checkbox" class="form-check-input" id="is_active" name="is_active" value="1"
                                <?php echo e(old('is_active', true) ? 'checked' : ''); ?>>
                            <label class="form-check-label" for="is_active">
                                <?php echo e(trans('cruds.fields.active')); ?>

                            </label>
                        </div>
                        <?php if($errors->has('is_active')): ?>
                            <div class="invalid-feedback d-block">
                                <?php echo e($errors->first('is_active')); ?>

                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="form-group row">
                    <label for="description" class="col-md-2 col-form-label"><?php echo e(__('cruds.fieldMappingConfiguration.fields.description')); ?></label>
                    <div class="col-md-4">
                        <textarea class="form-control <?php echo e($errors->has('description') ? 'is-invalid' : ''); ?>" 
                                 id="description" name="description" rows="2"><?php echo e(old('description')); ?></textarea>
                        <?php if($errors->has('description')): ?>
                            <div class="invalid-feedback">
                                <?php echo e($errors->first('description')); ?>

                            </div>
                        <?php endif; ?>
                    </div>
                    <label for="form_id" class="col-md-2 col-form-label required"><?php echo e(__('cruds.fieldMappingConfiguration.fields.form')); ?></label>
                    <div class="col-md-4">
                        <select class="form-control <?php echo e($errors->has('form_id') ? 'is-invalid' : ''); ?>" 
                                id="form_id" name="form_id" required>
                            <option value=""><?php echo e(__('global.pleaseSelect')); ?></option>
                            <?php $__currentLoopData = $forms; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $form): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($form->id); ?>" <?php echo e(old('form_id') == $form->id ? 'selected' : ''); ?>>
                                    <?php echo e($form->title); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <?php if($errors->has('form_id')): ?>
                            <div class="invalid-feedback">
                                <?php echo e($errors->first('form_id')); ?>

                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="form-group row">
                    <label for="integration_configuration_id" class="col-md-2 col-form-label required"><?php echo e(__('cruds.fieldMappingConfiguration.fields.integration_configuration_id')); ?></label>
                    <div class="col-md-10">
                        <div class="input-group">
                            <input type="hidden" id="integration_configuration_id" name="integration_configuration_id" value="<?php echo e(old('integration_configuration_id')); ?>">
                            <input type="text" class="form-control" id="integration_configuration_display" readonly 
                                   value="<?php echo e($integrations->firstWhere('id', old('integration_configuration_id')) ? 
                                          $integrations->firstWhere('id', old('integration_configuration_id'))->name . ' (' . 
                                          $integrations->firstWhere('id', old('integration_configuration_id'))->external_system . ' - ' .
                                          $integrations->firstWhere('id', old('integration_configuration_id'))->process_type . ')' : ''); ?>"
                                   placeholder="<?php echo e(__('global.clickToSelect')); ?>">
                            <div class="input-group-append">
                                <button class="btn btn-outline-secondary" type="button" id="selectEndpointBtn">
                                    <i class="fa fa-search"></i>
                                </button>
                                <button class="btn btn-outline-danger" type="button" id="clearEndpointBtn" style="display: <?php echo e(old('integration_configuration_id') ? 'block' : 'none'); ?>">
                                    <i class="fa fa-times"></i>
                                </button>
                            </div>
                        </div>
                        <?php if($errors->has('integration_configuration_id')): ?>
                            <div class="invalid-feedback d-block">
                                <?php echo e($errors->first('integration_configuration_id')); ?>

                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="form-group row">
                    <label for="state" class="col-md-2 col-form-label">
                        <?php echo e(__('cruds.fieldMappingConfiguration.fields.state')); ?>

                        <i class="fa fa-info-circle text-primary ms-1" data-bs-toggle="tooltip" title="<?php echo e(__('cruds.fieldMappingConfiguration.fields.state_helper')); ?>"></i>
                    </label>
                    <div class="col-md-4">
                        <select name="state" id="state" class="form-control">
                            <?php $__currentLoopData = \App\Models\FieldMappingConfiguration::getStateOptions(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($value); ?>" <?php echo e(old('state', 'draft') == $value ? 'selected' : ''); ?>>
                                    <?php echo e($label); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <?php if($errors->has('state')): ?>
                            <div class="invalid-feedback d-block">
                                <?php echo e($errors->first('state')); ?>

                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- <div class="form-group row mb-3">
                    <div class="col-12">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="use_form_fields_as_endpoint" name="use_form_fields_as_endpoint">
                            <label class="form-check-label" for="use_form_fields_as_endpoint">
                                <?php echo e(__('Use default JSON format')); ?>

                            </label>
                            <small class="form-text text-muted d-block">
                                <?php echo e(__('When enabled, form field keys will be used as the endpoint field names.')); ?>

                            </small>
                        </div>
                    </div>
                </div> -->

                <div class="form-group row">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center">
                            <label class="form-label required"><?php echo e(__('cruds.fieldMappingConfiguration.fields.field_mappings')); ?></label>
                            <button type="button" id="add-custom-field" class="btn btn-sm btn-outline-secondary">
                                <i class="fa fa-plus me-1"></i> <?php echo e(__('Add Endpoint Field')); ?>

                            </button>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <div class="row align-items-center">
                                    <div class="col">
                                        <!-- <h6 class="mb-0"><?php echo e(__('cruds.formIntegrationSetting.fields.field_mappings')); ?></h6> -->
                                    </div>
                                    <div class="col-auto">
                                        <!-- <?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.buttons.button','data' => ['type' => 'button','id' => 'import-json-btn','variant' => 'outline-secondary','size' => 'sm','icon' => 'file-import','class' => 'mr-2','title' => __('global.import_fields_from_json'),'style' => 'display: none;']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('buttons.button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'button','id' => 'import-json-btn','variant' => 'outline-secondary','size' => 'sm','icon' => 'file-import','class' => 'mr-2','title' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(__('global.import_fields_from_json')),'style' => 'display: none;']); ?>
                                            <?php echo e(__('global.import')); ?> JSON
                                         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?> -->
                                        <button type="button" 
                                                id="suggest-mappings-btn" 
                                                class="btn btn-sm btn-outline-primary"
                                                style="display: none;">
                                            <i class="fa fa-magic me-1"></i> <?php echo e(__('global.suggest_mapping')); ?>

                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="field-mappings-container">
                                    <div class="text-center text-muted py-4">
                                        <i class="fa fa-info-circle fa-2x mb-2"></i>
                                        <p><?php echo e(__('cruds.fieldMappingConfiguration.message.select_form_and_endpoint_to_configure_mappings')); ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <input type="hidden" name="field_mappings" id="field_mappings" value="<?php echo e(old('field_mappings', '')); ?>">
                        <?php if($errors->has('field_mappings')): ?>
                            <div class="invalid-feedback d-block">
                                <?php echo e($errors->first('field_mappings')); ?>

                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="form-group row">
                    <div class="col-md-8 offset-md-4 text-end">
                        <?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.buttons.cancel','data' => ['route' => route('admin.field-mapping-configurations.index'),'class' => 'me-2']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('buttons.cancel'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['route' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('admin.field-mapping-configurations.index')),'class' => 'me-2']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>
                        <?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.buttons.save','data' => ['route' => route('admin.field-mapping-configurations.store'),'class' => 'ms-2']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('buttons.save'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['route' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('admin.field-mapping-configurations.store')),'class' => 'ms-2']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>
                    </div>
                </div>
            </form>
            
            <!-- Custom Dialog for Endpoint Field Input -->
            <div id="customDialog" style="display: none; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 20px; border-radius: 5px; box-shadow: 0 0 10px rgba(0,0,0,0.3); z-index: 10000;">
                <div style="margin-bottom: 15px;">
                    <h5 id="endpointFieldLabel">Add Endpoint Field</h5>
                </div>
                <div class="form-group" style="margin-bottom: 15px;">
                    <label for="newEndpointFieldName">Field Name:</label>
                    <input type="text" class="form-control" id="newEndpointFieldName" style="width: 100%; padding: 5px; margin-top: 5px;">
                    <div class="invalid-feedback" id="fieldNameError"></div>
                </div>
                <div style="text-align: right;">
                    <button type="button" class="btn btn-secondary" id="cancelDialog" style="margin-right: 10px;">Cancel</button>
                    <button type="button" class="btn btn-primary" id="saveEndpointField">Add Field</button>
                </div>
            </div>
            <div id="dialogOverlay" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999;"></div>
        </div>
    </div>

    <!-- Transformation Configuration Modal -->
    <div class="modal fade" id="transformConfigModal" tabindex="-1" role="dialog" aria-labelledby="transformConfigModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="transformConfigModalLabel">Configure Transformation</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="transformConfigForm">
                        <!-- Dynamic form fields will be added here -->
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" form="transformConfigForm" class="btn btn-primary">Save Changes</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Endpoint Selection Modal -->
    <div id="endpointSelectionModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; z-index: 1050;">
        <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5);" id="modalBackdrop"></div>
        <div style="position: relative; z-index: 1051; margin: 30px auto; max-width: 900px; padding: 15px;">
            <div class="card">
                <div class="card-header text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><?php echo e(__('global.selectIntegrationConfiguration')); ?></h5>
                    <button type="button" class="btn-close btn-close-white" onclick="closeModal()" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="container-fluid">
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="modal_external_system_filter" class="form-label small text-muted mb-1"><?php echo e(__('cruds.integrationConfiguration.fields.external_system')); ?></label>
                                <select class="form-control form-control-sm" id="modal_external_system_filter">
                                    <option value=""><?php echo e(__('global.all')); ?></option>
                                    <?php $__currentLoopData = \App\Models\IntegrationConfiguration::getExternalSystemOptions(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($key); ?>"><?php echo e($value); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="modal_integration_method_filter" class="form-label small text-muted mb-1"><?php echo e(__('cruds.integrationConfiguration.fields.integration_method')); ?></label>
                                <select class="form-control form-control-sm" id="modal_integration_method_filter">
                                    <option value=""><?php echo e(__('global.all')); ?></option>
                                    <?php $__currentLoopData = \App\Models\IntegrationConfiguration::getIntegrationMethodOptions(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $method): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($method); ?>"><?php echo e($method); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="modal_process_type_filter" class="form-label small text-muted mb-1"><?php echo e(__('cruds.integrationConfiguration.fields.process_type')); ?></label>
                                <select class="form-control form-control-sm" id="modal_process_type_filter">
                                    <option value=""><?php echo e(__('global.all')); ?></option>
                                    <?php $__currentLoopData = \App\Models\IntegrationConfiguration::getProcessTypeOptions(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($type); ?>"><?php echo e($type); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-12">
                                <div class="table-responsive">
                                    <table class="table table-sm table-hover" id="endpointsTable">
                                        <thead>
                                            <tr>
                                                <th><?php echo e(__('cruds.integrationConfiguration.fields.name')); ?></th>
                                                <th><?php echo e(__('cruds.integrationConfiguration.fields.external_system')); ?></th>
                                                <th><?php echo e(__('cruds.integrationConfiguration.fields.process_type')); ?></th>
                                                <th><?php echo e(__('cruds.integrationConfiguration.fields.integration_method')); ?></th>
                                                <th></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php $__currentLoopData = $integrations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $integration): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr data-external-system="<?php echo e($integration->external_system); ?>" 
                                                    data-integration-method="<?php echo e($integration->integration_method); ?>"
                                                    data-process-type="<?php echo e($integration->process_type); ?>">
                                                    <td><?php echo e($integration->name); ?></td>
                                                    <td><?php echo e($integration->external_system_name); ?></td>
                                                    <td><?php echo e($integration->process_type_name); ?></td>
                                                    <td><?php echo e($integration->integration_method_name); ?></td>
                                                    <td class="text-right">
                                                        <button type="button" class="btn btn-sm btn-primary select-endpoint" 
                                                                data-id="<?php echo e($integration->id); ?>"
                                                                data-name="<?php echo e($integration->name); ?>"
                                                                data-external-system="<?php echo e($integration->external_system); ?>"
                                                                data-process-type="<?php echo e($integration->process_type); ?>">
                                                            <?php echo e(__('global.select')); ?>

                                                        </button>
                                                    </td>
                                                </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('global.cancel')); ?></button>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<!-- Field Mapping Transformations -->
<script>
// Debug: Log script loading
console.log('Loading field-mapping-transformations.js');
</script>
<script src="<?php echo e(asset('js/field-mapping-transformations.js')); ?>?v=<?php echo e(time()); ?>" onload="console.log('field-mapping-transformations.js loaded successfully')" onerror="console.error('Failed to load field-mapping-transformations.js')"></script>
<!-- Bootstrap JS Bundle with Popper -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

<!-- Main JavaScript -->
<script>
// Global variables
let formFields = [];
let endpointFields = [];
window.fieldMappings = {}; // Make this global for transformations

// Show placeholder when no fields are loaded
function showFieldMappingPlaceholder() {
    const container = $('#field-mappings-container');
    container.html(`
        <div class="text-center text-muted py-4">
            <i class="fa fa-info-circle fa-2x mb-2"></i>
            <p>Select both form and endpoint configuration to configure field mappings</p>
        </div>
    `);
}

// Display endpoint fields with form field mapping options
function displayEndpointFieldMappings() {
    const container = $('#field-mappings-container');
    
    if (endpointFields.length === 0) {
        container.html(`
            <div class="alert alert-info">
                <i class="fa fa-info-circle"></i> No endpoint fields available. Please select an endpoint configuration first.
            </div>
        `);
        return;
    }
    
    let html = `
        <table class="table table-bordered table-striped">
            <thead class="thead-light">
                <tr>
                    <th>Endpoint Field</th>
                    <th>Form Field</th>
                    <th>Required</th>
                </tr>
            </thead>
            <tbody>
    `;

    endpointFields.forEach(field => {
        const isRequired = field.required ? '<span class="badge badge-danger">Required</span>' : '';
        const fieldType = field.type ? `<span class="badge bg-info ms-2">${field.type}</span>` : '';
        html += `
            <tr>
                <td>
                    <div class="d-flex align-items-center">
                        <span class="me-2">${field.name}</span>
                        ${fieldType}
                        ${isRequired}
                    </div>
                </td>
                <td>
                    <select class="form-control form-control-sm form-field-select" data-endpoint-field="${field.name}">
                        <option value="">-- Select Form Field --</option>
                        ${formFields.map(f => `<option value="${f.key}">${f.label || f.key}</option>`).join('')}
                    </select>
                </td>
                <td>${isRequired}</td>
            </tr>
        `;
    });
    
    html += '</tbody></table>';
    container.html(html);
    
    // Apply any existing mappings
    applyExistingMappings();
    
    // Set up event listeners for field mapping changes
    setupFieldMappingListeners();
}

// Apply existing field mappings to the UI
function applyExistingMappings() {
    Object.entries(window.fieldMappings).forEach(([formField, endpointField]) => {
        const $select = $(`.form-field-select[data-endpoint-field="${endpointField}"]`);
        if ($select.length) {
            $select.val(formField);
        }
    });
}

// Set up event listeners for field mapping changes
function setupFieldMappingListeners() {
    $(document).off('change', '.form-field-select').on('change', '.form-field-select', function() {
        const $select = $(this);
        const endpointField = $select.data('endpoint-field');
        const formField = $select.val();
        
        // Remove any existing mapping for this form field
        Object.keys(window.fieldMappings).forEach(key => {
            if (window.fieldMappings[key] === endpointField) {
                delete window.fieldMappings[key];
            }
        });
        
        // Add new mapping if a form field was selected
        if (formField) {
            window.fieldMappings[formField] = endpointField;
        }
        
        // Update hidden field if it exists
        if ($('#field_mappings').length) {
            $('#field_mappings').val(JSON.stringify(window.fieldMappings));
        }
    });
}

// Helper function to dynamically load a script
function loadScript(src) {
    return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = src;
        script.onload = () => resolve();
        script.onerror = () => reject(new Error(`Script load error for ${src}`));
        document.head.appendChild(script);
    });
}

// Update all transformation dropdowns with available transformations
function updateTransformationDropdowns() {
    if (!window.fieldMappingTransformations) {
        console.warn('FieldMappingTransformations not available');
        return;
    }
    
    const transformations = window.fieldMappingTransformations.getTransformationOptions() || [];
    
    // Update all transformation dropdowns
    $('.transform-type').each(function() {
        const $select = $(this);
        const currentValue = $select.val();
        
        // Clear existing options except the first one (default)
        $select.find('option:not(:first)').remove();
        
        // Add transformation options
        transformations.forEach(transform => {
            $select.append(new Option(
                transform.label || transform.value,
                transform.value,
                false,
                transform.value === currentValue
            ));
            
            // Store hasParams as a data attribute for easier access
            if (transform.hasParams) {
                $select.find(`option[value="${transform.value}"]`).data('has-params', true);
            }
        });
    });
}

// Initialize the transformations instance
function initializeTransformationsInstance() {
    return new Promise((resolve, reject) => {
        try {
            window.fieldMappingTransformations = new FieldMappingTransformations();
            window.fieldMappingTransformations.init();
            
            // Update transformation dropdowns after a short delay to ensure initialization is complete
            setTimeout(() => {
                updateTransformationDropdowns();
                resolve(true);
            }, 100);
        } catch (error) {
            console.error('Error initializing transformations:', error);
            reject(error);
        }
    });
}

// Initialize transformations if available
function initializeTransformations() {
    return new Promise((resolve, reject) => {
        console.log('Initializing transformations...');
        
        // First check if the script loaded correctly
        if (typeof FieldMappingTransformations === 'undefined') {
            const error = 'FieldMappingTransformations class not found. Make sure the JavaScript file is loaded correctly.';
            console.error(error);
            
            // Show user-friendly error message
            showAlert('danger', 'Error: Failed to load transformation functionality. Please refresh the page and try again.');
            
            // Try to load the script dynamically as a fallback
            console.log('Attempting to load transformation script dynamically...');
            loadScript('<?php echo e(asset('js/field-mapping-transformations.js')); ?>?v=' + new Date().getTime())
                .then(() => {
                    console.log('Dynamically loaded script, checking again...', {
                        'typeof FieldMappingTransformations': typeof FieldMappingTransformations
                    });
                    
                    if (typeof FieldMappingTransformations === 'undefined') {
                        const errorMsg = 'Failed to load FieldMappingTransformations class after dynamic load';
                        console.error(errorMsg);
                        showAlert('danger', 'Error: ' + errorMsg);
                        reject(new Error(errorMsg));
                        return;
                    }
                    
                    console.log('Successfully loaded FieldMappingTransformations dynamically');
                    initializeTransformationsInstance().then(resolve).catch(reject);
                })
                .catch(err => {
                    const errorMsg = 'Failed to load transformation script: ' + err.message;
                    console.error(errorMsg, err);
                    showAlert('danger', 'Error: ' + errorMsg);
                    reject(new Error('Failed to load transformation functionality'));
                });
            return;
        }
        
        // If we get here, the class is available
        console.log('FieldMappingTransformations is available, initializing instance...');
        initializeTransformationsInstance().then(resolve).catch(reject);
    });
}

// Document ready handler
$(document).ready(function() {
    console.log('Document ready - initializing field mapping configuration');
    
    // Initialize tooltips if Bootstrap tooltips are available
    if (typeof $().tooltip === 'function') {
        $('[data-bs-toggle="tooltip"]').tooltip();
    }
    
    // Debug: Check if FieldMappingTransformations is available
    console.log('Checking if FieldMappingTransformations is available...', {
        'typeof FieldMappingTransformations': typeof FieldMappingTransformations,
        'window.FieldMappingTransformations': window.FieldMappingTransformations
    });
    
    // Call the initializeTransformations function
    initializeTransformations().then(() => {
        console.log('Transformations initialized successfully');
    }).catch(error => {
        console.error('Failed to initialize transformations:', error);
    });
    
    // Update field mapping section UI
    function updateFieldMappingSection() {
        const $container = $('#field-mappings-container');
        
        if (formFields.length === 0 && endpointFields.length === 0) {
            showFieldMappingPlaceholder();
            return;
        }
        
        if (endpointFields.length > 0) {
            displayEndpointFieldMappings();
        } else {
            $container.html('<div class="alert alert-info">Select an endpoint configuration to map fields.</div>');
        }
    }
    
    // This function is now defined at the top of the file for better scoping
    
    // Update the transformation badge UI for a field
    function updateTransformationBadge(fieldKey, hasTransform) {
        const $row = $(`tr[data-field-key="${fieldKey}"]`);
        if (!$row.length) {
            console.warn(`Row not found for field key: ${fieldKey}`);
            return;
        }
        
        // Remove any existing badges
        $row.find('.transform-badge').remove();
        
        // Add a new badge if there's a transformation
        if (hasTransform) {
            $row.find('.transform-type').after(
                ' <span class="badge bg-info transform-badge" title="Transformation configured">' + 
                '<i class="fa fa-cog"></i></span>'
            );
        }
    }
    
    // Load transformation configuration for existing field mappings
    function loadExistingTransformations() {
        if (!window.fieldMappings) return;
        
        Object.entries(window.fieldMappings).forEach(([fieldKey, mapping]) => {
            if (mapping.transform) {
                // Update the transformation dropdown
                const $row = $(`tr[data-field-key="${fieldKey}"]`);
                if ($row.length) {
                    const $transformSelect = $row.find('.transform-type');
                    $transformSelect.val(mapping.transform.type);
                    
                    // Show the configure button if the transform has parameters
                    const hasParams = window.fieldMappingTransformations && 
                                   window.fieldMappingTransformations.getTransformationConfig(mapping.transform.type)?.params?.length > 0;
                    $row.find('.configure-transform').toggleClass('d-none', !hasParams);
                    
                    // Update the badge
                    updateTransformationBadge(fieldKey, true);
                }
            }
        });
    }
    
    // Initialize transformations
    initializeTransformations()
        .then(() => {
            // After transformations are loaded, load any existing mappings
            loadExistingTransformations();
        })
        .catch(error => {
            console.error('Failed to initialize transformations:', error);
            // Show a user-friendly error message
            showAlert('warning', 'Some transformation features may not be available. Please refresh the page to try again.');
        });
    
    // Initialize tooltips if Bootstrap tooltips are available
    if (typeof $().tooltip === 'function') {
        $('[data-bs-toggle="tooltip"]').tooltip();
    }

    // Load old field mappings if validation failed
    <?php if(old('field_mappings')): ?>
        const oldMappings = <?php echo json_encode(old('field_mappings'), 15, 512) ?>;
        // Convert the old mappings to the correct format if needed
        if (Array.isArray(oldMappings)) {
            // Handle array format (if needed)
            oldMappings.forEach(mapping => {
                if (mapping && mapping.form_field && mapping.endpoint_field) {
                    fieldMappings[mapping.form_field] = mapping.endpoint_field;
                }
            });
        } else if (typeof oldMappings === 'object' && oldMappings !== null) {
            // Handle object format
            fieldMappings = { ...oldMappings };
        }
    <?php endif; ?>

    // Clear form fields and reset UI
    function clearFormFields() {
        formFields = [];
        endpointFields = [];
        window.fieldMappings = {};
        $('#field-mappings-container').html('<div class="alert alert-info">Select a form to view its fields.</div>');
        updateFieldMappingSection();
    }

    // Form selection changed
    $('#form_id').change(function() {
        const formId = $(this).val();
        console.log('Form selection changed to ID:', formId);
        
        if (!formId) {
            console.log('No form selected, clearing fields');
            clearFormFields();
            return;
        }

        // Show loading state
        const $formSelect = $(this);
        const originalHtml = $formSelect.html();
        $formSelect.prop('disabled', true).html('<option value=""><?php echo e(__("global.loading")); ?>...</option>');

        // Clear any existing alerts
        $('.alert-dismissible').alert('close');
        
        loadFormFields(formId)
            .done(function(response) {
                console.log('Form fields loaded successfully');
                // The loadFormFields function already updates the UI
            })
            .fail(function(xhr, status, error) {
                console.error('Error loading form fields:', status, error);
                console.error('Response:', xhr.responseText);
                
                let errorMsg = 'Failed to load form fields. Please try again.';
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (response.message) {
                        errorMsg = response.message;
                    } else if (response.error) {
                        errorMsg = response.error;
                    }
                } catch (e) {
                    console.error('Error parsing error response:', e);
                }
                
                showAlert('danger', errorMsg);
                formFields = [];
                updateFieldMappingSection();
            })
            .always(function() {
                $formSelect.prop('disabled', false).html(originalHtml).val(formId);
            });
    });
    
    // Show alert message
    function showAlert(type, message) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
        
        // Remove any existing alerts
        $('.alert-dismissible').alert('close');
        
        // Add the new alert
        const $form = $('#field-mapping-form');
        $form.prepend(alertHtml);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            $('.alert-dismissible').alert('close');
        }, 5000);
    }

    // Handle endpoint configuration change
    $('#integration_configuration_id').on('change', function() {
        const endpointId = $(this).val();
        const $endpointSelect = $(this);
        const originalHtml = $endpointSelect.html();
        
        if (!endpointId) {
            endpointFields = [];
            updateFieldMappingSection();
            return;
        }
        
        // Show loading state
        $endpointSelect.prop('disabled', true).html('<option value=""><?php echo e(__("global.loading")); ?>...</option>');
        
        loadEndpointFields(endpointId)
            .done(function(response) {
                endpointFields = response.fields || [];
                console.log('Endpoint fields loaded successfully:', endpointFields);
                
                if ($('#use_form_fields_as_endpoint').is(':checked')) {
                    // If using form fields as endpoint fields, rebuild based on form fields
                    $('#use_form_fields_as_endpoint').trigger('change');
                } else {
                    // Otherwise, update with endpoint fields
                    updateFieldMappingSection();
                }
            })
            .fail(function(jqXHR, textStatus, errorThrown) {
                console.error('Failed to load endpoint configuration:', textStatus, errorThrown);
                console.error('Response:', jqXHR.responseText);
                showAlert('danger', '<?php echo e(__("global.error_loading_endpoint_configuration")); ?>');
                endpointFields = [];
                updateFieldMappingSection();
            })
            .always(function() {
                $endpointSelect.prop('disabled', false).html(originalHtml).val(endpointId);
            });
    });

    // Filter endpoint configurations
    window.filterEndpointConfigurations = function() {
        const externalSystem = $('#external_system_filter').val();
        const integrationMethod = $('#integration_method_filter').val();
        const processType = $('#process_type_filter').val();

        let hasVisibleOptions = false;
        
        $('#integration_configuration_id option').each(function() {
            if ($(this).val() === '') {
                $(this).show();
                return true; // Skip the default option
            }

            const matchesExternalSystem = !externalSystem || $(this).data('external-system') === externalSystem;
            const matchesIntegrationMethod = !integrationMethod || $(this).data('integration-method') === integrationMethod;
            const matchesProcessType = !processType || $(this).data('process-type') === processType;

            if (matchesExternalSystem && matchesIntegrationMethod && matchesProcessType) {
                $(this).show();
                hasVisibleOptions = true;
            } else {
                $(this).hide();
                if ($(this).is(':selected')) {
                    $('#integration_configuration_id').val('').trigger('change');
                }
            }
        });
        
        // Show message if no options match the filters
        const $noResults = $('#no-endpoint-results');
        if (!hasVisibleOptions) {
            if ($noResults.length === 0) {
                $('#integration_configuration_id').after('<div id="no-endpoint-results" class="text-muted small mt-2"><?php echo e(__("global.no_matching_endpoints")); ?></div>');
            }
        } else {
            $noResults.remove();
            const $selectedOption = $('#integration_configuration_id option:selected');
            if ($selectedOption.length > 0 && $selectedOption.css('display') === 'none') {
                $('#integration_configuration_id').val('');
            }
        }
    }

    // Suggest mappings button
    $('#suggest-mappings-btn').click(function() {
        suggestFieldMappings();
    });



    // Helper function to flatten nested fields
    function flattenFields(fields, parentKey = '') {
        let result = [];
        
        Object.entries(fields).forEach(([key, field]) => {
            const fullKey = parentKey ? `${parentKey}.${key}` : key;
            
            // Skip panel and button fields as they're not mappable
            if (field.type === 'panel' || field.type === 'button') {
                return;
            }
            
            // Add the current field
            result.push({
                key: fullKey,
                label: field.label || key,
                type: field.type || 'text',
                required: field.required || false,
                placeholder: field.placeholder || '',
                description: field.description || ''
            });
            
            // Recursively process nested fields if they exist
            if (field.components) {
                result = result.concat(flattenFields(field.components, fullKey));
            }
        });
        
        return result;
    }
    
    // Load form fields via AJAX
    function loadFormFields(formId) {
        console.log('Loading form fields for form ID:', formId);
        
        // Show loading state
        const $formSelect = $('#form_id');
        const originalHtml = $formSelect.html();
        $formSelect.prop('disabled', true).html('<option value="">Loading form fields...</option>');
        
        $('#field-mappings-container').html('<div class="text-center p-4"><i class="fa fa-spinner fa-spin me-2"></i> Loading form fields...</div>');
        
        return $.ajax({
            url: '<?php echo e(route("admin.field-mapping-configurations.get-form-fields")); ?>',
            method: 'GET',
            data: { 
                form_id: formId,
                _token: '<?php echo e(csrf_token()); ?>' // Ensure CSRF token is included
            },
            dataType: 'json'
        })
        .always(function() {
            // Re-enable the form select after the request completes (success or error)
            $formSelect.prop('disabled', false).html(originalHtml);
            $formSelect.val(formId); // Reselect the current form
        })
        .done(function(response) {
            console.log('Form fields response received:', response);
            
            if (!response || !response.fields) {
                const errorMsg = response && response.error ? response.error : 'Invalid or empty response from server';
                console.error('Error loading form fields:', errorMsg);
                showAlert('danger', errorMsg);
                throw new Error(errorMsg);
            }
            
            try {
                // Flatten the nested fields structure
                formFields = flattenFields(response.fields);
                
                console.log(`Processed ${formFields.length} form fields`);
                
                if (formFields.length > 0) {
                    console.log('Form field keys:', formFields.map(f => f.key));
                    
                    // Update the UI with the form fields
                    displayFormFields();
                    updateFieldMappingSection();
                } else {
                    console.warn('No mappable form fields found in response');
                    $('#field-mappings-container').html(`
                        <div class="alert alert-warning">
                            <i class="fa fa-exclamation-triangle me-2"></i>
                            No mappable form fields found for the selected form.
                        </div>
                    `);
                }
            } catch (error) {
                console.error('Error processing form fields:', error);
                showAlert('danger', 'Error processing form fields: ' + error.message);
                throw error;
            }
            
            return formFields;
        })
        .fail(function(xhr, status, error) {
            let errorMsg = `Failed to load form fields: ${status}`;
            console.error(errorMsg, error);
            console.error('Response:', xhr.responseText);
            
            // Show more detailed error message
            let errorDetails = '';
            try {
                const response = JSON.parse(xhr.responseText);
                errorDetails = response.message || xhr.responseText;
            } catch (e) {
                errorDetails = xhr.responseText || 'No details available';
            }
                
            showError(`Failed to load form fields: ${error}`, errorDetails);
            clearFormFields();
            
            // Reject the promise to trigger .fail()
            return Promise.reject(error);
            // }
        });
    }

    // Load endpoint fields via AJAX
    function loadEndpointFields(integrationId) {
        if (!integrationId) {
            console.error('No integration ID provided');
            endpointFields = [];
            updateFieldMappingSection();
            return $.Deferred().resolve();
        }
        
        console.log('Loading endpoint fields for integration ID:', integrationId);
        
        return $.ajax({
            url: '<?php echo e(route("admin.field-mapping-configurations.get-integration-fields")); ?>',
            method: 'GET',
            data: { integration_id: integrationId },
            dataType: 'json'
        })
        .done(function(response) {
            console.log('Endpoint fields response:', response);
            if (response && (response.fields || response.data)) {
                endpointFields = response.fields || response.data;
                console.log('Loaded', endpointFields.length, 'endpoint fields');
            } else {
                console.warn('No fields found in response');
                endpointFields = [];
            }
            updateFieldMappingSection();
        })
        .fail(function(xhr, status, error) {
            console.error('Failed to load endpoint fields:', status, error);
            console.error('Response:', xhr.responseText);
            showError('Failed to load endpoint fields: ' + (xhr.responseJSON?.error || status));
            endpointFields = [];
            updateFieldMappingSection();
        });
    }



    // Update field mapping section visibility and content
    function updateFieldMappingSection() {
        console.log('Updating field mapping section. Form fields:', formFields, 'Endpoint fields:', endpointFields);
        
        // Show/hide suggest mappings button based on whether we have both form and endpoint fields
        if (formFields.length > 0 && endpointFields.length > 0) {
            $('#suggest-mappings-btn').show();
            // $('#import-json-btn').show();
            console.log('Displaying field mappings');
            displayEndpointFieldMappings();
        } else {
            $('#suggest-mappings-btn').hide();
            $('#import-json-btn').hide();
            
            // If we have endpoint fields but no form fields, still show the interface
            if (endpointFields.length > 0) {
                console.log('Displaying endpoint fields without form fields');
                displayEndpointFieldMappings();
            }
            // If we have form fields but no endpoint fields, show a message
            else if (formFields.length > 0) {
                console.log('Form fields loaded but no endpoint fields selected');
                $('#field-mappings-container').html(`
                    <div class="alert alert-info">
                        <i class="fa fa-info-circle"></i>
                        Please select an endpoint configuration to map form fields.
                    </div>
                `);
            }
            // If we have neither, show the loading state
            else {
                console.log('No form fields or endpoint fields available');
                showFieldMappingPlaceholder();
            }
        }
    }
        
    // Update the hidden field mappings input with current form state
    function updateFieldMappingsInput() {
        const mappings = [];
        const useFormFields = $('#use_form_fields_as_endpoint').is(':checked');
        
        // Function to process a single field mapping container
        const processFieldMapping = ($container) => {
            const $endpointField = $container.find('.endpoint-field');
            const $formFieldSelect = $container.find('.field-select');
            const $requiredCheckbox = $container.find('[name$="[required]"]');
            const $transformType = $container.find('.transform-type');
            
            // Get the endpoint field value - either from data attribute or input field
            const endpointField = $container.data('field-key') || $endpointField.val();
            const formField = $formFieldSelect.val();
            const isRequired = $requiredCheckbox.is(':checked');
            
            if (endpointField && formField) {
                const mapping = {
                    endpoint_field: endpointField,
                    form_field: formField,
                    required: isRequired
                };
                
                // Get transformation data if a transformation is selected
                const transformType = $transformType.val();
                if (transformType) {
                    const fieldKey = $container.data('field-key') || endpointField;
                    const savedMapping = fieldMappings[fieldKey] || {};
                    
                    if (savedMapping.transform && savedMapping.transform.type === transformType) {
                        // Use the saved transformation data
                        mapping.transform = savedMapping.transform;
                    } else {
                        // Create a new transformation entry
                        mapping.transform = { 
                            type: transformType,
                            params: {}
                        };
                        
                        // If we have a saved mapping for this field, preserve any matching parameters
                        if (savedMapping.transform && savedMapping.transform.params) {
                            // Get the transformation configuration to know what parameters are expected
                            if (window.fieldMappingTransformations) {
                                const transformConfig = window.fieldMappingTransformations.getTransformationConfig(transformType);
                                if (transformConfig && transformConfig.params) {
                                    transformConfig.params.forEach(param => {
                                        if (savedMapping.transform.params[param.name] !== undefined) {
                                            mapping.transform.params[param.name] = savedMapping.transform.params[param.name];
                                        }
                                    });
                                }
                            }
                        }
                    }
                }
                
                return mapping;
            }
            return null;
        };
        
        // Process all field mapping containers
        $('.field-mapping-row').each(function() {
            const $row = $(this);
            const mapping = processFieldMapping($row);
            if (mapping) {
                // Store the mapping in our fieldMappings object for later reference
                const fieldKey = $row.data('field-key') || mapping.endpoint_field;
                fieldMappings[fieldKey] = mapping;
                
                // Add to the mappings array
                mappings.push(mapping);
            }
        });
        
        // Process any custom field mappings (when not using form fields as endpoint fields)
        if (!useFormFields) {
            $('.field-mapping-container').each(function() {
                const mapping = processFieldMapping($(this));
                if (mapping) {
                    mappings.push(mapping);
                }
            });
        }
        
        // Update the hidden input with the mappings
        $('#field_mappings').val(JSON.stringify(mappings));
        console.log('Updated field mappings:', mappings);
    }

    // Display form fields in the table
    function displayFormFields() {
        const container = $('#field-mappings-container');
        container.html('');
        
        if (!formFields || formFields.length === 0) {
            container.html(`
                <div class="alert alert-warning">
                    <i class="fa fa-exclamation-triangle"></i> No form fields available to map.
                </div>
            `);
            return;
        }
        
        // Sort fields by their key for better organization
        const sortedFields = [...formFields].sort((a, b) => a.key.localeCompare(b.key));
        
        // Group fields by parent for better organization
        const fieldGroups = {};
        sortedFields.forEach(field => {
            const parts = field.key.split('.');
            const group = parts.length > 1 ? parts[0] : 'Other';
            
            if (!fieldGroups[group]) {
                fieldGroups[group] = [];
            }
            fieldGroups[group].push(field);
        });
        
        let html = `
            <div class="table-responsive">
                <table class="table table-bordered table-striped">
                    <thead class="table-light">
                        <tr>
                            <th style="width: 25%;">Form Field</th>
                            <th style="width: 25%;">Display Name</th>
                            <th style="width: 25%;">Transformation</th>
                            <th style="width: 10%;" class="text-center">Required</th>
                            <th style="width: 15%;" class="text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
        `;
        
        // Create a row for each form field
        Object.entries(fieldGroups).forEach(([group, fields]) => {
            // Add group header if not 'Other'
            if (group !== 'Other') {
                html += `
                    <tr class="table-group">
                        <td colspan="5" class="bg-light fw-bold">
                            <i class="fa fa-folder-open me-2"></i>${group}
                        </td>
                    </tr>
                `;
            }
            
            fields.forEach(formField => {
                const fieldKey = formField.key || '';
                const displayKey = fieldKey.includes('.') ? fieldKey.split('.').pop() : fieldKey;
                const fieldLabel = formField.label || displayKey;
                const fieldType = formField.type || 'text';
                const isRequired = formField.required || false;
                const savedMapping = fieldMappings[fieldKey] || {};
                const savedTransform = savedMapping.transform || {};
                
                // Get transformation options from the FieldMappingTransformations class
                const transformOptions = window.fieldMappingTransformations ? 
                    window.fieldMappingTransformations.getTransformationOptions() : [];
                    
                // Create transformation select dropdown
                let transformSelect = `
                    <select class="form-select form-select-sm transform-type" 
                            name="field_mapping[${fieldKey}][transform][type]" 
                            data-field-key="${fieldKey}">
                        <option value="">No Transformation</option>
                `;
                
                if (transformOptions && transformOptions.length > 0) {
                    transformOptions.forEach(option => {
                        if (option && option.value) {
                            const selected = (savedTransform.type === option.value) ? 'selected' : '';
                            const hasParams = option.params && option.params.length > 0;
                            const paramText = hasParams ? ' (config required)' : '';
                            transformSelect += `
                                <option value="${option.value}" 
                                        ${selected} 
                                        data-has-params="${hasParams}">
                                    ${option.label || option.value}${paramText}
                                </option>`;
                        }
                    });
                }
                transformSelect += '</select>';
                
                // Check if this field has a saved transformation with parameters
                const hasSavedTransform = savedTransform.type && savedTransform.params && 
                                       Object.keys(savedTransform.params).length > 0;
                
                // Get field type badge class
                const typeBadges = {
                    'text': 'bg-primary',
                    'number': 'bg-info',
                    'select': 'bg-success',
                    'date': 'bg-warning',
                    'datetime': 'bg-warning',
                    'time': 'bg-warning',
                    'checkbox': 'bg-danger',
                    'radio': 'bg-danger'
                };
                
                const fieldTypeClass = typeBadges[fieldType] || 'bg-secondary';
                
                // Add the row HTML
                html += `
                    <tr class="field-mapping-row" data-field-key="${fieldKey}">
                        <td>
                            <div class="d-flex align-items-center">
                                <span class="badge ${fieldTypeClass} me-2" title="${fieldType}">
                                    ${fieldType.substring(0, 1).toUpperCase() + fieldType.substring(1)}
                                </span>
                                <div>
                                    <div class="fw-medium">${fieldLabel}</div>
                                    <small class="text-muted font-monospace">${fieldKey}</small>
                                    ${formField.description ? `<div class="small text-muted mt-1">${formField.description}</div>` : ''}
                                </div>
                            </div>
                        </td>
                        <td>
                            <input type="text" 
                                   class="form-control form-control-sm" 
                                   name="field_mapping[${fieldKey}][label]" 
                                   value="${fieldLabel}" 
                                   placeholder="Display name">
                                   
                            <input type="hidden" 
                                   class="form-field-key" 
                                   name="field_mapping[${fieldKey}][field]" 
                                   value="${fieldKey}">
                        </td>
                        <td class="transform-cell">
                            <div class="transform-container">
                                ${transformSelect}
                                <div class="transform-params mt-2">
                                    ${hasSavedTransform ? `
                                        <div class="d-flex align-items-center">
                                            <span class="badge bg-info me-2">
                                                ${savedTransform.label || savedTransform.type}
                                            </span>
                                            <button type="button" 
                                                    class="btn btn-sm btn-outline-primary configure-transform" 
                                                    data-field-key="${fieldKey}" 
                                                    title="Configure Transformation">
                                                <i class="fa fa-cog"></i>
                                            </button>
                                        </div>
                                    ` : ''}
                                </div>
                            </div>
                        </td>
                        <td class="text-center align-middle">
                            <div class="form-check d-inline-block">
                                <input type="checkbox" 
                                       class="form-check-input" 
                                       name="field_mapping[${fieldKey}][required]" 
                                       value="1"
                                       ${isRequired ? 'checked' : ''}>
                            </div>
                        </td>
                        <td class="text-center align-middle">
                            <button type="button" 
                                    class="btn btn-sm btn-outline-primary configure-transform ${!savedTransform.type ? 'd-none' : ''}" 
                                    data-field-key="${fieldKey}" 
                                    title="Configure Transformation"
                                    ${!savedTransform.type ? 'disabled' : ''}>
                                <i class="fa fa-cog"></i> Configure
                            </button>
                        </td>
                    </tr>
                `;
            });
        
            html += `
                        </tbody>
                    </table>
                </div>
            `;
        
            container.html(html);
            
            // Update the mappings input when fields change
            $('.field-select, .endpoint-field, [name^="required_fields"]').on('change', function() {
                updateFieldMappingsInput();
            });
        });
    
        // Handle transformation type changes
        $(document).on('change', '.transform-type', function() {
            const $select = $(this);
            const $row = $select.closest('tr');
            const transformType = $select.val();
            const fieldKey = $row.data('field-key');
            
            if (!fieldKey) {
                console.error('No field key found for transformation change');
                return;
            }
            
            // Reset the transform params container and ensure it's empty
            const $paramsContainer = $row.find('.transform-params').empty();
            const $configureBtn = $row.find('.configure-transform');
            
            if (!transformType) {
                // No transformation selected
                $configureBtn.addClass('d-none').prop('disabled', true);
                
                // Clear any existing transform configuration
                if (window.fieldMappings[fieldKey] && window.fieldMappings[fieldKey].transform) {
                    delete window.fieldMappings[fieldKey].transform;
                    updateFieldMappingsInput();
                }
                return;
            }
            
            // Get the selected option
            const $selectedOption = $select.find('option:selected');
            const hasParams = $selectedOption.data('has-params') === true;
            
            // Update the configure button state
            if (hasParams) {
                $configureBtn.removeClass('d-none').prop('disabled', false);
            } else {
                $configureBtn.addClass('d-none').prop('disabled', true);
            }
            
            // Get the transform config if available
            let transformConfig = null;
            if (window.fieldMappingTransformations) {
                transformConfig = window.fieldMappingTransformations.getTransformationConfig(transformType);
            }
            
            // Initialize or update the field mapping
            if (!window.fieldMappings[fieldKey]) {
                window.fieldMappings[fieldKey] = {};
            }
            
            // For transforms with parameters, show the configuration modal
            if (hasParams && transformConfig) {
                showTransformConfigModal(transformConfig, fieldKey);
            } else if (!hasParams) {
                // For transforms without parameters, update the mapping directly
                window.fieldMappings[fieldKey].transform = { type: transformType };
                
                // Show a success badge
                $paramsContainer.html(`<span class="badge bg-success">${transformType} applied</span>`);
                
                // Update the hidden input
                updateFieldMappingsInput();
            }
        });
    
    // Handle configure transformation button click
    $(document).on('click', '.configure-transform', function() {
        const $row = $(this).closest('tr');
        const $transformSelect = $row.find('.transform-type');
        const transformType = $transformSelect.val();
        const fieldKey = $row.data('field-key');
        
        if (!transformType) {
            alert('Please select a transformation type first');
            return;
        }
        
        // Get the selected option to check if it has parameters
        const $selectedOption = $transformSelect.find('option:selected');
        const hasParams = $selectedOption.data('has-params') === true;
        
        if (!hasParams) {
            // For transforms without parameters, just update the UI
            if (!window.fieldMappings[fieldKey]) {
                window.fieldMappings[fieldKey] = {};
            }
            window.fieldMappings[fieldKey].transform = { type: transformType };
            
            // Show a success message
            $row.find('.transform-params').html(`<span class="badge bg-success">${transformType} applied</span>`);
            updateFieldMappingsInput();
            return;
        }
        
        // For transforms with parameters, show the configuration modal
        if (window.fieldMappingTransformations) {
            const transformConfig = window.fieldMappingTransformations.getTransformationConfig(transformType);
            if (transformConfig) {
                showTransformConfigModal(transformConfig, fieldKey);
            } else {
                console.warn(`No configuration found for transform type: ${transformType}`);
                alert('Failed to load transformation configuration. Please try again.');
            }
        } else {
            console.warn('FieldMappingTransformations not available');
            alert('Transformation system not initialized. Please refresh the page and try again.');
        }
    });
    
    // Show transformation configuration modal
    function showTransformConfigModal(transformConfig, fieldKey) {
        console.log('Showing transform config modal', { transformConfig, fieldKey });
        
        const $modal = $('#transformConfigModal');
        const $form = $modal.find('form');
        
        // Clear previous form and handlers
        $form.off('submit').empty();
        
        if (!transformConfig || !transformConfig.params || transformConfig.params.length === 0) {
            $form.html('<div class="alert alert-info">No configuration needed for this transformation.</div>');
            $modal.modal('show');
            return;
        }
        
        // Set modal title
        $modal.find('.modal-title').text(`Configure ${transformConfig.label || transformConfig.type} Transformation`);
        
        // Add form fields for each parameter
        transformConfig.params.forEach(param => {
            if (!param.name) {
                console.warn('Parameter missing name', param);
                return;
            }
            
            // Get saved parameter value or use default
            let paramValue = getSavedTransformParam(fieldKey, param.name);
            if (paramValue === undefined && param.default !== undefined) {
                paramValue = param.default;
            }
            
            let inputField = '';
            const inputId = `param-${fieldKey}-${param.name.replace(/[^a-z0-9]/gi, '-')}`;
            
            switch(param.type) {
                case 'select':
                    if (!param.options || !Array.isArray(param.options)) {
                        console.warn('Select parameter missing options', param);
                        inputField = '<div class="text-danger">Invalid configuration: missing options</div>';
                        break;
                    }
                    
                    const options = param.options.map(opt => {
                        const value = opt.value || opt;
                        const label = opt.label || opt.value || opt;
                        const selected = String(paramValue) === String(value) ? 'selected' : '';
                        return `<option value="${value}" ${selected}>${label}</option>`;
                    }).join('');
                    
                    inputField = `
                        <select class="form-select" id="${inputId}" name="${param.name}" 
                                ${param.required ? 'required' : ''}>
                            ${!param.required ? '<option value="">-- Select an option --</option>' : ''}
                            ${options}
                        </select>
                    `;
                    break;
                    
                case 'checkbox':
                    const checked = paramValue === true || paramValue === 'true' || paramValue === '1' ? 'checked' : '';
                    inputField = `
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="${inputId}" 
                                   name="${param.name}" value="1" ${checked}>
                            <label class="form-check-label" for="${inputId}">
                                ${param.label || param.name}
                            </label>
                        </div>
                    `;
                    break;
                    
                case 'number':
                    inputField = `
                        <input type="number" class="form-control" id="${inputId}" 
                               name="${param.name}" value="${paramValue || ''}" 
                               ${param.min !== undefined ? `min="${param.min}"` : ''}
                               ${param.max !== undefined ? `max="${param.max}"` : ''}
                               ${param.step !== undefined ? `step="${param.step}"` : ''}
                               ${param.required ? 'required' : ''}>
                    `;
                    break;
                    
                case 'textarea':
                    inputField = `
                        <textarea class="form-control" id="${inputId}" name="${param.name}" 
                                 rows="${param.rows || 3}" ${param.required ? 'required' : ''}>${paramValue || ''}</textarea>
                    `;
                    break;
                    
                default: // text, email, password, etc.
                    inputField = `
                        <input type="${param.type || 'text'}" class="form-control" id="${inputId}" 
                               name="${param.name}" value="${paramValue || ''}" 
                               ${param.required ? 'required' : ''}
                               ${param.placeholder ? `placeholder="${param.placeholder}"` : ''}>
                    `;
            }
            
            $form.append(`
                <div class="mb-3">
                    <label class="form-label">${param.label} ${param.required ? '<span class="text-danger">*</span>' : ''}</label>
                    ${param.description ? `<div class="form-text mb-1">${param.description}</div>` : ''}
                    ${inputField}
                </div>
            `);
        });
        
        // Add hidden field for field key
        $form.append(`<input type="hidden" name="field_key" value="${fieldKey}">`);
        
        // Handle form submission
        $form.off('submit').on('submit', function(e) {
            e.preventDefault();
            
            const formData = {};
            const $form = $(this);
            
            // Serialize form data
            $form.serializeArray().forEach(item => {
                // Handle checkboxes
                if (item.name.endsWith('[]')) {
                    const baseName = item.name.replace(/\[\]$/, '');
                    if (!formData[baseName]) {
                        formData[baseName] = [];
                    }
                    formData[baseName].push(item.value);
                } else {
                    formData[item.name] = item.value;
                }
            });
            
            // Get the field key and transformation type
            const fieldKey = formData.field_key;
            const transformType = formData.transform_type;
            
            if (!fieldKey) {
                console.error('No field key found in form data');
                showAlert('danger', 'Error: Missing field key');
                return;
            }
            
            console.log('Saving transformation', { fieldKey, transformType, formData });
            
            // Clean up the form data (remove meta fields)
            delete formData.field_key;
            delete formData.transform_type;
            
            // Initialize fieldMappings if needed
            if (!window.fieldMappings) {
                window.fieldMappings = {};
            }
            
            // Initialize this field's mapping if needed
            if (!window.fieldMappings[fieldKey]) {
                window.fieldMappings[fieldKey] = {};
            }
            
            // Update the transformation data
            window.fieldMappings[fieldKey].transform = {
                type: transformType,
                params: formData
            };
            
            console.log('Updated fieldMappings', window.fieldMappings);
            
            // Update the UI to show that this field has a transformation configured
            updateTransformationBadge(fieldKey, true);
            
            // Update the transformation type dropdown to show the selected value
            const $transformSelect = $(`tr[data-field-key="${fieldKey}"] .transform-type`);
            if ($transformSelect.length) {
                $transformSelect.val(transformType);
                
                // Show/hide configure button based on whether this transform has parameters
                const hasParams = window.fieldMappingTransformations && 
                               window.fieldMappingTransformations.getTransformationConfig(transformType)?.params?.length > 0;
                $transformSelect.closest('tr').find('.configure-transform').toggleClass('d-none', !hasParams);
            }
            
            // Close the modal
            $modal.modal('hide');
            
            // Update the hidden field with the latest mappings
            updateFieldMappingsInput();
            
            // Show success message
            showAlert('success', 'Transformation configuration saved successfully');
        });
        
        // Show the modal
        $modal.modal('show');
    }
    
    // Get saved transformation parameter value
    function getSavedTransformParam(fieldKey, paramName) {
        try {
            if (!fieldKey || !paramName) {
                console.warn('Missing fieldKey or paramName', { fieldKey, paramName });
                return null;
            }
            
            if (!window.fieldMappings || !window.fieldMappings[fieldKey]) {
                return null;
            }
            
            const mapping = window.fieldMappings[fieldKey];
            if (!mapping || !mapping.transform) {
                return null;
            }
            
            // Handle both direct params object and nested params object
            const params = mapping.transform.params || {};
            
            // Check for the parameter in various possible locations
            if (params[paramName] !== undefined) {
                return params[paramName];
            }
            
            // Try with array notation for backward compatibility
            if (Array.isArray(params) && params[paramName] !== undefined) {
                return params[paramName];
            }
            
            // Try with dot notation for nested params
            const nestedValue = paramName.split('.').reduce((obj, key) => {
                return (obj && obj[key] !== undefined) ? obj[key] : undefined;
            }, params);
            
            if (nestedValue !== undefined) {
                return nestedValue;
            }
            
            return null;
            
        } catch (error) {
            console.error('Error in getSavedTransformParam:', error);
            return null;
        }
    }
    
    // Save transformation configuration
    function saveTransformConfig(fieldKey, transformType, params) {
        if (!fieldMappings[fieldKey]) {
            fieldMappings[fieldKey] = {};
        }
        
        if (!fieldMappings[fieldKey].transform) {
            fieldMappings[fieldKey].transform = { type: transformType, params: {} };
        }
        
        // Update the transformation parameters
        fieldMappings[fieldKey].transform.params = params;
        
        // Update the UI to show that this field has a transformation
        $(`tr[data-field-key="${fieldKey}"] .transform-type`).val(transformType);
        
        // Update the hidden input
        updateFieldMappingsInput();
    }
    
    // Update field mappings when any field changes
    $(document).on('change input', '.field-select, .endpoint-field, [name$="[required]"], .transform-type', function() {
        updateFieldMappingsInput();
    });
    
    // Handle add/remove key-value pairs
    $(document).on('click', '.add-pair', function() {
        const $container = $(this).closest('.key-value-editor').find('.key-value-pairs');
        $container.append(`
            <div class="row mb-2 key-value-pair">
                <div class="col">
                    <input type="text" class="form-control key" placeholder="Key">
                </div>
                <div class="col">
                    <input type="text" class="form-control value" placeholder="Value">
                </div>
                <div class="col-auto">
                    <button type="button" class="btn btn-sm btn-outline-danger remove-pair">
                        <i class="fa fa-times"></i>
                    </button>
                </div>
            </div>
        `);
    });
    
    $(document).on('click', '.remove-pair', function() {
        if ($(this).closest('.key-value-pairs').find('.key-value-pair').length > 1) {
            $(this).closest('.key-value-pair').remove();
        } else {
            // Clear the inputs instead of removing the last pair
            $(this).closest('.key-value-pair').find('input').val('');
        }
    });
    
    // Toggle form fields as endpoint fields
    $(document).on('change', '#use_form_fields_as_endpoint', function() {
        console.log('Checkbox changed. Checked:', $(this).is(':checked'));
        const useFormFields = $(this).is(':checked');
        
        if (useFormFields) {
            displayFormFields();
        } else {
            displayEndpointFieldMappings();
        }
        
        // Update the mappings input
        updateFieldMappingsInput();
    });
    
    // Manually trigger change event on page load if checkbox is checked
    $(document).ready(function() {
    if ($('#use_form_fields_as_endpoint').is(':checked')) {
        $('#use_form_fields_as_endpoint').trigger('change');
    }
    });
    
    // Update endpoint field when form field selection changes (when in form-field-as-endpoint mode)
    $(document).on('change', '.field-select', function() {
        if ($('#use_form_fields_as_endpoint').is(':checked')) {
            const $row = $(this).closest('tr');
            const selectedValue = $(this).val();
            $row.find('.endpoint-field').val(selectedValue || '');
            updateFieldMappingsInput();
        }
    });
    
    // Handle click on "Add Endpoint Field" button
    $(document).on('click', '#add-custom-field', function(e) {
        console.log('Add Endpoint Field button clicked');
        
        // Reset the dialog
        $('#newEndpointFieldName').val('').removeClass('is-invalid');
        $('#fieldNameError').text('');
        
        // Set the label based on the current mode
        const useFormFields = $('#use_form_fields_as_endpoint').is(':checked');
        const labelText = useFormFields ? 
            'Enter the name for the new endpoint field (it will also be used as the form field key):' : 
            'Enter the name for the new endpoint field:';
        
        $('#endpointFieldLabel').text(labelText);
        
        // Show the dialog and overlay
        $('#customDialog, #dialogOverlay').show();
        
        // Focus the input field
        setTimeout(() => {
            $('#newEndpointFieldName').focus();
        }, 100);
    });
    
    // Handle dialog close
    $(document).on('click', '#cancelDialog, #dialogOverlay', function() {
        $('#customDialog, #dialogOverlay').hide();
    });
    
    // Function to add a new endpoint field
    function addNewEndpointField(fieldName) {
        const useFormFields = $('#use_form_fields_as_endpoint').is(':checked');
        
        // Add the new field to endpointFields
        const newField = {
            name: fieldName,
            type: 'text',
            required: false,
            is_form_field: false // Mark this as not a form field
        };
        
        // If using form fields as endpoint, also add it to formFields if not already present
        if (useFormFields) {
            if (!formFields.some(f => f.key === fieldName)) {
                formFields.push({
                    key: fieldName,
                    label: fieldName,
                    type: 'text'
                });
            }
            // Create a mapping for the new field
            fieldMappings[fieldName] = fieldName;
            
            // Update the display to show form fields
            displayFormFields();
        } else {
            // Add to endpoint fields and update display
            endpointFields.push(newField);
            displayEndpointFieldMappings();
        }
        
        console.log('Added new endpoint field:', fieldName);
        
        // Close the dialog
        $('#customDialog, #dialogOverlay').hide();
        
        // Update the mappings input
        updateFieldMappingsInput();
    }
    
    // Handle save button click in the modal
    $(document).on('click', '#saveEndpointField', function() {
        const fieldName = $('#newEndpointFieldName').val().trim();
        
        // Validate input
        if (!fieldName) {
            $('#newEndpointFieldName').addClass('is-invalid');
            $('#fieldNameError').text('Field name is required');
            return;
        }
        
        // Check if field already exists
        const fieldExists = endpointFields.some(field => field.name === fieldName);
        if (fieldExists) {
            $('#newEndpointFieldName').addClass('is-invalid');
            $('#fieldNameError').text('An endpoint field with this name already exists');
            return;
        }
        
        addNewEndpointField(fieldName);
    });
    
    // Handle Enter key in the input field
    $('#newEndpointFieldName').on('keypress', function(e) {
        if (e.which === 13) { // Enter key
            e.preventDefault();
            $('#saveEndpointField').click();
        }
    });
    // });

    // Reset field mappings to their default state
    function resetFieldMappings() {
        console.log('Resetting field mappings');
        // Clear any existing field mappings
        fieldMappings = {};
        
        // Reset the hidden input
        $('#field_mappings').val('');
        
        // Clear the container
        $('#field-mappings-container').html('');
    }

    // Display endpoint fields with form field mapping options
    function displayEndpointFieldMappings() {
        const container = $('#field-mappings-container');
        const useFormFields = $('#use_form_fields_as_endpoint').is(':checked');
        
        // Reset field mappings before rendering
        resetFieldMappings();
        
        if (endpointFields.length === 0) {
            container.html(`
                <div class="alert alert-info">
                    <i class="fa fa-info-circle"></i> No endpoint fields available. Please select an endpoint configuration first.
                </div>
            `);
            return;
        }

        let html = `
            <table class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th>${useFormFields ? 'Form Field' : 'Endpoint Field'}</th>
                        <th>${useFormFields ? 'Display Name' : 'Form Field'}</th>
                        <th>Transformation</th>
                        <th>Required</th>
                    </tr>
                </thead>
                <tbody>
        `;

        // Process each endpoint field
        endpointFields.forEach((field, index) => {
            const isRequired = field.required || false;
            let fieldName = field.name || '';
            
            // Skip if this is a form field (not an endpoint field)
            if (field.is_form_field) return;
            
            // If using form fields as endpoint, use the mapped form field as the endpoint field
            const displayName = useFormFields ? (fieldMappings[fieldName] || '') : fieldName;
            const endpointFieldName = useFormFields ? (fieldMappings[fieldName] || fieldName) : fieldName;
            
            // Find saved mapping if exists for this field
            const savedMapping = fieldMappings[fieldName] || '';
            
            html += `
                <tr class="field-mapping-row" data-field-key="${endpointFieldName}">
                    <td>${useFormFields ? (savedMapping || fieldName) : fieldName}${isRequired ? ' <span class="text-danger">*</span>' : ''}</td>
                    <td>
                        <select class="form-select field-select" 
                                name="field_mapping[${endpointFieldName}][field]" 
                                data-endpoint-field="${endpointFieldName}">
                            <option value="">-- Select Form Field --</option>
            `;

            // Add form field options
            formFields.forEach(formField => {
                const selected = savedMapping === formField.key ? 'selected' : '';
                html += `<option value="${formField.key}" ${selected}>${formField.label || formField.key}</option>`;
            });

            html += `
                        </select>
                    </td>
                    <td>
                        <select class="form-select transform-type" 
                                name="field_mapping[${endpointFieldName}][transform][type]">
                            <option value="">None</option>
                            <option value="uppercase">Uppercase</option>
                            <option value="lowercase">Lowercase</option>
                            <option value="trim">Trim</option>
                            <option value="custom">Custom</option>
                        </select>
                        <input type="text" 
                               class="form-control mt-1 transform-value d-none" 
                               name="field_mapping[${endpointFieldName}][transform][value]" 
                               placeholder="Enter transformation value/pattern">
                    </td>
                    <td class="text-center">
                        <input type="checkbox" 
                               class="form-check-input" 
                               name="field_mapping[${endpointFieldName}][required]" 
                               value="1"
                               ${isRequired ? 'checked' : ''}>
                    </td>
                    <td class="text-center">
                        <button type="button" class="btn btn-sm btn-outline-danger remove-field">
                            <i class="fa fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;
        });

        html += `
                    </tbody>
                </table>
            </div>
        `;

        container.html(html);
        
        // Update the mappings input when fields change
        $('.field-select, .endpoint-field, [name^="required_fields"]').on('change', function() {
            updateFieldMappingsInput();
        });
    }

    // Auto-suggest button click handler
    /**
     * Shows an alert message
     */
    function showAlert(type, message) {
        // Remove any existing alerts first
        $('.alert-dismissible').alert('close');
        
        // Create alert HTML
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
        
        // Add alert to the page (before the form)
        const $form = $('#field-mapping-form');
        $form.prepend(alertHtml);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            $('.alert-dismissible').alert('close');
        }, 5000);
    }
    
    /**
     * Suggests field mappings based on field names
     */
    function suggestFieldMappings() {
        console.log('Suggesting field mappings...');
        
        // Get the form and endpoint configuration IDs
        const formId = $('#form_id').val();
        const configId = $('#integration_configuration_id').val();
        
        if (!formId || !configId) {
            console.error('Form ID or Endpoint Configuration ID is missing');
            showAlert('warning', 'Please select both a form and an endpoint configuration first');
            return;
        }
        
        // Show loading state
        const $btn = $('#suggest-mappings-btn');
        const originalText = $btn.html();
        $btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> <?php echo e(__("global.suggesting")); ?>...');
        
        // Make AJAX request to get mapping suggestions
        $.ajax({
            url: '<?php echo e(route("admin.field-mapping-configurations.get-field-mapping-suggestions")); ?>',
            method: 'GET',
            data: {
                form_id: formId,
                integration_configuration_id: configId
            },
            success: function(response) {
                console.log('Received mapping suggestions:', response);
                
                if (response.suggestions && Object.keys(response.suggestions).length > 0) {
                    // Apply the suggested mappings
                    let appliedCount = 0;
                    Object.entries(response.suggestions).forEach(([formField, endpointField]) => {
                        const $select = $(`select[name^="field_mapping["][data-endpoint-field="${endpointField}"]`);
                        if ($select.length) {
                            $select.val(formField).trigger('change');
                            appliedCount++;
                        }
                    });
                    
                    // Update the hidden field mappings
                    updateFieldMappingsInput();
                    
                    if (appliedCount > 0) {
                        showAlert('success', `Successfully applied ${appliedCount} field mapping suggestions`);
                    } else {
                        showAlert('info', 'No matching fields found for suggestions');
                    }
                } else {
                    showAlert('info', 'No mapping suggestions available for the selected form and endpoint');
                }
            },
            error: function(xhr) {
                console.error('Error getting mapping suggestions:', xhr);
                let errorMessage = 'Failed to get mapping suggestions. Please try again.';
                
                // Try to get a more specific error message from the response
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (response.message) {
                        errorMessage = response.message;
                    }
                } catch (e) {
                    // If we can't parse the response, use the default message
                }
                
                showAlert('danger', errorMessage);
            },
            complete: function() {
                // Restore button state
                $btn.prop('disabled', false).html(originalText);
            }
        });
    }
    
    // Handle suggest mappings button click
    $(document).on('click', '#suggest-mappings-btn', function(e) {
        e.preventDefault();
        console.log('Auto-suggest button clicked');
        suggestFieldMappings();
    });

    // Filter endpoint options based on selected filters
    function filterEndpointOptions() {
        const externalSystem = $('#external_system_filter').val();
        const integrationMethod = $('#integration_method_filter').val();
        const processType = $('#process_type_filter').val();

        $('#integration_configuration_id option').each(function() {
            if ($(this).val() === '') return; // Skip the default option

            const extSystem = $(this).data('external-system');
            const intMethod = $(this).data('integration-method');
            const procType = $(this).data('process-type');

            let show = true;

            if (externalSystem && extSystem !== externalSystem) show = false;
            if (integrationMethod && intMethod !== integrationMethod) show = false;
            if (processType && procType !== processType) show = false;

            $(this).toggle(show);
        });
    }

    // Clear form fields
    function clearFormFields() {
        formFields = [];
        $('#form-fields-list').html('<p class="text-muted">Select a form to see its fields</p>');
    }

    // Show field mapping placeholder
    function showFieldMappingPlaceholder() {
        $('#field-mappings-container').html('<div class="text-center py-3 text-muted"><i class="fas fa-spinner fa-spin"></i> Loading form fields...</div>');
    }

    // Clear endpoint fields
    function clearEndpointFields() {
        endpointFields = [];
        $('#endpoint-fields-list').html('<p class="text-muted">Select an endpoint configuration to see its fields</p>');
        updateFieldMappingSection();
    }

    // Utility functions
    function showError(message) {
        // You can implement a toast notification or alert here
        alert('Error: ' + message);
    }

    function showSuccess(message) {
        // You can implement a toast notification here
        alert('Success: ' + message);
    }

    function showWarning(message) {
        // You can implement a toast notification here
        alert('Warning: ' + message);
    }

    // Simple modal functions
    function showModal() {
        const modal = document.getElementById('endpointSelectionModal');
        if (modal) {
            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';
            // Focus on the first input when modal opens
            const firstInput = modal.querySelector('input, select');
            if (firstInput) firstInput.focus();
        }
    }
    
    function hideModal() {
        const modal = document.getElementById('endpointSelectionModal');
        if (modal) {
            modal.style.display = 'none';
            document.body.style.overflow = '';
        }
    }
    
    // Close modal when clicking outside content or pressing Escape key
    function setupModalListeners() {
        const modal = document.getElementById('endpointSelectionModal');
        if (!modal) return;
        
        // Click outside
        modal.addEventListener('click', function(event) {
            if (event.target === modal || event.target.id === 'modalBackdrop') {
                closeModal();
            }
        });
        
        // Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeModal();
            }
        });
    }
    
    // Initialize modal listeners when document is ready
    $(document).ready(function() {
        setupModalListeners();
    });
    
    // Initialize if old values exist
    $(document).ready(function() {
        if ($('#form_id').val()) {
            loadFormFields($('#form_id').val());
        }
        if ($('#integration_configuration_id').val()) {
            loadEndpointFields($('#integration_configuration_id').val());
        }
        
        // Handle endpoint selection button click
        $('#selectEndpointBtn').on('click', showModal);
        
        // Handle clear endpoint button
        $('#clearEndpointBtn').on('click', function(e) {
            e.preventDefault();
            $('#integration_configuration_id').val('');
            $('#endpoint_configuration_display').val('');
            $(this).hide();
            endpointFields = [];
            updateFieldMappingSection();
        });
    });

    // Handle select endpoint button in modal
    $(document).on('click', '.select-endpoint', function() {
        const endpointId = $(this).data('id');
        const endpointName = $(this).data('name');
        const externalSystem = $(this).data('external-system');
        const processType = $(this).data('process-type');
        
        // Update the hidden input and display field
        $('#integration_configuration_id').val(endpointId).trigger('change');
        $('#endpoint_configuration_display').val(`${endpointName} (${externalSystem} - ${processType})`);
        
        // Show the clear button
        $('#clearEndpointBtn').show();
        
        // Close the modal first to prevent any interaction issues
        hideModal();
        
        // Load the endpoint fields
        loadEndpointFields(endpointId)
            .done(function(response) {
                endpointFields = response.fields || [];
                console.log('Endpoint fields loaded successfully:', endpointFields);
                updateFieldMappingSection();
            })
            .fail(function(jqXHR, textStatus, errorThrown) {
                console.error('Failed to load endpoint configuration:', textStatus, errorThrown);
            });
            
        // Make sure to close the modal even if there's an error
        return false;
    });

    // Filter endpoints in the modal
    function filterEndpointsInModal() {
        const externalSystem = $('#modal_external_system_filter').val();
        const integrationMethod = $('#modal_integration_method_filter').val();
        const processType = $('#modal_process_type_filter').val();

        $('#endpointsTable tbody tr').each(function() {
            const $row = $(this);
            const matchesExternalSystem = !externalSystem || $row.data('external-system') === externalSystem;
            const matchesIntegrationMethod = !integrationMethod || $row.data('integration-method') === integrationMethod;
            const matchesProcessType = !processType || $row.data('process-type') === processType;

            if (matchesExternalSystem && matchesIntegrationMethod && matchesProcessType) {
                $row.show();
            } else {
                $row.hide();
            }
        });
    }

    // Apply filters when any filter changes
    $(document).on('change', '#modal_external_system_filter, #modal_integration_method_filter, #modal_process_type_filter', function() {
        if (typeof filterEndpointsInModal === 'function') {
            filterEndpointsInModal();
        }
    });

    // Reset filters
    // Handle modal close and cleanup
    function closeModal() {
        hideModal();
        // Reset filters after a short delay to ensure modal is hidden first
        setTimeout(() => {
            if (typeof filterEndpointsInModal === 'function') {
                $('#modal_external_system_filter, #modal_integration_method_filter, #modal_process_type_filter').val('');
                filterEndpointsInModal();
            }
        }, 50);
        return false;
    }
}
});
</script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('styles'); ?>
<link href="<?php echo e(asset('css/field-mapping-transformations.css')); ?>" rel="stylesheet">
<style>
    /* Modal styles */
    .modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1050;
        display: none;
        overflow: auto;
    }
    
    .modal.show {
        display: block;
    }
    
    .modal-dialog {
        position: relative;
        width: auto;
        margin: 0.5rem;
        pointer-events: none;
    }
    
    .modal-content {
        position: relative;
        display: flex;
        flex-direction: column;
        width: 100%;
        pointer-events: auto;
        background-color: #fff;
        background-clip: padding-box;
        border: 1px solid rgba(0, 0, 0, 0.2);
        border-radius: 0.3rem;
        outline: 0;
    }
    
    .modal-backdrop {
        position: fixed;
        top: 0;
        left: 0;
        z-index: 1040;
        width: 100vw;
        height: 100vh;
        background-color: #000;
        opacity: 0.5;
    }
    
    body.modal-open {
        overflow: hidden;
        padding-right: 0 !important;
    }
    
    /* Custom dialog styles */
    #customDialog {
        min-width: 400px;
        max-width: 90%;
    }
    #customDialog .btn {
        padding: 5px 15px;
    }
.required::after {
    content: " *";
    color: red;
}

.form-field-item, .endpoint-field-item {
    cursor: pointer;
    transition: background-color 0.2s;
}

.form-field-item:hover, .endpoint-field-item:hover {
    background-color: #f8f9fa;
}

.mapping-row {
    background-color: #f8f9fa;
}

.badge {
    font-size: 0.7em;
}

#field-mapping-section .card-body {
    background-color: #fff;
}

.field-mappings-list {
    max-height: 500px;
    overflow-y: auto;
}
</style>

<!-- JSON Import Modal -->
<div class="modal fade" id="importJsonModal" tabindex="-1" role="dialog" aria-labelledby="importJsonModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="importJsonModalLabel">Import Field Mappings from JSON</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="jsonInput">Paste your JSON object with field mappings:</label>
                    <textarea class="form-control" id="jsonInput" rows="10" placeholder='{"form_field1": "endpoint_field1", "form_field2": "endpoint_field2"}'></textarea>
                    <small class="form-text text-muted">
                        Format: <code>{"form_field_name": "endpoint_field_name", ...}</code>
                    </small>
                </div>
                <div id="jsonError" class="alert alert-danger" style="display: none;"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="applyJsonMappings">Apply Mappings</button>
            </div>
        </div>
    </div>
</div>

<script>
// Document ready handler for JSON import functionality
$(document).ready(function() {
    // Import JSON button click handler
    $('#import-json-btn').on('click', function() {
        $('#jsonInput').val('');
        $('#jsonError').hide();
        $('#importJsonModal').modal('show');
    });

    // Apply JSON mappings
    $('#applyJsonMappings').on('click', function() {
        const jsonInput = $('#jsonInput').val().trim();
        
        if (!jsonInput) {
            showJsonError('Please enter JSON data');
            return;
        }

        try {
            // Parse the JSON input
            const jsonMappings = JSON.parse(jsonInput);
            
            if (typeof jsonMappings !== 'object' || jsonMappings === null) {
                throw new Error('Invalid JSON format. Expected an object with field mappings.');
            }

            // Clear existing mappings
            fieldMappings = {};
            
            // Process each mapping
            let validMappings = 0;
            
            for (const [formField, endpointField] of Object.entries(jsonMappings)) {
                if (typeof formField === 'string' && typeof endpointField === 'string' && 
                    formField.trim() !== '' && endpointField.trim() !== '') {
                    
                    // Check if the endpoint field exists
                    const endpointExists = endpointFields.some(field => field.name === endpointField);
                    
                    if (endpointExists) {
                        fieldMappings[formField] = endpointField;
                        validMappings++;
                        
                        // Update the select element if it exists
                        const $select = $(`.form-field-select[data-endpoint-field="${endpointField}"]`);
                        if ($select.length) {
                            $select.val(formField).trigger('change');
                        }
                    }
                }
            }
            
            // Update hidden fields
            updateHiddenFieldMappings();
            
            // Show success message
            showSuccess(`Successfully imported ${validMappings} field mappings`);
            
            // Close the modal
            $('#importJsonModal').modal('hide');
            
        } catch (error) {
            showJsonError('Invalid JSON: ' + error.message);
        }
    });
    
    // Show JSON error message
    function showJsonError(message) {
        const $errorDiv = $('#jsonError');
        $errorDiv.text(message).show();
        $('html, body').animate({
            scrollTop: $errorDiv.offset().top - 100
        }, 500);
    }
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Git Data Capture\application\resources\views/admin/field-mapping-configurations/create.blade.php ENDPATH**/ ?>