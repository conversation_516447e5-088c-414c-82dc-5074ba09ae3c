<?php

namespace App\Models;

use App\Traits\DataTableFilter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class FormFieldTemplate extends BaseModel
{
    use HasFactory, SoftDeletes, DataTableFilter;

    protected $fillable = [
        'title',
        'key',
        'icon',
        'schema',
        'tenant_id',
        'updated_by',
    ];

    protected $casts = [
        'schema' => 'json',
    ];
    public static function dataTableColumns(): array
    {
        return [
            [
                'data' => 'DT_RowIndex',
                'name' => 'DT_RowIndex',
                'title' => '#',
                'orderable' => false,
                'searchable' => false,
                'filter' => false,
                'width' => '10',
            ],
            [
                'data' => 'title',
                'name' => 'title',
                'title' => __('cruds.fields.component_title'),
                'filter' => 'text',
            ],
            [
                'data' => 'key',
                'name' => 'key',
                'title' => __('cruds.fields.component_key'),
                'filter' => 'text',
            ],
            // Optional icon column
            /*
        [
            'data' => 'icon',
            'name' => 'icon',
            'title' => __('cruds.fields.icon'),
            'filter' => 'text',
        ],
        */
            [
                'data' => 'updated_at',
                'name' => 'updated_at',
                'title' => __('cruds.fields.updated_at'),
                'class' => 'text-right',
                'filter' => 'date_range',
            ],
            [
                'data' => 'action',
                'name' => 'action',
                'title' => __('global.actions'),
                'orderable' => false,
                'searchable' => false,
                'filter' => false,
                'width' => '150',
            ],
        ];
    }

    public static function getComponents()
    {
        $templates = FormFieldTemplate::orderBy('title', 'asc')->get();
        $comonents = [];
        foreach ($templates as $template) {
            $comonents[$template->key] = [
                "title" => $template->title,
                "key" => $template->key,
                "icon" => $template->icon,
                "schema" => json_decode($template->schema, true)
            ];
        }
        return $comonents;
    }
}
