<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class QrDecoderField extends Model
{
    use HasFactory;

    protected $fillable = [
        'profile_id',
        'segment',
        'start',
        'length',
        'ai',
        'field_name',
        'transform_rule',
        'optional',
        'notes',
        'order'
    ];

    protected $casts = [
        'optional' => 'boolean',
    ];

    public function profile()
    {
        return $this->belongsTo(QrDecoderProfile::class, 'profile_id');
    }
}
