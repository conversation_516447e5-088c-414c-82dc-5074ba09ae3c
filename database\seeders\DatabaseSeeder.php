<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        $this->call([
            // Seed users first as other seeders might depend on them
            UserSeeder::class,
            
            // Then seed field definitions
            FieldDefinitionSeeder::class,
            
            // Then seed integration configurations
            IntegrationConfigurationSeeder::class,
            
            // Then seed field mapping configurations
            FieldMappingConfigurationSeeder::class,
            
            // Finally, seed any form field templates
            FormFieldTemplateSeeder::class,
        ]);
    }
}
