@extends('layouts.app')

@section('title', 'Submission Status')

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-line"></i>
                        Submission Status
                    </h3>
                    <div class="card-tools">
                        <a href="{{ route('forms.index') }}" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Forms
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Submission Overview -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5>{{ $status['form']['title'] }}</h5>
                            <p class="text-muted">
                                <i class="fas fa-clock"></i>
                                Submitted: {{ \Carbon\Carbon::parse($status['submitted_at'])->format('M d, Y \a\t g:i A') }}
                            </p>
                            <p class="text-muted mb-0">
                                <i class="fas fa-fingerprint"></i>
                                ID: <code>{{ $status['uuid'] }}</code>
                            </p>
                        </div>
                        <div class="col-md-6 text-md-end">
                            <div class="status-badge mb-2">
                                <span class="badge badge-lg badge-{{ $status['status'] === 'completed' ? 'success' : ($status['status'] === 'failed' ? 'danger' : ($status['status'] === 'partial' ? 'warning' : 'info')) }}">
                                    {{ $status['status_label'] }}
                                </span>
                            </div>
                            @if($status['status'] === 'failed' || $status['status'] === 'partial')
                                <div>
                                    <button type="button" class="btn btn-sm btn-warning" onclick="retrySubmission()">
                                        <i class="fas fa-redo"></i> Retry Failed
                                    </button>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Progress Bar -->
                    <div class="mb-4">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="fw-bold">Overall Progress</span>
                            <span class="text-muted">{{ $status['successful_integrations'] }}/{{ $status['total_integrations'] }} integrations</span>
                        </div>
                        <div class="progress" style="height: 20px;">
                            <div class="progress-bar 
                                @if($status['progress_percentage'] == 100) bg-success
                                @elseif($status['progress_percentage'] > 0) bg-warning
                                @else bg-secondary
                                @endif" 
                                role="progressbar" 
                                style="width: {{ $status['progress_percentage'] }}%"
                                aria-valuenow="{{ $status['progress_percentage'] }}" 
                                aria-valuemin="0" 
                                aria-valuemax="100">
                                {{ $status['progress_percentage'] }}%
                            </div>
                        </div>
                    </div>

                    <!-- Integration Status -->
                    @if(!empty($status['syncs']))
                        <div class="mb-4">
                            <h6 class="fw-bold mb-3">Integration Status</h6>
                            <div class="row">
                                @foreach($status['syncs'] as $sync)
                                    <div class="col-md-6 mb-3">
                                        <div class="card border-left-{{ $sync['status'] === 'completed' ? 'success' : ($sync['status'] === 'failed' ? 'danger' : 'info') }}">
                                            <div class="card-body py-3">
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <div>
                                                        <h6 class="mb-1">{{ $sync['integration_name'] }}</h6>
                                                        <small class="text-muted">{{ $sync['target_system'] }}</small>
                                                    </div>
                                                    <span class="badge badge-{{ $sync['status'] === 'completed' ? 'success' : ($sync['status'] === 'failed' ? 'danger' : 'info') }}">
                                                        {{ $sync['status_label'] }}
                                                    </span>
                                                </div>
                                                
                                                @if($sync['attempted_at'])
                                                    <small class="text-muted d-block mt-2">
                                                        <i class="fas fa-clock"></i>
                                                        @if($sync['completed_at'])
                                                            Completed: {{ \Carbon\Carbon::parse($sync['completed_at'])->format('g:i A') }}
                                                        @else
                                                            Started: {{ \Carbon\Carbon::parse($sync['attempted_at'])->format('g:i A') }}
                                                        @endif
                                                    </small>
                                                @endif

                                                @if($sync['retry_count'] > 0)
                                                    <small class="text-warning d-block">
                                                        <i class="fas fa-redo"></i>
                                                        Retry attempt: {{ $sync['retry_count'] }}
                                                    </small>
                                                @endif

                                                @if($sync['error_message'])
                                                    <small class="text-danger d-block mt-1">
                                                        <i class="fas fa-exclamation-triangle"></i>
                                                        {{ $sync['error_message'] }}
                                                    </small>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    <!-- Auto-refresh notice -->
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        This page automatically refreshes every 10 seconds to show the latest status.
                        <button type="button" class="btn btn-sm btn-outline-info ms-2" onclick="refreshStatus()">
                            <i class="fas fa-sync-alt"></i> Refresh Now
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Retry Modal -->
<div class="modal fade" id="retryModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-redo text-warning"></i>
                    Retry Failed Integrations
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <div id="retry-loading" style="display: none;">
                    <div class="spinner-border text-warning mb-3" role="status">
                        <span class="visually-hidden">Retrying...</span>
                    </div>
                    <p>Retrying failed integrations...</p>
                </div>
                <div id="retry-content">
                    <p>This will retry all failed integrations for this submission.</p>
                    <p class="text-muted">Are you sure you want to continue?</p>
                </div>
            </div>
            <div class="modal-footer" id="retry-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning" onclick="confirmRetry()">
                    <i class="fas fa-redo"></i> Retry Now
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.badge-lg {
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
}

.card.border-left-success {
    border-left: 4px solid #28a745;
}

.card.border-left-danger {
    border-left: 4px solid #dc3545;
}

.card.border-left-info {
    border-left: 4px solid #17a2b8;
}

.status-badge {
    font-size: 1.1rem;
}

.progress {
    border-radius: 10px;
}

.progress-bar {
    border-radius: 10px;
}
</style>
@endpush

@push('scripts')
<script>
const submissionUuid = "{{ $status['uuid'] }}";
const statusUrl = "{{ route('forms.status.json', $status['uuid']) }}";
const retryUrl = "{{ route('forms.retry', $status['uuid']) }}";
const csrfToken = "{{ csrf_token() }}";

let refreshInterval;

document.addEventListener('DOMContentLoaded', function() {
    // Start auto-refresh
    startAutoRefresh();
});

function startAutoRefresh() {
    refreshInterval = setInterval(refreshStatus, 10000); // Refresh every 10 seconds
}

function stopAutoRefresh() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
    }
}

function refreshStatus() {
    fetch(statusUrl)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update the page with new status
                updateStatusDisplay(data.data);
            }
        })
        .catch(error => {
            console.error('Error refreshing status:', error);
        });
}

function updateStatusDisplay(status) {
    // This would update the page content with new status
    // For simplicity, we'll just reload the page if status changed significantly
    const currentStatus = "{{ $status['status'] }}";
    if (status.status !== currentStatus) {
        location.reload();
    }
}

function retrySubmission() {
    const modal = new bootstrap.Modal(document.getElementById('retryModal'));
    modal.show();
}

function confirmRetry() {
    // Show loading state
    document.getElementById('retry-content').style.display = 'none';
    document.getElementById('retry-footer').style.display = 'none';
    document.getElementById('retry-loading').style.display = 'block';

    // Make retry request
    fetch(retryUrl, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': csrfToken,
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        const modal = bootstrap.Modal.getInstance(document.getElementById('retryModal'));
        modal.hide();

        if (data.success) {
            showAlert('success', 'Retry initiated successfully. The page will refresh to show updated status.');
            setTimeout(() => {
                location.reload();
            }, 2000);
        } else {
            showAlert('error', data.message || 'Failed to initiate retry');
        }
    })
    .catch(error => {
        const modal = bootstrap.Modal.getInstance(document.getElementById('retryModal'));
        modal.hide();
        console.error('Retry error:', error);
        showAlert('error', 'An error occurred while retrying. Please try again.');
    });
}

function showAlert(type, message) {
    const alertClass = type === 'error' ? 'alert-danger' : 'alert-success';
    const icon = type === 'error' ? 'fas fa-exclamation-circle' : 'fas fa-check-circle';
    
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert ${alertClass} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        <i class="${icon}"></i> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Insert at top of card body
    const cardBody = document.querySelector('.card-body');
    cardBody.insertBefore(alertDiv, cardBody.firstChild);

    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Stop auto-refresh when page is hidden
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        stopAutoRefresh();
    } else {
        startAutoRefresh();
    }
});
</script>
@endpush
