@extends('layouts.admin')
@section('pageTitle', __('global.create') . ' ' . __('cruds.formIntegrationSetting.title_singular'))

@push('styles')
<style>
    .required:after {
        content: " *";
        color: red;
    }
    .help-block {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 0.25rem;
    }
    .form-group {
        margin-bottom: 1.5rem;
    }
    .card-header h4 {
        margin: 0;
        color: #495057;
    }
</style>
@endpush

@section('content')
    <div class="card card-default">
        <div class="card-header separator">
            <div class="card-title mainheading">
                <h4>{{ __('global.create') }} {{ __('cruds.formIntegrationSetting.title_singular') }}</h4>
            </div>
            <div class="card-controls">
                <ul>
                    <li>
                        <x-buttons.back :route="route('admin.form-integration-settings.index')" :text="__('global.back_to_list')" />
                    </li>
                </ul>
            </div>
        </div>
        <div class="card-block">
            @if ($errors->any())
                <div class="alert alert-danger">
                    <ul>
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <form method="POST" action="{{ route('admin.form-integration-settings.store') }}" id="integration-form">
                @csrf

                <div class="form-group row">
                    <label for="name" class="col-md-2 col-form-label required">{{ __('cruds.formIntegrationSetting.fields.name') }}</label>
                    <div class="col-md-4">
                        <input type="text" class="form-control {{ $errors->has('name') ? 'is-invalid' : '' }}" 
                               id="name" name="name" value="{{ old('name') }}" required>
                        @if($errors->has('name'))
                            <div class="invalid-feedback">
                                {{ $errors->first('name') }}
                            </div>
                        @endif
                    </div>
                    <label for="is_active" class="col-md-2 col-form-label required">{{ trans('cruds.fields.status') }}</label>
                    <div class="col-md-4">
                        <div class="form-check mt-2">
                            <input type="checkbox" class="form-check-input" id="is_active" name="is_active" value="1"
                                {{ old('is_active', true) ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_active">
                                {{ trans('cruds.fields.active') }}
                            </label>
                        </div>
                        @if($errors->has('is_active'))
                            <div class="invalid-feedback d-block">
                                {{ $errors->first('is_active') }}
                            </div>
                        @endif
                    </div>
                </div>

                <div class="form-group row">
                    <label for="description" class="col-md-2 col-form-label">{{ __('cruds.formIntegrationSetting.fields.description') }}</label>
                    <div class="col-md-4">
                        <textarea class="form-control {{ $errors->has('description') ? 'is-invalid' : '' }}" 
                                 id="description" name="description" rows="2">{{ old('description') }}</textarea>
                        @if($errors->has('description'))
                            <div class="invalid-feedback">
                                {{ $errors->first('description') }}
                            </div>
                        @endif
                    </div>
                    <label for="form_id" class="col-md-2 col-form-label required">{{ __('cruds.formIntegrationSetting.fields.form') }}</label>
                    <div class="col-md-4">
                        <select class="form-control {{ $errors->has('form_id') ? 'is-invalid' : '' }}" 
                                id="form_id" name="form_id" required>
                            <option value="">{{ __('global.pleaseSelect') }}</option>
                            @foreach($forms as $form)
                                <option value="{{ $form->id }}" {{ old('form_id') == $form->id ? 'selected' : '' }}>
                                    {{ $form->title }}
                                </option>
                            @endforeach
                        </select>
                        @if($errors->has('form_id'))
                            <div class="invalid-feedback">
                                {{ $errors->first('form_id') }}
                            </div>
                        @endif
                    </div>
                </div>

                <div class="form-group row">
                    <label for="endpoint_configuration_id" class="col-md-2 col-form-label required">{{ __('cruds.formIntegrationSetting.fields.endpoint_configuration') }}</label>
                    <div class="col-md-10">
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label for="external_system_filter" class="form-label small text-muted mb-1">{{ __('cruds.integrationConfiguration.fields.external_system') }}</label>
                                <select class="form-control form-control-sm" id="external_system_filter">
                                    <option value="">{{ __('global.all') }}</option>
                                    @foreach(\App\Models\IntegrationConfiguration::getExternalSystemOptions() as $key => $value)
                                        <option value="{{ $key }}">{{ $value }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="integration_method_filter" class="form-label small text-muted mb-1">{{ __('cruds.integrationConfiguration.fields.integration_method') }}</label>
                                <select class="form-control form-control-sm" id="integration_method_filter">
                                    <option value="">{{ __('global.all') }}</option>
                                    @foreach(\App\Models\IntegrationConfiguration::getIntegrationMethodOptions() as $method)
                                        <option value="{{ $method }}">{{ $method }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="process_type_filter" class="form-label small text-muted mb-1">{{ __('cruds.integrationConfiguration.fields.process_type') }}</label>
                                <select class="form-control form-control-sm" id="process_type_filter">
                                    <option value="">{{ __('global.all') }}</option>
                                    @foreach(\App\Models\IntegrationConfiguration::getProcessTypeOptions() as $type)
                                        <option value="{{ $type }}">{{ $type }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <button type="button" id="reset-filters" class="btn btn-sm btn-outline-secondary w-100">
                                    <i class="fa fa-undo"></i> {{ __('global.reset') }}
                                </button>
                            </div>
                        </div>
                        
                        <select class="form-control {{ $errors->has('endpoint_configuration_id') ? 'is-invalid' : '' }}" 
                                id="endpoint_configuration_id" name="endpoint_configuration_id" required>
                            <option value="">{{ __('global.pleaseSelect') }}</option>
                            @foreach($integrations as $integration)
                                <option value="{{ $integration->id }}"
                                        data-external-system="{{ $integration->external_system }}"
                                        data-integration-method="{{ $integration->integration_method }}"
                                        data-process-type="{{ $integration->process_type }}"
                                        {{ old('endpoint_configuration_id') == $integration->id ? 'selected' : '' }}>
                                    {{ $integration->name }} ({{ $integration->external_system }} - {{ $integration->process_type }})
                                </option>
                            @endforeach
                        </select>
                        @if($errors->has('endpoint_configuration_id'))
                            <div class="invalid-feedback">
                                {{ $errors->first('endpoint_configuration_id') }}
                            </div>
                        @endif
                        <div class="form-text small text-muted mt-1">
                            {{ __('global.filter_help_text') }}
                        </div>
                    </div>
                </div>

                <div class="form-group row mb-3">
                    <div class="col-12">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="use_form_fields_as_endpoint" name="use_form_fields_as_endpoint">
                            <label class="form-check-label" for="use_form_fields_as_endpoint">
                                {{ __('Use form field keys as endpoint fields') }}
                            </label>
                            <small class="form-text text-muted d-block">
                                {{ __('When enabled, form field keys will be used as the endpoint field names.') }}
                            </small>
                        </div>
                    </div>
                </div>

                <div class="form-group row">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center">
                            <label class="form-label required">{{ __('cruds.formIntegrationSetting.fields.field_mappings') }}</label>
                            <button type="button" id="add-custom-field" class="btn btn-sm btn-outline-secondary">
                                <i class="fa fa-plus me-1"></i> {{ __('Add Endpoint Field') }}
                            </button>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <div class="row align-items-center">
                                    <div class="col">
                                        <!-- <h6 class="mb-0">{{ __('cruds.formIntegrationSetting.fields.field_mappings') }}</h6> -->
                                    </div>
                                    <div class="col-auto">
                                        <!-- <x-buttons.button 
                                            type="button" 
                                            id="import-json-btn" 
                                            variant="outline-secondary" 
                                            size="sm" 
                                            icon="file-import" 
                                            class="mr-2" 
                                            :title="__('global.import_fields_from_json')"
                                            style="display: none;"
                                        >
                                            {{ __('global.import') }} JSON
                                        </x-buttons.button> -->
                                        <button type="button" 
                                                id="suggest-mappings-btn" 
                                                class="btn btn-sm btn-outline-primary"
                                                style="display: none;">
                                            <i class="fa fa-magic me-1"></i> {{ __('global.suggest_mapping') }}
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="field-mappings-container">
                                    <div class="text-center text-muted py-4">
                                        <i class="fa fa-info-circle fa-2x mb-2"></i>
                                        <p>{{ __('cruds.formIntegrationSetting.message.select_form_and_endpoint_to_configure_mappings') }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <input type="hidden" name="field_mappings" id="field_mappings" value="{{ old('field_mappings', '') }}">
                        @if($errors->has('field_mappings'))
                            <div class="invalid-feedback d-block">
                                {{ $errors->first('field_mappings') }}
                            </div>
                        @endif
                    </div>
                </div>

                <div class="form-group row">
                    <div class="col-md-8 offset-md-4 text-end">
                        <x-buttons.cancel :route="route('admin.form-integration-settings.index')" class="me-2"/>
                        <x-buttons.save :route="route('admin.form-integration-settings.store')" class="ms-2"/>
                    </div>
                </div>
            </form>
            
            <!-- Custom Dialog for Endpoint Field Input -->
            <div id="customDialog" style="display: none; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 20px; border-radius: 5px; box-shadow: 0 0 10px rgba(0,0,0,0.3); z-index: 10000;">
                <div style="margin-bottom: 15px;">
                    <h5 id="endpointFieldLabel">Add Endpoint Field</h5>
                </div>
                <div class="form-group" style="margin-bottom: 15px;">
                    <label for="newEndpointFieldName">Field Name:</label>
                    <input type="text" class="form-control" id="newEndpointFieldName" style="width: 100%; padding: 5px; margin-top: 5px;">
                    <div class="invalid-feedback" id="fieldNameError"></div>
                </div>
                <div style="text-align: right;">
                    <button type="button" class="btn btn-secondary" id="cancelDialog" style="margin-right: 10px;">Cancel</button>
                    <button type="button" class="btn btn-primary" id="saveEndpointField">Add Field</button>
                </div>
            </div>
            <div id="dialogOverlay" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999;"></div>
        </div>
    </div>
@endsection

@section('scripts')
<!-- Bootstrap JS Bundle with Popper -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
$(document).ready(function() {
    let formFields = [];
    let endpointFields = [];
    let fieldMappings = {};
    
    // Initialize tooltips if Bootstrap tooltips are available
    if (typeof $().tooltip === 'function') {
        $('[data-bs-toggle="tooltip"]').tooltip();
    }

    // Load old field mappings if validation failed
    @if(old('field_mappings'))
        const oldMappings = @json(old('field_mappings'));
        // Convert the old mappings to the correct format if needed
        if (Array.isArray(oldMappings)) {
            // Handle array format (if needed)
            oldMappings.forEach(mapping => {
                if (mapping && mapping.form_field && mapping.endpoint_field) {
                    fieldMappings[mapping.form_field] = mapping.endpoint_field;
                }
            });
        } else if (typeof oldMappings === 'object' && oldMappings !== null) {
            // Handle object format
            fieldMappings = { ...oldMappings };
        }
    @endif

    // Form selection changed
    $('#form_id').change(function() {
        const formId = $(this).val();
        console.log('Form selection changed to ID:', formId);
        
        if (!formId) {
            console.log('No form selected, clearing fields');
            clearFormFields();
            return;
        }

        // Show loading state
        const $formSelect = $(this);
        const originalHtml = $formSelect.html();
        $formSelect.prop('disabled', true).html('<option value="">{{ __("global.loading") }}...</option>');

        loadFormFields(formId).done(function(response) {
            formFields = response.fields || [];
            updateFieldMappingSection();
        }).fail(function(xhr) {
            console.error('Error loading form fields:', xhr.responseText);
            showAlert('danger', '{{ __("global.error_loading_form_fields") }}');
        }).always(function() {
            $formSelect.prop('disabled', false).html(originalHtml).val(formId);
        });
    });
    
    // Show alert message
    function showAlert(type, message) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
        
        // Remove any existing alerts
        $('.alert-dismissible').alert('close');
        
        // Add the new alert
        $('.card-block').prepend(alertHtml);
    }

    // Handle endpoint configuration change
    $('#endpoint_configuration_id').on('change', function() {
        const endpointId = $(this).val();
        const $endpointSelect = $(this);
        const originalHtml = $endpointSelect.html();
        
        if (!endpointId) {
            endpointFields = [];
            updateFieldMappingSection();
            return;
        }
        
        // Show loading state
        $endpointSelect.prop('disabled', true).html('<option value="">{{ __("global.loading") }}...</option>');
        
        loadEndpointFields(endpointId)
            .done(function(response) {
                endpointFields = response.fields || [];
                console.log('Endpoint fields loaded successfully:', endpointFields);
                
                if ($('#use_form_fields_as_endpoint').is(':checked')) {
                    // If using form fields as endpoint fields, rebuild based on form fields
                    $('#use_form_fields_as_endpoint').trigger('change');
                } else {
                    // Otherwise, update with endpoint fields
                    updateFieldMappingSection();
                }
            })
            .fail(function(jqXHR, textStatus, errorThrown) {
                console.error('Failed to load endpoint configuration:', textStatus, errorThrown);
                console.error('Response:', jqXHR.responseText);
                showAlert('danger', '{{ __("global.error_loading_endpoint_configuration") }}');
                endpointFields = [];
                updateFieldMappingSection();
            })
            .always(function() {
                $endpointSelect.prop('disabled', false).html(originalHtml).val(endpointId);
            });
    });

    // Filter endpoint configurations
    function filterEndpointConfigurations() {
        const externalSystem = $('#external_system_filter').val();
        const integrationMethod = $('#integration_method_filter').val();
        const processType = $('#process_type_filter').val();

        let hasVisibleOptions = false;
        
        $('#endpoint_configuration_id option').each(function() {
            if ($(this).val() === '') {
                $(this).show();
                return true; // Skip the default option
            }

            const matchesExternalSystem = !externalSystem || $(this).data('external-system') === externalSystem;
            const matchesIntegrationMethod = !integrationMethod || $(this).data('integration-method') === integrationMethod;
            const matchesProcessType = !processType || $(this).data('process-type') === processType;

            if (matchesExternalSystem && matchesIntegrationMethod && matchesProcessType) {
                $(this).show();
                hasVisibleOptions = true;
            } else {
                $(this).hide();
                if ($(this).is(':selected')) {
                    $('#endpoint_configuration_id').val('').trigger('change');
                }
            }
        });
        
        // Show message if no options match the filters
        const $noResults = $('#no-endpoint-results');
        if (!hasVisibleOptions) {
            if ($noResults.length === 0) {
                $('#endpoint_configuration_id').after('<div id="no-endpoint-results" class="text-muted small mt-2">{{ __("global.no_matching_endpoints") }}</div>');
            }
        } else {
            $noResults.remove();
            const $selectedOption = $('#endpoint_configuration_id option:selected');
            if ($selectedOption.length > 0 && $selectedOption.css('display') === 'none') {
                $('#endpoint_configuration_id').val('');
            }
        }
    }

    // Suggest mappings button
    $('#suggest-mappings-btn').click(function() {
        suggestFieldMappings();
    });



    // Load form fields via AJAX
    function loadFormFields(formId) {
        console.log('Loading form fields for form ID:', formId);
        
        return $.ajax({
            url: '{{ route("admin.form-integration-settings.get-form-fields") }}',
            method: 'GET',
            data: { 
                form_id: formId,
                _token: '{{ csrf_token() }}' // Ensure CSRF token is included
            },
            dataType: 'json',
            success: function(response) {
                console.log('Form fields response received:', response);
                
                if (!response) {
                    console.error('Empty response received from server');
                    throw new Error('Empty response from server');
                }
                
                // Handle different response formats
                if (response.fields) {
                    formFields = Array.isArray(response.fields) ? response.fields : [];
                } else if (response.data) {
                    formFields = Array.isArray(response.data) ? response.data : [];
                } else if (Array.isArray(response)) {
                    formFields = response; // Direct array response
                } else {
                    console.warn('Unexpected response format:', response);
                    formFields = [];
                }
                
                console.log(`Processed ${formFields.length} form fields`);
                
                // Log field keys for debugging
                if (formFields.length > 0) {
                    console.log('Form field keys:', formFields.map(f => f.key || f.name || 'unknown'));
                } else {
                    console.warn('No form fields found in response');
                }
                
                return Promise.resolve(formFields);
            },
            error: function(xhr, status, error) {
                let errorMsg = `Failed to load form fields: ${status}`;
                console.error(errorMsg, error);
                console.error('Response:', xhr.responseText);
                
                // Show more detailed error message
                let errorDetails = '';
                try {
                    const response = JSON.parse(xhr.responseText);
                    errorDetails = response.message || xhr.responseText;
                } catch (e) {
                    errorDetails = xhr.responseText || 'No details available';
                }
                
                showError(`Failed to load form fields: ${error}`, errorDetails);
                clearFormFields();
                
                // Reject the promise to trigger .fail()
                return Promise.reject(error);
            }
        });
    }

    // Load endpoint fields via AJAX
    function loadEndpointFields(integrationId) {
        return $.ajax({
            url: '{{ route("admin.form-integration-settings.get-integration-fields") }}',
            method: 'GET',
            data: { integration_id: integrationId },
            success: function(response) {
                console.log('Endpoint fields response:', response);
                endpointFields = response.fields || response.data || [];
            },
            error: function(xhr, status, error) {
                console.error('Failed to load endpoint fields:', xhr.responseText);
                showError('Failed to load endpoint fields');
                clearEndpointFields();
            }
        });
    }



    // Update field mapping section visibility and content
    function updateFieldMappingSection() {
        console.log('Updating field mapping section. Form fields:', formFields, 'Endpoint fields:', endpointFields);
        
        // Show/hide suggest mappings button based on whether we have both form and endpoint fields
        if (formFields.length > 0 && endpointFields.length > 0) {
            $('#suggest-mappings-btn').show();
            // $('#import-json-btn').show();
            console.log('Displaying field mappings');
            displayEndpointFieldMappings();
        } else {
            $('#suggest-mappings-btn').hide();
            $('#import-json-btn').hide();
            
            // If we have endpoint fields but no form fields, still show the interface
            if (endpointFields.length > 0) {
                console.log('Displaying endpoint fields without form fields');
                displayEndpointFieldMappings();
            }
            // If we have form fields but no endpoint fields, show a message
            else if (formFields.length > 0) {
                console.log('Form fields loaded but no endpoint fields selected');
                $('#field-mappings-container').html(`
                    <div class="alert alert-info">
                        <i class="fa fa-info-circle"></i>
                        Please select an endpoint configuration to map form fields.
                    </div>
                `);
            }
            // If we have neither, show the loading state
            else {
                console.log('No form fields or endpoint fields available');
                showFieldMappingPlaceholder();
            }
        }
    }
        
    // Update the hidden field mappings input with current form state
    function updateFieldMappingsInput() {
        const mappings = [];
        const useFormFields = $('#use_form_fields_as_endpoint').is(':checked');
        
        if (useFormFields) {
            // When using form fields as endpoint fields
            $('.field-mapping-row').each(function() {
                const $row = $(this);
                const endpointField = $row.data('field-key');
                const $formFieldSelect = $row.find('.field-select');
                const $transformType = $row.find('.transform-type');
                const $transformValue = $row.find('.transform-value');
                const $requiredCheckbox = $row.find('[name$="[required]"]');
                
                const formField = $formFieldSelect.val();
                const transformType = $transformType.val();
                const transformValue = transformType === 'custom' ? $transformValue.val() : '';
                const isRequired = $requiredCheckbox.is(':checked');
                
                if (endpointField && formField) {
                    const mapping = {
                        endpoint_field: endpointField,
                        form_field: formField,
                        required: isRequired
                    };
                    
                    // Add transformation if specified
                    if (transformType) {
                        mapping.transform = {
                            type: transformType
                        };
                        if (transformValue) {
                            mapping.transform.value = transformValue;
                        }
                    }
                    
                    mappings.push(mapping);
                }
            });
        } else {
            // When using separate endpoint fields
            $('.field-mapping-row').each(function() {
                const $row = $(this);
                const $endpointField = $row.find('.endpoint-field');
                const $formFieldSelect = $row.find('.field-select');
                const $transformType = $row.find('.transform-type');
                const $transformValue = $row.find('.transform-value');
                const $requiredCheckbox = $row.find('[name$="[required]"]');
                
                const endpointField = $endpointField.val();
                const formField = $formFieldSelect.val();
                const transformType = $transformType.val();
                const transformValue = transformType === 'custom' ? $transformValue.val() : '';
                const isRequired = $requiredCheckbox.is(':checked');
                
                if (endpointField && formField) {
                    const mapping = {
                        endpoint_field: endpointField,
                        form_field: formField,
                        required: isRequired
                    };
                    
                    // Add transformation if specified
                    if (transformType) {
                        mapping.transform = {
                            type: transformType
                        };
                        if (transformValue) {
                            mapping.transform.value = transformValue;
                        }
                    }
                    
                    mappings.push(mapping);
                }
            });
            $('.field-mapping-row').each(function() {
                const $row = $(this);
                const $endpointField = $row.find('.endpoint-field');
                const $formFieldSelect = $row.find('.field-select');
                const $transformType = $row.find('.transform-type');
                const $transformValue = $row.find('.transform-value');
                const $requiredCheckbox = $row.find('[name$="[required]"]');
                
                const endpointField = $endpointField.val();
                const formField = $formFieldSelect.val();
                const transformType = $transformType.val();
                const transformValue = transformType === 'custom' ? $transformValue.val() : '';
                const isRequired = $requiredCheckbox.is(':checked');
                
                if (endpointField && formField) {
                    const mapping = {
                        endpoint_field: endpointField,
                        form_field: formField,
                        required: isRequired
                    };
                    
                    // Add transformation if specified
                    if (transformType) {
                        mapping.transform = {
                            type: transformType
                        };
                        if (transformValue) {
                            mapping.transform.value = transformValue;
                        }
                    }
                    
                    mappings.push(mapping);
                }
            });
        }
        
        // Update the hidden input with the mappings
        $('#field_mappings').val(JSON.stringify(mappings));
        console.log('Updated field mappings:', mappings);
    }

    // Display form fields in the table
    function displayFormFields() {
        const container = $('#field-mappings-container');
        container.html('');
        
        if (formFields.length === 0) {
            container.html(`
                <div class="alert alert-warning">
                    <i class="fa fa-exclamation-triangle"></i> No form fields available to map.
                </div>
            `);
            return;
        }
        
        // Create new table based on form fields
        let html = `
            <table class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th>Form Field</th>
                        <th>Display Name</th>
                        <th>Transformation</th>
                        <th>Required</th>
                    </tr>
                </thead>
                <tbody>
        `;
        
        // Create a row for each form field
        formFields.forEach(formField => {
            const fieldKey = formField.key;
            const isRequired = endpointFields.some(ef => ef.name === fieldKey && ef.required) || false;
            const savedMapping = fieldMappings[fieldKey];
            
            html += `
                <tr class="field-mapping-row" data-field-key="${fieldKey}">
                    <td>
                        <input type="text" 
                               class="form-control endpoint-field" 
                               name="endpoint_fields[${fieldKey}]" 
                               value="${fieldKey}" 
                               readonly>
                    </td>
                    <td>
                        <select class="form-select field-select" 
                                name="field_mapping[${fieldKey}][field]" 
                                data-endpoint-field="${fieldKey}">
                            <option value="">-- Select Form Field --</option>
                            <option value="${fieldKey}" selected>${formField.label || fieldKey}</option>
                        </select>
                    </td>
                    <td>
                        <select class="form-select transform-type" 
                                name="field_mapping[${fieldKey}][transform][type]">
                            <option value="">None</option>
                            <option value="uppercase">Uppercase</option>
                            <option value="lowercase">Lowercase</option>
                            <option value="trim">Trim</option>
                            <option value="custom">Custom</option>
                        </select>
                        <input type="text" 
                               class="form-control mt-1 transform-value d-none" 
                               name="field_mapping[${fieldKey}][transform][value]" 
                               placeholder="Enter transformation value/pattern">
                    </td>
                    <td class="text-center">
                        <input type="checkbox" 
                               class="form-check-input" 
                               name="field_mapping[${fieldKey}][required]" 
                               value="1"
                               ${isRequired ? 'checked' : ''}>
                    </td>
                </tr>
            `;
        });
        
        html += `
                    </tbody>
                </table>
            </div>
        `;
        
        container.html(html);
        
        // Update the mappings input when fields change
        $('.field-select, .endpoint-field, [name^="required_fields"]').on('change', function() {
            updateFieldMappingsInput();
        });
    }
    
    // Handle transformation type changes
    $(document).on('change', '.transform-type', function() {
        const $transformValue = $(this).siblings('.transform-value');
        if ($(this).val() === 'custom') {
            $transformValue.removeClass('d-none');
        } else {
            $transformValue.addClass('d-none');
        }
        updateFieldMappingsInput();
    });
    
    // Update field mappings when any field changes
    $(document).on('change input', '.field-select, .endpoint-field, [name$="[required]"], .transform-type, .transform-value', function() {
        updateFieldMappingsInput();
    });
    
    // Toggle form fields as endpoint fields
    $(document).on('change', '#use_form_fields_as_endpoint', function() {
        console.log('Checkbox changed. Checked:', $(this).is(':checked'));
        const useFormFields = $(this).is(':checked');
        
        if (useFormFields) {
            displayFormFields();
        } else {
            displayEndpointFieldMappings();
        }
        
        // Update the mappings input
        updateFieldMappingsInput();
    });
    
    // Manually trigger change event on page load if checkbox is checked
    $(document).ready(function() {
    if ($('#use_form_fields_as_endpoint').is(':checked')) {
        $('#use_form_fields_as_endpoint').trigger('change');
    }
    });
    
    // Update endpoint field when form field selection changes (when in form-field-as-endpoint mode)
    $(document).on('change', '.field-select', function() {
        if ($('#use_form_fields_as_endpoint').is(':checked')) {
            const $row = $(this).closest('tr');
            const selectedValue = $(this).val();
            $row.find('.endpoint-field').val(selectedValue || '');
            updateFieldMappingsInput();
        }
    });
    
    // Handle click on "Add Endpoint Field" button
    $(document).on('click', '#add-custom-field', function(e) {
        console.log('Add Endpoint Field button clicked');
        
        // Reset the dialog
        $('#newEndpointFieldName').val('').removeClass('is-invalid');
        $('#fieldNameError').text('');
        
        // Set the label based on the current mode
        const useFormFields = $('#use_form_fields_as_endpoint').is(':checked');
        const labelText = useFormFields ? 
            'Enter the name for the new endpoint field (it will also be used as the form field key):' : 
            'Enter the name for the new endpoint field:';
        
        $('#endpointFieldLabel').text(labelText);
        
        // Show the dialog and overlay
        $('#customDialog, #dialogOverlay').show();
        
        // Focus the input field
        setTimeout(() => {
            $('#newEndpointFieldName').focus();
        }, 100);
    });
    
    // Handle dialog close
    $(document).on('click', '#cancelDialog, #dialogOverlay', function() {
        $('#customDialog, #dialogOverlay').hide();
    });
    
    // Function to add a new endpoint field
    function addNewEndpointField(fieldName) {
        const useFormFields = $('#use_form_fields_as_endpoint').is(':checked');
        
        // Add the new field to endpointFields
        const newField = {
            name: fieldName,
            type: 'text',
            required: false,
            is_form_field: false // Mark this as not a form field
        };
        
        // If using form fields as endpoint, also add it to formFields if not already present
        if (useFormFields) {
            if (!formFields.some(f => f.key === fieldName)) {
                formFields.push({
                    key: fieldName,
                    label: fieldName,
                    type: 'text'
                });
            }
            // Create a mapping for the new field
            fieldMappings[fieldName] = fieldName;
            
            // Update the display to show form fields
            displayFormFields();
        } else {
            // Add to endpoint fields and update display
            endpointFields.push(newField);
            displayEndpointFieldMappings();
        }
        
        console.log('Added new endpoint field:', fieldName);
        
        // Close the dialog
        $('#customDialog, #dialogOverlay').hide();
        
        // Update the mappings input
        updateFieldMappingsInput();
    }
    
    // Handle save button click in the modal
    $(document).on('click', '#saveEndpointField', function() {
        const fieldName = $('#newEndpointFieldName').val().trim();
        
        // Validate input
        if (!fieldName) {
            $('#newEndpointFieldName').addClass('is-invalid');
            $('#fieldNameError').text('Field name is required');
            return;
        }
        
        // Check if field already exists
        const fieldExists = endpointFields.some(field => field.name === fieldName);
        if (fieldExists) {
            $('#newEndpointFieldName').addClass('is-invalid');
            $('#fieldNameError').text('An endpoint field with this name already exists');
            return;
        }
        
        addNewEndpointField(fieldName);
    });
    
    // Handle Enter key in the input field
    $('#newEndpointFieldName').on('keypress', function(e) {
        if (e.which === 13) { // Enter key
            e.preventDefault();
            $('#saveEndpointField').click();
        }
    });
    // });

    // Reset field mappings to their default state
    function resetFieldMappings() {
        console.log('Resetting field mappings');
        // Clear any existing field mappings
        fieldMappings = {};
        
        // Reset the hidden input
        $('#field_mappings').val('');
        
        // Clear the container
        $('#field-mappings-container').html('');
    }

    // Display endpoint fields with form field mapping options
    function displayEndpointFieldMappings() {
        const container = $('#field-mappings-container');
        const useFormFields = $('#use_form_fields_as_endpoint').is(':checked');
        
        // Reset field mappings before rendering
        resetFieldMappings();
        
        if (endpointFields.length === 0) {
            container.html(`
                <div class="alert alert-info">
                    <i class="fa fa-info-circle"></i> No endpoint fields available. Please select an endpoint configuration first.
                </div>
            `);
            return;
        }

        let html = `
            <table class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th>${useFormFields ? 'Form Field' : 'Endpoint Field'}</th>
                        <th>${useFormFields ? 'Display Name' : 'Form Field'}</th>
                        <th>Transformation</th>
                        <th>Required</th>
                    </tr>
                </thead>
                <tbody>
        `;

        // Process each endpoint field
        endpointFields.forEach((field, index) => {
            const isRequired = field.required || false;
            let fieldName = field.name || '';
            
            // Skip if this is a form field (not an endpoint field)
            if (field.is_form_field) return;
            
            // If using form fields as endpoint, use the mapped form field as the endpoint field
            const displayName = useFormFields ? (fieldMappings[fieldName] || '') : fieldName;
            const endpointFieldName = useFormFields ? (fieldMappings[fieldName] || fieldName) : fieldName;
            
            // Find saved mapping if exists for this field
            const savedMapping = fieldMappings[fieldName] || '';
            
            html += `
                <tr class="field-mapping-row" data-field-key="${endpointFieldName}">
                    <td>${useFormFields ? (savedMapping || fieldName) : fieldName}${isRequired ? ' <span class="text-danger">*</span>' : ''}</td>
                    <td>
                        <select class="form-select field-select" 
                                name="field_mapping[${endpointFieldName}][field]" 
                                data-endpoint-field="${endpointFieldName}">
                            <option value="">-- Select Form Field --</option>
            `;

            // Add form field options
            formFields.forEach(formField => {
                const selected = savedMapping === formField.key ? 'selected' : '';
                html += `<option value="${formField.key}" ${selected}>${formField.label || formField.key}</option>`;
            });

            html += `
                        </select>
                    </td>
                    <td>
                        <select class="form-select transform-type" 
                                name="field_mapping[${endpointFieldName}][transform][type]">
                            <option value="">None</option>
                            <option value="uppercase">Uppercase</option>
                            <option value="lowercase">Lowercase</option>
                            <option value="trim">Trim</option>
                            <option value="custom">Custom</option>
                        </select>
                        <input type="text" 
                               class="form-control mt-1 transform-value d-none" 
                               name="field_mapping[${endpointFieldName}][transform][value]" 
                               placeholder="Enter transformation value/pattern">
                    </td>
                    <td class="text-center">
                        <input type="checkbox" 
                               class="form-check-input" 
                               name="field_mapping[${endpointFieldName}][required]" 
                               value="1"
                               ${isRequired ? 'checked' : ''}>
                    </td>
                    <td class="text-center">
                        <button type="button" class="btn btn-sm btn-outline-danger remove-field">
                            <i class="fa fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;
        });

        html += `
                    </tbody>
                </table>
            </div>
        `;

        container.html(html);
        
        // Update the mappings input when fields change
        $('.field-select, .endpoint-field, [name^="required_fields"]').on('change', function() {
            updateFieldMappingsInput();
        });
    }

    // Auto-suggest button click handler
    /**
     * Shows an alert message
     */
    function showAlert(type, message) {
        // Remove any existing alerts first
        $('.alert-dismissible').alert('close');
        
        // Create alert HTML
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
        
        // Add alert to the page (before the form)
        $('form#integration-form').prepend(alertHtml);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            $('.alert-dismissible').alert('close');
        }, 5000);
    }
    
    /**
     * Suggests field mappings based on field names
     */
    function suggestFieldMappings() {
        console.log('Suggesting field mappings...');
        
        // Get the form and endpoint configuration IDs
        const formId = $('#form_id').val();
        const configId = $('#endpoint_configuration_id').val();
        
        if (!formId || !configId) {
            console.error('Form ID or Endpoint Configuration ID is missing');
            showAlert('warning', 'Please select both a form and an endpoint configuration first');
            return;
        }
        
        // Show loading state
        const $btn = $('#suggest-mappings-btn');
        const originalText = $btn.html();
        $btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> {{ __("global.suggesting") }}...');
        
        // Make AJAX request to get mapping suggestions
        $.ajax({
            url: '{{ route("admin.form-integration-settings.get-field-mapping-suggestions") }}',
            method: 'GET',
            data: {
                form_id: formId,
                endpoint_configuration_id: configId
            },
            success: function(response) {
                console.log('Received mapping suggestions:', response);
                
                if (response.suggestions && Object.keys(response.suggestions).length > 0) {
                    // Apply the suggested mappings
                    let appliedCount = 0;
                    Object.entries(response.suggestions).forEach(([formField, endpointField]) => {
                        const $select = $(`select[name^="field_mapping["][data-endpoint-field="${endpointField}"]`);
                        if ($select.length) {
                            $select.val(formField).trigger('change');
                            appliedCount++;
                        }
                    });
                    
                    // Update the hidden field mappings
                    updateFieldMappingsInput();
                    
                    if (appliedCount > 0) {
                        showAlert('success', `Successfully applied ${appliedCount} field mapping suggestions`);
                    } else {
                        showAlert('info', 'No matching fields found for suggestions');
                    }
                } else {
                    showAlert('info', 'No mapping suggestions available for the selected form and endpoint');
                }
            },
            error: function(xhr) {
                console.error('Error getting mapping suggestions:', xhr);
                let errorMessage = 'Failed to get mapping suggestions. Please try again.';
                
                // Try to get a more specific error message from the response
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (response.message) {
                        errorMessage = response.message;
                    }
                } catch (e) {
                    // If we can't parse the response, use the default message
                }
                
                showAlert('danger', errorMessage);
            },
            complete: function() {
                // Restore button state
                $btn.prop('disabled', false).html(originalText);
            }
        });
    }
    
    // Handle suggest mappings button click
    $(document).on('click', '#suggest-mappings-btn', function(e) {
        e.preventDefault();
        console.log('Auto-suggest button clicked');
        suggestFieldMappings();
    });

    // Filter endpoint options based on selected filters
    function filterEndpointOptions() {
        const externalSystem = $('#external_system_filter').val();
        const integrationMethod = $('#integration_method_filter').val();
        const processType = $('#process_type_filter').val();

        $('#endpoint_configuration_id option').each(function() {
            if ($(this).val() === '') return; // Skip the default option

            const extSystem = $(this).data('external-system');
            const intMethod = $(this).data('integration-method');
            const procType = $(this).data('process-type');

            let show = true;

            if (externalSystem && extSystem !== externalSystem) show = false;
            if (integrationMethod && intMethod !== integrationMethod) show = false;
            if (processType && procType !== processType) show = false;

            $(this).toggle(show);
        });
    }

    // Clear form fields
    function clearFormFields() {
        formFields = [];
        $('#form-fields-list').html('<p class="text-muted">Select a form to see its fields</p>');
    }

    // Show field mapping placeholder
    function showFieldMappingPlaceholder() {
        $('#field-mappings-container').html('<div class="text-center py-3 text-muted"><i class="fas fa-spinner fa-spin"></i> Loading form fields...</div>');
    }

    // Clear endpoint fields
    function clearEndpointFields() {
        endpointFields = [];
        $('#endpoint-fields-list').html('<p class="text-muted">Select an endpoint configuration to see its fields</p>');
        updateFieldMappingSection();
    }

    // Utility functions
    function showError(message) {
        // You can implement a toast notification or alert here
        alert('Error: ' + message);
    }

    function showSuccess(message) {
        // You can implement a toast notification here
        alert('Success: ' + message);
    }

    function showWarning(message) {
        // You can implement a toast notification here
        alert('Warning: ' + message);
    }

    // Initialize if old values exist
    if ($('#form_id').val()) {
        loadFormFields($('#form_id').val());
    }
    if ($('#endpoint_configuration_id').val()) {
        loadEndpointFields($('#endpoint_configuration_id').val());
    }

    // Close the document ready function
});
</script>
@endsection

@section('styles')
<style>
    /* Custom dialog styles */
    #customDialog {
        min-width: 400px;
        max-width: 90%;
    }
    #customDialog .btn {
        padding: 5px 15px;
    }
.required::after {
    content: " *";
    color: red;
}

.form-field-item, .endpoint-field-item {
    cursor: pointer;
    transition: background-color 0.2s;
}

.form-field-item:hover, .endpoint-field-item:hover {
    background-color: #f8f9fa;
}

.mapping-row {
    background-color: #f8f9fa;
}

.badge {
    font-size: 0.7em;
}

#field-mapping-section .card-body {
    background-color: #fff;
}

.field-mappings-list {
    max-height: 500px;
    overflow-y: auto;
}
</style>

<!-- JSON Import Modal -->
<div class="modal fade" id="importJsonModal" tabindex="-1" role="dialog" aria-labelledby="importJsonModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="importJsonModalLabel">Import Field Mappings from JSON</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="jsonInput">Paste your JSON object with field mappings:</label>
                    <textarea class="form-control" id="jsonInput" rows="10" placeholder='{"form_field1": "endpoint_field1", "form_field2": "endpoint_field2"}'></textarea>
                    <small class="form-text text-muted">
                        Format: <code>{"form_field_name": "endpoint_field_name", ...}</code>
                    </small>
                </div>
                <div id="jsonError" class="alert alert-danger" style="display: none;"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="applyJsonMappings">Apply Mappings</button>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Import JSON button click handler
    $('#import-json-btn').on('click', function() {
        $('#jsonInput').val('');
        $('#jsonError').hide();
        $('#importJsonModal').modal('show');
    });

    // Apply JSON mappings
    $('#applyJsonMappings').on('click', function() {
        const jsonInput = $('#jsonInput').val().trim();
        
        if (!jsonInput) {
            showJsonError('Please enter JSON data');
            return;
        }

        try {
            // Parse the JSON input
            const jsonMappings = JSON.parse(jsonInput);
            
            if (typeof jsonMappings !== 'object' || jsonMappings === null) {
                throw new Error('Invalid JSON format. Expected an object with field mappings.');
            }

            // Clear existing mappings
            fieldMappings = {};
            
            // Process each mapping
            let validMappings = 0;
            
            for (const [formField, endpointField] of Object.entries(jsonMappings)) {
                if (typeof formField === 'string' && typeof endpointField === 'string' && 
                    formField.trim() !== '' && endpointField.trim() !== '') {
                    
                    // Check if the endpoint field exists
                    const endpointExists = endpointFields.some(field => field.name === endpointField);
                    
                    if (endpointExists) {
                        fieldMappings[formField] = endpointField;
                        validMappings++;
                        
                        // Update the select element if it exists
                        const $select = $(`.form-field-select[data-endpoint-field="${endpointField}"]`);
                        if ($select.length) {
                            $select.val(formField).trigger('change');
                        }
                    }
                }
            }
            
            // Update hidden fields
            updateHiddenFieldMappings();
            
            // Show success message
            showSuccess(`Successfully imported ${validMappings} field mappings`);
            
            // Close the modal
            $('#importJsonModal').modal('hide');
            
        } catch (error) {
            showJsonError('Invalid JSON: ' + error.message);
        }
    });
    
    // Show JSON error message
    function showJsonError(message) {
        const $errorDiv = $('#jsonError');
        $errorDiv.text(message).show();
        $('html, body').animate({
            scrollTop: $errorDiv.offset().top - 100
        }, 500);
    }
});
</script>

@endsection
