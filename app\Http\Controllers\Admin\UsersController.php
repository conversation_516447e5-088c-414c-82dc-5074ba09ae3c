<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreUserRequest;
use App\Http\Requests\UpdateUserRequest;
use App\Models\User;
use App\Models\UserGroup;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;
use Gate;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Hash;

class UsersController extends Controller
{
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $query = User::with('userGroup')->orderBy('created_at', 'desc');
            $query = $query->applyDataTableFilters($request);

            $user = auth()->user();

            $query = $query->where('type', '>', $user->type);

            return DataTables::of($query)
                ->addIndexColumn()
                ->addColumn('action', function ($row) {

                    $actions = [];
                    // $actions['view'] = route('admin.usergroups.show', $row->id);
                    // if ($user->isAdmin() || $user->isSuperAdmin()) {
                    $actions['edit'] = route('admin.users.edit', $row->id);
                    $actions['delete'] = route('admin.users.destroy', $row->id);
                    // }
                    $html = view('partials.datatablesActions', compact('actions', 'row'))->render();
                    return $html;
                })
                ->addColumn('user_group', function ($row) {
                    return $row->userGroup ? $row->userGroup->name : 'No Group';
                })
                ->addColumn('role_type', function ($row) {
                    switch ($row->type) {
                        case User::TYPE_SUPER_ADMIN:
                            return '<span class="badge badge-danger">Super Admin</span>';
                        case User::TYPE_ADMIN:
                            return '<span class="badge badge-warning">Admin</span>';
                        case User::TYPE_USER:
                            return '<span class="badge badge-info">User</span>';
                        default:
                            return '<span class="badge badge-secondary">Unknown</span>';
                    }
                })
                // ->editColumn('created_at', function ($row) {
                //     return $row->created_at->format('Y-m-d');
                // })
                ->rawColumns(['action', 'role_type'])
                ->make(true);
        }

        return view('admin.users.index');
    }

    public function create()
    {
        $userGroups = UserGroup::active()->pluck('name', 'id');
        return view('admin.users.create', compact('userGroups'));
    }

    public function store(StoreUserRequest $request)
    {
        $data = $request->validated();
        $data['password'] = Hash::make($request->password);

        User::create($data);

        return redirect()->route('admin.users.index')
            ->with('success', 'User created successfully.');
    }

    public function show(User $user)
    {
        $user->load('userGroup');
        return view('admin.users.show', compact('user'));
    }

    public function edit(User $user)
    {
        $userGroups = UserGroup::active()->pluck('name', 'id');
        return view('admin.users.edit', compact('user', 'userGroups'));
    }

    public function update(UpdateUserRequest $request, User $user)
    {
        $data = $request->validated();

        // Remove password from data if it's not provided
        if (isset($data['password']) && empty($data['password'])) {
            unset($data['password']);
        } elseif (isset($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        }

        $user->update($data);

        return redirect()->route('admin.users.index')
            ->with('success', 'User updated successfully.');
    }

    public function destroy(User $user)
    {
        $user->delete();

        return redirect()->route('admin.users.index')
            ->with('success', 'User deleted successfully.');
    }

    public function massDestroy(Request $request)
    {
        User::whereIn('id', $request->ids)->delete();
        return response(null, Response::HTTP_NO_CONTENT);
    }
}
