<?php

use Illuminate\Support\Facades\Route;

// Include the check endpoint configs route (temporary for debugging)
require __DIR__ . '/check-endpoint-configs.php';

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::redirect('/', '/login');

Auth::routes();


Route::group(
    ['prefix' => 'admin', 'as' => 'admin.', 'namespace' => 'App\Http\Controllers\Admin', 'middleware' => ['user_type:admin,super_admin']],
    function () {

        // Route::middleware('user_type:admin')->group(function () {
        // Route::get('/admin', fn() => view('admin'));

        // Roles
        Route::delete('roles/destroy', 'RolesController@massDestroy')->name('roles.massDestroy');
        Route::resource('roles', 'RolesController');

        // Users
        Route::delete('users/destroy', 'UsersController@massDestroy')->name('users.massDestroy');
        Route::resource('users', 'UsersController');

        // User Groups
        Route::get('usergroups/forms', 'UserGroupController@formGroups')->name('usergroups.forms');
        Route::post('usergroups/forms', 'UserGroupController@storeFormGroups')->name('usergroups.storeForms');

        Route::delete('usergroups/destroy', 'UserGroupController@massDestroy')->name('usergroups.massDestroy');
        Route::resource('usergroups', 'UserGroupController');

        // Forms

        Route::get('forms/{form}/clone', 'FormsController@clone')->name('forms.clone');

        Route::get('forms/{form}/preview', 'FormsController@preview')->name('forms.preview');
        Route::get('forms/standard', 'FormsController@getStandardForms')->name('forms.getStandardForms');

        Route::resource('forms', 'FormsController');

        Route::resource('qr-decoder-profiles', 'QrDecoderProfileController');

        // Form Field Templates
        Route::delete('form-field-templates/destroy', 'FormFieldTemplatesController@massDestroy')->name('form-field-templates.massDestroy');
        Route::resource('form-field-templates', 'FormFieldTemplatesController');




        // Form Submissions
        Route::resource('form-submissions', 'FormSubmissionController', ['parameters' => ['form-submissions' => 'uuid']]);
        Route::post('form-submissions/{uuid}/retry', 'FormSubmissionController@retry')->name('form-submissions.retry');
        Route::get('form-submissions-statistics', 'FormSubmissionController@statistics')->name('form-submissions.statistics');
        Route::get('form-submissions-export', 'FormSubmissionController@export')->name('form-submissions.export');

        // Audit Logs
        Route::get('audit-logs', 'AuditLogController@index')->name('audit-logs.index');
        Route::get('audit-logs/{id}', 'AuditLogController@show')->name('audit-logs.show');
        Route::get('audit-logs-statistics', 'AuditLogController@statistics')->name('audit-logs.statistics');
        Route::get('audit-logs-export', 'AuditLogController@export')->name('audit-logs.export');

        // Error Logs
        Route::get('error-logs', 'ErrorLogController@index')->name('error-logs.index');
        Route::get('error-logs/{id}', 'ErrorLogController@show')->name('error-logs.show');
        Route::post('error-logs/{uuid}/resolve', 'ErrorLogController@resolve')->name('error-logs.resolve');
        Route::get('error-logs-statistics', 'ErrorLogController@statistics')->name('error-logs.statistics');
        Route::get('error-logs-export', 'ErrorLogController@export')->name('error-logs.export');
        Route::get('/', function () {
            return view('welcome');
        });
        //
    }
);
// Superadmin routes - only integration configurations
Route::group(
    ['prefix' => 'superadmin', 'as' => 'superadmin.', 'namespace' => 'App\Http\Controllers', 'middleware' => ['auth', 'user_type:super_admin']],
    function () {
        // Superadmin dashboard
        Route::get('/dashboard', fn() => view('super_admin'))->name('dashboard');

        // Field Mapping Configurations
        // Data Capture Fields
        Route::delete('data-capture-fields/destroy', 'Admin\DataCaptureFieldsController@massDestroy')->name('data-capture-fields.massDestroy');
        Route::resource('data-capture-fields', 'Admin\DataCaptureFieldsController');
        // Integration Configuration routes
        Route::resource('integration-configurations', 'Admin\IntegrationConfigurationController')->names([
            'index' => 'integration-configurations.index',
            'create' => 'integration-configurations.create',
            'store' => 'integration-configurations.store',
            'show' => 'integration-configurations.show',
            'edit' => 'integration-configurations.edit',
            'update' => 'integration-configurations.update',
            'destroy' => 'integration-configurations.destroy',
        ]);
    }
);

// Field Mapping Configurations routes - under admin
Route::group(
    ['prefix' => 'admin', 'as' => 'admin.', 'namespace' => 'App\Http\Controllers\Admin', 'middleware' => ['auth', 'user_type:admin']],
    function () {
        // AJAX routes for field mapping configurations
        Route::get('field-mapping-configurations/get-form-fields', 'FieldMappingConfigurationsController@getFormFields')
            ->name('field-mapping-configurations.get-form-fields');
        Route::get('field-mapping-configurations/get-integration-fields', 'FieldMappingConfigurationsController@getIntegrationFields')
            ->name('field-mapping-configurations.get-integration-fields');
        Route::get('field-mapping-configurations/get-field-mapping-suggestions', 'FieldMappingConfigurationsController@getFieldMappingSuggestions')
            ->name('field-mapping-configurations.get-field-mapping-suggestions');

        // Get endpoint fields for field mapping configurations
        Route::get('field-mapping-configurations/get-endpoint-fields', 'FieldMappingConfigurationsController@getEndpointFields')
            ->name('field-mapping-configurations.get-endpoint-fields');

        // Field Mapping Configuration resource routes
        Route::delete('field-mapping-configurations/destroy', 'FieldMappingConfigurationsController@massDestroy')
            ->name('field-mapping-configurations.massDestroy');
            
        Route::resource('field-mapping-configurations', 'FieldMappingConfigurationsController')->names([
            'index' => 'field-mapping-configurations.index',
            'create' => 'field-mapping-configurations.create',
            'store' => 'field-mapping-configurations.store',
            'show' => 'field-mapping-configurations.show',
            'edit' => 'field-mapping-configurations.edit',
            'update' => 'field-mapping-configurations.update',
            'destroy' => 'field-mapping-configurations.destroy',
        ]);

        // Integration Configuration routes
        Route::resource('integration-configurations', 'IntegrationConfigurationController')->names([
            'index' => 'integration-configurations.index',
            'create' => 'integration-configurations.create',
            'store' => 'integration-configurations.store',
            'show' => 'integration-configurations.show',
            'edit' => 'integration-configurations.edit',
            'update' => 'integration-configurations.update',
            'destroy' => 'integration-configurations.destroy',
        ]);
    }
);

// Superadmin routes - only integration configurations
// Route::group(
//     ['prefix' => 'superadmin', 'as' => 'superadmin.', 'namespace' => 'App\Http\Controllers', 'middleware' => ['auth', 'user_type:super_admin']],
//     function () {
//         // Superadmin dashboard
//         Route::get('/dashboard', fn() => view('super_admin'))->name('dashboard');

//         // Integration Configuration routes
//         Route::resource('integration-configurations', 'Admin\IntegrationConfigurationController')->names([
//             'index' => 'integration-configurations.index',
//             'create' => 'integration-configurations.create',
//             'store' => 'integration-configurations.store',
//             'show' => 'integration-configurations.show',
//             'edit' => 'integration-configurations.edit',
//             'update' => 'integration-configurations.update',
//             'destroy' => 'integration-configurations.destroy',
//         ]);
//     }
// );

Route::middleware('auth')->namespace('App\Http\Controllers')->group(function () {
    Route::get('/home', 'MenuController@mobilehome')->name('mobilehome');
    Route::get('/incoming', 'MenuController@incoming')->name('incoming');

    Route::get('/outgoing', 'MenuController@outgoing')->name('outgoing');
    Route::post('/forms/callIntegeration/{integrationAlias}', 'Admin\FormsController@callIntegeration')->name('forms.callIntegeration');
    Route::get('forms/{module}', 'FormController@getMobileForms')->name('mobile.forms');

    Route::post('forms/{form}/submit', 'Admin\FormsController@submit')->name('forms.submit');

    Route::get('forms/{form}/render', 'Admin\FormsController@render')->name('forms.render');
});
