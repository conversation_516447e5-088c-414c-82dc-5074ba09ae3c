<?php

namespace App\Services;

use App\Models\FormSubmission;
use App\Models\FormSubmissionSync;
use App\Models\ErrorLog;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class ErrorLoggerService
{
    /**
     * Log a general error
     */
    public function logError(
        string $level,
        string $type,
        string $message,
        array $context = [],
        ?FormSubmission $submission = null,
        ?FormSubmissionSync $sync = null
    ): ErrorLog {
        $errorLog = ErrorLog::createError(
            $level,
            $type,
            $message,
            $submission?->id,
            $sync?->id,
            $context['error_code'] ?? null,
            $context['stack_trace'] ?? null,
            $context
        );

        // Also log to <PERSON><PERSON>'s logging system
        Log::channel('form_sync_errors')->log($level, $message, array_merge($context, [
            'error_uuid' => $errorLog->uuid,
            'submission_uuid' => $submission?->uuid,
            'sync_uuid' => $sync?->uuid,
        ]));

        return $errorLog;
    }

    /**
     * Log a form submission error
     */
    public function logSubmissionError(
        ?FormSubmission $submission,
        \Exception $exception,
        string $level = ErrorLog::LEVEL_ERROR,
        string $type = ErrorLog::TYPE_SYSTEM_ERROR,
        array $context = []
    ): ErrorLog {
        return ErrorLog::createFromException(
            $exception,
            $level,
            $type,
            $submission?->id,
            null,
            $context
        );
    }

    /**
     * Log a form sync error
     */
    public function logSyncError(
        FormSubmissionSync $sync,
        \Exception $exception,
        string $level = ErrorLog::LEVEL_ERROR,
        string $type = ErrorLog::TYPE_SYSTEM_ERROR,
        array $context = []
    ): ErrorLog {
        return ErrorLog::createFromException(
            $exception,
            $level,
            $type,
            $sync->form_submission_id,
            $sync->id,
            array_merge($context, [
                'integration_name' => $sync->getIntegrationName(),
                'target_system' => $sync->getTargetSystem(),
                'retry_count' => $sync->retry_count,
            ])
        );
    }

    /**
     * Log a validation error
     */
    public function logValidationError(
        array $validationErrors,
        ?FormSubmission $submission = null,
        array $context = []
    ): ErrorLog {
        $message = 'Validation failed: ' . implode('; ', array_map(
            fn($field, $errors) => "{$field}: " . implode(', ', (array) $errors),
            array_keys($validationErrors),
            $validationErrors
        ));

        return $this->logError(
            ErrorLog::LEVEL_WARNING,
            ErrorLog::TYPE_VALIDATION_ERROR,
            $message,
            array_merge($context, ['validation_errors' => $validationErrors]),
            $submission
        );
    }

    /**
     * Log a network error
     */
    public function logNetworkError(
        string $message,
        FormSubmissionSync $sync,
        array $context = []
    ): ErrorLog {
        return $this->logError(
            ErrorLog::LEVEL_ERROR,
            ErrorLog::TYPE_NETWORK_ERROR,
            $message,
            array_merge($context, [
                'integration_name' => $sync->getIntegrationName(),
                'target_system' => $sync->getTargetSystem(),
            ]),
            $sync->formSubmission,
            $sync
        );
    }

    /**
     * Log an API error
     */
    public function logApiError(
        string $message,
        FormSubmissionSync $sync,
        ?string $responseBody = null,
        ?int $statusCode = null,
        array $context = []
    ): ErrorLog {
        return $this->logError(
            ErrorLog::LEVEL_ERROR,
            ErrorLog::TYPE_API_ERROR,
            $message,
            array_merge($context, [
                'integration_name' => $sync->getIntegrationName(),
                'target_system' => $sync->getTargetSystem(),
                'response_body' => $responseBody,
                'status_code' => $statusCode,
            ]),
            $sync->formSubmission,
            $sync
        );
    }

    /**
     * Log a timeout error
     */
    public function logTimeoutError(
        string $message,
        FormSubmissionSync $sync,
        int $timeoutSeconds,
        array $context = []
    ): ErrorLog {
        return $this->logError(
            ErrorLog::LEVEL_ERROR,
            ErrorLog::TYPE_TIMEOUT_ERROR,
            $message,
            array_merge($context, [
                'integration_name' => $sync->getIntegrationName(),
                'target_system' => $sync->getTargetSystem(),
                'timeout_seconds' => $timeoutSeconds,
            ]),
            $sync->formSubmission,
            $sync
        );
    }

    /**
     * Log an authentication error
     */
    public function logAuthenticationError(
        string $message,
        FormSubmissionSync $sync,
        array $context = []
    ): ErrorLog {
        return $this->logError(
            ErrorLog::LEVEL_CRITICAL,
            ErrorLog::TYPE_AUTHENTICATION_ERROR,
            $message,
            array_merge($context, [
                'integration_name' => $sync->getIntegrationName(),
                'target_system' => $sync->getTargetSystem(),
            ]),
            $sync->formSubmission,
            $sync
        );
    }

    /**
     * Log a configuration error
     */
    public function logConfigurationError(
        string $message,
        ?FormSubmissionSync $sync = null,
        array $context = []
    ): ErrorLog {
        return $this->logError(
            ErrorLog::LEVEL_CRITICAL,
            ErrorLog::TYPE_CONFIGURATION_ERROR,
            $message,
            array_merge($context, [
                'integration_name' => $sync?->getIntegrationName(),
                'target_system' => $sync?->getTargetSystem(),
            ]),
            $sync?->formSubmission,
            $sync
        );
    }

    /**
     * Log a queue error
     */
    public function logQueueError(
        string $message,
        \Exception $exception,
        array $context = []
    ): ErrorLog {
        return ErrorLog::createFromException(
            $exception,
            ErrorLog::LEVEL_ERROR,
            ErrorLog::TYPE_QUEUE_ERROR,
            null,
            null,
            array_merge($context, ['queue_message' => $message])
        );
    }

    /**
     * Resolve an error
     */
    public function resolveError(
        string $errorUuid,
        User $user,
        string $notes = null
    ): ErrorLog {
        $errorLog = ErrorLog::where('uuid', $errorUuid)->firstOrFail();
        $errorLog->markAsResolved($user, $notes);

        Log::info('Error resolved', [
            'error_uuid' => $errorUuid,
            'resolved_by' => $user->id,
            'resolution_notes' => $notes,
        ]);

        return $errorLog;
    }

    /**
     * Get error statistics
     */
    public function getErrorStatistics(int $hours = 24): array
    {
        $since = now()->subHours($hours);

        $stats = [
            'total_errors' => ErrorLog::where('created_at', '>=', $since)->count(),
            'critical_errors' => ErrorLog::where('created_at', '>=', $since)
                ->where('error_level', ErrorLog::LEVEL_CRITICAL)->count(),
            'error_level_errors' => ErrorLog::where('created_at', '>=', $since)
                ->where('error_level', ErrorLog::LEVEL_ERROR)->count(),
            'warning_errors' => ErrorLog::where('created_at', '>=', $since)
                ->where('error_level', ErrorLog::LEVEL_WARNING)->count(),
            'resolved_errors' => ErrorLog::where('created_at', '>=', $since)
                ->where('resolved', true)->count(),
            'unresolved_errors' => ErrorLog::where('created_at', '>=', $since)
                ->where('resolved', false)->count(),
        ];

        // Error types breakdown
        $errorTypes = ErrorLog::where('created_at', '>=', $since)
            ->selectRaw('error_type, COUNT(*) as count')
            ->groupBy('error_type')
            ->pluck('count', 'error_type')
            ->toArray();

        $stats['error_types'] = $errorTypes;

        // Resolution rate
        $stats['resolution_rate'] = $stats['total_errors'] > 0
            ? round(($stats['resolved_errors'] / $stats['total_errors']) * 100, 2)
            : 0;

        return $stats;
    }

    /**
     * Get unresolved critical errors
     */
    public function getUnresolvedCriticalErrors(): \Illuminate\Database\Eloquent\Collection
    {
        return ErrorLog::with(['formSubmission', 'formSubmissionSync'])
            ->where('error_level', ErrorLog::LEVEL_CRITICAL)
            ->where('resolved', false)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get recent errors for a submission
     */
    public function getSubmissionErrors(
        FormSubmission $submission,
        int $hours = 24
    ): \Illuminate\Database\Eloquent\Collection {
        return ErrorLog::where('form_submission_id', $submission->id)
            ->where('created_at', '>=', now()->subHours($hours))
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get recent errors for a sync
     */
    public function getSyncErrors(
        FormSubmissionSync $sync,
        int $hours = 24
    ): \Illuminate\Database\Eloquent\Collection {
        return ErrorLog::where('form_submission_sync_id', $sync->id)
            ->where('created_at', '>=', now()->subHours($hours))
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Clean up old resolved errors
     */
    public function cleanupOldErrors(int $daysToKeep = 90): int
    {
        $cutoffDate = now()->subDays($daysToKeep);

        $deletedCount = ErrorLog::where('resolved', true)
            ->where('resolved_at', '<', $cutoffDate)
            ->delete();

        Log::info('Cleaned up old resolved errors', [
            'deleted_count' => $deletedCount,
            'cutoff_date' => $cutoffDate,
        ]);

        return $deletedCount;
    }
}
