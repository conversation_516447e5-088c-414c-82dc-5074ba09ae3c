@extends('layouts.admin')
@section('pageTitle', trans('cruds.fields.form_details'))

@section('content')
<div class="card card-default">
    <div class="card-header separator">
        <div class="card-title mainheading">
            <h4>{{ trans('cruds.fields.form_details') }}</h4>
        </div>
        <div class="card-controls">
            <ul>
                <li>
                    <a class="btn btn-secondary" href="{{ route('admin.forms.index') }}">
                        {{ trans('global.back_to_list') }}
                    </a>
                </li>
                @if(auth()->user()->isAdmin() || auth()->user()->isSuperAdmin())
                <li>
                    <a class="btn btn-info" href="{{ route('admin.forms.edit', $form->id) }}">
                        {{ trans('global.edit') }}
                    </a>
                </li>
                @endif
                <li>
                    <a class="btn btn-success" href="{{ route('admin.forms.preview', $form->id) }}" target="_blank">
                        {{ trans('cruds.fields.preview_form') }}
                    </a>
                </li>
            </ul>
        </div>
    </div>

    <div class="card-block">
        <div class="row">
            <div class="col-md-6">
                <table class="table table-bordered">
                    <tr>
                        <th width="30%">{{ trans('cruds.fields.title') }}</th>
                        <td>{{ $form->title }}</td>
                    </tr>
                    <tr>
                        <th>{{ trans('cruds.fields.user_group') }}</th>
                        <td>
                            @if($form->userGroups->count() > 0)
                                @foreach($form->userGroups as $userGroup)
                                    <span class="badge badge-primary">{{ $userGroup->name }}</span>
                                @endforeach
                            @else
                                <span class="text-muted">{{ trans('cruds.fields.all_groups') }}</span>
                            @endif
                        </td>
                    </tr>
                    <tr>
                        <th>{{ trans('cruds.fields.created_by') }}</th>
                        <td>{{ $form->creator ? $form->creator->name : trans('cruds.fields.unknown') }}</td>
                    </tr>
                    <tr>
                        <th>{{ trans('cruds.fields.status') }}</th>
                        <td>
                            @if($form->is_active)
                                <span class="badge badge-success">{{ trans('cruds.fields.active') }}</span>
                            @else
                                <span class="badge badge-danger">{{ trans('cruds.fields.inactive') }}</span>
                            @endif
                        </td>
                    </tr>
                    <tr>
                        <th>{{ trans('cruds.fields.created_at') }}</th>
                        <td>{{ $form->created_at->format('Y-m-d H:i:s') }}</td>
                    </tr>
                    <tr>
                        <th>{{ trans('cruds.fields.updated_at') }}</th>
                        <td>{{ $form->updated_at->format('Y-m-d H:i:s') }}</td>
                    </tr>
                </table>
            </div>
            <div class="col-md-6">
                @if($form->userGroup)
                <div class="card">
                    <div class="card-header">
                        <h5>{{ trans('cruds.fields.group_information') }}</h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr>
                                <th>{{ trans('cruds.fields.group_name') }}:</th>
                                <td>{{ $form->userGroup->name }}</td>
                            </tr>
                            <tr>
                                <th>{{ trans('cruds.fields.description') }}:</th>
                                <td>{{ $form->userGroup->description ?? 'N/A' }}</td>
                            </tr>
                            <tr>
                                <th>{{ trans('cruds.fields.status') }}:</th>
                                <td>
                                    @if($form->userGroup->is_active)
                                        <span class="badge badge-success">{{ trans('cruds.fields.active') }}</span>
                                    @else
                                        <span class="badge badge-danger">{{ trans('cruds.fields.inactive') }}</span>
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <th>{{ trans('cruds.fields.total_members') }}:</th>
                                <td>{{ $form->userGroup->users->count() }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                @endif
            </div>
        </div>

        <div class="mt-4">
            <h5>{{ trans('cruds.fields.form_structure') }}</h5>
            <div class="card">
                <div class="card-body">
                    <pre class="bg-light p-3" style="max-height: 400px; overflow-y: auto;">{{ json_encode($form->content, JSON_PRETTY_PRINT) }}</pre>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
