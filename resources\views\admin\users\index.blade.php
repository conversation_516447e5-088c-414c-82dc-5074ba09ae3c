@extends('layouts.admin')
@section('pageTitle', __('cruds.user.title'))

@section('content')
    <div class="card card-default">
        <div class="card-header separator">
            <div class="card-title mainheading">
                <h4>{{ __('cruds.user.title') }}</h4>
            </div>
            <div class="card-controls">
                <ul>
                    <li>
                        <x-add-button :url="route('admin.users.create')" :text="__('cruds.user.title_singular')" />
                    </li>
                </ul>
            </div>
        </div>

        <div class="card-block">
            @php
                $columns = \App\Models\User::dataTableColumns();
            @endphp

            <x-datatable :columns="$columns" :ajax="route('admin.users.index')" :order="[[5, 'desc']]" />
        </div>
    </div>
@endsection
