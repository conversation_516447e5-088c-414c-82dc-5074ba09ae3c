@extends('layouts.admin')
@section('pageTitle', 'Edit Endpoint Configuration')

@push('styles')
<style>
    .required:after {
        content: " *";
        color: red;
    }
    .help-block {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 0.25rem;
    }
    .form-group {
        margin-bottom: 1.5rem;
    }
    .card-header h4 {
        margin: 0;
        color: #495057;
    }
</style>
@endpush

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card card-default">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h4><i class="fa fa-edit"></i> {{ trans('global.edit') }} {{ trans('cruds.endpointConfiguration.title_singular') }}</h4>
                        </div>
                        <div class="col-auto">
                            <a href="{{ route('admin.endpoint-configurations.index') }}" class="btn btn-secondary">
                                <i class="fa fa-arrow-left"></i> {{ trans('global.back_to_list') }}
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('admin.endpoint-configurations.update', $endpointConfiguration) }}" enctype="multipart/form-data">
                        @method('PUT')
                        @csrf

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="required" for="name">{{ trans('cruds.endpointConfiguration.fields.name') }}</label>
                                    <input class="form-control {{ $errors->has('name') ? 'is-invalid' : '' }}" type="text" name="name" id="name" value="{{ old('name', $endpointConfiguration->name) }}" required placeholder="Enter configuration name" readonly>
                                    @if($errors->has('name'))
                                        <div class="invalid-feedback">
                                            {{ $errors->first('name') }}
                                        </div>
                                    @endif
                                    <small class="help-block">{{ trans('cruds.endpointConfiguration.fields.name_helper') }}</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="required" for="target_type">Target System</label>
                                    <select class="form-control select2 {{ $errors->has('target_type') ? 'is-invalid' : '' }}" name="target_type" id="target_type" required>
                                        @foreach($targetOptions as $key => $label)
                                            <option value="{{ $key }}" {{ old('target_type', $endpointConfiguration->target_type) == $key ? 'selected' : '' }}>{{ $label }}</option>
                                        @endforeach
                                    </select>
                                    @if($errors->has('target_type'))
                                        <div class="invalid-feedback">
                                            {{ $errors->first('target_type') }}
                                        </div>
                                    @endif
                                    <small class="help-block">Select the target system for this endpoint</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="required" for="process_selection">{{ trans('cruds.endpointConfiguration.fields.process_selection') }}</label>
                                    <select class="form-control select2 {{ $errors->has('process_selection') ? 'is-invalid' : '' }}" name="process_selection" id="process_selection" required>
                                        <option value="">{{ trans('global.pleaseSelect') }}</option>
                                        @foreach($processOptions as $key => $label)
                                            <option value="{{ $key }}" {{ old('process_selection', $endpointConfiguration->process_selection) == $key ? 'selected' : '' }}>{{ $label }}</option>
                                        @endforeach
                                    </select>
                                    @if($errors->has('process_selection'))
                                        <div class="invalid-feedback">
                                            {{ $errors->first('process_selection') }}
                                        </div>
                                    @endif
                                    <small class="help-block">{{ trans('cruds.endpointConfiguration.fields.process_selection_helper') }}</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="required" for="endpoint_type">{{ trans('cruds.endpointConfiguration.fields.endpoint_type') }}</label>
                                    <select class="form-control select2 {{ $errors->has('endpoint_type') ? 'is-invalid' : '' }}" name="endpoint_type" id="endpoint_type" required>
                                        @foreach($endpointTypeOptions as $key => $label)
                                            <option value="{{ $key }}" {{ old('endpoint_type', $endpointConfiguration->endpoint_type) == $key ? 'selected' : '' }}>{{ $label }}</option>
                                        @endforeach
                                    </select>
                                    @if($errors->has('endpoint_type'))
                                        <div class="invalid-feedback">
                                            {{ $errors->first('endpoint_type') }}
                                        </div>
                                    @endif
                                    <small class="help-block">{{ trans('cruds.endpointConfiguration.fields.endpoint_type_helper') }}</small>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="required" for="url">{{ trans('cruds.endpointConfiguration.fields.url') }}</label>
                            <textarea class="form-control {{ $errors->has('url') ? 'is-invalid' : '' }}" name="url" id="url" rows="3" required placeholder="IDORequestService/ido/invoke/SLStockActItems?method=ItemMiscReceiptSp">{{ old('url', $endpointConfiguration->url) }}</textarea>
                            @if($errors->has('url'))
                                <div class="invalid-feedback">
                                    {{ $errors->first('url') }}
                                </div>
                            @endif
                            <small class="help-block">{{ trans('cruds.endpointConfiguration.fields.url_helper') }}</small>
                        </div>

                        <div class="form-group">
                            <label for="body_data_field">{{ trans('cruds.endpointConfiguration.fields.body_data_field') }}</label>
                            <div class="card">
                                <div class="card-header">
                                    <div class="row align-items-center">
                                        <div class="col">
                                            <h6 class="mb-0">Field Mapping Configuration</h6>
                                        </div>
                                        <div class="col-auto">
                                            <button type="button" class="btn btn-sm btn-outline-primary mr-2" id="importJsonBtn" title="Import fields from JSON">
                                                <i class="fa fa-file-import"></i> Import JSON
                                            </button>
                                            <button type="button" class="btn btn-sm btn-success" id="addFieldBtn">
                                                <i class="fa fa-plus"></i> Add Field
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div id="fieldMappingContainer">
                                        <!-- Dynamic fields will be added here -->
                                    </div>
                                    <div id="noFieldsMessage" class="text-center text-muted py-3">
                                        <i class="fa fa-info-circle"></i> No fields added yet. Click "Add Field" to start.
                                    </div>
                                </div>
                            </div>
                            <input type="hidden" name="body_data_field" id="body_data_field_hidden" value="{!! htmlspecialchars(old('body_data_field', $endpointConfiguration->body_data_field_json), ENT_QUOTES) !!}">
                            @if($errors->has('body_data_field'))
                                <div class="invalid-feedback d-block">
                                    {{ $errors->first('body_data_field') }}
                                </div>
                            @endif
                            <small class="help-block">{{ trans('cruds.endpointConfiguration.fields.body_data_field_helper') }}</small>
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-checkbox">
                                <input type="hidden" name="is_active" value="0">
                                <input class="custom-control-input" type="checkbox" name="is_active" id="is_active" value="1" {{ old('is_active', $endpointConfiguration->is_active) == 1 ? 'checked' : '' }}>
                                <label class="custom-control-label" for="is_active">
                                    {{ trans('cruds.endpointConfiguration.fields.is_active') }}
                                </label>
                                @if($errors->has('is_active'))
                                    <div class="invalid-feedback d-block">
                                        {{ $errors->first('is_active') }}
                                    </div>
                                @endif
                                <small class="help-block d-block">{{ trans('cruds.endpointConfiguration.fields.is_active_helper') }}</small>
                            </div>
                        </div>

                        <hr>

                        <div class="form-group mb-0">
                            <button class="btn btn-primary" type="submit">
                                <i class="fa fa-save"></i> {{ trans('global.update') }}
                            </button>
                            <a class="btn btn-secondary ml-2" href="{{ route('admin.endpoint-configurations.index') }}">
                                <i class="fa fa-times"></i> {{ trans('global.cancel') }}
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    let fieldCounter = 0;

    // Data types options
    const dataTypes = [
        'string', 'integer', 'float', 'boolean', 'date', 'datetime', 'email', 'url', 'text', 'json'
    ];

    // Load existing data if editing
    const existingData = $('#body_data_field_hidden').val();
    if (existingData) {
        try {
            const fields = JSON.parse(existingData);
            if (Array.isArray(fields)) {
                fields.forEach(field => addFieldRow(field));
                updateNoFieldsMessage();
            }
        } catch (e) {
            console.log('Error parsing JSON data:', e);
        }
    }

    // Add field button click
    $(document).on('click', '#addFieldBtn', function(e) {
        e.preventDefault();
        addFieldRow();
    });

    // Function to add a new field row
    function addFieldRow(fieldData = {}) {
        fieldCounter++;

        const fieldRow = `
            <div class="field-row border rounded p-3 mb-3" data-field-id="${fieldCounter}">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group mb-2">
                            <label class="small font-weight-bold">Field Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control form-control-sm field-name"
                                   placeholder="e.g., item_code" value="${fieldData.name || ''}" required>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group mb-2">
                            <label class="small font-weight-bold">Data Type</label>
                            <select class="form-control form-control-sm field-datatype">
                                ${dataTypes.map(type =>
                                    `<option value="${type}" ${fieldData.datatype === type ? 'selected' : ''}>${type}</option>`
                                ).join('')}
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group mb-2">
                            <label class="small font-weight-bold">Max Length</label>
                            <input type="number" class="form-control form-control-sm field-maxlength"
                                   placeholder="255" value="${fieldData.maxlength !== null && fieldData.maxlength !== undefined ? fieldData.maxlength : ''}" min="1">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group mb-2">
                            <label class="small font-weight-bold">Required</label>
                            <select class="form-control form-control-sm field-required">
                                <option value="false" ${fieldData.required === false ? 'selected' : ''}>No</option>
                                <option value="true" ${fieldData.required === true ? 'selected' : ''}>Yes</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group mb-2">
                            <label class="small font-weight-bold">Default Value</label>
                            <input type="text" class="form-control form-control-sm field-default"
                                   placeholder="Optional" value="${fieldData.default !== null && fieldData.default !== undefined ? fieldData.default : ''}">
                        </div>
                    </div>
                    <div class="col-md-1">
                        <div class="form-group mb-2">
                            <label class="small font-weight-bold">&nbsp;</label>
                            <button type="button" class="btn btn-sm btn-danger btn-block remove-field">
                                <i class="fa fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-11">
                        <div class="form-group mb-0">
                            <label class="small font-weight-bold">Description</label>
                            <input type="text" class="form-control form-control-sm field-description"
                                   placeholder="Optional field description" value="${fieldData.description || ''}">
                        </div>
                    </div>
                </div>
            </div>
        `;

        $('#fieldMappingContainer').append(fieldRow);
        updateNoFieldsMessage();
        updateHiddenField();
    }

    // Remove field
    $(document).on('click', '.remove-field', function() {
        $(this).closest('.field-row').remove();
        updateNoFieldsMessage();
        updateHiddenField();
    });

    // Update hidden field when any input changes
    $(document).on('input change', '.field-row input, .field-row select', function() {
        updateHiddenField();
    });

    // Function to update the hidden field with JSON data
    function updateHiddenField() {
        const fields = [];

        $('.field-row').each(function() {
            const row = $(this);
            const field = {
                name: row.find('.field-name').val().trim(),
                datatype: row.find('.field-datatype').val(),
                maxlength: row.find('.field-maxlength').val() ? parseInt(row.find('.field-maxlength').val()) : null,
                required: row.find('.field-required').val() === 'true',
                default: row.find('.field-default').val().trim() || null,
                description: row.find('.field-description').val().trim() || null
            };

            if (field.name) {
                fields.push(field);
            }
        });

        $('#body_data_field_hidden').val(JSON.stringify(fields));
    }

    // Function to show/hide no fields message
    function updateNoFieldsMessage() {
        const fieldRows = $('.field-row');
        if (fieldRows.length === 0) {
            $('#noFieldsMessage').show();
        } else {
            $('#noFieldsMessage').hide();
        }
    }

    // Function to determine data type from value
    function determineDataType(value) {
        if (value === null || value === undefined) return 'string';
        if (Array.isArray(value)) return 'array';
        if (typeof value === 'object') return 'object';
        if (typeof value === 'number') {
            return Number.isInteger(value) ? 'integer' : 'float';
        }
        if (typeof value === 'boolean') return 'boolean';
        if (typeof value === 'string') {
            // Check for date/datetime
            const date = new Date(value);
            if (!isNaN(date.getTime())) {
                return value.includes('T') || value.includes(' ') ? 'datetime' : 'date';
            }
            // Check for email
            if (value.includes('@') && value.includes('.')) {
                return 'email';
            }
            // Check for URL
            try {
                new URL(value);
                return 'url';
            } catch (e) {}
        }
        return 'string';
    }

    // Function to flatten object and create field mappings
    function generateFieldMappingsFromSample(data, prefix = '', flatten = true) {
        const fields = [];
        
        if (Array.isArray(data) && data.length > 0) {
            // If it's an array, use the first item to determine structure
            return generateFieldMappingsFromSample(data[0], prefix, flatten);
        } else if (data && typeof data === 'object') {
            Object.entries(data).forEach(([key, value]) => {
                const fieldName = prefix ? `${prefix}_${key}` : key;
                
                if (value !== null && typeof value === 'object' && !Array.isArray(value) && flatten) {
                    // Recursively process nested objects if flattening is enabled
                    fields.push(...generateFieldMappingsFromSample(value, fieldName, flatten));
                } else {
                    const field = {
                        name: fieldName,
                        datatype: determineDataType(value),
                        required: false,
                        description: `Field ${fieldName} from sample data`
                    };
                    
                    // Set reasonable defaults based on data type
                    if (field.datatype === 'string') {
                        field.maxlength = 255;
                        field.default = '';
                    } else if (field.datatype === 'integer' || field.datatype === 'float') {
                        field.maxlength = 20;
                        field.default = '0';
                    } else if (field.datatype === 'boolean') {
                        field.default = 'false';
                    } else if (field.datatype === 'array' || field.datatype === 'object') {
                        field.datatype = 'json';
                        field.default = field.datatype === 'array' ? '[]' : '{}';
                    }
                    
                    fields.push(field);
                }
            });
        }
        
        return fields;
    }

    // Initial state
    updateNoFieldsMessage();

    // JSON Import Modal
    const importModal = `
    <div class="modal fade" id="importJsonModal" tabindex="-1" role="dialog" aria-labelledby="importJsonModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="importJsonModalLabel">Import Field Mappings</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs mb-3" id="importTabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="json-tab" data-toggle="tab" href="#jsonTab" role="tab" aria-controls="jsonTab" aria-selected="true">Field Mappings</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="sample-tab" data-toggle="tab" href="#sampleTab" role="tab" aria-controls="sampleTab" aria-selected="false">From Sample Data</a>
                        </li>
                    </ul>
                    
                    <div class="tab-content" id="importTabsContent">
                        <!-- Field Mappings Tab -->
                        <div class="tab-pane fade show active" id="jsonTab" role="tabpanel" aria-labelledby="json-tab">
                            <div class="form-group">
                                <label for="jsonInput">Paste your JSON array of field mappings:</label>
                                <textarea class="form-control" id="jsonInput" rows="10" placeholder='[
  {
    "name": "field1",
    "datatype": "string",
    "maxlength": 255,
    "required": true,
    "default": "",
    "description": "Field description"
  },
  {
    "name": "field2",
    "datatype": "integer",
    "required": false,
    "description": "Numeric field"
  }
]'></textarea>
                                <small class="form-text text-muted">
                                    Format: An array of objects with field properties (name, datatype, maxlength, required, default, description)
                                </small>
                            </div>
                        </div>
                        
                        <!-- Sample Data Tab -->
                        <div class="tab-pane fade" id="sampleTab" role="tabpanel" aria-labelledby="sample-tab">
                            <div class="form-group">
                                <label for="sampleDataInput">Paste sample JSON response from your endpoint:</label>
                                <textarea class="form-control" id="sampleDataInput" rows="10" placeholder='{
  "id": 123,
  "name": "Example Product",
  "price": 99.99,
  "in_stock": true,
  "tags": ["electronics", "new"],
  "details": {
    "weight": 2.5,
    "dimensions": "10x15x5cm"
  },
  "created_at": "2023-01-01T12:00:00Z"
}'></textarea>
                                <small class="form-text text-muted">
                                    Paste a sample JSON response to automatically generate field mappings
                                </small>
                            </div>
                            <div class="form-group">
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" class="custom-control-input" id="flattenNested" checked>
                                    <label class="custom-control-label" for="flattenNested">Flatten nested objects (e.g., details.weight → details_weight)</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div id="jsonError" class="alert alert-danger mt-3" style="display: none;"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="importJsonSubmit">Import Fields</button>
                </div>
            </div>
        </div>
    </div>`;

    // Add modal to DOM
    $('body').append(importModal);

    // Open import modal
    $('#importJsonBtn').on('click', function() {
        $('#jsonInput').val('');
        $('#sampleDataInput').val('');
        $('#jsonError').hide();
        $('#importJsonModal').modal('show');
    });

    // Handle JSON import
    $('#importJsonSubmit').on('click', function() {
        const activeTab = $('#importTabs .tab-pane.active').attr('id');
        
        try {
            let fields = [];
            
            if (activeTab === 'jsonTab') {
                // Handle direct field mappings
                const jsonInput = $('#jsonInput').val().trim();
                if (!jsonInput) {
                    showJsonError('Please enter JSON data');
                    return;
                }
                
                const parsed = JSON.parse(jsonInput);
                if (!Array.isArray(parsed)) {
                    throw new Error('Invalid format. Expected an array of field objects.');
                }
                fields = parsed;
            } 
            else if (activeTab === 'sampleTab') {
                // Handle sample data import
                const sampleDataInput = $('#sampleDataInput').val().trim();
                if (!sampleDataInput) {
                    showJsonError('Please paste sample JSON data');
                    return;
                }
                
                const sampleData = JSON.parse(sampleDataInput);
                const flattenNested = $('#flattenNested').is(':checked');
                fields = generateFieldMappingsFromSample(sampleData, '', flattenNested);
                
                if (fields.length === 0) {
                    throw new Error('Could not generate any fields from the sample data');
                }
            }

            // Clear existing fields
            $('.field-row').remove();
            
            // Add each field from the JSON
            fields.forEach(field => {
                if (field && typeof field === 'object' && field.name) {
                    addFieldRow(field);
                }
            });
            
            updateNoFieldsMessage();
            updateHiddenField();
            
            $('#importJsonModal').modal('hide');
            showToast('success', `Successfully imported ${fields.length} fields`);
            
        } catch (error) {
            showJsonError('Invalid JSON: ' + error.message);
        }
    });
    
    // Show JSON error message
    function showJsonError(message) {
        const $errorDiv = $('#jsonError');
        $errorDiv.text(message).show();
        $('html, body').animate({
            scrollTop: $errorDiv.offset().top - 100
        }, 500);
    }
    
    // Show toast notification
    function showToast(type, message) {
        // You can implement a toast notification here or use alert for now
        alert(message);
    }
});
</script>

<style>
    .field-row {
        background-color: #f8f9fa;
        border-radius: 4px;
        margin-bottom: 15px;
        transition: all 0.2s;
    }
    .field-row:hover {
        background-color: #f1f3f5;
    }
    .form-control-sm {
        height: calc(1.5em + 0.5rem + 2px);
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        line-height: 1.5;
        border-radius: 0.2rem;
    }
    .modal-body textarea {
        font-family: monospace;
        font-size: 0.9em;
    }
</style>
@endsection
