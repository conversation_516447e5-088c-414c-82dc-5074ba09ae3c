@extends('layouts.admin')
@section('pageTitle', trans('cruds.form.title') . ' ' . trans('cruds.usergroup.title_singular'))

@section('content')
    <div class="card card-default">
        <div class="card-header separator">

            <div class="card-title mainheading">
                <h4> {{ trans('cruds.form.title') }} {{ trans('cruds.usergroup.title') }}</h4>
            </div>
            <div class="card-controls">
                <ul>
                    <li>
                        <x-cancel-button :url="route('admin.usergroups.forms')" :text="trans('global.back_to_list')" />
                    </li>
                </ul>
            </div>
        </div>

        <div class="card-block">
            @if ($errors->any())
                <div class="alert alert-danger">
                    <ul>
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif
            <form method="GET" action="{{ route('admin.usergroups.forms') }}" class="mb-4">
                <div class="row g-3 align-items-end">
                    {{-- Module Filter --}}
                    <div class="col-md-3">
                        <label for="module" class="form-label">Module</label>
                        <select name="module" id="module" class="form-select">
                            <option value="">-- All Modules --</option>
                            @foreach ($form_modules as $module => $label)
                                <option value="{{ $module }}" {{ request('module') == $module ? 'selected' : '' }}>
                                    {{ $label }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    {{-- Process Type Filter --}}
                    <div class="col-md-3">
                        <label for="group_filter" class="form-label"> User Group</label>
                        <select name="group_filter" id="group_filter" class="form-select select2" multiple>
                            <option value="">-- All Groups --</option>
                            @foreach ($groups as $group)
                                <option value="{{ $group->id }}"
                                    {{ request('group_filter') == $group->id ? 'selected' : '' }}>
                                    {{ ucfirst($group->name) }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    {{-- Forms Filter (Multiple) --}}
                    <div class="col-md-4">
                        <label for="forms" class="form-label">Forms</label>
                        <select name="forms[]" id="forms" class="form-select select2" multiple>
                            @foreach ($formsAll as $f)
                                <option value="{{ $f->id }}"
                                    {{ collect(request('forms'))->contains($f->id) ? 'selected' : '' }}>
                                    {{ $f->title }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary w-100">Filter</button>
                        {{-- <a href="{{ route('admin.usergroups.forms') }}" class="btn btn-secondary w-100">Clear Filters</a> --}}

                    </div>
                </div>
            </form>
            <form method="POST" action="{{ route('admin.usergroups.storeForms') }}">
                @csrf
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th rowspan="2">Form</th>
                            <th colspan="{{ $groups->count() }}" class="text-center">User Groups</th>
                        </tr>
                        <tr>
                            @foreach ($groups as $group)
                                <th class="text-center">{{ $group->name }}</th>
                            @endforeach
                        </tr>
                    </thead>
                    <tbody>
                        @php $currentProcessType = null; @endphp

                        @foreach ($forms as $form)
                            {{-- Insert process_type header row --}}
                            @if ($currentProcessType !== $form->process_type)
                                <tr class="table-secondary fw-bold">
                                    <td colspan="{{ 1 + $groups->count() }}">
                                        {{ ucfirst($form->process_type) }}
                                    </td>
                                </tr>
                                @php $currentProcessType = $form->process_type; @endphp
                            @endif

                            <tr>
                                <td>{{ $form->title }}</td>
                                @foreach ($groups as $group)
                                    <td class="text-center">
                                        <input type="checkbox" name="assignments[{{ $form->id }}][]"
                                            value="{{ $group->id }}"
                                            {{ $form->userGroups->contains($group->id) ? 'checked' : '' }}>
                                    </td>
                                @endforeach
                            </tr>
                        @endforeach
                    </tbody>
                </table>

                <div class="form-group row  mt-3">
                    <div class="col-md-10 offset-md-2 text-right">

                        <x-cancel-button :url="route('admin.usergroups.index')" :text="trans('global.cancel')" />
                        <x-save-button />
                    </div>
                </div>
                {{-- <button type="submit" class="btn btn-primary mt-3">Save Assignments</button> --}}
            </form>

        </div>
    </div>
@endsection
@section('scripts')
    <script>
        $(function() {
            $('.select2').select2({
                width: '100%',
                placeholder: "Select options",
                allowClear: true
            });
        })
    </script>
@endsection
