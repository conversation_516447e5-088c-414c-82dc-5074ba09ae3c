<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\FieldMappingConfiguration;
use App\Models\Form;
use App\Models\IntegrationConfiguration;
use App\Models\User;
use App\Models\UserGroup;

class FieldMappingConfigurationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $this->command->info('Starting Field Mapping Configuration seeding...');

        // Get or create a super admin user
        $superAdmin = User::where('type', 1)->first();
        if (!$superAdmin) {
            $superAdmin = User::create([
                'name' => 'System Admin',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'type' => 1,
                'email_verified_at' => now(),
            ]);
            $this->command->info('Created system admin user for seeding');
        }

        // Get or create a user group
        $userGroup = UserGroup::first();
        if (!$userGroup) {
            $userGroup = UserGroup::create([
                'name' => 'Default Group',
                'description' => 'Default user group for testing',
                'is_active' => true,
            ]);
            $this->command->info('Created default user group for seeding');
        }

        // Create hardcoded forms for testing
        $forms = $this->createTestForms($superAdmin, $userGroup);

        // Get existing integration configurations
        $integrationConfigurations = IntegrationConfiguration::all();

        if ($integrationConfigurations->isEmpty()) {
            $this->command->error('No integration configurations found. Please run IntegrationConfigurationSeeder first.');
            return;
        }

        // Create field mapping configurations
        $fieldMappingConfigurations = $this->getFieldMappingConfigurationData($forms, $integrationConfigurations, $superAdmin);

        foreach ($fieldMappingConfigurations as $settingData) {
            // Check if field mapping configuration already exists
            $existingSetting = FieldMappingConfiguration::where('name', $settingData['name'])->first();

            if (!$existingSetting) {
                FieldMappingConfiguration::create($settingData);
                $this->command->info("Created field mapping configuration: {$settingData['name']}");
            } else {
                $this->command->info("Field mapping configuration already exists: {$settingData['name']}");
            }
        }

        $this->command->info('Field Mapping Configuration seeding completed!');
    }

    /**
     * Create test forms with hardcoded data
     */
    private function createTestForms($superAdmin, $userGroup)
    {
        $forms = [];

        // Inventory Issue Form
        $inventoryIssueForm = [
            'title' => 'Inventory Issue Form',
            'content' => [
                'components' => [
                    [
                        'key' => 'item_code',
                        'type' => 'textfield',
                        'label' => 'Item Code',
                        'validate' => ['required' => true],
                        'description' => 'Enter the item code to issue'
                    ],
                    [
                        'key' => 'quantity',
                        'type' => 'number',
                        'label' => 'Quantity',
                        'validate' => ['required' => true, 'min' => 1],
                        'description' => 'Enter the quantity to issue'
                    ],
                    [
                        'key' => 'location_code',
                        'type' => 'textfield',
                        'label' => 'Location Code',
                        'validate' => ['required' => true],
                        'description' => 'Enter the location code'
                    ],
                    [
                        'key' => 'cost_center',
                        'type' => 'textfield',
                        'label' => 'Cost Center',
                        'validate' => ['required' => false],
                        'description' => 'Enter the cost center'
                    ],
                    [
                        'key' => 'reason',
                        'type' => 'textarea',
                        'label' => 'Reason',
                        'validate' => ['required' => false],
                        'description' => 'Enter the reason for issue'
                    ]
                ]
            ],
            'user_group_id' => $userGroup->id,
            'created_by' => $superAdmin->id,
            'is_active' => true,
            'tenant_id' => 1,
            'module' => 'inventory',
        ];

        // Inventory Receipt Form
        $inventoryReceiptForm = [
            'title' => 'Inventory Receipt Form',
            'content' => [
                'components' => [
                    [
                        'key' => 'material_number',
                        'type' => 'textfield',
                        'label' => 'Material Number',
                        'validate' => ['required' => true],
                        'description' => 'Enter the material number'
                    ],
                    [
                        'key' => 'quantity',
                        'type' => 'number',
                        'label' => 'Quantity',
                        'validate' => ['required' => true, 'min' => 1],
                        'description' => 'Enter the quantity received'
                    ],
                    [
                        'key' => 'plant_code',
                        'type' => 'textfield',
                        'label' => 'Plant Code',
                        'validate' => ['required' => true],
                        'description' => 'Enter the plant code'
                    ],
                    [
                        'key' => 'storage_location',
                        'type' => 'textfield',
                        'label' => 'Storage Location',
                        'validate' => ['required' => true],
                        'description' => 'Enter the storage location'
                    ],
                    [
                        'key' => 'unit_cost',
                        'type' => 'number',
                        'label' => 'Unit Cost',
                        'validate' => ['required' => false],
                        'description' => 'Enter the unit cost'
                    ]
                ]
            ],
            'user_group_id' => $userGroup->id,
            'created_by' => $superAdmin->id,
            'is_active' => true,
            'tenant_id' => 1,
            'module' => 'inventory',
        ];

        // Stock Transfer Form
        $stockTransferForm = [
            'title' => 'Stock Transfer Form',
            'content' => [
                'components' => [
                    [
                        'key' => 'item_code',
                        'type' => 'textfield',
                        'label' => 'Item Code',
                        'validate' => ['required' => true],
                        'description' => 'Enter the item code to transfer'
                    ],
                    [
                        'key' => 'quantity',
                        'type' => 'number',
                        'label' => 'Quantity',
                        'validate' => ['required' => true, 'min' => 1],
                        'description' => 'Enter the quantity to transfer'
                    ],
                    [
                        'key' => 'from_location',
                        'type' => 'textfield',
                        'label' => 'From Location',
                        'validate' => ['required' => true],
                        'description' => 'Enter the source location'
                    ],
                    [
                        'key' => 'to_location',
                        'type' => 'textfield',
                        'label' => 'To Location',
                        'validate' => ['required' => true],
                        'description' => 'Enter the destination location'
                    ],
                    [
                        'key' => 'transfer_reason',
                        'type' => 'select',
                        'label' => 'Transfer Reason',
                        'data' => [
                            'values' => [
                                ['label' => 'Rebalancing', 'value' => 'REBALANCE'],
                                ['label' => 'Maintenance', 'value' => 'MAINTENANCE'],
                                ['label' => 'Quality Issue', 'value' => 'QUALITY'],
                                ['label' => 'Other', 'value' => 'OTHER']
                            ]
                        ],
                        'validate' => ['required' => true],
                        'description' => 'Select the reason for transfer'
                    ]
                ]
            ],
            'user_group_id' => $userGroup->id,
            'created_by' => $superAdmin->id,
            'is_active' => true,
            'tenant_id' => 1,
            'module' => 'inventory',
        ];

        // Create the forms
        $formData = [$inventoryIssueForm, $inventoryReceiptForm, $stockTransferForm];

        foreach ($formData as $data) {
            $existingForm = Form::where('title', $data['title'])->first();
            if (!$existingForm) {
                $form = Form::create($data);
                $forms[] = $form;
                $this->command->info("Created test form: {$data['title']}");
            } else {
                $forms[] = $existingForm;
                $this->command->info("Test form already exists: {$data['title']}");
            }
        }

        return collect($forms);
    }

    /**
     * Get integration settings data
     */
    private function getFieldMappingConfigurationData($forms, $integrationConfigurations, $superAdmin)
    {
        $settings = [];

        // Find specific forms and integrations for mapping
        $issueForm = $forms->firstWhere('title', 'Inventory Issue Form');
        $receiptForm = $forms->firstWhere('title', 'Inventory Receipt Form');
        $transferForm = $forms->firstWhere('title', 'Stock Transfer Form');
        $poReceiptForm = $forms->firstWhere('title', 'PO Receipt Form');


        // CSI Misc Issue Integration
        $csiIssueIntegration = $integrationConfigurations->where('external_system', IntegrationConfiguration::SYSTEM_CSI)
            ->where('process_selection', IntegrationConfiguration::PROCESS_MISC_ISSUE)->first();

        if ($issueForm && $csiIssueIntegration) {
            $settings[] = [
                'name' => 'CSI Inventory Issue Integration',
                'form_id' => $issueForm->id,
                'integration_configuration_id' => $csiIssueIntegration->id,
                'field_mappings' => [
                    'item_code' => 'item_code',
                    'quantity' => 'quantity',
                    'location_code' => 'location_code',
                    'cost_center' => 'cost_center',
                    'reason' => 'reason'
                ],
                'description' => 'Integration between Inventory Issue Form and CSI Misc Issue integration',
                'is_active' => true,
                'created_by' => $superAdmin->id,
                'updated_by' => $superAdmin->id,
            ];
        }

        // CSI Misc Receipt Integration
        $csiReceiptIntegration = $integrationConfigurations->where('external_system', IntegrationConfiguration::SYSTEM_CSI)
            ->where('process_type', IntegrationConfiguration::PROCESS_MISC_RECEIPT)->first();

        if ($receiptForm && $csiReceiptIntegration) {
            $settings[] = [
                'name' => 'CSI Inventory Receipt Integration',
                'form_id' => $receiptForm->id,
                'integration_configuration_id' => $csiReceiptIntegration->id,
                'field_mappings' => [
                    'material_number' => 'material_number',
                    'quantity' => 'quantity',
                    'plant_code' => 'plant_code',
                    'storage_location' => 'storage_location',
                    'unit_cost' => 'unit_cost'
                ],
                'description' => 'Integration between Inventory Receipt Form and CSI Misc Receipt integration',
                'is_active' => true,
                'created_by' => $superAdmin->id,
                'updated_by' => $superAdmin->id,
            ];
        }

        // CSI Quantity Move Integration
        $csiMoveIntegration = $integrationConfigurations->where('external_system', IntegrationConfiguration::SYSTEM_CSI)
            ->where('process_type', IntegrationConfiguration::PROCESS_QUANTITY_MOVE)->first();

        if ($transferForm && $csiMoveIntegration) {
            $settings[] = [
                'name' => 'CSI Stock Transfer Integration',
                'form_id' => $transferForm->id,
                'integration_configuration_id' => $csiMoveIntegration->id,
                'field_mappings' => [
                    'item_code' => 'item_code',
                    'quantity' => 'quantity',
                    'from_location' => 'from_location',
                    'to_location' => 'to_location',
                    'transfer_reason' => 'reason'
                ],
                'description' => 'Integration between Stock Transfer Form and CSI Quantity Move integration',
                'is_active' => true,
                'created_by' => $superAdmin->id,
                'updated_by' => $superAdmin->id,
            ];
        }

        // Infor WMS PO Receipt Integration
        $inforPoReceiptIntegration = $integrationConfigurations->where('external_system', IntegrationConfiguration::SYSTEM_INFOR_WMS)
            ->where('process_type', IntegrationConfiguration::PROCESS_PO_RECEIPT)->first();

        /* sample data for infor wms integration
        [{"key": "documentID_id", "name": "documentID_id", "default": "RECEIPTKEY123", "datatype": "string", "required": false, "maxlength": 255}, {"key": "sourceLocation", "name": "sourceLocation", "default": null, "datatype": "date", "required": false, "maxlength": null}, {"key": "type", "name": "type", "default": "7", "datatype": "integer", "required": false, "maxlength": 20}, {"key": "notes", "name": "notes", "default": "Receipt for PO FTOVRD60", "datatype": "string", "required": false, "maxlength": 255}, {"key": "asnItems_sku", "name": "asnItems_sku", "default": "SKU001", "datatype": "string", "required": false, "maxlength": 255}, {"key": "asnItems_qtyExpected", "name": "asnItems_qtyExpected", "default": "100", "datatype": "integer", "required": false, "maxlength": 20}, {"key": "asnItems_uom", "name": "asnItems_uom", "default": "EA", "datatype": "string", "required": false, "maxlength": 255}, {"key": "asnItems_purchaseOrderReference_documentID_id", "name": "asnItems_purchaseOrderReference_documentID_id", "default": "FTOVRD60", "datatype": "string", "required": false, "maxlength": 255}, {"key": "asnItems_purchaseOrderReference_lineNumber", "name": "asnItems_purchaseOrderReference_lineNumber", "default": null, "datatype": "date", "required": false, "maxlength": null}, {"key": "asnItems_notes", "name": "asnItems_notes", "default": "First line receipt", "datatype": "string", "required": false, "maxlength": 255}] 
        
        sample content for po receipt form
        "{\"components\":[{\"label\":\"Warehouse\",\"labelPosition\":\"left-left\",\"placeholder\":\"Warehouse\",\"applyMaskOn\":\"change\",\"tableView\":true,\"validate\":{\"required\":true},\"validateWhenHidden\":false,\"key\":\"warehouse\",\"type\":\"textfield\",\"input\":true},{\"label\":\"Notes\",\"labelPosition\":\"left-left\",\"placeholder\":\"Notes\",\"applyMaskOn\":\"change\",\"autoExpand\":false,\"tableView\":true,\"validateWhenHidden\":false,\"key\":\"notes\",\"type\":\"textarea\",\"input\":true},{\"title\":\"Line Entries\",\"theme\":\"primary\",\"collapsible\":false,\"key\":\"lineEntries\",\"type\":\"panel\",\"label\":\"Panel\",\"input\":false,\"tableView\":false,\"components\":[{\"label\":\"Item\",\"labelPosition\":\"left-left\",\"placeholder\":\"Item\",\"applyMaskOn\":\"change\",\"tableView\":true,\"validate\":{\"required\":true},\"validateWhenHidden\":false,\"key\":\"item_1\",\"logic\":[{\"name\":\"isSubmitt\",\"trigger\":{\"type\":\"javascript\",\"javascript\":\"result=data.isSubmitting === true;\\n\"},\"actions\":[{\"name\":\"set req false\",\"type\":\"property\",\"property\":{\"label\":\"Required\",\"value\":\"validate.required\",\"type\":\"boolean\"},\"state\":false}]}],\"type\":\"textfield\",\"input\":true},{\"label\":\"Qty\",\"labelPosition\":\"left-left\",\"placeholder\":\"Qty Expected\",\"applyMaskOn\":\"change\",\"tableView\":true,\"validate\":{\"required\":true},\"validateWhenHidden\":false,\"key\":\"qty\",\"logic\":[{\"name\":\"isSubmitt\",\"trigger\":{\"type\":\"javascript\",\"javascript\":\"result=data.isSubmitting === true;\\n\"},\"actions\":[{\"name\":\"set req false\",\"type\":\"property\",\"property\":{\"label\":\"Required\",\"value\":\"validate.required\",\"type\":\"boolean\"},\"state\":false}]}],\"type\":\"textfield\",\"input\":true},{\"label\":\"UOM\",\"labelPosition\":\"left-left\",\"placeholder\":\"UOM\",\"applyMaskOn\":\"change\",\"tableView\":true,\"validate\":{\"required\":true},\"validateWhenHidden\":false,\"key\":\"UOM\",\"logic\":[{\"name\":\"isSubmitt\",\"trigger\":{\"type\":\"javascript\",\"javascript\":\"result=data.isSubmitting === true;\\n\"},\"actions\":[{\"name\":\"set req false\",\"type\":\"property\",\"property\":{\"label\":\"Required\",\"value\":\"validate.required\",\"type\":\"boolean\"},\"state\":false}]}],\"type\":\"textfield\",\"input\":true},{\"label\":\"PO #\",\"labelPosition\":\"left-left\",\"placeholder\":\"PO #\",\"applyMaskOn\":\"change\",\"tableView\":true,\"validate\":{\"required\":true},\"validateWhenHidden\":false,\"key\":\"po_num\",\"logic\":[{\"name\":\"isSubmitt\",\"trigger\":{\"type\":\"javascript\",\"javascript\":\"result=data.isSubmitting === true;\\n\"},\"actions\":[{\"name\":\"set req false\",\"type\":\"property\",\"property\":{\"label\":\"Required\",\"value\":\"validate.required\",\"type\":\"boolean\"},\"state\":false}]}],\"type\":\"textfield\",\"input\":true},{\"label\":\"PO Line #\",\"labelPosition\":\"left-left\",\"placeholder\":\"PO Line #\",\"applyMaskOn\":\"change\",\"tableView\":true,\"validate\":{\"required\":true},\"validateWhenHidden\":false,\"key\":\"po_line\",\"logic\":[{\"name\":\"isSubmitt\",\"trigger\":{\"type\":\"javascript\",\"javascript\":\"result=data.isSubmitting === true;\\n\"},\"actions\":[{\"name\":\"set req false\",\"type\":\"property\",\"property\":{\"label\":\"Required\",\"value\":\"validate.required\",\"type\":\"boolean\"},\"state\":false}]}],\"type\":\"textfield\",\"input\":true},{\"label\":\"Notes\",\"labelPosition\":\"left-left\",\"placeholder\":\"Notes\",\"applyMaskOn\":\"change\",\"tableView\":false,\"validateWhenHidden\":false,\"key\":\"notes1\",\"type\":\"textfield\",\"input\":true},{\"label\":\"Add\",\"action\":\"custom\",\"showValidations\":false,\"theme\":\"info\",\"leftIcon\":\"bi bi-plus\",\"tableView\":false,\"key\":\"add\",\"type\":\"button\",\"custom\":\"data.isSubmitting = false;\\r\\n\\r\\ntableData = instance.root.getComponent('lineEntry').dataValue || [];\\r\\n// console.log(tableData)\\r\\nlet valid = true;\\r\\nlet itmfield=instance.root.getComponent('item_1');\\r\\n// console.log(itmfield)\\r\\nif (!itmfield.checkValidity()) {\\r\\n  valid = false;\\r\\n  instance.root.getComponent('item_1').setPristine(false); // Show validation error\\r\\n}\\r\\nif (!instance.root.getComponent('qty').checkValidity()) {\\r\\n  valid = false;\\r\\n  instance.root.getComponent('qty').setPristine(false); // Show validation error\\r\\n}\\r\\nif (!instance.root.getComponent('po_num').checkValidity()) {\\r\\n  valid = false;\\r\\n  instance.root.getComponent('po_num').setPristine(false); // Show validation error\\r\\n}\\r\\nif (!instance.root.getComponent('po_line').checkValidity()) {\\r\\n  valid = false;\\r\\n  instance.root.getComponent('po_line').setPristine(false); // Show validation error\\r\\n}\\r\\nif (!instance.root.getComponent('UOM').checkValidity()) {\\r\\n  valid = false;\\r\\n  instance.root.getComponent('UOM').setPristine(false); // Show validation error\\r\\n}\\r\\n\\r\\nif(!valid)\\r\\nreturn false;\\r\\ntableData.push({\\r\\n  item: data.item_1,\\r\\n  qty: data.qty,\\r\\n   po_num: data.po_num,\\r\\n    po_line: data.po_line,\\r\\n      UOM: data.UOM,\\r\\n        notes: data.notes,\\r\\n});\\r\\ninstance.root.getComponent('lineEntry').setValue(tableData);// clear fieldsinstance.root.getComponent('nameField').setValue('');\\r\\n// instance.root.getComponent('item_1').setValue('');\\r\\n// instance.root.getComponent('qty').setValue('');\\r\\n// instance.root.getComponent('po_num').setValue('');\\r\\n// instance.root.getComponent('po_line').setValue('');\\r\\n// instance.root.getComponent('UOM').setValue('');\\r\\n// instance.root.getComponent('notes').setValue('');\\r\\n// data.triggerValidation = false;\\r\\n\",\"input\":true}]},{\"label\":\"isSubmitting\",\"defaultValue\":\"false\",\"key\":\"isSubmitting\",\"type\":\"hidden\",\"input\":true,\"tableView\":false},{\"label\":\"Line Entry\",\"customClass\":\"lineEntryCont\",\"hideLabel\":true,\"tableView\":false,\"addAnother\":\"Add Line\",\"validate\":{\"required\":true},\"validateWhenHidden\":false,\"rowDrafts\":false,\"key\":\"lineEntry\",\"type\":\"editgrid\",\"displayAsTable\":false,\"input\":true,\"components\":[{\"label\":\"Item\",\"labelPosition\":\"left-left\",\"placeholder\":\"Item\",\"applyMaskOn\":\"change\",\"tableView\":true,\"validateWhenHidden\":false,\"key\":\"item\",\"type\":\"textfield\",\"input\":true},{\"label\":\"Qty\",\"labelPosition\":\"left-left\",\"placeholder\":\"Item\",\"applyMaskOn\":\"change\",\"tableView\":true,\"validateWhenHidden\":false,\"key\":\"qty\",\"type\":\"textfield\",\"input\":true},{\"label\":\"UOM\",\"labelPosition\":\"left-left\",\"placeholder\":\"UOM\",\"applyMaskOn\":\"change\",\"tableView\":true,\"validateWhenHidden\":false,\"key\":\"UOM\",\"type\":\"textfield\",\"input\":true},{\"label\":\"PO #\",\"labelPosition\":\"left-left\",\"placeholder\":\"Purchase Order\",\"applyMaskOn\":\"change\",\"tableView\":true,\"validateWhenHidden\":false,\"key\":\"po_num\",\"type\":\"textfield\",\"input\":true},{\"label\":\"PO Line #\",\"labelPosition\":\"left-left\",\"placeholder\":\"Purchase Order Line\",\"applyMaskOn\":\"change\",\"tableView\":true,\"validateWhenHidden\":false,\"key\":\"po_line\",\"type\":\"textfield\",\"input\":true},{\"label\":\"Notes\",\"labelPosition\":\"left-left\",\"placeholder\":\"Notes\",\"applyMaskOn\":\"change\",\"tableView\":false,\"validateWhenHidden\":false,\"key\":\"notes\",\"type\":\"textfield\",\"input\":true}]},{\"label\":\"Save\",\"action\":\"custom\",\"showValidations\":false,\"tableView\":false,\"key\":\"submit\",\"type\":\"button\",\"custom\":\"console.log(data)\\r\\ndata.isSubmitting = true;\\r\\n\\r\\n// Submit the form programmatically\\r\\nsetTimeout(() => {\\r\\ninstance.emit('submitButton');\\r\\n\\r\\n}, 500); // Wa\\r\\n\\r\\n// Optional: reset the flag afterward\\r\\nsetTimeout(() => {\\r\\n  data.isSubmitting = false;\\r\\n}, 1000); // Wait until\",\"input\":true}]}"
        */
        if ($poReceiptForm && $inforPoReceiptIntegration) {
            $settings[] = [
                'name' => 'Infor WMS PO Receipt Integration',
                'form_id' => $poReceiptForm->id,
                'integration_configuration_id' => $inforPoReceiptIntegration->id,
                'field_mappings' => [
                    'documentID_id' => 'documentID_id',
                    'sourceLocation' => 'sourceLocation',
                    'type' => 'type',
                    'notes' => 'notes',
                    'asnItems_sku' => 'asnItems_sku',
                    'asnItems_qtyExpected' => 'asnItems_qtyExpected',
                    'asnItems_uom' => 'asnItems_uom',
                    'asnItems_purchaseOrderReference_documentID_id' => 'asnItems_purchaseOrderReference_documentID_id',
                    'asnItems_purchaseOrderReference_lineNumber' => 'asnItems_purchaseOrderReference_lineNumber',
                    'asnItems_notes' => 'asnItems_notes',
                ],
                'description' => 'Integration between PO Receipt Form and Infor WMS PO Receipt integration',
                'is_active' => true,
                'created_by' => $superAdmin->id,
                'updated_by' => $superAdmin->id,
            ];
        }

        return $settings;
    }
}

