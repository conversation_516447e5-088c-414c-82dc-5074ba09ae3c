<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('form_submission_syncs', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->unsignedBigInteger('form_submission_id');
            $table->unsignedBigInteger('form_integration_setting_id');
            $table->enum('status', ['pending', 'processing', 'completed', 'failed', 'retrying'])->default('pending');
            $table->json('sync_data')->nullable();
            $table->json('response_data')->nullable();
            $table->text('error_message')->nullable();
            $table->string('error_code', 50)->nullable();
            $table->timestamp('attempted_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->integer('retry_count')->default(0);
            $table->integer('max_retries')->default(3);
            $table->timestamp('next_retry_at')->nullable();
            $table->integer('processing_time_ms')->nullable();
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('form_submission_id')->references('id')->on('form_submissions')->onDelete('cascade');
            $table->foreign('form_integration_setting_id')->references('id')->on('form_integration_settings')->onDelete('cascade');

            // Indexes for performance
            $table->index('form_submission_id', 'idx_form_submission_syncs_submission_id');
            $table->index('form_integration_setting_id', 'idx_form_submission_syncs_integration_id');
            $table->index('status', 'idx_form_submission_syncs_status');
            $table->index(['next_retry_at', 'status'], 'idx_form_submission_syncs_retry');
            $table->index(['status', 'attempted_at'], 'idx_form_submission_syncs_status_date');
            $table->index(['form_submission_id', 'status'], 'idx_form_submission_syncs_sub_status');

            // Unique constraint to prevent duplicate syncs
            $table->unique(['form_submission_id', 'form_integration_setting_id'], 'unique_submission_integration');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('form_submission_syncs');
    }
};
