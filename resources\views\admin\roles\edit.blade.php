@extends('layouts.admin')
@section('pageTitle',trans('global.edit')." ". trans('cruds.role.title_singular'))

@section('content')

<div class="card card-default">
    <div class="card-header  separator">
        <div class="card-title mainheading"> 
            <h4>      
                {{ trans('global.edit') }} {{ trans('cruds.role.title_singular') }}
            </h4>
        </div>
        <div class="card-controls">
            <ul>

            </ul>
        </div>
    </div>
    <div class="card-block">
        <form method="POST" id="form-work" class="form-horizontal"  action="{{ route("admin.roles.update", [$role->id]) }}" enctype="multipart/form-data">
            @method('PUT')
            @csrf
            <div class="form-group row">
                <div class="col-md-3">
                    <h5 class="bold inputlabel required" for="title">{{ trans('cruds.role.fields.title') }}</h5>
                </div>
                <div class="col-md-9">
                    <input class="form-control {{ $errors->has('title') ? 'is-invalid' : '' }}" type="text" name="title" id="title" value="{{ old('title', $role->title) }}" required>
                    @if($errors->has('title'))
                    <span class="text-danger">{{ $errors->first('title') }}</span>
                    @endif
                    <span class="help-block">{{ trans('cruds.role.fields.title_helper') }}</span>
                </div>

            </div>


            {{-- <div class="form-group row">
                <div class="col-md-3">
                    <label class="required" for="permissions">{{ trans('cruds.role.fields.permissions') }}</label>

                </div>
                <div class="col-md-9">
                    <div style="padding-bottom: 4px">
                        <span class="btn btn-info btn-xs select-all" style="border-radius: 0">{{ trans('global.select_all') }}</span>
                        <span class="btn btn-info btn-xs deselect-all" style="border-radius: 0">{{ trans('global.deselect_all') }}</span>
                    </div>
                    <select class="form-control select2 {{ $errors->has('permissions') ? 'is-invalid' : '' }}" name="permissions[]" id="permissions" multiple required>
                        @foreach($permissions as $id => $permission)
                        <option value="{{ $id }}" {{ (in_array($id, old('permissions', [])) || $role->permissions->contains($id)) ? 'selected' : '' }}>{{ $permission }}</option>
                        @endforeach
                    </select>
                    @if($errors->has('permissions'))
                    <span class="text-danger">{{ $errors->first('permissions') }}</span>
                    @endif
                    <span class="help-block">{{ trans('cruds.role.fields.permissions_helper') }}</span>
                </div>

            </div> --}}

             <div class="form-group row">
                <div class="col-md-3">
                    <label class="required" for="permissions">{{ trans('cruds.role.fields.permissions') }} {{trans('global.list')}}</label>

                </div>
                <div class="col-md-9">
                    <div class="row">
                        @foreach($permissions as $id => $permission)
                        <div class="col-md-4">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" name="permissions[]" id="permissions" value="{{$id}}" {{ (in_array($id, old('permissions', [])) || $role->permissions->contains($id)) ? 'checked' : '' }}> 
                                <label class="form-check-label" for="exampleCheck1" >{{$permission}}</label>
                              </div>
                            {{-- <label class="ribbon">{{$permission}}</label> --}}

                        </div>
                        @endforeach
                    </div>
                </div>

            </div>
            <div class="form-group row">
                <div class="col-md-3">

                </div>
                <div class="col-md-9">
                    <button class="btn btn-danger" type="submit">
                        {{ trans('global.save') }}
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

@endsection
