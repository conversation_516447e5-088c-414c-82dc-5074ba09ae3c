<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreUserGroupRequest;
use App\Http\Requests\UpdateUserGroupRequest;
use App\Models\Form;
use App\Models\UserGroup;
use Illuminate\Http\Request;
use Gate;
use Symfony\Component\HttpFoundation\Response;
use Yajra\DataTables\Facades\DataTables;

class UserGroupController extends Controller
{
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $query = UserGroup::withCount('users')->orderBy('created_at', 'desc');
            $query = $query->applyDataTableFilters($request);

            return DataTables::of($query)
                ->addIndexColumn()
                ->addColumn('checkbox', function ($row) {
                    return '<input type="checkbox" name="ids[]" value="' . $row->id . '" data-entry-id="' . $row->id . '">';
                })
                ->addColumn('action', function ($row) {


                    $actions = [];
                    // $actions['view'] = route('admin.usergroups.show', $row->id);
                    // if ($user->isAdmin() || $user->isSuperAdmin()) {
                    $actions['edit'] = route('admin.usergroups.edit', $row->id);
                    $actions['delete'] = route('admin.usergroups.destroy', $row->id);
                    // }
                    $html = view('partials.datatablesActions', compact('actions', 'row'))->render();

                    return $html;
                })
                ->addColumn('status', function ($row) {
                    return $row->is_active ?
                        '<span class="badge badge-success">Active</span>' :
                        '<span class="badge badge-danger">Inactive</span>';
                })
                ->addColumn('users_count', function ($row) {
                    return $row->users_count;
                })
                // ->editColumn('created_at', function ($row) {
                //     return $row->created_at->format('Y-m-d H:i:s');
                // })
                ->rawColumns(['checkbox', 'action', 'status'])
                ->make(true);
        }

        return view('admin.usergroups.index');
    }

    public function create()
    {
        return view('admin.usergroups.create');
    }

    public function store(StoreUserGroupRequest $request)
    {
        UserGroup::create($request->validated());

        return redirect()->route('admin.usergroups.index')
            ->with('success', 'User Group created successfully.');
    }

    public function show(UserGroup $usergroup)
    {
        $usergroup->load('users');
        return view('admin.usergroups.show', compact('usergroup'));
    }

    public function edit(UserGroup $usergroup)
    {
        return view('admin.usergroups.edit', compact('usergroup'));
    }

    public function update(UpdateUserGroupRequest $request, UserGroup $usergroup)
    {
        $usergroup->update($request->validated());

        return redirect()->route('admin.usergroups.index')
            ->with('success', 'User Group updated successfully.');
    }

    public function destroy(UserGroup $usergroup)
    {
        $usergroup->delete();

        return redirect()->route('admin.usergroups.index')
            ->with('success', 'User Group deleted successfully.');
    }

    public function massDestroy(Request $request)
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'exists:user_groups,id'
        ]);

        UserGroup::whereIn('id', $request->ids)->delete();

        return response()->json([
            'success' => true,
            'message' => 'User Groups deleted successfully.'
        ]);
    }
    public function formGroups(Request $request)
    {
        // Get filter inputs
        $module      = $request->input('module');
        $processType = $request->input('process_type');   // <-- new filter
        $formIds     = $request->input('forms', []);
        $groupFilter = $request->input('group_filter', []);

        // Base query with relationships
        $formsQuery = Form::with('userGroups');

        // Apply filters
        if ($module) {
            $formsQuery->where('module', $module);
        }

        if ($processType) {
            $formsQuery->where('process_type', $processType);
        }

        if (!empty($formIds)) {
            $formsQuery->whereIn('id', $formIds);
        }

        if ($groupFilter) {
            $formsQuery->whereHas('userGroups', function ($q) use ($groupFilter) {
                $q->whereIn('user_groups.id', (array) $groupFilter);
            });
        }

        // Order so forms are grouped nicely
        $forms = $formsQuery->orderBy('process_type')->orderBy('title')->get();
        $formsAll =  Form::all();
        // Dropdown data
        $groups       = UserGroup::all();
        $form_modules = Form::$form_modules;
        $processTypes = Form::pluck('process_type', 'process_type')->unique();

        return view('admin.usergroups.forms', compact(
            'forms',
            'groups',
            'formsAll',
            'form_modules',
            'processTypes'
        ));
    }

    public function storeFormGroups(Request $request)
    {
        $validated = $request->validate([
            'assignments' => 'required|array'
        ]);
        // dd($validated);
        // Reset all assignments
        foreach ($validated['assignments'] as $formId => $groupIds) {
            $form = Form::find($formId);
            $form->userGroups()->sync($groupIds ?? []);
        }

        return redirect()->back()->with('success', 'Assignments updated successfully');
    }
}
