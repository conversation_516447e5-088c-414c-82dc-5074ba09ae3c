/* Field Mapping Transformations */

/* Transformation container */
.transform-container {
    position: relative;
}

/* Transformation type select */
.transform-type {
    margin-bottom: 8px;
}

/* Transformation parameters */
.transform-params {
    padding: 8px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #dee2e6;
    margin-top: 5px;
}

/* Configure button */
.configure-transform {
    margin-top: 5px;
    width: 100%;
}

/* Key-Value Editor */
.key-value-editor {
    margin-top: 10px;
}

.key-value-pairs {
    margin-bottom: 10px;
}

.key-value-pair {
    margin-bottom: 8px;
    align-items: center;
}

.key-value-pair .form-control {
    margin-right: 5px;
}

.key-value-pair .btn {
    padding: 0.25rem 0.5rem;
}

/* Transformation badge */
.transform-badge {
    margin-left: 5px;
    font-size: 0.75rem;
    vertical-align: middle;
}

/* Modal styles */
#transformConfigModal .modal-body {
    max-height: 70vh;
    overflow-y: auto;
}

/* Code editor styling */
.code-editor {
    font-family: 'Courier New', Courier, monospace;
    font-size: 0.9em;
    min-height: 150px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .key-value-pair {
        flex-wrap: wrap;
    }
    
    .key-value-pair .col {
        margin-bottom: 5px;
    }
    
    .key-value-pair .col-auto {
        width: 100%;
        text-align: right;
    }
}
