<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\IntegrationConfiguration;
use App\Models\User;

class IntegrationConfigurationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Get admin user
        $admin = User::where('email', '<EMAIL>')->first();
        
        // if (!$admin) {
        //     $admin = User::factory()->create([
        //         'name' => 'Admin',
        //         'email' => '<EMAIL>',
        //         'password' => bcrypt('password'),
        //     ]);
        // }

        // CSI Integration Configurations
        $csiConfigurations = [
            [
                'name' => 'CSI Misc Issue API',
                'external_system' => 'csi_10',
                'integration_method' => 'api',
                'process_type' => 'misc_issue',
                'endpoint_url' => 'IDORequestService/ido/invoke/SLItemLocs?method=IaPostSp',
                'request_fields' => [
                    [
                        'name' => 'TransQty',
                        'datatype' => 'integer',
                        'maxlength' => 19,
                        'required' => true,
                        'default' => null,
                        'description' => 'Quantity to issue'
                    ],
                    [
                        'name' => 'Whse',
                        'datatype' => 'string',
                        'maxlength' => 4,
                        'required' => true,
                        'default' => null,
                        'description' => 'Warehouse code'
                    ],
                    [
                        'name' => 'Item',
                        'datatype' => 'string',
                        'maxlength' => 30,
                        'required' => true,
                        'default' => null,
                        'description' => 'Item'
                    ],
                    [
                        'name' => 'Loc',
                        'datatype' => 'string',
                        'maxlength' => 15,
                        'required' => true,
                        'default' => null,
                        'description' => 'Location'
                    ],
                    [
                        'name' => 'Lot',
                        'datatype' => 'string',
                        'maxlength' => 15,
                        'required' => false,
                        'default' => null,
                        'description' => 'Lot number'
                    ],
                    [
                        'name' => 'ReasonCode',
                        'datatype' => 'string',
                        'maxlength' => 3,
                        'required' => false,
                        'default' => null,
                        'description' => 'Reason code'
                    ]
                ],
                'is_active' => true,
                'created_by' => 1,
                'updated_by' => 1
            ],
            [
                'name' => 'CSI Misc Receipt API',
                'external_system' => 'csi_10',
                'integration_method' => 'api',
                'process_type' => 'misc_receipt',
                'endpoint_url' => 'IDORequestService/ido/invoke/SLStockActItems?method=ItemMiscReceiptSp',
                'request_fields' => [
                    [
                        'name' => 'Item',
                        'datatype' => 'string',
                        'maxlength' => 30,
                        'required' => true,
                        'default' => null,
                        'description' => 'Item'
                    ],
                    [
                        'name' => 'Whse',
                        'datatype' => 'string',
                        'maxlength' => 4,
                        'required' => true,
                        'default' => null,
                        'description' => 'Warehouse code'
                    ],
                    [
                        'name' => 'Qty',
                        'datatype' => 'integer',
                        'maxlength' => 20,
                        'required' => true,
                        'default' => 1,
                        'description' => 'Quantity to receive'
                    ],
                    [
                        'name' => 'UM',
                        'datatype' => 'string',
                        'maxlength' => 3,
                        'required' => true,
                        'default' => null,
                        'description' => 'Unit of measure'
                    ],
                    [
                        'name' => 'Loc',
                        'datatype' => 'string',
                        'maxlength' => 15,
                        'required' => true,
                        'default' => null,
                        'description' => 'Location'
                    ],
                    [
                        'name' => 'Lot',
                        'datatype' => 'string',
                        'maxlength' => 15,
                        'required' => false,
                        'default' => null,
                        'description' => 'Lot number'
                    ],
                    [
                        'name' => 'Reason',
                        'datatype' => 'string',
                        'maxlength' => 3,
                        'required' => false,
                        'default' => null,
                        'description' => 'Reason code'
                    ]
                ],
                'is_active' => true,
                'created_by' => 1,
                'updated_by' => 1
            ],
            [
                'name' => 'CSI Quantity Move API',
                'external_system' => 'csi_10',
                'integration_method' => 'api',
                'process_type' => 'quantity_move',
                'endpoint_url' => 'IDORequestService/ido/invoke/SLItemLocs?method=MvPostSp',
                'request_fields' => [
                    [
                        'name' => 'Qty',
                        'datatype' => 'integer',
                        'maxlength' => 19,
                        'required' => true,
                        'default' => null,
                        'description' => 'Quantity to move'
                    ],
                    [
                        'name' => 'Item',
                        'datatype' => 'string',
                        'maxlength' => 30,
                        'required' => true,
                        'default' => null,
                        'description' => 'Item'
                    ],
                    [
                        'name' => 'FromWhse',
                        'datatype' => 'string',
                        'maxlength' => 4,
                        'required' => true,
                        'default' => null,
                        'description' => 'Source warehouse code'
                    ],
                    [
                        'name' => 'FromLoc',
                        'datatype' => 'string',
                        'maxlength' => 15,
                        'required' => true,
                        'default' => null,
                        'description' => 'Source location'
                    ],
                    [
                        'name' => 'FromLot',
                        'datatype' => 'string',
                        'maxlength' => 15,
                        'required' => false,
                        'default' => null,
                        'description' => 'Source lot number'
                    ],
                    [
                        'name' => 'ToWhse',
                        'datatype' => 'string',
                        'maxlength' => 4,
                        'required' => true,
                        'default' => null,
                        'description' => 'Destination warehouse code'
                    ],
                    [
                        'name' => 'ToLoc',
                        'datatype' => 'string',
                        'maxlength' => 15,
                        'required' => true,
                        'default' => null,
                        'description' => 'Destination location'
                    ],
                    [
                        'name' => 'ToLot',
                        'datatype' => 'string',
                        'maxlength' => 15,
                        'required' => false,
                        'default' => null,
                        'description' => 'Destination lot number'
                    ],
                ],
                'is_active' => true,
                'created_by' => 1,
                'updated_by' => 1
            ],
            [
                'name' => 'Infor WMS PO Receipt API',
                'external_system' => 'infor_wms',
                'integration_method' => 'api',
                'process_type' => 'po_receipt',
                'endpoint_url' => '/{warehouse}/advancedshipnotice',
                'request_fields' => [
                    [
                        'name' => 'Qty',
                        'datatype' => 'integer',
                        'maxlength' => 19,
                        'required' => true,
                        'default' => null,
                        'description' => 'Quantity to move'
                    ],
                    [
                        'name' => 'Item',
                        'datatype' => 'string',
                        'maxlength' => 30,
                        'required' => true,
                        'default' => null,
                        'description' => 'Item'
                    ],
                    [
                        'name' => 'FromWhse',
                        'datatype' => 'string',
                        'maxlength' => 4,
                        'required' => true,
                        'default' => null,
                        'description' => 'Source warehouse code'
                    ],
                    [
                        'name' => 'FromLoc',
                        'datatype' => 'string',
                        'maxlength' => 15,
                        'required' => true,
                        'default' => null,
                        'description' => 'Source location'
                    ],
                    [
                        'name' => 'FromLot',
                        'datatype' => 'string',
                        'maxlength' => 15,
                        'required' => false,
                        'default' => null,
                        'description' => 'Source lot number'
                    ],
                    [
                        'name' => 'ToWhse',
                        'datatype' => 'string',
                        'maxlength' => 4,
                        'required' => true,
                        'default' => null,
                        'description' => 'Destination warehouse code'
                    ],
                    [
                        'name' => 'ToLoc',
                        'datatype' => 'string',
                        'maxlength' => 15,
                        'required' => true,
                        'default' => null,
                        'description' => 'Destination location'
                    ],
                    [
                        'name' => 'ToLot',
                        'datatype' => 'string',
                        'maxlength' => 15,
                        'required' => false,
                        'default' => null,
                        'description' => 'Destination lot number'
                    ],
                ],
                'is_active' => true,
                'created_by' => 1,
                'updated_by' => 1
            ]
        ];

        // Combine all configurations
        $allConfigurations = array_merge($csiConfigurations);

        // Create integration configurations
        foreach ($allConfigurations as $config) {
            // Check if configuration already exists
            $existingConfig = IntegrationConfiguration::where('name', $config['name'])->first();

            if (!$existingConfig) {
                IntegrationConfiguration::create([
                    'name' => $config['name'],
                    'external_system' => $config['external_system'],
                    'integration_method' => $config['integration_method'],
                    'process_type' => $config['process_type'],
                    'endpoint_url' => $config['endpoint_url'],
                    'request_fields' => $config['request_fields'],
                    'is_active' => $config['is_active'],
                    'created_by' => $config['created_by'],
                    'updated_by' => $config['updated_by'],
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);

                $this->command->info("Created integration configuration: {$config['name']}");
            } else {
                $this->command->info("Integration configuration already exists: {$config['name']}");
            }
        }

        $this->command->info('Integration Configuration seeding completed!');
    }
}
