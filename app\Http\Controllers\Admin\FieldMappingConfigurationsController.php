<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\FieldMappingConfiguration;
use App\Models\Form;
use App\Models\IntegrationConfiguration;
use App\Models\IntegrationEndpoint;
use App\Http\Requests\StoreFieldMappingConfigurationRequest;
use App\Http\Requests\UpdateFieldMappingConfigurationRequest;
use App\Services\FieldMappingService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Yajra\DataTables\Facades\DataTables;

class FieldMappingConfigurationsController extends Controller
{
    protected FieldMappingService $fieldMappingService;

    public function __construct()
    {
        $this->fieldMappingService = app(FieldMappingService::class);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->getDataTableData($request);
        }

        $columns = FieldMappingConfiguration::getDataTableColumns();
        return view('admin.field-mapping-configurations.index', compact('columns'));
    }

    protected function getDataTableData(Request $request)
    {
        $query = FieldMappingConfiguration::with([
            'form',
            'integrationConfiguration',
            'createdBy'
        ]);

        return DataTables::of($query)
            ->addIndexColumn()
            ->editColumn('name', function ($row) {
                return $row->name ? $row->name : '';
            })
            ->addColumn('form.title', function ($row) {
                return $row->form ? $row->form->title : '';
            })
            ->addColumn('endpoint_configuration', function ($row) {
                return $row->integrationConfiguration ? $row->integrationConfiguration->name : '';
            })
            ->addColumn('external_system', function ($row) {
                return $row->integrationConfiguration ? 
                    IntegrationConfiguration::getExternalSystemOptions()[$row->integrationConfiguration->external_system] ?? 
                    $row->integrationConfiguration->external_system : '';
            })
            ->addColumn('integration_method', function ($row) {
                return $row->integrationConfiguration ? 
                    IntegrationConfiguration::getIntegrationMethodOptions()[$row->integrationConfiguration->integration_method] ?? 
                    $row->integrationConfiguration->integration_method : '';
            })
            ->addColumn('process_type', function ($row) {
                return $row->integrationConfiguration ? 
                    IntegrationConfiguration::getProcessTypeOptions()[$row->integrationConfiguration->process_type] ?? 
                    $row->integrationConfiguration->process_type : '';
            })
            ->addColumn('status', function ($row) {
                return $row->is_active ? 
                    '<span class="badge badge-success">Active</span>' : 
                    '<span class="badge badge-secondary">Inactive</span>';
            })
            ->addColumn('updated_by', function ($row) {
                return $row->updatedBy ? $row->updatedBy->name : ($row->createdBy ? $row->createdBy->name : 'System');
            })
            ->addColumn('updated_at', function ($row) {
                return $row->updated_at ? $row->updated_at->format('Y-m-d H:i:s') : '';
            })
            ->addColumn('actions', function ($row) {
                $viewRoute = route('admin.field-mapping-configurations.show', $row->id);
                $editRoute = route('admin.field-mapping-configurations.edit', $row->id);
                $deleteRoute = route('admin.field-mapping-configurations.destroy', $row->id);
                
                return view('partials.datatablesActions', [
                    'actions' => [
                        'view' => $viewRoute,
                        'edit' => $editRoute,
                        'delete' => $deleteRoute,
                        'name' => $row->name // Add name for delete confirmation
                    ]
                ]);
            })
            ->rawColumns(['actions', 'status'])
            ->make(true);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // TODO: Re-enable permission checking when user management system is ready
        // abort_if(auth()->user()->type !== 1, Response::HTTP_FORBIDDEN, '403 Forbidden');

        $forms = Form::active()->get(['id', 'title']);
        $integrations = IntegrationConfiguration::active()->get(['id', 'name', 'external_system', 'process_type', 'integration_method']);
        $targetOptions = IntegrationConfiguration::getExternalSystemOptions();
        $processOptions = IntegrationConfiguration::getProcessTypeOptions();
        $endpointTypeOptions = IntegrationConfiguration::getIntegrationMethodOptions();

        return view('admin.field-mapping-configurations.create', compact(
            'forms',
            'integrations',
            'targetOptions',
            'processOptions',
            'endpointTypeOptions'
        ));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreFieldMappingConfigurationRequest $request)
    {
        try {
            $data = $request->validated();
            $data['created_by'] = Auth::id();
            $data['updated_by'] = Auth::id();

            $configuration = FieldMappingConfiguration::create($data);

            // Validate the integration setting
            $validationErrors = $this->fieldMappingService->validateConfiguration($configuration);

            if (!empty($validationErrors)) {
                Log::warning('Field mapping configuration created with validation warnings', [
                    'integration_setting_id' => $integrationSetting->id,
                    'warnings' => $validationErrors
                ]);
            }

            return redirect()
                ->route('admin.field-mapping-configurations.index')
                ->with('success', 'Field mapping configuration created successfully.');

        } catch (\Exception $e) {
            Log::error('Failed to create field mapping configuration', [
                'error' => $e->getMessage(),
                'request_data' => $request->validated()
            ]);

            return redirect()
                ->back()
                ->withInput()
                ->with('error', 'Failed to create field mapping configuration: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(FieldMappingConfiguration $fieldMappingConfiguration)
    {
        // TODO: Re-enable permission checking when user management system is ready
        // abort_if(auth()->user()->type !== 1, Response::HTTP_FORBIDDEN, '403 Forbidden');

        $fieldMappingConfiguration->load(['form', 'integrationConfiguration', 'createdBy', 'updatedBy']);

        // Get validation status
        $validationErrors = [];
        
        try {
            if ($fieldMappingConfiguration->integrationConfiguration) {
                $formData = $fieldMappingConfiguration->form_data ?? [];
                $adapter = $this->fieldMappingService->getAdapter($fieldMappingConfiguration->integrationConfiguration->external_system);
                $validationErrors = $adapter->validateFormData($formData, $fieldMappingConfiguration);
            }
        } catch (\Exception $e) {
            Log::error('Error validating field mapping configuration: ' . $e->getMessage(), [
                'configuration_id' => $fieldMappingConfiguration->id,
                'trace' => $e->getTraceAsString()
            ]);
        }

        return view('admin.field-mapping-configurations.show', compact('fieldMappingConfiguration', 'validationErrors'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(FieldMappingConfiguration $fieldMappingConfiguration)
    {
        // TODO: Re-enable permission checking when user management system is ready
        // abort_if(auth()->user()->type !== 1, Response::HTTP_FORBIDDEN, '403 Forbidden');

        $fieldMappingConfiguration->load(['form', 'integrationConfiguration']);

        $forms = Form::active()->get(['id', 'title']);
        $integrations = IntegrationConfiguration::active()->get(['id', 'name', 'external_system', 'process_type', 'integration_method']);
        $targetOptions = IntegrationConfiguration::getExternalSystemOptions();
        $processOptions = IntegrationConfiguration::getProcessTypeOptions();
        $endpointTypeOptions = IntegrationConfiguration::getIntegrationMethodOptions();

        return view('admin.field-mapping-configurations.edit', compact(
            'fieldMappingConfiguration',
            'forms',
            'integrations',
            'targetOptions',
            'processOptions',
            'endpointTypeOptions'
        ));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateFieldMappingConfigurationRequest $request, FieldMappingConfiguration $fieldMappingConfiguration)
    {
        try {
            $data = $request->validated();
            $data['updated_by'] = Auth::id();

            $fieldMappingConfiguration->update($data);

            // Validate the updated integration setting
            $validationErrors = $this->fieldMappingService->validateConfiguration($fieldMappingConfiguration);

            if (!empty($validationErrors)) {
                Log::warning('Form integration setting updated with validation warnings', [
                    'integration_setting_id' => $formIntegrationSetting->id,
                    'warnings' => $validationErrors
                ]);
            }

            return redirect()
                ->route('admin.field-mapping-configurations.index')
                ->with('success', 'Field mapping configuration updated successfully.');

        } catch (\Exception $e) {
            Log::error('Failed to update form integration setting', [
                'integration_setting_id' => $formIntegrationSetting->id,
                'error' => $e->getMessage(),
                'request_data' => $request->validated()
            ]);

            return redirect()
                ->back()
                ->withInput()
                ->with('error', 'Failed to update form integration setting: ' . $e->getMessage());
        }
    }

    /**
     * Get form fields for AJAX request
     */
    public function getFormFields(Request $request)
    {
        $formId = $request->input('form_id');

        if (!$formId) {
            return response()->json(['error' => 'Form ID is required'], 400);
        }

        try {
            $form = Form::findOrFail($formId);
            
            // Log the raw form structure for debugging
            Log::debug('Raw form structure', [
                'form_id' => $form->id,
                'form_structure' => $form->form_structure,
                'form_structure_type' => gettype($form->form_structure),
                'form_structure_length' => strlen($form->form_structure)
            ]);
            
            // Decode the form structure for further inspection
            $formStructure = json_decode($form->form_structure, true);
            Log::debug('Decoded form structure', [
                'form_id' => $form->id,
                'is_array' => is_array($formStructure),
                'keys' => is_array($formStructure) ? array_keys($formStructure) : 'Not an array',
                'json_error' => json_last_error() !== JSON_ERROR_NONE ? json_last_error_msg() : null
            ]);
            
            $formFields = $this->fieldMappingService->getFormFields($form);
            
            Log::debug('Extracted form fields', [
                'form_id' => $form->id,
                'field_count' => is_array($formFields) ? count($formFields) : 0,
                'fields' => is_array($formFields) ? array_keys($formFields) : 'Not an array'
            ]);
            
            return response()->json([
                'fields' => $formFields,
                '_debug' => [
                    'form_id' => $form->id,
                    'form_title' => $form->title,
                    'structure_type' => gettype($form->form_structure),
                    'structure_length' => strlen($form->form_structure),
                    'json_error' => json_last_error() !== JSON_ERROR_NONE ? json_last_error_msg() : null,
                    'extracted_fields' => is_array($formFields) ? array_keys($formFields) : []
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get form fields', [
                'form_id' => $formId,
                'error' => $e->getMessage()
            ]);

            return response()->json(['error' => 'Failed to get form fields'], 500);
        }
    }

    /**
     * Get integration fields for AJAX request
     */
    public function getIntegrationFields(Request $request)
    {
        $integrationId = $request->input('integration_id');

        if (!$integrationId) {
            return response()->json(['error' => 'Integration ID is required'], 400);
        }

        try {
            $integration = IntegrationConfiguration::findOrFail($integrationId);
            $integrationFields = $this->fieldMappingService->getIntegrationFields($integration);
            return response()->json(['fields' => $integrationFields]);

        } catch (\Exception $e) {
            Log::error('Failed to get integration fields', [
                'integration_id' => $integrationId,
                'error' => $e->getMessage()
            ]);

            return response()->json(['error' => 'Failed to get integration fields'], 500);
        }
    }

    /**
     * Get field mapping suggestions for AJAX request
     */
    public function getFieldMappingSuggestions(Request $request)
    {
        try {
            $formId = $request->input('form_id');
            $integrationId = $request->input('integration_configuration_id');

            if (!$formId || !$integrationId) {
                return response()->json(['error' => 'Form ID and Integration ID are required'], 400);
            }

            // Get form fields
            $form = Form::findOrFail($formId);
            $formFields = $this->fieldMappingService->getFormFields($form);
            
            // Get integration fields
            $integration = IntegrationConfiguration::findOrFail($integrationId);
            $integrationFields = $this->fieldMappingService->getIntegrationFields($integration);
            
            // Get suggestions
            $suggestionService = app(\App\Services\FieldMappingSuggestionService::class);
            $suggestions = $suggestionService->generateSuggestions($formFields, $integrationFields);
            
            // Prepare detailed suggestions with additional context
            $detailedSuggestions = [];

            foreach ($suggestions as $formField => $integrationField) {
                $formFieldData = collect($formFields)->firstWhere('key', $formField);
                $integrationFieldData = collect($integrationFields)->firstWhere('name', $integrationField);

                if ($formFieldData && $integrationFieldData) {
                    $matchInfo = $suggestionService->generateSuggestions([$formFieldData], [$integrationFieldData]);
                    $detailedSuggestions[] = [
                        'form_field' => $formField,
                        'form_label' => $formFieldData['label'] ?? $formField,
                        'integration_field' => $integrationField,
                        'integration_description' => $integrationFieldData['description'] ?? '',
                        'confidence' => 0.9, // Default high confidence for suggested matches
                        'reason' => 'Intelligent matching algorithm'
                    ];
                }
            }

            return response()->json([
                'success' => true,
                'form_fields' => $formFields,
                'integration_fields' => $integrationFields,
                'suggestions' => $suggestions,
                'detailed_suggestions' => $detailedSuggestions,
                'total_suggestions' => count($suggestions),
                'unmapped_form_fields' => array_filter($formFields, function($field) use ($suggestions) {
                    return !isset($suggestions[$field['key']]);
                }),
                'unmapped_integration_fields' => array_filter($integrationFields, function($field) use ($suggestions) {
                    return !in_array($field['name'], array_values($suggestions));
                })
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get field mapping suggestions', [
                'form_id' => $formId ?? null,
                'integration_id' => $integrationId ?? null,
                'error' => $e->getMessage()
            ]);

            return response()->json(['error' => 'Failed to get field mapping suggestions'], 500);
        }
    }

    /**
     * Get endpoint fields for the given endpoint configuration ID
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getEndpointFields(Request $request)
    {
        try {
            $request->validate([
                'endpoint_id' => 'required|exists:integration_configurations,id',
            ]);

            $endpointId = $request->input('endpoint_id');
            $endpoint = IntegrationConfiguration::findOrFail($endpointId);

            // Parse the request fields from the endpoint configuration
            $requestFields = json_decode($endpoint->request_fields, true) ?? [];

            // Format the fields for the response
            $fields = array_map(function($field) {
                return [
                    'name' => $field['name'] ?? '',
                    'type' => $field['type'] ?? 'string',
                    'required' => $field['required'] ?? false,
                    'description' => $field['description'] ?? '',
                    'example' => $field['example'] ?? ''
                ];
            }, $requestFields);

            return response()->json([
                'success' => true,
                'fields' => $fields
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get endpoint fields: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to load endpoint fields: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Mass delete field mapping configurations.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function massDestroy(Request $request)
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'exists:field_mapping_configurations,id',
        ]);

        try {
            FieldMappingConfiguration::whereIn('id', $request->ids)->delete();
            
            return response()->json([
                'success' => true,
                'message' => __('Field mapping configurations deleted successfully')
            ]);
        } catch (\Exception $e) {
            Log::error('Field Mapping Configurations Mass Deletion Error: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while deleting the field mapping configurations.'
            ], 500);
        }
    }
}
