<script src="https://cdn.form.io/js/formio.form.min.js"></script>
<script>
    var $components = <?= json_encode($components) ?>;

    var $dcfields = <?= json_encode($dcfields) ?>;

    // console.log(ccomonents);
    var schema = {
        noDefaultSubmitButton: true,
        builder: {
            dcFeilds: {
                title: 'DC Feilds',
                weight: 0,
                default: true,
                components: $dcfields
            },
            customGroup: {
                title: 'Templates',
                weight: 0,
                default: true,
                components: $components
            },
            basic: false,
            advanced: false,
            premium: false,
            data: false,
            customBasic: {
                title: 'Form Elements',
                default: true,
                weight: 0,
                components: {
                    textfield: true,
                    textarea: true,
                    email: false,
                    phoneNumber: false,
                    number: true,
                    checkbox: true,
                    selectboxes: false,
                    datetime: true,
                    select: true,
                    radio: true,
                    button: true,
                }
            },
            data: {
                default: true,
                weight: 1,
                components: {
                    hidden: true,
                    container: false,
                    editgrid: true,
                    datamap: false,
                    datagrid: true,
                }
            },

            layout: {
                // title: 'Form s',
                default: true,
                weight: 1,
                components: {
                    htmlelement: false,
                    well: false,
                    fieldset: false,
                    tabs: false,
                    panel: true,
                    content: false,
                    table: true,
                    columns: true,
                }
            }
        },
        // editForm: {
        //     textfield: [{
        //         key: 'api',
        //         ignore: true
        //     }]
        // }
    };
    <?php
    // dd($form->content);
    ?>
    var existingForm = {!! isset($form) ? $form->content : 'false' !!};
    // console.log(existingForm)
    var systemFields = @json($systemFields); // already resolved values

    if (!existingForm) {
        existingForm = {
            display: 'form',
            components: [...systemFields]
        };
    } else {
        // If form exists, make sure system fields are added (avoid duplicates)
        systemFields.forEach(field => {
            const exists = existingForm.components?.some(c => c.key === field.key);
            if (!exists) {
                existingForm.components.push(field);
            }
        });
    }
    var builder;
    Formio.builder(document.getElementById('builder'), existingForm, schema).then(function(formBuilder) {
        builder = formBuilder;

        function lockSystemFields() {
            systemFields.forEach(field => {
                // Target the builder component by key
                const compWrapper = document.querySelector(`.formio-component-${field.key}`);
                if (compWrapper) {
                    const dragContainer = compWrapper.closest('[ref="dragComponent"]');
                    if (dragContainer) {
                        dragContainer.classList.add('system-field');

                        // Hide remove button
                        const removeBtn = dragContainer.querySelector('[ref="removeComponent"]');
                        if (removeBtn) removeBtn.style.display = 'none';
                        const editJsonBtn = dragContainer.querySelector('[ref="editJson"]');
                        if (editJsonBtn) editJsonBtn.style.display = 'none';


                        // Hide edit button
                        const editBtn = dragContainer.querySelector('[ref="editComponent"]');
                        if (editBtn) editBtn.style.display = 'none';

                        // Add a system badge
                        const label = compWrapper.querySelector('[ref="element"]');
                        if (label && !label.querySelector('.system-badge')) {
                            const badge = document.createElement('span');
                            badge.innerText = ' (System Field)';
                            badge.className = 'system-badge';

                            label.appendChild(badge);
                        }
                    }
                }
            });
        }
        // builder.on('render', function() {
        //     lockSystemFields();
        // });
        builder.on('change', function() {

            document.getElementById('content').value = JSON.stringify(builder.schema);
            lockSystemFields();
        });
        lockSystemFields();
    });

    // Form submission
    document.getElementById('formData').addEventListener('submit', function(e) {
        if (builder) {
            document.getElementById('content').value = JSON.stringify(builder.schema);
        }

        if (!document.getElementById('content').value || document.getElementById('content').value === '{}') {
            e.preventDefault();
            alert('Please design your form using the form builder.');
        }
    });

    // Initialize Select2
    $(document).ready(function() {
        $('#user_groups').select2({
            // placeholder: "{{ trans('cruds.fields.select_user_groups') }}",
            allowClear: true
        });


    });
</script>
