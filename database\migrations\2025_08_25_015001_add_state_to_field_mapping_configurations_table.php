<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('field_mapping_configurations', function (Blueprint $table) {
            $table->enum('state', ['draft', 'finalized'])->default('draft')->after('is_active');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('field_mapping_configurations', function (Blueprint $table) {
            $table->dropColumn('state');
        });
    }
};
