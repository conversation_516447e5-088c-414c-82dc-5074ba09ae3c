<?php

namespace App\Traits;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Carbon\Carbon;

trait DataTableFilter
{
    /**
     * Apply DataTable filters dynamically
     */
    public function scopeApplyDataTableFilters(Builder $query, Request $request): Builder
    {

        $columns = self::dataTableColumns();
        // ✅ 1. Global Search
        $globalSearch = $request->input('search.value');
        // dd($globalSearch);
        if ($globalSearch) {

            $query->where(function ($q) use ($globalSearch, $columns) {
                foreach ($columns as $col) {
                    $colName    = $col['name'] ?? $col['data'] ?? null;
                    $filterType = $col['filter'] ?? null;

                    if (!$colName || !$filterType) {
                        continue;
                    }

                    // Skip non-text-like filters
                    if (in_array($filterType, ['select', 'boolean', 'date_range'])) {
                        continue;
                    }
                    // dd($col);
                    if ($filterType === "relation" && str_contains($colName, '.')) {
                        [$relation, $relCol] = explode('.', $colName, 2);
                        $q->orWhereHas($relation, function (Builder $sub) use ($relCol, $globalSearch) {
                            $this->applyFilterCondition($sub, $relCol, $globalSearch, 'text');
                        });
                    } else {
                        $q->orWhere(function ($sub) use ($colName, $globalSearch) {
                            $this->applyFilterCondition($sub, $colName, $globalSearch, 'text');
                        });
                    }
                }
            });
        }

        foreach ($columns as $index => $col) {
            $colName    = $col['name'] ?? $col['data'] ?? null; // prefer "name"
            $filterType = $col['filter'] ?? null;
            $searchVal  = $request->input("columns.$index.search.value");

            if (!$colName || !$filterType || $searchVal === null || $searchVal === '') {
                continue;
            }

            // ✅ Custom callback for advanced filters
            if (isset($col['filter_callback']) && is_callable($col['filter_callback'])) {
                $query = call_user_func($col['filter_callback'], $query, $searchVal);
                continue;
            }

            // ✅ Relationship filter (e.g. "creator.name")
            if ($filterType === "relation" && str_contains($colName, '.')) {
                [$relation, $relCol] = explode('.', $colName, 2);
                $query->whereHas($relation, function (Builder $q) use ($relCol, $searchVal, $filterType) {
                    $this->applyFilterCondition($q, $relCol, $searchVal, $filterType);
                });
                continue;
            }

            // ✅ Regular column filter
            $this->applyFilterCondition($query, $colName, $searchVal, $filterType);
        }

        return $query;
    }

    /**
     * Apply a single filter condition
     */
    protected function applyFilterCondition(Builder $query, string $colName, string $searchVal, string $filterType): void
    {
        switch ($filterType) {
            case 'text':
                // dd($colName, $searchVal);
                $query->where($colName, 'like', "%{$searchVal}%");
                break;

            case 'select':
            case 'boolean':
                $query->where($colName, $searchVal);
                break;

            case 'date_range':
                if (str_contains($searchVal, '|')) {
                    [$start, $end] = explode('|', $searchVal);

                    $start = trim($start);
                    $end   = trim($end);

                    if ($start && $end) {
                        try {
                            // Try strict ISO format first (YYYY-MM-DD)
                            $startDate = Carbon::createFromFormat('Y-m-d', $start)->startOfDay();
                            $endDate   = Carbon::createFromFormat('Y-m-d', $end)->endOfDay();
                        } catch (\Exception $e) {
                            // Fallback to free parse
                            $startDate = Carbon::parse($start)->startOfDay();
                            $endDate   = Carbon::parse($end)->endOfDay();
                        }

                        $query->whereBetween($colName, [$startDate, $endDate]);
                    }
                }
                break;
        }
    }
}
