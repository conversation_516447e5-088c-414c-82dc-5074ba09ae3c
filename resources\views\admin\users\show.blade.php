@extends('layouts.admin')
@section('pageTitle', __('global.view') . ' ' . __('cruds.user.title_singular'))

@section('content')
<div class="card card-default">
    <div class="card-header separator">
        <div class="card-title mainheading">
            <h4>{{ __('global.view') }} {{ __('cruds.user.title_singular') }}</h4>
        </div>
        <div class="card-controls">
            <ul>
                <li>
                    <a class="btn btn-secondary" href="{{ route('admin.users.index') }}">
                        {{ __('global.back_to_list') }}
                    </a>
                </li>
                <li>
                    <a class="btn btn-info" href="{{ route('admin.users.edit', $user->id) }}">
                        {{ __('global.edit') }}
                    </a>
                </li>
            </ul>
        </div>
    </div>

    <div class="card-block">
        <div class="row">
            <div class="col-md-6">
                <table class="table table-bordered">
                    <tr>
                        <th width="30%">{{ __('cruds.fields.name') }}</th>
                        <td>{{ $user->name }}</td>
                    </tr>
                    <tr>
                        <th>{{ __('cruds.fields.email') }}</th>
                        <td>{{ $user->email }}</td>
                    </tr>
                    <tr>
                        <th>{{ __('cruds.fields.role') }}</th>
                        <td>
                            @switch($user->type)
                                @case(1)
                                    <span class="badge badge-danger">Super Admin</span>
                                    @break
                                @case(2)
                                    <span class="badge badge-warning">Admin</span>
                                    @break
                                @case(3)
                                    <span class="badge badge-info">User</span>
                                    @break
                                @default
                                    <span class="badge badge-secondary">{{ __('cruds.fields.unknown') }}</span>
                            @endswitch
                        </td>
                    </tr>
                    <tr>
                        <th>{{ __('cruds.fields.user_group') }}</th>
                        <td>
                            @if($user->userGroup)
                                <span class="badge badge-primary">{{ $user->userGroup->name }}</span>
                            @else
                                <span class="text-muted">{{ __('cruds.fields.no_group_assigned') }}</span>
                            @endif
                        </td>
                    </tr>
                    <tr>
                        <th>{{ __('cruds.fields.email_verified') }}</th>
                        <td>
                            @if($user->email_verified_at)
                                <span class="badge badge-success">{{ __('cruds.fields.verified') }}</span>
                                <br><small>{{ $user->email_verified_at->format('Y-m-d H:i:s') }}</small>
                            @else
                                <span class="badge badge-warning">{{ __('cruds.fields.not_verified') }}</span>
                            @endif
                        </td>
                    </tr>
                    <tr>
                        <th>{{ __('global.created_at') }}</th>
                        <td>{{ $user->created_at->format('Y-m-d H:i:s') }}</td>
                    </tr>
                    <tr>
                        <th>{{ __('global.updated_at') }}</th>
                        <td>{{ $user->updated_at->format('Y-m-d H:i:s') }}</td>
                    </tr>
                </table>
            </div>
            <div class="col-md-6">
                @if($user->userGroup)
                <div class="card">
                    <div class="card-header">
                        <h5>{{ __('cruds.fields.user_group_information') }}</h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr>
                                <th>{{ __('cruds.fields.group_name') }}:</th>
                                <td>{{ $user->userGroup->name }}</td>
                            </tr>
                            <tr>
                                <th>{{ __('cruds.fields.description') }}:</th>
                                <td>{{ $user->userGroup->description ?? 'N/A' }}</td>
                            </tr>
                            <tr>
                                <th>{{ __('cruds.fields.status') }}:</th>
                                <td>
                                    @if($user->userGroup->is_active)
                                        <span class="badge badge-success">{{ __('cruds.fields.active') }}</span>
                                    @else
                                        <span class="badge badge-danger">{{ __('cruds.fields.inactive') }}</span>
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <th>{{ __('cruds.fields.total_members') }}:</th>
                                <td>{{ $user->userGroup->users->count() }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
