<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\FormSubmission;
use App\Models\FormSubmissionSync;
use App\Models\ErrorLog;
use App\Services\FormSubmissionService;
use App\Services\FormSyncEngineService;
use App\Services\AuditLoggerService;
use App\Services\ErrorLoggerService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Yajra\DataTables\Facades\DataTables;

class FormSubmissionController extends Controller
{
    protected FormSubmissionService $formSubmissionService;
    protected FormSyncEngineService $syncEngine;
    protected AuditLoggerService $auditLogger;
    protected ErrorLoggerService $errorLogger;

    public function __construct(
        FormSubmissionService $formSubmissionService,
        FormSyncEngineService $syncEngine,
        AuditLoggerService $auditLogger,
        ErrorLoggerService $errorLogger
    ) {
        $this->formSubmissionService = $formSubmissionService;
        $this->syncEngine = $syncEngine;
        $this->auditLogger = $auditLogger;
        $this->errorLogger = $errorLogger;
    }

    /**
     * Display a listing of form submissions
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $query = FormSubmission::with(['form', 'user'])
                ->orderBy('submitted_at', 'desc');

            return DataTables::of($query)
                ->addColumn('form_title', function ($row) {
                    return $row->form->title ?? 'Unknown Form';
                })
                ->addColumn('user_name', function ($row) {
                    return $row->user ? ($row->user->name ?? $row->user->email) : 'Anonymous';
                })
                ->addColumn('status_badge', function ($row) {
                    $color = $row->getStatusColor();
                    return "<span class=\"badge badge-{$color}\">{$row->getStatusLabel()}</span>";
                })
                ->addColumn('progress', function ($row) {
                    $percentage = $row->getProgressPercentage();
                    $color = $percentage == 100 ? 'success' : ($percentage > 0 ? 'warning' : 'secondary');
                    return "<div class=\"progress\">
                        <div class=\"progress-bar bg-{$color}\" style=\"width: {$percentage}%\">
                            {$percentage}%
                        </div>
                    </div>";
                })
                ->addColumn('integrations', function ($row) {
                    return "{$row->successful_integrations}/{$row->total_integrations}";
                })
                ->addColumn('action', function ($row) {
                    $actions = '<div class="btn-group" role="group">';
                    $actions .= '<a href="' . route('admin.form-submissions.show', $row->uuid) . '" class="btn btn-sm btn-info">View</a>';

                    if ($row->failed_integrations > 0) {
                        $actions .= '<button type="button" class="btn btn-sm btn-warning retry-submission" data-uuid="' . $row->uuid . '">Retry</button>';
                    }

                    $actions .= '</div>';
                    return $actions;
                })
                ->editColumn('submitted_at', function ($row) {
                    return $row->submitted_at->format('Y-m-d H:i:s');
                })
                ->rawColumns(['status_badge', 'progress', 'action'])
                ->make(true);
        }

        return view('admin.form-submissions.index');
    }

    /**
     * Show the specified form submission
     */
    public function show(string $uuid)
    {
        $submission = FormSubmission::where('uuid', $uuid)
            ->with(['form', 'user', 'syncs.formIntegrationSetting.endpointConfiguration'])
            ->firstOrFail();

        $auditLogs = $this->auditLogger->getSubmissionAuditTrail($submission);
        $errorLogs = $this->errorLogger->getSubmissionErrors($submission);

        return view('admin.form-submissions.show', compact('submission', 'auditLogs', 'errorLogs'));
    }

    /**
     * Retry failed syncs for a submission
     */
    public function retry(Request $request, string $uuid): JsonResponse
    {
        try {
            $submission = FormSubmission::where('uuid', $uuid)->firstOrFail();

            $user = Auth::user();
            $reason = $request->input('reason', 'Manual retry from admin interface');

            // Log the manual retry action
            $this->auditLogger->logUserAction(
                $user,
                \App\Models\AuditLog::EVENT_MANUAL_RETRY,
                [
                    'reason' => $reason,
                    'submission_uuid' => $uuid,
                ],
                $submission
            );

            // Retry failed syncs
            $this->formSubmissionService->retryFailedSyncs($submission);

            return response()->json([
                'success' => true,
                'message' => 'Retry initiated successfully',
            ]);

        } catch (\Exception $e) {
            $this->errorLogger->logError(
                \App\Models\ErrorLog::LEVEL_ERROR,
                \App\Models\ErrorLog::TYPE_SYSTEM_ERROR,
                $e->getMessage(),
                [
                    'submission_uuid' => $uuid,
                    'user_id' => Auth::id(),
                    'action' => 'manual_retry',
                ]
            );

            return response()->json([
                'success' => false,
                'message' => 'Failed to initiate retry: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get submission statistics
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            $hours = $request->input('hours', 24);

            $syncStats = $this->syncEngine->getSyncStatistics($hours);
            $errorStats = $this->errorLogger->getErrorStatistics($hours);

            $since = now()->subHours($hours);

            $submissionStats = [
                'total_submissions' => FormSubmission::where('submitted_at', '>=', $since)->count(),
                'completed_submissions' => FormSubmission::where('submitted_at', '>=', $since)
                    ->where('status', FormSubmission::STATUS_COMPLETED)->count(),
                'failed_submissions' => FormSubmission::where('submitted_at', '>=', $since)
                    ->where('status', FormSubmission::STATUS_FAILED)->count(),
                'pending_submissions' => FormSubmission::where('submitted_at', '>=', $since)
                    ->where('status', FormSubmission::STATUS_PENDING)->count(),
                'processing_submissions' => FormSubmission::where('submitted_at', '>=', $since)
                    ->where('status', FormSubmission::STATUS_PROCESSING)->count(),
            ];

            return response()->json([
                'success' => true,
                'data' => [
                    'submissions' => $submissionStats,
                    'syncs' => $syncStats,
                    'errors' => $errorStats,
                    'period_hours' => $hours,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve statistics: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Export submission data
     */
    public function export(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'format' => 'required|in:csv,json',
                'status' => 'sometimes|string',
                'form_id' => 'sometimes|integer',
                'date_from' => 'sometimes|date',
                'date_to' => 'sometimes|date',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors(),
                ], 422);
            }

            $query = FormSubmission::with(['form', 'user', 'syncs']);

            // Apply filters
            if ($request->has('status')) {
                $query->where('status', $request->input('status'));
            }

            if ($request->has('form_id')) {
                $query->where('form_id', $request->input('form_id'));
            }

            if ($request->has('date_from')) {
                $query->where('submitted_at', '>=', $request->input('date_from'));
            }

            if ($request->has('date_to')) {
                $query->where('submitted_at', '<=', $request->input('date_to'));
            }

            $submissions = $query->orderBy('submitted_at', 'desc')->get();

            // Log the export action
            $this->auditLogger->logDataExport(
                Auth::user(),
                'form_submissions_' . $request->input('format'),
                $request->only(['status', 'form_id', 'date_from', 'date_to']),
                $submissions->count()
            );

            $format = $request->input('format');
            $filename = 'form_submissions_' . now()->format('Y-m-d_H-i-s') . '.' . $format;

            if ($format === 'csv') {
                return $this->exportCsv($submissions, $filename);
            } else {
                return $this->exportJson($submissions, $filename);
            }

        } catch (\Exception $e) {
            $this->errorLogger->logError(
                \App\Models\ErrorLog::LEVEL_ERROR,
                \App\Models\ErrorLog::TYPE_SYSTEM_ERROR,
                $e->getMessage(),
                [
                    'user_id' => Auth::id(),
                    'action' => 'export_submissions',
                    'request_data' => $request->all(),
                ]
            );

            return response()->json([
                'success' => false,
                'message' => 'Export failed: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Export submissions as CSV
     */
    protected function exportCsv($submissions, $filename)
    {
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function () use ($submissions) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'UUID', 'Form Title', 'User', 'Status', 'Submitted At',
                'Total Integrations', 'Successful', 'Failed', 'Progress %'
            ]);

            foreach ($submissions as $submission) {
                fputcsv($file, [
                    $submission->uuid,
                    $submission->form->title ?? 'Unknown',
                    $submission->user ? ($submission->user->name ?? $submission->user->email) : 'Anonymous',
                    $submission->getStatusLabel(),
                    $submission->submitted_at->format('Y-m-d H:i:s'),
                    $submission->total_integrations,
                    $submission->successful_integrations,
                    $submission->failed_integrations,
                    $submission->getProgressPercentage(),
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Export submissions as JSON
     */
    protected function exportJson($submissions, $filename)
    {
        $data = $submissions->map(function ($submission) {
            return [
                'uuid' => $submission->uuid,
                'form' => [
                    'id' => $submission->form->id,
                    'title' => $submission->form->title,
                ],
                'user' => $submission->user ? [
                    'id' => $submission->user->id,
                    'name' => $submission->user->name,
                    'email' => $submission->user->email,
                ] : null,
                'status' => $submission->status,
                'status_label' => $submission->getStatusLabel(),
                'submitted_at' => $submission->submitted_at,
                'completed_at' => $submission->completed_at,
                'total_integrations' => $submission->total_integrations,
                'successful_integrations' => $submission->successful_integrations,
                'failed_integrations' => $submission->failed_integrations,
                'progress_percentage' => $submission->getProgressPercentage(),
                'submission_data' => $submission->submission_data,
                'metadata' => $submission->metadata,
            ];
        });

        $headers = [
            'Content-Type' => 'application/json',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        return response()->json($data, 200, $headers);
    }
}
