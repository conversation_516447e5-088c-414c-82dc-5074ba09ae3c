<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\FormFieldTemplate;

class FormFieldTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $templates = [
            [
                'title' => 'Item Field',
                'key' => 'itemField',
                'icon' => 'terminal',
                'schema' => [
                    'label' => 'Item',
                    'type' => 'textfield',
                    'key' => 'item_num',
                    'input' => true,
                    'validate' => [
                        'required' => true
                    ]
                ]
            ],
            [
                'title' => 'UOM Field',
                'key' => 'uomField',
                'icon' => 'terminal',
                'schema' => [
                    'label' => 'UOM',
                    'type' => 'textfield',
                    'key' => 'uom',
                    'input' => true,
                    'validate' => [
                        'required' => true
                    ]
                ]
            ],
            [
                'title' => 'Quantity Field',
                'key' => 'qtyField',
                'icon' => 'hashtag',
                'schema' => [
                    'label' => 'Qty',
                    'type' => 'number',
                    'key' => 'qty',
                    'input' => true,
                    'validate' => [
                        'required' => false
                    ]
                ]
            ],
            [
                'title' => 'Email Field',
                'key' => 'emailField',
                'icon' => 'envelope',
                'schema' => [
                    'label' => 'Email Address',
                    'type' => 'email',
                    'key' => 'email',
                    'input' => true,
                    'validate' => [
                        'required' => true
                    ]
                ]
            ]
        ];

        foreach ($templates as $template) {
            FormFieldTemplate::create($template);
        }
    }
}
