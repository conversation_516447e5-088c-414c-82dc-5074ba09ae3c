{"__meta": {"id": "01K4VAX316454HT3R4JXA338BR", "datetime": "2025-09-11 02:50:32", "utime": **********.87186, "method": "GET", "uri": "/admin/field-mapping-configurations/get-form-fields?form_id=4&_token=4RbyFb2oU1pjc4UNp6CmU470XASLVRa4SNAgSVgT", "ip": "127.0.0.1"}, "messages": {"count": 8, "messages": [{"message": "[02:50:32] LOG.warning: strlen(): Passing null to parameter #1 ($string) of type string is deprecated in D:\\Git Data Capture\\application\\app\\Http\\Controllers\\Admin\\FieldMappingConfigurationsController.php on line 278", "message_html": null, "is_string": false, "label": "warning", "time": **********.851922, "xdebug_link": null, "collector": "log"}, {"message": "[02:50:32] LOG.debug: Raw form structure {\n    \"form_id\": 4,\n    \"form_structure\": null,\n    \"form_structure_type\": \"NULL\",\n    \"form_structure_length\": 0\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.853005, "xdebug_link": null, "collector": "log"}, {"message": "[02:50:32] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in D:\\Git Data Capture\\application\\app\\Http\\Controllers\\Admin\\FieldMappingConfigurationsController.php on line 282", "message_html": null, "is_string": false, "label": "warning", "time": **********.853454, "xdebug_link": null, "collector": "log"}, {"message": "[02:50:32] LOG.debug: Decoded form structure {\n    \"form_id\": 4,\n    \"is_array\": false,\n    \"keys\": \"Not an array\",\n    \"json_error\": \"Syntax error\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.853892, "xdebug_link": null, "collector": "log"}, {"message": "[02:50:32] LOG.debug: Processing form structure {\n    \"form_id\": 4,\n    \"form_title\": \"PO Receipt\",\n    \"structure_type\": \"array\",\n    \"structure_keys\": [\n        \"components\"\n    ],\n    \"structure_sample\": {\n        \"components\": [\n            {\n                \"label\": \"Whse No.\",\n                \"labelPosition\": \"left-left\",\n                \"placeholder\": \"Whse No.\",\n                \"applyMaskOn\": \"change\",\n                \"tableView\": true,\n                \"validate\": {\n                    \"required\": true\n                },\n                \"validateWhenHidden\": false,\n                \"key\": \"whse_num\",\n                \"type\": \"textfield\",\n                \"input\": true\n            },\n            {\n                \"label\": \"To Loc\",\n                \"labelPosition\": \"left-left\",\n                \"placeholder\": \"To Loc\",\n                \"applyMaskOn\": \"change\",\n                \"tableView\": true,\n                \"validate\": {\n                    \"required\": true\n                },\n                \"validateWhenHidden\": false,\n                \"key\": \"to_loc\",\n                \"type\": \"textfield\",\n                \"input\": true\n            },\n            {\n                \"title\": \"Lines\",\n                \"collapsible\": false,\n                \"key\": \"lineEntryPanel\",\n                \"type\": \"panel\",\n                \"label\": \"Panel\",\n                \"input\": false,\n                \"tableView\": false,\n                \"components\": [\n                    {\n                        \"label\": \"Item No.\",\n                        \"labelPosition\": \"left-left\",\n                        \"placeholder\": \"Item No.\",\n                        \"applyMaskOn\": \"change\",\n                        \"tableView\": true,\n                        \"validate\": {\n                            \"required\": true\n                        },\n                        \"validateWhenHidden\": false,\n                        \"key\": \"item_num\",\n                        \"type\": \"textfield\",\n                        \"input\": true\n                    },\n                    {\n                        \"label\": \"Lot No.\",\n                        \"labelPosition\": \"left-left\",\n                        \"placeholder\": \"Lot No.\",\n                        \"applyMaskOn\": \"change\",\n                        \"tableView\": true,\n                        \"validate\": {\n                            \"required\": true\n                        },\n                        \"validateWhenHidden\": false,\n                        \"key\": \"lot_num\",\n                        \"type\": \"textfield\",\n                        \"input\": true\n                    },\n                    {\n                        \"label\": \"Qty\",\n                        \"labelPosition\": \"left-left\",\n                        \"placeholder\": \"Qty\",\n                        \"applyMaskOn\": \"change\",\n                        \"mask\": false,\n                        \"tableView\": false,\n                        \"delimiter\": false,\n                        \"requireDecimal\": false,\n                        \"inputFormat\": \"plain\",\n                        \"truncateMultipleSpaces\": false,\n                        \"validate\": {\n                            \"required\": true\n                        },\n                        \"validateWhenHidden\": false,\n                        \"key\": \"qty\",\n                        \"type\": \"number\",\n                        \"input\": true\n                    },\n                    {\n                        \"label\": \"UOM\",\n                        \"labelPosition\": \"left-left\",\n                        \"placeholder\": \"UOM\",\n                        \"applyMaskOn\": \"change\",\n                        \"tableView\": true,\n                        \"validate\": {\n                            \"required\": true\n                        },\n                        \"validateWhenHidden\": false,\n                        \"key\": \"uom\",\n                        \"type\": \"textfield\",\n                        \"input\": true\n                    },\n                    {\n                        \"label\": \"PO No.\",\n                        \"labelPosition\": \"left-left\",\n                        \"placeholder\": \"PO Number\",\n                        \"applyMaskOn\": \"change\",\n                        \"tableView\": true,\n                        \"validate\": {\n                            \"required\": true\n                        },\n                        \"validateWhenHidden\": false,\n                        \"key\": \"po_num\",\n                        \"type\": \"textfield\",\n                        \"input\": true\n                    },\n                    {\n                        \"label\": \"PO Line\",\n                        \"labelPosition\": \"left-left\",\n                        \"placeholder\": \"PO Line\",\n                        \"applyMaskOn\": \"change\",\n                        \"tableView\": true,\n                        \"validate\": {\n                            \"required\": true\n                        },\n                        \"validateWhenHidden\": false,\n                        \"key\": \"po_line\",\n                        \"type\": \"textfield\",\n                        \"input\": true\n                    },\n                    {\n                        \"label\": \"LPN\",\n                        \"labelPosition\": \"left-left\",\n                        \"placeholder\": \"LPN\",\n                        \"applyMaskOn\": \"change\",\n                        \"tableView\": true,\n                        \"validate\": {\n                            \"required\": true\n                        },\n                        \"validateWhenHidden\": false,\n                        \"key\": \"uom1\",\n                        \"type\": \"textfield\",\n                        \"input\": true\n                    },\n                    {\n                        \"label\": \"Add\",\n                        \"action\": \"custom\",\n                        \"showValidations\": false,\n                        \"theme\": \"info\",\n                        \"leftIcon\": \"bi bi-plus\",\n                        \"tableView\": false,\n                        \"key\": \"add\",\n                        \"type\": \"button\",\n                        \"custom\": \"data.isSubmitting = false;\\r\\n\\r\\n\\/\\/ Get existing table data\\r\\nlet tableData = instance.root.getComponent('lineEntry').dataValue || [];\\r\\nlet valid = true;\\r\\n\\r\\n\\/\\/ Get the panel component (replace 'yourPanelKey' with actual panel key)\\r\\nlet panelComp = instance.root.getComponent('lineEntryPanel');\\r\\n\\r\\n\\/\\/ Prepare object for the new row\\r\\nlet newRow = {};\\r\\n\\r\\n\\/\\/ Loop over each child component inside the panel\\r\\npanelComp.components.forEach(child => {\\r\\n  \\/\\/ Only process actual input fields (skip containers\\/panels\\/etc.)\\r\\n  if (child.type !== 'textfield' && child.type !== 'number' && child.type !== 'select' && child.type !== 'datetime') {\\r\\n    return;\\r\\n  }\\r\\n\\r\\n  \\/\\/ Validate the field\\r\\n  if (!child.checkValidity()) {\\r\\n    valid = false;\\r\\n    child.setPristine(false); \\/\\/ Show validation error\\r\\n  }\\r\\n\\r\\n  \\/\\/ Add to the new row object using its key\\r\\n  newRow[child.key] = child.getValue();\\r\\n});\\r\\n\\r\\n\\/\\/ Stop if any field is invalid\\r\\nif (!valid) return false;\\r\\n\\r\\n\\/\\/ Push new row into the edit grid\\r\\ntableData.push(newRow);\\r\\n\\r\\n\\/\\/ Update the edit grid's value\\r\\ninstance.root.getComponent('lineEntry').setValue(tableData);\\r\\n\\r\\n\\/\\/ Optionally clear the panel fields\\r\\n\\/\\/ panelComp.components.forEach(child => {\\r\\n\\/\\/   if (typeof child.setValue === 'function') {\\r\\n\\/\\/     child.setValue('');\\r\\n\\/\\/   }\\r\\n\\/\\/ });\\r\\n\",\n                        \"input\": true\n                    }\n                ]\n            },\n            {\n                \"label\": \"Line Entries\",\n                \"conditionalAddButton\": \"1==2\",\n                \"hideLabel\": true,\n                \"tableView\": false,\n                \"modal\": true,\n                \"validate\": {\n                    \"required\": true\n                },\n                \"validateWhenHidden\": false,\n                \"rowDrafts\": false,\n                \"key\": \"lineEntry\",\n                \"type\": \"editgrid\",\n                \"displayAsTable\": true,\n                \"input\": true,\n                \"components\": [\n                    {\n                        \"label\": \"Item No.\",\n                        \"labelPosition\": \"left-left\",\n                        \"placeholder\": \"Item No.\",\n                        \"applyMaskOn\": \"change\",\n                        \"tableView\": true,\n                        \"validate\": {\n                            \"required\": true\n                        },\n                        \"validateWhenHidden\": false,\n                        \"key\": \"item_num\",\n                        \"type\": \"textfield\",\n                        \"input\": true\n                    },\n                    {\n                        \"label\": \"Lot No.\",\n                        \"labelPosition\": \"left-left\",\n                        \"placeholder\": \"Lot No.\",\n                        \"applyMaskOn\": \"change\",\n                        \"tableView\": true,\n                        \"validate\": {\n                            \"required\": true\n                        },\n                        \"validateWhenHidden\": false,\n                        \"key\": \"lot_num\",\n                        \"type\": \"textfield\",\n                        \"input\": true\n                    },\n                    {\n                        \"label\": \"Qty\",\n                        \"labelPosition\": \"left-left\",\n                        \"placeholder\": \"Qty\",\n                        \"applyMaskOn\": \"change\",\n                        \"mask\": false,\n                        \"tableView\": false,\n                        \"delimiter\": false,\n                        \"requireDecimal\": false,\n                        \"inputFormat\": \"plain\",\n                        \"truncateMultipleSpaces\": false,\n                        \"validate\": {\n                            \"required\": true\n                        },\n                        \"validateWhenHidden\": false,\n                        \"key\": \"qty\",\n                        \"type\": \"number\",\n                        \"input\": true\n                    },\n                    {\n                        \"label\": \"UOM\",\n                        \"labelPosition\": \"left-left\",\n                        \"placeholder\": \"UOM\",\n                        \"applyMaskOn\": \"change\",\n                        \"tableView\": false,\n                        \"validate\": {\n                            \"required\": true\n                        },\n                        \"validateWhenHidden\": false,\n                        \"key\": \"uom\",\n                        \"type\": \"textfield\",\n                        \"input\": true\n                    },\n                    {\n                        \"label\": \"PO No.\",\n                        \"labelPosition\": \"left-left\",\n                        \"placeholder\": \"PO Number\",\n                        \"applyMaskOn\": \"change\",\n                        \"tableView\": true,\n                        \"validate\": {\n                            \"required\": true\n                        },\n                        \"validateWhenHidden\": false,\n                        \"key\": \"po_num\",\n                        \"type\": \"textfield\",\n                        \"input\": true\n                    },\n                    {\n                        \"label\": \"PO Line\",\n                        \"labelPosition\": \"left-left\",\n                        \"placeholder\": \"PO Line\",\n                        \"applyMaskOn\": \"change\",\n                        \"tableView\": true,\n                        \"validate\": {\n                            \"required\": true\n                        },\n                        \"validateWhenHidden\": false,\n                        \"key\": \"po_line\",\n                        \"type\": \"textfield\",\n                        \"input\": true\n                    },\n                    {\n                        \"label\": \"LPN\",\n                        \"labelPosition\": \"left-left\",\n                        \"placeholder\": \"LPN\",\n                        \"applyMaskOn\": \"change\",\n                        \"tableView\": false,\n                        \"validate\": {\n                            \"required\": true\n                        },\n                        \"validateWhenHidden\": false,\n                        \"key\": \"uom1\",\n                        \"type\": \"textfield\",\n                        \"input\": true\n                    }\n                ]\n            },\n            {\n                \"type\": \"button\",\n                \"label\": \"Submit Document\",\n                \"key\": \"submit\",\n                \"disableOnInvalid\": true,\n                \"input\": true,\n                \"tableView\": false\n            }\n        ]\n    }\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.855232, "xdebug_link": null, "collector": "log"}, {"message": "[02:50:32] LOG.debug: Extracted form fields {\n    \"form_id\": 4,\n    \"form_title\": \"PO Receipt\",\n    \"field_count\": 27,\n    \"field_keys\": [\n        \"whse_num\",\n        \"to_loc\",\n        \"lineEntryPanel.item_num\",\n        \"lineEntryPanel.lot_num\",\n        \"lineEntryPanel.qty\",\n        \"lineEntryPanel.uom\",\n        \"lineEntryPanel.po_num\",\n        \"lineEntryPanel.po_line\",\n        \"lineEntryPanel.uom1\",\n        \"lineEntryPanel.add\",\n        \"lineEntryPanel\",\n        \"lineEntry.item_num\",\n        \"lineEntry.lot_num\",\n        \"lineEntry.qty\",\n        \"lineEntry.uom\",\n        \"lineEntry.po_num\",\n        \"lineEntry.po_line\",\n        \"lineEntry.uom1\",\n        \"lineEntry\",\n        \"lineEntry.[].item_num\",\n        \"lineEntry.[].lot_num\",\n        \"lineEntry.[].qty\",\n        \"lineEntry.[].uom\",\n        \"lineEntry.[].po_num\",\n        \"lineEntry.[].po_line\",\n        \"lineEntry.[].uom1\",\n        \"submit\"\n    ]\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.855889, "xdebug_link": null, "collector": "log"}, {"message": "[02:50:32] LOG.debug: Extracted form fields {\n    \"form_id\": 4,\n    \"field_count\": 27,\n    \"fields\": [\n        \"whse_num\",\n        \"to_loc\",\n        \"lineEntryPanel.item_num\",\n        \"lineEntryPanel.lot_num\",\n        \"lineEntryPanel.qty\",\n        \"lineEntryPanel.uom\",\n        \"lineEntryPanel.po_num\",\n        \"lineEntryPanel.po_line\",\n        \"lineEntryPanel.uom1\",\n        \"lineEntryPanel.add\",\n        \"lineEntryPanel\",\n        \"lineEntry.item_num\",\n        \"lineEntry.lot_num\",\n        \"lineEntry.qty\",\n        \"lineEntry.uom\",\n        \"lineEntry.po_num\",\n        \"lineEntry.po_line\",\n        \"lineEntry.uom1\",\n        \"lineEntry\",\n        \"lineEntry.[].item_num\",\n        \"lineEntry.[].lot_num\",\n        \"lineEntry.[].qty\",\n        \"lineEntry.[].uom\",\n        \"lineEntry.[].po_num\",\n        \"lineEntry.[].po_line\",\n        \"lineEntry.[].uom1\",\n        \"submit\"\n    ]\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.856281, "xdebug_link": null, "collector": "log"}, {"message": "[02:50:32] LOG.warning: strlen(): Passing null to parameter #1 ($string) of type string is deprecated in D:\\Git Data Capture\\application\\app\\Http\\Controllers\\Admin\\FieldMappingConfigurationsController.php on line 304", "message_html": null, "is_string": false, "label": "warning", "time": **********.864156, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 3, "start": **********.627877, "end": **********.871914, "duration": 0.24403691291809082, "duration_str": "244ms", "measures": [{"label": "Booting", "start": **********.627877, "relative_start": 0, "end": **********.783546, "relative_end": **********.783546, "duration": 0.****************, "duration_str": "156ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.783555, "relative_start": 0.*****************, "end": **********.871919, "relative_end": 5.0067901611328125e-06, "duration": 0.****************, "duration_str": "88.36ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.803054, "relative_start": 0.*****************, "end": **********.807288, "relative_end": **********.807288, "duration": 0.004233837127685547, "duration_str": "4.23ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "21MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "9.x", "tooltip": {"Laravel Version": "9.52.20", "PHP Version": "8.1.15", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 3, "nb_statements": 2, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00645, "accumulated_duration_str": "6.45ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 15, "namespace": "middleware", "name": "auth", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}], "start": **********.82769, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php:168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "dc_local", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.835314, "duration": 0.00555, "duration_str": "5.55ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php:59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "dc_local", "explain": null, "start_percent": 0, "width_percent": 86.047}, {"sql": "select * from `forms` where `forms`.`id` = '4' and `forms`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/FieldMappingConfigurationsController.php", "file": "D:\\Git Data Capture\\application\\app\\Http\\Controllers\\Admin\\FieldMappingConfigurationsController.php", "line": 271}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.847046, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "FieldMappingConfigurationsController.php:271", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/FieldMappingConfigurationsController.php", "file": "D:\\Git Data Capture\\application\\app\\Http\\Controllers\\Admin\\FieldMappingConfigurationsController.php", "line": 271}, "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fapp%2FHttp%2FControllers%2FAdmin%2FFieldMappingConfigurationsController.php:271", "ajax": false, "filename": "FieldMappingConfigurationsController.php", "line": "271"}, "connection": "dc_local", "explain": null, "start_percent": 86.047, "width_percent": 13.953}]}, "models": {"data": {"App\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fapp%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Form": {"retrieved": 1, "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fapp%2FModels%2FForm.php:1", "ajax": false, "filename": "Form.php", "line": "?"}}}, "count": 2, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 2}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8001/admin/field-mapping-configurations/get-form-fields?_token=4RbyFb2oU1pjc4UNp6Cm...", "action_name": "admin.field-mapping-configurations.get-form-fields", "controller_action": "App\\Http\\Controllers\\Admin\\FieldMappingConfigurationsController@getFormFields", "uri": "GET admin/field-mapping-configurations/get-form-fields", "controller": "App\\Http\\Controllers\\Admin\\FieldMappingConfigurationsController@getFormFields<a href=\"vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fapp%2FHttp%2FControllers%2FAdmin%2FFieldMappingConfigurationsController.php:262\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "/admin", "file": "<a href=\"vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fapp%2FHttp%2FControllers%2FAdmin%2FFieldMappingConfigurationsController.php:262\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/FieldMappingConfigurationsController.php:262-318</a>", "middleware": "web, auth, user_type:admin", "duration": "247ms", "peak_memory": "22MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-380881534 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>form_id</span>\" => \"<span class=sf-dump-str>4</span>\"\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4RbyFb2oU1pjc4UNp6CmU470XASLVRa4SNAgSVgT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-380881534\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-568176052 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-568176052\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-371559256 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko/20100101 Firefox/142.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"63 characters\">http://localhost:8001/admin/field-mapping-configurations/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ikl0NkhzVzBjcklEeU5PTHZTenlyZVE9PSIsInZhbHVlIjoiREE2TGw3ZEJYYm9xTjQ1TUN1SnBNdUJUejAvUXdHNDV3YWI1b3V6Nm03akFTRTIrbmVKQmRpSE8wNnExUVRneHBFQk05bmVqQ3BoYVVKc0daV052bUJ1L3dZSWpkeHEydzJWNVpXd1VoMmlMNGVLaFB6U21CeExXd0NTZFVQRnEiLCJtYWMiOiI5ODg1NTgzZjcyZGFmNGYxZTM2MjA5NDdlYzYyZWE2ZDFjYjFkMGU4ODA0MDRiZDIyNGMyMDVmZDllNjFkOGEwIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImZLTjV0eEMvWjJXSmNqcXJHRnE0Q3c9PSIsInZhbHVlIjoiQW1qd2tjZVpSelQvOWdacm1lU0U4MjdncndFZVdJV05SUWFicXgvcW52WWdGNHRtRURHakJHRkxwOW1NeW1kRW9DQWdvdG1ucHQvQUVHYzk1MTREUE9mcEttZm1oREVnRCtzYWlFeDRqZlA4bkl3OXl3ak55Y0cxcEE4MlJOc3UiLCJtYWMiOiI4MGY2OGQyOTZkNzAwY2UyYzFkMTFhMmM1YjUyZTIzNTY5ODYwM2M2MzBjNGUyZjlhZDEzYjY0YmQ3ZjI1MDJjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">u=0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-371559256\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-389209966 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4RbyFb2oU1pjc4UNp6CmU470XASLVRa4SNAgSVgT</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3qWAZMAIgZlFqvqcm0sdt0aBVP4ghvpst3qVJvce</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-389209966\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-569672978 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 11 Sep 2025 02:50:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-569672978\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2065700332 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4RbyFb2oU1pjc4UNp6CmU470XASLVRa4SNAgSVgT</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"63 characters\">http://localhost:8001/admin/field-mapping-configurations/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1757556567</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2065700332\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8001/admin/field-mapping-configurations/get-form-fields?_token=4RbyFb2oU1pjc4UNp6Cm...", "action_name": "admin.field-mapping-configurations.get-form-fields", "controller_action": "App\\Http\\Controllers\\Admin\\FieldMappingConfigurationsController@getFormFields"}, "badge": null}}