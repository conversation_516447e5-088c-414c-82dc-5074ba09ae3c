<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FieldDefinition extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tenant_id',
        'field_key',
        'name',
        'label',
        'type',
        'options',
        'is_required',
        'validation_rules',
        'sort_order',
        'is_active',
        'description',
    ];

    /**
     * The "booting" method of the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Generate a field_key from the name if not provided
        static::saving(function ($model) {
            if (empty($model->field_key) && !empty($model->name)) {
                $model->field_key = strtolower(preg_replace('/[^a-zA-Z0-9_]/', '_', $model->name));
            }
        });
    }

    /**
     * Scope a query to only include system fields (no tenant).
     */
    public function scopeSystem($query)
    {
        return $query->whereNull('tenant_id');
    }

    /**
     * Scope a query to only include tenant-specific fields.
     */
    public function scopeForTenant($query, $tenantId = null)
    {
        return $query->where('tenant_id', $tenantId ?? auth()->user()->tenant_id);
    }

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'options' => 'array',
        'is_required' => 'boolean',
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];
}
