<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class QrProfileStoreRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
        // return $this->user()?->can('manage-qr-decoder') ?? false;
    }

    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255|unique:qr_decoder_profiles,name,' . ($this->route('profile')?->id ?? 'null'),
            'process_type' => 'required|string|max:255',
            'mode' => 'required|in:delimiter,fixed-length,gs1',
            'delimiter' => 'nullable|string|max:8',
            'sample_qr' => 'nullable|string',
            'vendor_num' => 'nullable|string|max:255',
            'is_active' => 'boolean',
            // 'fields' => 'array',
            // 'fields.*.segment' => 'nullable|integer|min:1',
            // 'fields.*.start' => 'nullable|integer|min:0',
            // 'fields.*.length' => 'nullable|integer|min:1',
            // 'fields.*.ai' => 'nullable|string|max:4',
            // 'fields.*.field_name' => 'required|string|max:255',
            // 'fields.*.transform_rule' => 'nullable|string|max:1024',
            // 'fields.*.optional' => 'boolean',
            // 'fields.*.order' => 'nullable|integer|min:0',
        ];
    }
}
