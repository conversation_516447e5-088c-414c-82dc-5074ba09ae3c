<?php

namespace App\Services;

use App\Models\FormSubmission;
use App\Models\FormSubmissionSync;
use App\Models\AuditLog;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AuditLoggerService
{
    /**
     * Log a form submission event
     */
    public function logSubmissionEvent(
        FormSubmission $submission,
        string $eventType,
        array $metadata = [],
        ?User $actor = null,
        string $actorType = AuditLog::ACTOR_SYSTEM
    ): AuditLog {
        return $this->createAuditLog([
            'form_submission_id' => $submission->id,
            'event_type' => $eventType,
            'event_description' => $this->generateEventDescription($eventType, $metadata),
            'actor_type' => $actorType,
            'actor_id' => $actor?->id ?? Auth::id(),
            'metadata' => $metadata,
        ]);
    }

    /**
     * Log a form submission sync event
     */
    public function logSyncEvent(
        FormSubmissionSync $sync,
        string $eventType,
        array $metadata = [],
        ?User $actor = null,
        string $actorType = AuditLog::ACTOR_SYSTEM
    ): AuditLog {
        return $this->createAuditLog([
            'form_submission_id' => $sync->form_submission_id,
            'form_submission_sync_id' => $sync->id,
            'event_type' => $eventType,
            'event_description' => $this->generateEventDescription($eventType, $metadata),
            'actor_type' => $actorType,
            'actor_id' => $actor?->id ?? Auth::id(),
            'metadata' => array_merge($metadata, [
                'integration_name' => $sync->getIntegrationName(),
                'target_system' => $sync->getTargetSystem(),
            ]),
        ]);
    }

    /**
     * Log a user action
     */
    public function logUserAction(
        User $user,
        string $action,
        array $context = [],
        ?FormSubmission $submission = null,
        ?FormSubmissionSync $sync = null
    ): AuditLog {
        return $this->createAuditLog([
            'form_submission_id' => $submission?->id,
            'form_submission_sync_id' => $sync?->id,
            'event_type' => $action,
            'event_description' => $this->generateEventDescription($action, $context),
            'actor_type' => $user->isAdmin() || $user->isSuperAdmin()
                ? AuditLog::ACTOR_ADMIN
                : AuditLog::ACTOR_USER,
            'actor_id' => $user->id,
            'metadata' => $context,
        ]);
    }

    /**
     * Log a status change with old and new values
     */
    public function logStatusChange(
        FormSubmission $submission,
        string $oldStatus,
        string $newStatus,
        ?User $actor = null,
        string $actorType = AuditLog::ACTOR_SYSTEM,
        array $additionalData = []
    ): AuditLog {
        return $this->createAuditLog([
            'form_submission_id' => $submission->id,
            'event_type' => AuditLog::EVENT_STATUS_UPDATED,
            'event_description' => "Status changed from '{$oldStatus}' to '{$newStatus}'",
            'actor_type' => $actorType,
            'actor_id' => $actor?->id ?? Auth::id(),
            'old_values' => ['status' => $oldStatus],
            'new_values' => ['status' => $newStatus],
            'metadata' => $additionalData,
        ]);
    }

    /**
     * Log a sync status change with old and new values
     */
    public function logSyncStatusChange(
        FormSubmissionSync $sync,
        string $oldStatus,
        string $newStatus,
        ?User $actor = null,
        string $actorType = AuditLog::ACTOR_SYSTEM,
        array $additionalData = []
    ): AuditLog {
        return $this->createAuditLog([
            'form_submission_id' => $sync->form_submission_id,
            'form_submission_sync_id' => $sync->id,
            'event_type' => AuditLog::EVENT_STATUS_UPDATED,
            'event_description' => "Sync status changed from '{$oldStatus}' to '{$newStatus}' for {$sync->getIntegrationName()}",
            'actor_type' => $actorType,
            'actor_id' => $actor?->id ?? Auth::id(),
            'old_values' => ['status' => $oldStatus],
            'new_values' => ['status' => $newStatus],
            'metadata' => array_merge($additionalData, [
                'integration_name' => $sync->getIntegrationName(),
                'target_system' => $sync->getTargetSystem(),
            ]),
        ]);
    }

    /**
     * Log data export event
     */
    public function logDataExport(
        User $user,
        string $exportType,
        array $filters = [],
        int $recordCount = 0,
        ?FormSubmission $submission = null
    ): AuditLog {
        return $this->createAuditLog([
            'form_submission_id' => $submission?->id,
            'event_type' => AuditLog::EVENT_DATA_EXPORTED,
            'event_description' => "Data exported: {$exportType} ({$recordCount} records)",
            'actor_type' => $user->isAdmin() || $user->isSuperAdmin()
                ? AuditLog::ACTOR_ADMIN
                : AuditLog::ACTOR_USER,
            'actor_id' => $user->id,
            'metadata' => [
                'export_type' => $exportType,
                'filters' => $filters,
                'record_count' => $recordCount,
            ],
        ]);
    }

    /**
     * Log manual retry event
     */
    public function logManualRetry(
        User $user,
        FormSubmissionSync $sync,
        string $reason = null
    ): AuditLog {
        return $this->createAuditLog([
            'form_submission_id' => $sync->form_submission_id,
            'form_submission_sync_id' => $sync->id,
            'event_type' => AuditLog::EVENT_MANUAL_RETRY,
            'event_description' => "Manual retry initiated for {$sync->getIntegrationName()}" .
                ($reason ? " - Reason: {$reason}" : ''),
            'actor_type' => $user->isAdmin() || $user->isSuperAdmin()
                ? AuditLog::ACTOR_ADMIN
                : AuditLog::ACTOR_USER,
            'actor_id' => $user->id,
            'metadata' => [
                'reason' => $reason,
                'integration_name' => $sync->getIntegrationName(),
                'target_system' => $sync->getTargetSystem(),
                'previous_retry_count' => $sync->retry_count,
            ],
        ]);
    }

    /**
     * Create audit log entry with request context
     */
    protected function createAuditLog(array $data): AuditLog
    {
        // Add request context if available
        if (app()->bound('request')) {
            $request = app('request');
            if ($request instanceof Request) {
                $data['ip_address'] = $request->ip();
                $data['user_agent'] = $request->userAgent();
            }
        }

        return AuditLog::create($data);
    }

    /**
     * Generate human-readable event description
     */
    protected function generateEventDescription(string $eventType, array $metadata = []): string
    {
        return match ($eventType) {
            AuditLog::EVENT_SUBMISSION_CREATED =>
                'Form submission created' .
                (isset($metadata['form_title']) ? " for '{$metadata['form_title']}'" : ''),

            AuditLog::EVENT_SUBMISSION_VALIDATED =>
                'Form submission validated and processing started',

            AuditLog::EVENT_SYNC_STARTED =>
                'Synchronization started' .
                (isset($metadata['integration_name']) ? " for {$metadata['integration_name']}" : '') .
                (isset($metadata['retry_count']) && $metadata['retry_count'] > 0 ? " (retry #{$metadata['retry_count']})" : ''),

            AuditLog::EVENT_SYNC_COMPLETED =>
                'Synchronization completed successfully' .
                (isset($metadata['integration_name']) ? " for {$metadata['integration_name']}" : '') .
                (isset($metadata['processing_time_ms']) ? " in {$metadata['processing_time_ms']}ms" : ''),

            AuditLog::EVENT_SYNC_FAILED =>
                'Synchronization failed' .
                (isset($metadata['integration_name']) ? " for {$metadata['integration_name']}" : '') .
                (isset($metadata['processing_time_ms']) ? " after {$metadata['processing_time_ms']}ms" : '') .
                (isset($metadata['exception_message']) ? " - {$metadata['exception_message']}" : ''),

            AuditLog::EVENT_SYNC_RETRIED =>
                'Synchronization scheduled for retry' .
                (isset($metadata['retry_count']) ? " (attempt #{$metadata['retry_count']})" : '') .
                (isset($metadata['next_retry_at']) ? " at {$metadata['next_retry_at']}" : ''),

            AuditLog::EVENT_STATUS_UPDATED =>
                'Status updated' .
                (isset($metadata['old_status'], $metadata['new_status'])
                    ? " from '{$metadata['old_status']}' to '{$metadata['new_status']}'"
                    : ''),

            AuditLog::EVENT_ERROR_RESOLVED =>
                'Error marked as resolved' .
                (isset($metadata['error_message']) ? " - {$metadata['error_message']}" : ''),

            AuditLog::EVENT_MANUAL_RETRY =>
                'Manual retry initiated' .
                (isset($metadata['reason']) ? " - Reason: {$metadata['reason']}" : ''),

            AuditLog::EVENT_DATA_EXPORTED =>
                'Data exported' .
                (isset($metadata['export_type']) ? " ({$metadata['export_type']})" : '') .
                (isset($metadata['record_count']) ? " - {$metadata['record_count']} records" : ''),

            default => ucwords(str_replace('_', ' ', $eventType))
        };
    }

    /**
     * Get audit trail for a submission
     */
    public function getSubmissionAuditTrail(FormSubmission $submission): \Illuminate\Database\Eloquent\Collection
    {
        return AuditLog::where('form_submission_id', $submission->id)
            ->with(['actor', 'formSubmissionSync'])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get audit trail for a sync
     */
    public function getSyncAuditTrail(FormSubmissionSync $sync): \Illuminate\Database\Eloquent\Collection
    {
        return AuditLog::where('form_submission_sync_id', $sync->id)
            ->with(['actor'])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get recent audit logs
     */
    public function getRecentAuditLogs(int $hours = 24, int $limit = 100): \Illuminate\Database\Eloquent\Collection
    {
        return AuditLog::with(['actor', 'formSubmission', 'formSubmissionSync'])
            ->where('created_at', '>=', now()->subHours($hours))
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }
}
