<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ErrorLog;
use App\Models\FormSubmission;
use App\Services\ErrorLoggerService;
use App\Services\AuditLoggerService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Yajra\DataTables\Facades\DataTables;

class ErrorLogController extends Controller
{
    protected ErrorLoggerService $errorLogger;
    protected AuditLoggerService $auditLogger;

    public function __construct(
        ErrorLoggerService $errorLogger,
        AuditLoggerService $auditLogger
    ) {
        $this->errorLogger = $errorLogger;
        $this->auditLogger = $auditLogger;
    }

    /**
     * Display a listing of error logs
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $query = ErrorLog::with([
                'formSubmission.form',
                'formSubmissionSync.formIntegrationSetting',
                'resolvedBy'
            ])->orderBy('created_at', 'desc');

            // Apply filters
            if ($request->has('error_level') && $request->error_level !== '') {
                $query->where('error_level', $request->error_level);
            }

            if ($request->has('error_type') && $request->error_type !== '') {
                $query->where('error_type', $request->error_type);
            }

            if ($request->has('resolved') && $request->resolved !== '') {
                $query->where('resolved', $request->resolved === 'true');
            }

            if ($request->has('date_from') && $request->date_from !== '') {
                $query->where('created_at', '>=', $request->date_from);
            }

            if ($request->has('date_to') && $request->date_to !== '') {
                $query->where('created_at', '<=', $request->date_to . ' 23:59:59');
            }

            if ($request->has('submission_uuid') && $request->submission_uuid !== '') {
                $query->whereHas('formSubmission', function ($q) use ($request) {
                    $q->where('uuid', 'like', '%' . $request->submission_uuid . '%');
                });
            }

            return DataTables::of($query)
                ->addColumn('error_level_badge', function ($row) {
                    $color = $row->getErrorLevelColor();
                    $icon = match ($row->error_level) {
                        'critical' => 'fas fa-exclamation-triangle',
                        'error' => 'fas fa-times-circle',
                        'warning' => 'fas fa-exclamation-circle',
                        'info' => 'fas fa-info-circle',
                        default => 'fas fa-question-circle'
                    };
                    return "<span class=\"badge badge-{$color}\">
                        <i class=\"{$icon}\"></i> {$row->getErrorLevelLabel()}
                    </span>";
                })
                ->addColumn('error_type_badge', function ($row) {
                    return "<span class=\"badge badge-secondary\">{$row->getErrorTypeLabel()}</span>";
                })
                ->addColumn('submission_info', function ($row) {
                    if ($row->formSubmission) {
                        $formTitle = $row->formSubmission->form->title ?? 'Unknown Form';
                        $submissionUuid = substr($row->formSubmission->uuid, 0, 8) . '...';
                        return "<strong>{$formTitle}</strong><br><small>{$submissionUuid}</small>";
                    }
                    return '<span class="text-muted">N/A</span>';
                })
                ->addColumn('integration_info', function ($row) {
                    if ($row->formSubmissionSync && $row->formSubmissionSync->formIntegrationSetting) {
                        $integrationName = $row->formSubmissionSync->formIntegrationSetting->name;
                        $targetSystem = $row->formSubmissionSync->getTargetSystem();
                        return "<strong>{$integrationName}</strong><br><small>{$targetSystem}</small>";
                    }
                    return '<span class="text-muted">N/A</span>';
                })
                ->addColumn('resolved_status', function ($row) {
                    if ($row->resolved) {
                        $resolverName = $row->getResolverName();
                        $resolvedAt = $row->resolved_at->format('Y-m-d H:i');
                        return "<span class=\"badge badge-success\">Resolved</span><br>
                                <small>by {$resolverName}<br>at {$resolvedAt}</small>";
                    }
                    return '<span class="badge badge-danger">Unresolved</span>';
                })
                ->addColumn('action', function ($row) {
                    $actions = '<div class="btn-group" role="group">';
                    $actions .= '<button type="button" class="btn btn-sm btn-info view-details" data-id="' . $row->id . '">
                        <i class="fas fa-eye"></i> Details
                    </button>';

                    if (!$row->resolved) {
                        $actions .= '<button type="button" class="btn btn-sm btn-success resolve-error" data-uuid="' . $row->uuid . '">
                            <i class="fas fa-check"></i> Resolve
                        </button>';
                    }

                    $actions .= '</div>';
                    return $actions;
                })
                ->editColumn('created_at', function ($row) {
                    return $row->created_at->format('Y-m-d H:i:s');
                })
                ->editColumn('error_message', function ($row) {
                    return strlen($row->error_message) > 60
                        ? substr($row->error_message, 0, 60) . '...'
                        : $row->error_message;
                })
                ->rawColumns(['error_level_badge', 'error_type_badge', 'submission_info', 'integration_info', 'resolved_status', 'action'])
                ->make(true);
        }

        // Get filter options
        $errorLevels = [
            ['value' => 'critical', 'label' => 'Critical'],
            ['value' => 'error', 'label' => 'Error'],
            ['value' => 'warning', 'label' => 'Warning'],
            ['value' => 'info', 'label' => 'Info'],
        ];

        $errorTypes = ErrorLog::select('error_type')
            ->distinct()
            ->orderBy('error_type')
            ->pluck('error_type')
            ->map(function ($errorType) {
                return [
                    'value' => $errorType,
                    'label' => ucwords(str_replace('_', ' ', $errorType))
                ];
            });

        return view('admin.error-logs.index', compact('errorLevels', 'errorTypes'));
    }

    /**
     * Show error log details
     */
    public function show(int $id): JsonResponse
    {
        try {
            $errorLog = ErrorLog::with([
                'formSubmission.form',
                'formSubmissionSync.formIntegrationSetting.endpointConfiguration',
                'resolvedBy'
            ])->findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $errorLog->id,
                    'uuid' => $errorLog->uuid,
                    'error_level' => $errorLog->error_level,
                    'error_level_label' => $errorLog->getErrorLevelLabel(),
                    'error_type' => $errorLog->error_type,
                    'error_type_label' => $errorLog->getErrorTypeLabel(),
                    'error_message' => $errorLog->error_message,
                    'error_code' => $errorLog->error_code,
                    'stack_trace' => $errorLog->stack_trace,
                    'context_data' => $errorLog->context_data,
                    'resolved' => $errorLog->resolved,
                    'resolved_at' => $errorLog->resolved_at?->format('Y-m-d H:i:s'),
                    'resolver_name' => $errorLog->getResolverName(),
                    'resolution_notes' => $errorLog->resolution_notes,
                    'created_at' => $errorLog->created_at->format('Y-m-d H:i:s'),
                    'form_submission' => $errorLog->formSubmission ? [
                        'uuid' => $errorLog->formSubmission->uuid,
                        'status' => $errorLog->formSubmission->status,
                        'form_title' => $errorLog->formSubmission->form->title ?? 'Unknown',
                        'submitted_at' => $errorLog->formSubmission->submitted_at->format('Y-m-d H:i:s'),
                    ] : null,
                    'integration' => $errorLog->formSubmissionSync ? [
                        'name' => $errorLog->formSubmissionSync->formIntegrationSetting->name ?? 'Unknown',
                        'target_system' => $errorLog->formSubmissionSync->getTargetSystem(),
                        'status' => $errorLog->formSubmissionSync->status,
                    ] : null,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error log not found',
            ], 404);
        }
    }

    /**
     * Resolve an error
     */
    public function resolve(Request $request, string $uuid): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'resolution_notes' => 'required|string|max:1000',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors(),
                ], 422);
            }

            $user = Auth::user();
            $errorLog = $this->errorLogger->resolveError(
                $uuid,
                $user,
                $request->input('resolution_notes')
            );

            return response()->json([
                'success' => true,
                'message' => 'Error resolved successfully',
                'data' => [
                    'resolved_at' => $errorLog->resolved_at->format('Y-m-d H:i:s'),
                    'resolver_name' => $errorLog->getResolverName(),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to resolve error: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get error log statistics
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            $hours = $request->input('hours', 24);
            $stats = $this->errorLogger->getErrorStatistics($hours);

            // Critical unresolved errors
            $criticalErrors = $this->errorLogger->getUnresolvedCriticalErrors();

            // Error trends (last 7 days)
            $trends = [];
            for ($i = 6; $i >= 0; $i--) {
                $date = now()->subDays($i);
                $dayStart = $date->startOfDay();
                $dayEnd = $date->endOfDay();

                $trends[] = [
                    'date' => $date->format('Y-m-d'),
                    'total' => ErrorLog::whereBetween('created_at', [$dayStart, $dayEnd])->count(),
                    'critical' => ErrorLog::whereBetween('created_at', [$dayStart, $dayEnd])
                        ->where('error_level', 'critical')->count(),
                    'resolved' => ErrorLog::whereBetween('created_at', [$dayStart, $dayEnd])
                        ->where('resolved', true)->count(),
                ];
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'statistics' => $stats,
                    'critical_errors' => $criticalErrors->map(function ($error) {
                        return [
                            'uuid' => $error->uuid,
                            'error_message' => $error->error_message,
                            'created_at' => $error->created_at->diffForHumans(),
                            'form_title' => $error->formSubmission->form->title ?? 'Unknown',
                        ];
                    }),
                    'trends' => $trends,
                    'period_hours' => $hours,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve statistics: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Export error logs
     */
    public function export(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'format' => 'required|in:csv,json',
                'error_level' => 'sometimes|string',
                'error_type' => 'sometimes|string',
                'resolved' => 'sometimes|boolean',
                'date_from' => 'sometimes|date',
                'date_to' => 'sometimes|date',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors(),
                ], 422);
            }

            $query = ErrorLog::with([
                'formSubmission.form',
                'formSubmissionSync.formIntegrationSetting',
                'resolvedBy'
            ]);

            // Apply filters
            if ($request->has('error_level')) {
                $query->where('error_level', $request->input('error_level'));
            }

            if ($request->has('error_type')) {
                $query->where('error_type', $request->input('error_type'));
            }

            if ($request->has('resolved')) {
                $query->where('resolved', $request->input('resolved'));
            }

            if ($request->has('date_from')) {
                $query->where('created_at', '>=', $request->input('date_from'));
            }

            if ($request->has('date_to')) {
                $query->where('created_at', '<=', $request->input('date_to') . ' 23:59:59');
            }

            $errorLogs = $query->orderBy('created_at', 'desc')->get();

            // Log the export action
            $this->auditLogger->logDataExport(
                Auth::user(),
                'error_logs_' . $request->input('format'),
                $request->only(['error_level', 'error_type', 'resolved', 'date_from', 'date_to']),
                $errorLogs->count()
            );

            $format = $request->input('format');
            $filename = 'error_logs_' . now()->format('Y-m-d_H-i-s') . '.' . $format;

            if ($format === 'csv') {
                return $this->exportCsv($errorLogs, $filename);
            } else {
                return $this->exportJson($errorLogs, $filename);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Export failed: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Export error logs as CSV
     */
    protected function exportCsv($errorLogs, $filename)
    {
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function () use ($errorLogs) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'UUID', 'Error Level', 'Error Type', 'Error Message', 'Form Title',
                'Submission UUID', 'Integration Name', 'Resolved', 'Resolver', 'Created At'
            ]);

            foreach ($errorLogs as $log) {
                fputcsv($file, [
                    $log->uuid,
                    $log->getErrorLevelLabel(),
                    $log->getErrorTypeLabel(),
                    $log->error_message,
                    $log->formSubmission->form->title ?? 'N/A',
                    $log->formSubmission->uuid ?? 'N/A',
                    $log->formSubmissionSync->formIntegrationSetting->name ?? 'N/A',
                    $log->resolved ? 'Yes' : 'No',
                    $log->getResolverName(),
                    $log->created_at->format('Y-m-d H:i:s'),
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Export error logs as JSON
     */
    protected function exportJson($errorLogs, $filename)
    {
        $data = $errorLogs->map(function ($log) {
            return [
                'uuid' => $log->uuid,
                'error_level' => $log->error_level,
                'error_type' => $log->error_type,
                'error_message' => $log->error_message,
                'error_code' => $log->error_code,
                'stack_trace' => $log->stack_trace,
                'context_data' => $log->context_data,
                'resolved' => $log->resolved,
                'resolved_at' => $log->resolved_at,
                'resolution_notes' => $log->resolution_notes,
                'created_at' => $log->created_at,
                'form_submission' => $log->formSubmission ? [
                    'uuid' => $log->formSubmission->uuid,
                    'form_title' => $log->formSubmission->form->title,
                ] : null,
                'integration' => $log->formSubmissionSync ? [
                    'name' => $log->formSubmissionSync->formIntegrationSetting->name,
                    'target_system' => $log->formSubmissionSync->getTargetSystem(),
                ] : null,
                'resolver' => $log->resolvedBy ? [
                    'name' => $log->resolvedBy->name,
                    'email' => $log->resolvedBy->email,
                ] : null,
            ];
        });

        $headers = [
            'Content-Type' => 'application/json',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        return response()->json($data, 200, $headers);
    }
}
