@extends('layouts.app')

@section('title', 'My Submission History')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-history"></i>
                        My Submission History
                    </h3>
                    <div class="card-tools">
                        <a href="{{ route('forms.index') }}" class="btn btn-sm btn-primary">
                            <i class="fas fa-plus"></i> Submit New Form
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <form method="GET" action="{{ route('forms.history') }}" class="d-flex gap-2">
                                <select name="status" class="form-select">
                                    <option value="">All Statuses</option>
                                    <option value="pending" {{ request('status') === 'pending' ? 'selected' : '' }}>Pending</option>
                                    <option value="processing" {{ request('status') === 'processing' ? 'selected' : '' }}>Processing</option>
                                    <option value="completed" {{ request('status') === 'completed' ? 'selected' : '' }}>Completed</option>
                                    <option value="failed" {{ request('status') === 'failed' ? 'selected' : '' }}>Failed</option>
                                    <option value="partial" {{ request('status') === 'partial' ? 'selected' : '' }}>Partial</option>
                                </select>
                                <select name="form_id" class="form-select">
                                    <option value="">All Forms</option>
                                    @foreach($userForms as $form)
                                        <option value="{{ $form->id }}" {{ request('form_id') == $form->id ? 'selected' : '' }}>
                                            {{ $form->title }}
                                        </option>
                                    @endforeach
                                </select>
                                <button type="submit" class="btn btn-outline-primary">
                                    <i class="fas fa-filter"></i> Filter
                                </button>
                                @if(request()->hasAny(['status', 'form_id']))
                                    <a href="{{ route('forms.history') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times"></i> Clear
                                    </a>
                                @endif
                            </form>
                        </div>
                    </div>

                    @if($submissions->isEmpty())
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">No Submissions Found</h4>
                            @if(request()->hasAny(['status', 'form_id']))
                                <p class="text-muted">No submissions match your current filters.</p>
                                <a href="{{ route('forms.history') }}" class="btn btn-outline-primary">
                                    <i class="fas fa-times"></i> Clear Filters
                                </a>
                            @else
                                <p class="text-muted">You haven't submitted any forms yet.</p>
                                <a href="{{ route('forms.index') }}" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> Submit Your First Form
                                </a>
                            @endif
                        </div>
                    @else
                        <!-- Submissions Table -->
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Form</th>
                                        <th>Status</th>
                                        <th>Progress</th>
                                        <th>Submitted</th>
                                        <th>Completed</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($submissions as $submission)
                                        <tr>
                                            <td>
                                                <div>
                                                    <strong>{{ $submission->form->title }}</strong>
                                                    <br>
                                                    <small class="text-muted">
                                                        <i class="fas fa-fingerprint"></i>
                                                        {{ Str::limit($submission->uuid, 8) }}...
                                                    </small>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge badge-{{ $submission->getStatusColor() }}">
                                                    {{ $submission->getStatusLabel() }}
                                                </span>
                                            </td>
                                            <td>
                                                <div class="progress" style="height: 20px; min-width: 100px;">
                                                    <div class="progress-bar 
                                                        @if($submission->getProgressPercentage() == 100) bg-success
                                                        @elseif($submission->getProgressPercentage() > 0) bg-warning
                                                        @else bg-secondary
                                                        @endif" 
                                                        role="progressbar" 
                                                        style="width: {{ $submission->getProgressPercentage() }}%">
                                                        {{ $submission->getProgressPercentage() }}%
                                                    </div>
                                                </div>
                                                <small class="text-muted">
                                                    {{ $submission->successful_integrations }}/{{ $submission->total_integrations }} integrations
                                                </small>
                                            </td>
                                            <td>
                                                <div>
                                                    {{ $submission->submitted_at->format('M d, Y') }}
                                                    <br>
                                                    <small class="text-muted">{{ $submission->submitted_at->format('g:i A') }}</small>
                                                </div>
                                            </td>
                                            <td>
                                                @if($submission->completed_at)
                                                    <div>
                                                        {{ $submission->completed_at->format('M d, Y') }}
                                                        <br>
                                                        <small class="text-muted">{{ $submission->completed_at->format('g:i A') }}</small>
                                                    </div>
                                                @else
                                                    <span class="text-muted">-</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('forms.status', $submission->uuid) }}" 
                                                       class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i> View
                                                    </a>
                                                    @if($submission->failed_integrations > 0)
                                                        <button type="button" 
                                                                class="btn btn-sm btn-outline-warning retry-btn" 
                                                                data-uuid="{{ $submission->uuid }}">
                                                            <i class="fas fa-redo"></i> Retry
                                                        </button>
                                                    @endif
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center">
                            {{ $submissions->appends(request()->query())->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Retry Modal -->
<div class="modal fade" id="retryModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-redo text-warning"></i>
                    Retry Failed Integrations
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <div id="retry-loading" style="display: none;">
                    <div class="spinner-border text-warning mb-3" role="status">
                        <span class="visually-hidden">Retrying...</span>
                    </div>
                    <p>Retrying failed integrations...</p>
                </div>
                <div id="retry-content">
                    <p>This will retry all failed integrations for the selected submission.</p>
                    <p class="text-muted">Are you sure you want to continue?</p>
                </div>
            </div>
            <div class="modal-footer" id="retry-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning" onclick="confirmRetry()">
                    <i class="fas fa-redo"></i> Retry Now
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.progress {
    border-radius: 10px;
}

.progress-bar {
    border-radius: 10px;
}

.table td {
    vertical-align: middle;
}

.btn-group .btn {
    border-radius: 0.25rem;
    margin-right: 2px;
}
</style>
@endpush

@push('scripts')
<script>
const csrfToken = "{{ csrf_token() }}";
let currentRetryUuid = null;

document.addEventListener('DOMContentLoaded', function() {
    // Handle retry button clicks
    document.querySelectorAll('.retry-btn').forEach(button => {
        button.addEventListener('click', function() {
            currentRetryUuid = this.dataset.uuid;
            const modal = new bootstrap.Modal(document.getElementById('retryModal'));
            modal.show();
        });
    });
});

function confirmRetry() {
    if (!currentRetryUuid) return;

    // Show loading state
    document.getElementById('retry-content').style.display = 'none';
    document.getElementById('retry-footer').style.display = 'none';
    document.getElementById('retry-loading').style.display = 'block';

    // Make retry request
    const retryUrl = `/forms/${currentRetryUuid}/retry`;
    
    fetch(retryUrl, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': csrfToken,
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        const modal = bootstrap.Modal.getInstance(document.getElementById('retryModal'));
        modal.hide();

        if (data.success) {
            showAlert('success', 'Retry initiated successfully. Please refresh the page to see updated status.');
            
            // Optionally refresh the page after a delay
            setTimeout(() => {
                location.reload();
            }, 2000);
        } else {
            showAlert('error', data.message || 'Failed to initiate retry');
        }
    })
    .catch(error => {
        const modal = bootstrap.Modal.getInstance(document.getElementById('retryModal'));
        modal.hide();
        console.error('Retry error:', error);
        showAlert('error', 'An error occurred while retrying. Please try again.');
    })
    .finally(() => {
        // Reset modal state
        document.getElementById('retry-content').style.display = 'block';
        document.getElementById('retry-footer').style.display = 'block';
        document.getElementById('retry-loading').style.display = 'none';
        currentRetryUuid = null;
    });
}

function showAlert(type, message) {
    const alertClass = type === 'error' ? 'alert-danger' : 'alert-success';
    const icon = type === 'error' ? 'fas fa-exclamation-circle' : 'fas fa-check-circle';
    
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert ${alertClass} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        <i class="${icon}"></i> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Insert at top of card body
    const cardBody = document.querySelector('.card-body');
    cardBody.insertBefore(alertDiv, cardBody.firstChild);

    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
@endpush
