<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Rename the table
        Schema::rename('endpoint_configurations', 'integration_configurations');
        
        // Update any foreign key constraints if they exist
        Schema::table('form_integration_settings', function (Blueprint $table) {
            $table->dropForeign(['endpoint_configuration_id']);
            $table->renameColumn('endpoint_configuration_id', 'integration_configuration_id');
            $table->foreign('integration_configuration_id')
                  ->references('id')
                  ->on('integration_configurations')
                  ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Revert foreign key changes
        Schema::table('form_integration_settings', function (Blueprint $table) {
            $table->dropForeign(['integration_configuration_id']);
            $table->renameColumn('integration_configuration_id', 'endpoint_configuration_id');
            $table->foreign('endpoint_configuration_id')
                  ->references('id')
                  ->on('endpoint_configurations')
                  ->onDelete('cascade');
        });
        
        // Revert table name
        Schema::rename('integration_configurations', 'endpoint_configurations');
    }
};
