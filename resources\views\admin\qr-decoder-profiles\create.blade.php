@extends('layouts.admin')
@section('pageTitle', trans('global.create') . ' ' . trans('cruds.qr-decoder-profiles.title_singular'))

@section('content')
    <div class="card card-default">
        <div class="card-header separator">
            <div class="card-title mainheading">
                <h4>{{ trans('global.create') }} {{ trans('cruds.qr-decoder-profiles.title_singular') }}</h4>
            </div>
            <div class="card-controls">
                <ul>
                    <li>
                        <x-cancel-button :url="route('admin.qr-decoder-profiles.index')" :text="trans('global.back_to_list')" />
                    </li>
                </ul>
            </div>
        </div>

        <div class="card-block">
            @if ($errors->any())
                <div class="alert alert-danger">
                    <ul>
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <form method="POST" action="{{ route('admin.qr-decoder-profiles.store') }}">
                @csrf

                <div class="form-group row">
                    <label for="name" class="col-md-2 col-form-label">{{ trans('cruds.fields.name') }} <span
                            class="text-danger">*</span></label>
                    <div class="col-md-4">
                        <input type="text" class="form-control" id="name" name="name" value="{{ old('name') }}"
                            required>
                    </div>
                    <label for="is_active" class="col-md-2 col-form-label">{{ trans('cruds.fields.status') }}</label>
                    <div class="col-md-4">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="is_active" name="is_active" value="1"
                                {{ old('is_active', true) ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_active">
                                {{ trans('cruds.fields.active') }}
                            </label>
                        </div>
                    </div>

                </div>

                <div class="form-group row">
                    <label for="type" class="col-md-2 col-form-label">{{ trans('cruds.fields.process_type') }}</label>
                    <div class="col-md-4">
                        <select class="form-control {{ $errors->has('process_type') ? 'is-invalid' : '' }}"
                            name="process_type" id="process_type">
                            <option value="">{{ trans('global.pleaseSelect') }}</option>
                            @foreach (\App\Models\IntegrationConfiguration::getProcessTypeOptions() as $type)
                                <option value="{{ $type }}">{{ $type }}</option>
                            @endforeach

                        </select>
                        @if ($errors->has('process_type'))
                            <div class="invalid-feedback">
                                {{ $errors->first('process_type') }}
                            </div>
                        @endif

                    </div>
                    <label for="type" class="col-md-2 col-form-label">{{ trans('cruds.fields.mode') }}</label>
                    <div class="col-md-4">
                        <select class="form-control {{ $errors->has('mode') ? 'is-invalid' : '' }}" name="mode"
                            id="mode">
                            <option value="">{{ trans('global.pleaseSelect') }}</option>
                            @foreach (\App\Models\QrDecoderProfile::$modes as $key => $type)
                                <option value="{{ $key }}">{{ $type }}</option>
                            @endforeach

                        </select>
                        @if ($errors->has('mode'))
                            <div class="invalid-feedback">
                                {{ $errors->first('mode') }}
                            </div>
                        @endif

                    </div>
                </div>
                <div class="form-group row">
                    <label for="name" class="col-md-2 col-form-label">{{ trans('cruds.fields.vendor_num') }}</label>
                    <div class="col-md-4">
                        <input type="text" class="form-control" id="vendor_num" name="vendor_num"
                            value="{{ old('vendor_num') }}">
                    </div>

                    <label for="name" class="col-md-2 col-form-label">{{ trans('cruds.fields.delimiter') }} </label>
                    <div class="col-md-4">
                        <input type="text" class="form-control" id="delimiter" name="delimiter"
                            value="{{ old('delimiter') }}">
                    </div>
                </div>
                <div class="form-group row">
                    <label for="name" class="col-md-2 col-form-label">{{ trans('cruds.fields.sample') }}
                    </label>
                    <div class="col-md-4">
                        <textarea type="text" class="form-control" id="sample_qr" name="sample_qr" value="{{ old('sample_qr') }}"></textarea>
                    </div>
                </div>



                <div class="form-group row">
                    <div class="col-md-10 offset-md-2 text-right">

                        <x-cancel-button :url="route('admin.qr-decoder-profiles.index')" :text="trans('global.cancel')" />
                        <x-save-button />
                    </div>
                </div>
            </form>
        </div>
    </div>
@endsection
