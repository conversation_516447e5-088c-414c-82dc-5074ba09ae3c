<?php

namespace Tests\Unit;

use App\Models\EndpointConfiguration;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class EndpointConfigurationTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_has_fillable_fields()
    {
        $fillable = [
            'name',
            'external_system',
            'integration_method',
            'process_type',
            'endpoint_url',
            'request_fields',
            'is_active',
            'created_by',
            'updated_by',
        ];

        $this->assertEquals($fillable, (new EndpointConfiguration())->getFillable());
    }

    /** @test */
    public function it_casts_fields_to_appropriate_types()
    {
        $casts = [
            'id' => 'int',
            'is_active' => 'boolean',
            'request_fields' => 'array',
            'deleted_at' => 'datetime',
        ];

        $this->assertEquals($casts, (new EndpointConfiguration())->getCasts());
    }

    /** @test */
    public function it_has_constants_for_external_systems()
    {
        $this->assertIsArray(EndpointConfiguration::EXTERNAL_SYSTEMS);
        $this->assertNotEmpty(EndpointConfiguration::EXTERNAL_SYSTEMS);
        $this->assertArrayHasKey('CSI_10', EndpointConfiguration::EXTERNAL_SYSTEMS);
        $this->assertArrayHasKey('SAP_B1', EndpointConfiguration::EXTERNAL_SYSTEMS);
    }

    /** @test */
    public function it_has_constants_for_integration_methods()
    {
        $this->assertIsArray(EndpointConfiguration::INTEGRATION_METHODS);
        $this->assertNotEmpty(EndpointConfiguration::INTEGRATION_METHODS);
        $this->assertContains('API', EndpointConfiguration::INTEGRATION_METHODS);
        $this->assertContains('File Transfer', EndpointConfiguration::INTEGRATION_METHODS);
        $this->assertContains('MQ', EndpointConfiguration::INTEGRATION_METHODS);
    }

    /** @test */
    public function it_has_constants_for_process_types()
    {
        $this->assertIsArray(EndpointConfiguration::PROCESS_TYPES);
        $this->assertNotEmpty(EndpointConfiguration::PROCESS_TYPES);
        $this->assertContains('PO Receipt', EndpointConfiguration::PROCESS_TYPES);
        $this->assertContains('Misc Issue', EndpointConfiguration::PROCESS_TYPES);
    }

    /** @test */
    public function it_can_be_created_with_valid_attributes()
    {
        $config = EndpointConfiguration::create([
            'name' => 'Test Integration',
            'external_system' => 'CSI_10',
            'integration_method' => 'API',
            'process_type' => 'PO Receipt',
            'endpoint_url' => 'https://api.example.com/endpoint',
            'request_fields' => ['field1' => 'value1'],
            'is_active' => true,
        ]);

        $this->assertDatabaseHas('endpoint_configurations', [
            'name' => 'Test Integration',
            'external_system' => 'CSI_10',
            'integration_method' => 'API',
            'process_type' => 'PO Receipt',
            'endpoint_url' => 'https://api.example.com/endpoint',
            'is_active' => true,
        ]);

        $this->assertIsArray($config->request_fields);
        $this->assertEquals('value1', $config->request_fields['field1']);
    }

    /** @test */
    public function it_has_scopes_for_filtering()
    {
        // Test active scope
        $activeConfig = EndpointConfiguration::factory()->create(['is_active' => true]);
        $inactiveConfig = EndpointConfiguration::factory()->create(['is_active' => false]);

        $this->assertCount(1, EndpointConfiguration::active()->get());
        $this->assertTrue(EndpointConfiguration::active()->first()->is($activeConfig));
    }
}
