<?php

namespace Database\Seeders;

use App\Models\FieldDefinition;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class FieldDefinitionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $fields = [
            [
                'field_key' => 'warehouse_number',
                'field_name' => 'Warehouse Number',
                'label' => 'Warehouse Number',
                'data_type' => 'string',
                'is_required' => true,
                'max_length' => 50,
                'default_value' => null,
                'validation_rules' => 'required|string|max:50',
                'sort_order' => 1,
                'is_active' => true,
                'description' => 'Warehouse identification number',
                'field_group' => 'warehouse_info',
            ],
            [
                'field_key' => 'to_location',
                'field_name' => 'To Location',
                'label' => 'To Location',
                'data_type' => 'string',
                'is_required' => true,
                'max_length' => 100,
                'default_value' => null,
                'validation_rules' => 'required|string|max:100',
                'sort_order' => 2,
                'is_active' => true,
                'description' => 'Destination location for the items',
                'field_group' => 'warehouse_info',
            ],
            [
                'field_key' => 'header_notes',
                'field_name' => 'Header Notes',
                'label' => 'Header Notes',
                'data_type' => 'text',
                'is_required' => false,
                'max_length' => 1000,
                'default_value' => null,
                'validation_rules' => 'nullable|string|max:1000',
                'sort_order' => 3,
                'is_active' => true,
                'description' => 'General notes for the entire transaction',
                'field_group' => 'notes',
            ],
            [
                'field_key' => 'item_number',
                'field_name' => 'Item Number',
                'data_type' => 'string',
                'is_required' => true,
                'max_length' => 50,
                'default_value' => null,
                'validation_rules' => 'required|string|max:50',
                'sort_order' => 4,
                'is_active' => true,
                'description' => 'Unique identifier for the item',
                'field_group' => 'item_info',
            ],
            [
                'field_key' => 'lot_number',
                'field_name' => 'Lot Number',
                'data_type' => 'string',
                'is_required' => true,
                'max_length' => 50,
                'default_value' => null,
                'validation_rules' => 'required|string|max:50',
                'sort_order' => 5,
                'is_active' => true,
                'description' => 'Lot or batch number of the item',
                'field_group' => 'item_info',
            ],
            [
                'field_key' => 'quantity',
                'field_name' => 'Quantity',
                'data_type' => 'decimal',
                'is_required' => true,
                'max_length' => null,
                'default_value' => null,
                'validation_rules' => 'required|numeric|min:0',
                'sort_order' => 6,
                'is_active' => true,
                'description' => 'Quantity of items',
                'field_group' => 'item_info',
            ],
            [
                'field_key' => 'uom',
                'field_name' => 'UOM',
                'data_type' => 'string',
                'is_required' => true,
                'max_length' => 20,
                'default_value' => 'EA',
                'validation_rules' => 'required|string|max:20',
                'sort_order' => 7,
                'is_active' => true,
                'description' => 'Unit of Measure',
                'field_group' => 'item_info',
            ],
            [
                'field_key' => 'po_number',
                'field_name' => 'PO Number',
                'data_type' => 'string',
                'is_required' => false,
                'max_length' => 50,
                'default_value' => null,
                'validation_rules' => 'nullable|string|max:50',
                'sort_order' => 8,
                'is_active' => true,
                'description' => 'Purchase Order number',
                'field_group' => 'purchase_info',
            ],
            [
                'field_key' => 'po_line',
                'field_name' => 'PO Line',
                'data_type' => 'string',
                'is_required' => false,
                'max_length' => 20,
                'default_value' => null,
                'validation_rules' => 'nullable|string|max:20',
                'sort_order' => 9,
                'is_active' => true,
                'description' => 'Purchase Order line number',
                'field_group' => 'purchase_info',
            ],
            [
                'field_key' => 'line_notes',
                'field_name' => 'Line Notes',
                'data_type' => 'text',
                'is_required' => false,
                'max_length' => 500,
                'default_value' => null,
                'validation_rules' => 'nullable|string|max:500',
                'sort_order' => 10,
                'is_active' => true,
                'description' => 'Notes specific to this line item',
                'field_group' => 'notes',
            ],
            [
                'field_key' => 'lpn',
                'field_name' => 'LPN',
                'data_type' => 'string',
                'is_required' => false,
                'max_length' => 50,
                'default_value' => null,
                'validation_rules' => 'nullable|string|max:50',
                'sort_order' => 11,
                'is_active' => true,
                'description' => 'License Plate Number',
                'field_group' => 'shipping_info',
            ],
            [
                'field_key' => 'vendor_do',
                'field_name' => 'Vendor DO',
                'data_type' => 'string',
                'is_required' => false,
                'max_length' => 50,
                'default_value' => null,
                'validation_rules' => 'nullable|string|max:50',
                'sort_order' => 12,
                'is_active' => true,
                'description' => 'Vendor Delivery Order number',
                'field_group' => 'purchase_info',
            ],
            [
                'field_key' => 'manufacturing_date',
                'field_name' => 'Manufacturing Date',
                'data_type' => 'date',
                'is_required' => false,
                'max_length' => null,
                'default_value' => null,
                'validation_rules' => 'nullable|date',
                'sort_order' => 13,
                'is_active' => true,
                'description' => 'Date when the item was manufactured',
                'field_group' => 'item_info',
            ],
            [
                'field_key' => 'expiry_date',
                'field_name' => 'Expiry Date',
                'data_type' => 'date',
                'is_required' => false,
                'max_length' => null,
                'default_value' => null,
                'validation_rules' => 'nullable|date|after_or_equal:today',
                'sort_order' => 14,
                'is_active' => true,
                'description' => 'Expiration date of the item',
                'field_group' => 'item_info',
            ]
        ];

        foreach ($fields as $field) {
            // Ensure tenant_id is set to null for global fields
            $field['tenant_id'] = null;
            
            // Create or update the field definition
            FieldDefinition::updateOrCreate(
                ['field_key' => $field['field_key'], 'tenant_id' => null],
                $field
            );
        }
    }
}