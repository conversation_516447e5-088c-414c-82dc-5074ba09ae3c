<?php

namespace App\Jobs;

use App\Models\FormSubmissionSync;
use App\Services\FormSyncEngineService;
use App\Services\ErrorLoggerService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SyncFormSubmissionJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected FormSubmissionSync $sync;

    /**
     * The number of times the job may be attempted.
     */
    public int $tries = 1; // We handle retries manually through the sync engine

    /**
     * The maximum number of seconds the job can run.
     */
    public int $timeout = 60;

    /**
     * Create a new job instance.
     */
    public function __construct(FormSubmissionSync $sync)
    {
        $this->sync = $sync;
        $this->onQueue('form_submissions');
    }

    /**
     * Execute the job.
     */
    public function handle(FormSyncEngineService $syncEngine): void
    {
        Log::info('Sync form submission job started', [
            'sync_uuid' => $this->sync->uuid,
            'submission_uuid' => $this->sync->formSubmission->uuid,
            'integration_name' => $this->sync->getIntegrationName(),
            'job_id' => $this->job->getJobId(),
        ]);

        try {
            $syncEngine->processSync($this->sync);

            Log::info('Sync form submission job completed', [
                'sync_uuid' => $this->sync->uuid,
                'submission_uuid' => $this->sync->formSubmission->uuid,
                'integration_name' => $this->sync->getIntegrationName(),
                'final_status' => $this->sync->fresh()->status,
                'job_id' => $this->job->getJobId(),
            ]);

        } catch (\Exception $e) {
            Log::error('Sync form submission job failed', [
                'sync_uuid' => $this->sync->uuid,
                'submission_uuid' => $this->sync->formSubmission->uuid,
                'integration_name' => $this->sync->getIntegrationName(),
                'error' => $e->getMessage(),
                'job_id' => $this->job->getJobId(),
            ]);

            // The sync engine handles error logging and retry logic
            // So we don't need to do additional error handling here
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Sync form submission job failed permanently', [
            'sync_uuid' => $this->sync->uuid,
            'submission_uuid' => $this->sync->formSubmission->uuid,
            'integration_name' => $this->sync->getIntegrationName(),
            'error' => $exception->getMessage(),
        ]);

        // Mark sync as failed if it's not already handled
        if ($this->sync->fresh()->status === \App\Models\FormSubmissionSync::STATUS_PROCESSING) {
            $this->sync->markAsFailed($exception->getMessage(), (string) $exception->getCode());
        }

        // Log the permanent failure
        app(ErrorLoggerService::class)->logSyncError(
            $this->sync,
            $exception,
            \App\Models\ErrorLog::LEVEL_CRITICAL,
            \App\Models\ErrorLog::TYPE_QUEUE_ERROR,
            [
                'permanent_failure' => true,
                'job_failure' => true,
            ]
        );
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        return [
            'form_sync',
            'sync:' . $this->sync->uuid,
            'submission:' . $this->sync->formSubmission->uuid,
            'integration:' . $this->sync->form_integration_setting_id,
        ];
    }
}
