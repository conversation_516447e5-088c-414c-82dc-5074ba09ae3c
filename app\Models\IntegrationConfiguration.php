<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class IntegrationConfiguration extends Model
{
    use HasFactory;

    protected $table = 'integration_configurations';

    // External System constants
    public const SYSTEM_CSI = 'csi_10';
    public const SYSTEM_SAP_B1 = 'sap_b1';
    public const SYSTEM_INFOR_WMS = 'infor_wms';
    // public const SYSTEM_SALESFORCE = 'salesforce';
    // public const SYSTEM_DYNAMICS = 'microsoft_dynamics';
    // public const SYSTEM_ORACLE = 'oracle';

    // Process Type constants
    public const PROCESS_MISC_ISSUE = 'misc_issue';
    public const PROCESS_MISC_RECEIPT = 'misc_receipt';
    public const PROCESS_QUANTITY_MOVE = 'quantity_move';
    public const PROCESS_PO_RECEIPT = 'po_receipt';

    // Integration Method constants
    public const METHOD_API = 'api';
    // public const METHOD_FILE = 'file';
    // public const METHOD_MQ = 'message_queue';
    // public const METHOD_STORED_PROCEDURE = 'stored_procedure';

    protected $fillable = [
        'name',
        'endpoint_alias',
        'external_system',
        'integration_method',
        'process_type',
        'endpoint_url',
        'request_fields',
        'field_mapping',
        'is_active',
        'created_by',
        'updated_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */


    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = [
        'external_system_name',
        'process_type_name',
        'integration_method_name',
    ];

    /**
     * Get the display name for the target type.
     *
     * @return string
     */
    public function getExternalSystemNameAttribute()
    {
        return self::getExternalSystemOptions()[$this->external_system] ?? $this->external_system;
    }

    /**
     * Get the display name for the process type.
     *
     * @return string
     */
    public function getProcessTypeNameAttribute()
    {
        return self::getProcessTypeOptions()[$this->process_type] ?? $this->process_type;
    }

    /**
     * Get the display name for the integration method.
     *
     * @return string
     */
    public function getIntegrationMethodNameAttribute()
    {
        return self::getIntegrationMethodOptions()[$this->integration_method] ?? $this->integration_method;
    }

    protected $casts = [
        'is_active' => 'boolean',
        'request_fields' => 'array',
        'field_mapping' => 'array',
    ];

    // Relationships
    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    // Available options for dropdowns
    public static function getExternalSystemOptions()
    {
        return [
            self::SYSTEM_CSI => 'CSI 10',
            self::SYSTEM_SAP_B1 => 'SAP B1',
            self::SYSTEM_INFOR_WMS => 'Infor WMS',
            // self::SYSTEM_SALESFORCE => 'Salesforce',
            // self::SYSTEM_DYNAMICS => 'Microsoft Dynamics',
            // self::SYSTEM_ORACLE => 'Oracle',
        ];
    }

    public static function getProcessTypeOptions()
    {
        return [
            self::PROCESS_MISC_ISSUE => 'Misc Issue',
            self::PROCESS_MISC_RECEIPT => 'Misc Receipt',
            self::PROCESS_QUANTITY_MOVE => 'Quantity Move',
            self::PROCESS_PO_RECEIPT => 'PO Receipt',
        ];
    }

    public static function getIntegrationMethodOptions()
    {
        return [
            self::METHOD_API => 'API',
            // self::METHOD_FILE => 'File Transfer',
            // self::METHOD_MQ => 'Message Queue',
            // self::METHOD_STORED_PROCEDURE => 'Stored Procedure',
        ];
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    // Keep old method names for backward compatibility
    public static function getProcessOptions()
    {
        return self::getProcessTypeOptions();
    }

    /**
     * @deprecated Use getIntegrationMethodOptions() instead
     */
    public static function getEndpointTypeOptions()
    {
        return self::getIntegrationMethodOptions();
    }

    /**
     * Alias for backward compatibility
     */
    public static function getIntegrationTypeOptions()
    {
        return self::getIntegrationMethodOptions();
    }

    public function scopeByExternalSystem($query, $system)
    {
        return $query->where('external_system', $system);
    }

    public function scopeByProcessType($query, $processType)
    {
        return $query->where('process_type', $processType);
    }

    public function scopeByIntegrationMethod($query, $method)
    {
        return $query->where('integration_method', $method);
    }

    /**
     * Get request fields as JSON string for forms
     */
    public function getRequestFieldsJsonAttribute()
    {
        return is_array($this->request_fields)
            ? json_encode($this->request_fields, JSON_PRETTY_PRINT)
            : $this->request_fields;
    }
}
