<?php

namespace App\Services;

use App\Contracts\FormIntegrationAdapterInterface;
use App\Models\FormIntegrationSetting;
use App\Adapters\CsiFormIntegrationAdapter;
use App\Adapters\SapFormIntegrationAdapter;
use Illuminate\Support\Facades\Log;

class FormIntegrationService
{
    private array $adapters = [];

    public function __construct()
    {
        $this->registerAdapters();
    }

    /**
     * Register all available adapters
     */
    private function registerAdapters(): void
    {
        $this->adapters['CSI'] = new CsiFormIntegrationAdapter();
        $this->adapters['SAP'] = new SapFormIntegrationAdapter();
    }

    /**
     * Get adapter for specific target system
     */
    public function getAdapter(string $targetSystem): FormIntegrationAdapterInterface
    {
        if (!isset($this->adapters[$targetSystem])) {
            throw new \InvalidArgumentException("No adapter found for target system: {$targetSystem}");
        }

        return $this->adapters[$targetSystem];
    }

    /**
     * Get all available target systems
     */
    public function getAvailableTargets(): array
    {
        return array_keys($this->adapters);
    }

    /**
     * Process form submission through integration setting
     */
    public function processFormSubmission(array $formData, FormIntegrationSetting $integrationSetting): array
    {
        try {
            $targetSystem = $integrationSetting->endpointConfiguration->target_type;
            $adapter = $this->getAdapter($targetSystem);

            // Validate form data
            $validationErrors = $adapter->validateFormData($formData, $integrationSetting);
            if (!empty($validationErrors)) {
                return [
                    'success' => false,
                    'errors' => $validationErrors,
                    'data' => null
                ];
            }

            // Transform form data
            $transformedData = $adapter->transformFormData($formData, $integrationSetting);

            // Prepare API data
            $apiData = $adapter->prepareApiData($transformedData, $integrationSetting);

            Log::info('Form integration processing completed', [
                'integration_setting_id' => $integrationSetting->id,
                'target_system' => $targetSystem,
                'form_id' => $integrationSetting->form_id,
                'endpoint_id' => $integrationSetting->endpoint_configuration_id
            ]);

            return [
                'success' => true,
                'errors' => [],
                'data' => $apiData
            ];

        } catch (\Exception $e) {
            Log::error('Form integration processing failed', [
                'integration_setting_id' => $integrationSetting->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'errors' => ['An error occurred while processing the form submission: ' . $e->getMessage()],
                'data' => null
            ];
        }
    }

    /**
     * Validate integration setting configuration
     */
    public function validateIntegrationSetting(FormIntegrationSetting $integrationSetting): array
    {
        $errors = [];

        try {
            // Check if form exists and is active
            if (!$integrationSetting->form || !$integrationSetting->form->is_active) {
                $errors[] = 'The selected form is not available or inactive.';
            }

            // Check if endpoint configuration exists and is active
            if (!$integrationSetting->integrationConfiguration || !$integrationSetting->integrationConfiguration->is_active) {
                $errors[] = 'The selected endpoint configuration is not available or inactive.';
            }

            // Validate field mappings
            $mappingErrors = $integrationSetting->validateFieldMappings();
            $errors = array_merge($errors, $mappingErrors);

            // Check if adapter exists for the target system
            $targetSystem = $integrationSetting->integrationConfiguration->target_type ?? null;
            if ($targetSystem && !isset($this->adapters[$targetSystem])) {
                $errors[] = "No adapter available for target system: {$targetSystem}";
            }

            // Validate that the adapter supports the process type
            if ($targetSystem && isset($this->adapters[$targetSystem])) {
                $adapter = $this->adapters[$targetSystem];
                $processType = $integrationSetting->integrationConfiguration->process_selection;
                $supportedProcesses = $adapter->getSupportedProcesses();

                if (!in_array($processType, $supportedProcesses)) {
                    $errors[] = "Target system {$targetSystem} does not support process type: {$processType}";
                }
            }

        } catch (\Exception $e) {
            $errors[] = 'An error occurred while validating the integration setting: ' . $e->getMessage();
        }

        return $errors;
    }

    /**
     * Get form fields for a specific form
     */
    public function getFormFields(int $formId): array
    {
        try {
            Log::debug('Getting form fields for form ID: ' . $formId);
            
            $form = \App\Models\Form::find($formId);
            if (!$form) {
                Log::warning('Form not found with ID: ' . $formId);
                return [];
            }

            Log::debug('Form found, content type: ' . gettype($form->content));
            
            // If content is a string, try to decode it
            $formContent = $form->content;
            if (is_string($formContent)) {
                $formContent = json_decode($formContent, true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    Log::error('Failed to decode form content JSON', [
                        'form_id' => $formId,
                        'json_error' => json_last_error_msg(),
                        'content_sample' => substr($form->content, 0, 200) . '...' // Log first 200 chars of content
                    ]);
                    return [];
                }
            }
            
            if (empty($formContent) || !is_array($formContent)) {
                Log::error('Form content is empty or invalid', [
                    'form_id' => $formId,
                    'content_type' => gettype($formContent)
                ]);
                return [];
            }
            
            // Log the structure of the form content for debugging
            Log::debug('Form content structure:', [
                'form_id' => $formId,
                'has_components' => isset($formContent['components']),
                'components_count' => isset($formContent['components']) ? count($formContent['components']) : 0,
                'content_keys' => array_keys($formContent)
            ]);
            
            // Extract fields directly without using the model's accessor
            $fields = [];
            if (isset($formContent['components']) && is_array($formContent['components'])) {
                $this->extractFieldsRecursive($formContent['components'], $fields);
            }
            
            Log::debug('Extracted ' . count($fields) . ' form fields', [
                'form_id' => $formId,
                'field_keys' => array_column($fields, 'key')
            ]);
            
            return $fields;

        } catch (\Exception $e) {
            Log::error('Failed to get form fields', [
                'form_id' => $formId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return [];
        }
    }

    /**
     * Get integration fields for a specific integration configuration
     */
    public function getIntegrationFields(int $integrationConfigurationId): array
    {
        try {
            $integrationConfig = \App\Models\IntegrationConfiguration::find($integrationConfigurationId);
            if (!$integrationConfig) {
                return [];
            }

            return $integrationConfig->request_fields ?? [];

        } catch (\Exception $e) {
            Log::error('Failed to get integration fields', [
                'integration_configuration_id' => $integrationConfigurationId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Create intelligent field mapping suggestions
     */
    public function suggestFieldMappings(array $formFields, array $integrationFields): array
    {
        $suggestionService = new \App\Services\FieldMappingSuggestionService();
        return $suggestionService->generateSuggestions($formFields, $integrationFields);
    }

    /**
     * Get integration statistics
     */
    public function getIntegrationStatistics(): array
    {
        try {
            $totalSettings = FormIntegrationSetting::count();
            $activeSettings = FormIntegrationSetting::where('form_integration_settings.is_active', true)->count();
            $settingsByTarget = FormIntegrationSetting::where('form_integration_settings.is_active', true)
                ->join('endpoint_configurations', 'form_integration_settings.endpoint_configuration_id', '=', 'endpoint_configurations.id')
                ->selectRaw('endpoint_configurations.target_type, COUNT(*) as count')
                ->groupBy('endpoint_configurations.target_type')
                ->pluck('count', 'target_type')
                ->toArray();

            return [
                'total_settings' => $totalSettings,
                'active_settings' => $activeSettings,
                'inactive_settings' => $totalSettings - $activeSettings,
                'settings_by_target' => $settingsByTarget,
                'available_targets' => $this->getAvailableTargets()
            ];

        } catch (\Exception $e) {
            Log::error('Failed to get integration statistics', [
                'error' => $e->getMessage()
            ]);

            return [
                'total_settings' => 0,
                'active_settings' => 0,
                'inactive_settings' => 0,
                'settings_by_target' => [],
                'available_targets' => $this->getAvailableTargets()
            ];
        }
    }

    /**
     * Recursively extract fields from form components
     * 
     * @param array $components Array of form components
     * @param array &$fields Reference to the fields array to populate
     */
    private function extractFieldsRecursive($components, &$fields)
    {
        if (!is_array($components)) {
            return;
        }

        foreach ($components as $component) {
            if (!is_array($component)) {
                continue;
            }

            // Skip components without a key
            if (empty($component['key'])) {
                // Still process nested components even if the parent doesn't have a key
                $this->processNestedComponents($component, $fields);
                continue;
            }

            $componentType = $component['type'] ?? 'unknown';
            
            // Skip non-input components like buttons, containers, etc.
            if (in_array($componentType, ['button', 'submit', 'panel', 'fieldset', 'columns', 'table', 'tabs', 'well'])) {
                $this->processNestedComponents($component, $fields);
                continue;
            }

            // Check if this is an input component
            $isInputComponent = isset($component['input']) ? (bool)$component['input'] : true; // Default to true if not specified
            
            // Some components might not have 'input' property but are still input fields
            $knownInputTypes = ['textfield', 'email', 'password', 'phoneNumber', 'number', 'checkbox', 'select', 'radio', 'textarea', 'selectboxes', 'datetime'];
            $isKnownInputType = in_array($componentType, $knownInputTypes);
            
            if ($isInputComponent || $isKnownInputType) {
                $fields[] = [
                    'key' => $component['key'],
                    'label' => $component['label'] ?? $component['key'],
                    'type' => $componentType,
                    'required' => $component['validate']['required'] ?? false,
                ];
            }

            // Process any nested components
            $this->processNestedComponents($component, $fields);
        }
    }
    
    /**
     * Process nested components within a component
     */
    private function processNestedComponents($component, &$fields)
    {
        // Handle nested components (like panels, fieldsets, etc.)
        if (isset($component['components']) && is_array($component['components'])) {
            $this->extractFieldsRecursive($component['components'], $fields);
        }

        // Handle columns layout specifically
        if (isset($component['columns']) && is_array($component['columns'])) {
            foreach ($component['columns'] as $column) {
                if (isset($column['components']) && is_array($column['components'])) {
                    $this->extractFieldsRecursive($column['components'], $fields);
                }
            }
        }
        
        // Handle tabs
        if (isset($component['tabs']) && is_array($component['tabs'])) {
            foreach ($component['tabs'] as $tab) {
                if (isset($tab['components']) && is_array($tab['components'])) {
                    $this->extractFieldsRecursive($tab['components'], $fields);
                }
            }
        }
        
        // Handle rows in tables
        if (isset($component['rows']) && is_array($component['rows'])) {
            foreach ($component['rows'] as $row) {
                if (isset($row['columns']) && is_array($row['columns'])) {
                    foreach ($row['columns'] as $column) {
                        if (isset($column['components']) && is_array($column['components'])) {
                            $this->extractFieldsRecursive($column['components'], $fields);
                        }
                    }
                }
            }
        }
    }
}
