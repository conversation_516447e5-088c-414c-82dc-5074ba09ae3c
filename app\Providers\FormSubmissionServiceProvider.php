<?php

namespace App\Providers;

use App\Services\AuditLoggerService;
use App\Services\ErrorLoggerService;
use App\Services\FormSubmissionService;
use App\Services\FormSyncEngineService;
use Illuminate\Support\ServiceProvider;

class FormSubmissionServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->singleton(AuditLoggerService::class, function ($app) {
            return new AuditLoggerService();
        });

        $this->app->singleton(ErrorLoggerService::class, function ($app) {
            return new ErrorLoggerService();
        });

        $this->app->singleton(FormSubmissionService::class, function ($app) {
            return new FormSubmissionService(
                $app->make(AuditLoggerService::class),
                $app->make(ErrorLoggerService::class)
            );
        });

        $this->app->singleton(FormSyncEngineService::class, function ($app) {
            return new FormSyncEngineService(
                $app->make(\App\Services\FormIntegrationService::class),
                $app->make(AuditLoggerService::class),
                $app->make(ErrorLoggerService::class)
            );
        });
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        //
    }
}
