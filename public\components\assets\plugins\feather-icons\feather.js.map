{"version": 3, "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///webpack/bootstrap 3c0ac6e2b54477142528", "webpack:///./node_modules/core-js/modules/_wks.js", "webpack:///./node_modules/core-js/modules/_global.js", "webpack:///./node_modules/core-js/modules/_object-dp.js", "webpack:///./node_modules/core-js/modules/_is-object.js", "webpack:///./node_modules/core-js/modules/_descriptors.js", "webpack:///./node_modules/core-js/modules/_has.js", "webpack:///./node_modules/core-js/modules/_core.js", "webpack:///./node_modules/core-js/modules/_hide.js", "webpack:///./node_modules/core-js/modules/_an-object.js", "webpack:///./node_modules/core-js/modules/_redefine.js", "webpack:///./node_modules/core-js/modules/_export.js", "webpack:///./node_modules/core-js/modules/_fails.js", "webpack:///./node_modules/core-js/modules/_ctx.js", "webpack:///./node_modules/core-js/modules/_iterators.js", "webpack:///./node_modules/core-js/modules/_defined.js", "webpack:///./node_modules/core-js/modules/_property-desc.js", "webpack:///./node_modules/core-js/modules/_uid.js", "webpack:///./node_modules/core-js/modules/_to-iobject.js", "webpack:///./node_modules/core-js/modules/_to-integer.js", "webpack:///./node_modules/core-js/modules/_iter-define.js", "webpack:///./node_modules/core-js/modules/_to-length.js", "webpack:///./node_modules/core-js/modules/_shared-key.js", "webpack:///./node_modules/core-js/modules/_set-to-string-tag.js", "webpack:///./node_modules/core-js/modules/_to-object.js", "webpack:///./node_modules/core-js/modules/_classof.js", "webpack:///./node_modules/core-js/modules/_for-of.js", "webpack:///./dist/icons.json", "webpack:///./node_modules/core-js/modules/es6.string.iterator.js", "webpack:///./node_modules/core-js/modules/_ie8-dom-define.js", "webpack:///./node_modules/core-js/modules/_dom-create.js", "webpack:///./node_modules/core-js/modules/_to-primitive.js", "webpack:///./node_modules/core-js/modules/_object-create.js", "webpack:///./node_modules/core-js/modules/_object-keys.js", "webpack:///./node_modules/core-js/modules/_iobject.js", "webpack:///./node_modules/core-js/modules/_cof.js", "webpack:///./node_modules/core-js/modules/_shared.js", "webpack:///./node_modules/core-js/modules/_enum-bug-keys.js", "webpack:///./node_modules/core-js/modules/_iter-call.js", "webpack:///./node_modules/core-js/modules/_is-array-iter.js", "webpack:///./node_modules/core-js/modules/core.get-iterator-method.js", "webpack:///./node_modules/core-js/modules/_iter-detect.js", "webpack:///./node_modules/core-js/modules/_object-pie.js", "webpack:///./node_modules/core-js/modules/_iter-step.js", "webpack:///./node_modules/core-js/modules/_redefine-all.js", "webpack:///./node_modules/core-js/modules/_an-instance.js", "webpack:///./node_modules/core-js/modules/_meta.js", "webpack:///./src/to-svg.js", "webpack:///./node_modules/core-js/fn/array/from.js", "webpack:///./node_modules/core-js/modules/_string-at.js", "webpack:///./node_modules/core-js/modules/_library.js", "webpack:///./node_modules/core-js/modules/_a-function.js", "webpack:///./node_modules/core-js/modules/_iter-create.js", "webpack:///./node_modules/core-js/modules/_object-dps.js", "webpack:///./node_modules/core-js/modules/_object-keys-internal.js", "webpack:///./node_modules/core-js/modules/_array-includes.js", "webpack:///./node_modules/core-js/modules/_to-index.js", "webpack:///./node_modules/core-js/modules/_html.js", "webpack:///./node_modules/core-js/modules/_object-gpo.js", "webpack:///./node_modules/core-js/modules/es6.array.from.js", "webpack:///./node_modules/core-js/modules/_create-property.js", "webpack:///./node_modules/core-js/fn/object/assign.js", "webpack:///./node_modules/core-js/modules/es6.object.assign.js", "webpack:///./node_modules/core-js/modules/_object-assign.js", "webpack:///./node_modules/core-js/modules/_object-gops.js", "webpack:///./node_modules/core-js/fn/set.js", "webpack:///./node_modules/core-js/modules/es6.object.to-string.js", "webpack:///./node_modules/core-js/modules/web.dom.iterable.js", "webpack:///./node_modules/core-js/modules/es6.array.iterator.js", "webpack:///./node_modules/core-js/modules/_add-to-unscopables.js", "webpack:///./node_modules/core-js/modules/es6.set.js", "webpack:///./node_modules/core-js/modules/_collection-strong.js", "webpack:///./node_modules/core-js/modules/_set-species.js", "webpack:///./node_modules/core-js/modules/_collection.js", "webpack:///./node_modules/core-js/modules/_inherit-if-required.js", "webpack:///./node_modules/core-js/modules/_set-proto.js", "webpack:///./node_modules/core-js/modules/_object-gopd.js", "webpack:///./node_modules/core-js/modules/es7.set.to-json.js", "webpack:///./node_modules/core-js/modules/_collection-to-json.js", "webpack:///./node_modules/core-js/modules/_array-from-iterable.js", "webpack:///./src/index.js", "webpack:///./src/replace.js"], "names": ["toSvg", "DEFAULT_OPTIONS", "xmlns", "width", "height", "viewBox", "fill", "stroke", "key", "options", "Error", "combinedOptions", "Object", "assign", "class", "addDefaultClassNames", "attributes", "optionsToAtrributes", "classNames", "classNamesArray", "trim", "split", "classNamesSet", "Set", "add", "Array", "from", "join", "keys", "for<PERSON>ach", "push", "module", "exports", "icons", "replace", "document", "elementsToReplace", "querySelectorAll", "replaceElement", "element", "getAttribute", "console", "error", "elementClassAttr", "svgString", "svgDocument", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "svgElement", "querySelector", "parentNode", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,O;ACVA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;;AAGA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAK;AACL;AACA;;AAEA;AACA;AACA;AACA,mCAA2B,0BAA0B,EAAE;AACvD,yCAAiC,eAAe;AAChD;AACA;AACA;;AAEA;AACA,8DAAsD,+DAA+D;;AAErH;AACA;;AAEA;AACA;;;;;;;AC7DA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,uB;;;;;;ACVA;AACA;AACA;AACA,uCAAuC,gC;;;;;;ACHvC;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,UAAU;AACb;AACA;AACA;AACA,E;;;;;;ACfA;AACA;AACA,E;;;;;;ACFA;AACA;AACA,iCAAiC,QAAQ,gBAAgB,UAAU,GAAG;AACtE,CAAC,E;;;;;;ACHD,uBAAuB;AACvB;AACA;AACA,E;;;;;;ACHA,6BAA6B;AAC7B,qCAAqC,gC;;;;;;ACDrC;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA,E;;;;;;ACPA;AACA;AACA;AACA;AACA,E;;;;;;ACJA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA,CAAC,E;;;;;;AC/BD;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,qFAAqF,uBAAuB;AAC5G,mEAAmE;AACnE,gEAAgE;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd,cAAc;AACd,cAAc;AACd,cAAc;AACd,eAAe;AACf,eAAe;AACf,eAAe;AACf,gBAAgB;AAChB,yB;;;;;;AC1CA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,E;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,E;;;;;;ACnBA,oB;;;;;;ACAA;AACA;AACA;AACA;AACA,E;;;;;;ACJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,E;;;;;;ACPA;AACA;AACA;AACA;AACA,E;;;;;;ACJA;AACA;AACA;AACA;AACA;AACA,E;;;;;;ACLA;AACA;AACA;AACA;AACA;AACA,E;;;;;;;ACLA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,4BAA4B,aAAa;;AAEzC;AACA;AACA;AACA;AACA;AACA,wCAAwC,oCAAoC;AAC5E,4CAA4C,oCAAoC;AAChF,KAAK,2BAA2B,oCAAoC;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,mBAAmB;AACnC;AACA;AACA,iCAAiC,2BAA2B;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,E;;;;;;ACrEA;AACA;AACA;AACA;AACA,2DAA2D;AAC3D,E;;;;;;ACLA;AACA;AACA;AACA;AACA,E;;;;;;ACJA;AACA;AACA;;AAEA;AACA,kEAAkE,+BAA+B;AACjG,E;;;;;;ACNA;AACA;AACA;AACA;AACA,E;;;;;;ACJA;AACA;AACA;AACA;AACA,yBAAyB,kBAAkB,EAAE;;AAE7C;AACA;AACA;AACA;AACA,GAAG,UAAU;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,E;;;;;;ACtBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC,iBAAiB,EAAE;AACxD;AACA;AACA;AACA;AACA;AACA,gEAAgE,gBAAgB;AAChF;AACA;AACA,GAAG,2CAA2C,gCAAgC;AAC9E;AACA;AACA;AACA;AACA;AACA,wB;;;;;;ACxBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,E;;;;;;;ACtOA;AACA;;AAEA;AACA;AACA,6BAA6B;AAC7B,cAAc;AACd;AACA,CAAC;AACD;AACA;AACA;AACA,+BAA+B;AAC/B;AACA;AACA,UAAU;AACV,CAAC,E;;;;;;AChBD;AACA,qEAAsE,gBAAgB,UAAU,GAAG;AACnG,CAAC,E;;;;;;ACFD;AACA;AACA;AACA;AACA;AACA;AACA,E;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,E;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;;;;;;ACxCA;AACA;AACA;;AAEA;AACA;AACA,E;;;;;;ACNA;AACA;AACA;AACA;AACA,E;;;;;;ACJA,iBAAiB;;AAEjB;AACA;AACA,E;;;;;;ACJA;AACA;AACA,mDAAmD;AACnD;AACA,uCAAuC;AACvC,E;;;;;;ACLA;AACA;AACA;AACA,a;;;;;;ACHA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,E;;;;;;ACXA;AACA;AACA;AACA;;AAEA;AACA;AACA,E;;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,E;;;;;;ACPA;AACA;;AAEA;AACA;AACA,+BAA+B,qBAAqB;AACpD,+BAA+B,SAAS,EAAE;AAC1C,CAAC,UAAU;;AAEX;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,SAAS,mBAAmB;AACvD,+BAA+B,aAAa;AAC5C;AACA,GAAG,UAAU;AACb;AACA,E;;;;;;ACpBA,cAAc,sB;;;;;;ACAd;AACA,UAAU;AACV,E;;;;;;ACFA;AACA;AACA;AACA;AACA,E;;;;;;ACJA;AACA;AACA;AACA,GAAG;AACH,E;;;;;;ACJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iDAAiD;AACjD,CAAC;AACD;AACA,qBAAqB;AACrB;AACA,SAAS;AACT,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,E;;;;;;;;;;;;kBC5BwBA,K;;AApBxB;;;;;;AAEA,IAAMC,kBAAkB;AACtBC,SAAO,4BADe;AAEtBC,SAAO,EAFe;AAGtBC,UAAQ,EAHc;AAItBC,WAAS,WAJa;AAKtBC,QAAM,MALgB;AAMtBC,UAAQ,cANc;AAOtB,kBAAgB,CAPM;AAQtB,oBAAkB,OARI;AAStB,qBAAmB;AATG,CAAxB;;AAYA;;;;;;AAlBA;;;;AAwBe,SAASP,KAAT,CAAeQ,GAAf,EAAkC;AAAA,MAAdC,OAAc,uEAAJ,EAAI;;AAC/C,MAAI,CAACD,GAAL,EAAU;AACR,UAAM,IAAIE,KAAJ,CAAU,sDAAV,CAAN;AACD;;AAED,MAAI,CAAC,gBAAMF,GAAN,CAAL,EAAiB;AACf,UAAM,IAAIE,KAAJ,yBAA+BF,GAA/B,oEAAN;AACD;;AAED,MAAMG,kBAAkBC,OAAOC,MAAP,CAAc,EAAd,EAAkBZ,eAAlB,EAAmCQ,OAAnC,CAAxB;;AAEAE,kBAAgBG,KAAhB,GAAwBC,qBAAqBJ,gBAAgBG,KAArC,EAA4CN,GAA5C,CAAxB;;AAEA,MAAMQ,aAAaC,oBAAoBN,eAApB,CAAnB;;AAEA,mBAAeK,UAAf,SAA6B,gBAAMR,GAAN,CAA7B;AACD;;AAED;;;;;;AAMA,SAASO,oBAAT,CAA8BG,UAA9B,EAA0CV,GAA1C,EAA+C;AAC7C;AACA,MAAMW,kBAAkBD,aAAaA,WAAWE,IAAX,GAAkBC,KAAlB,CAAwB,KAAxB,CAAb,GAA8C,EAAtE;;AAEA;AACA,MAAMC,gBAAgB,IAAIC,GAAJ,CAAQJ,eAAR,CAAtB;;AAEA;AACAG,gBAAcE,GAAd,CAAkB,SAAlB,EAA6BA,GAA7B,cAA4ChB,GAA5C;;AAEA,SAAOiB,MAAMC,IAAN,CAAWJ,aAAX,EAA0BK,IAA1B,CAA+B,GAA/B,CAAP;AACD;;AAED;;;;;AAKA,SAASV,mBAAT,CAA6BR,OAA7B,EAAsC;AACpC,MAAMO,aAAa,EAAnB;;AAEAJ,SAAOgB,IAAP,CAAYnB,OAAZ,EAAqBoB,OAArB,CAA6B,eAAO;AAClCb,eAAWc,IAAX,CAAmBtB,GAAnB,UAA2BC,QAAQD,GAAR,CAA3B;AACD,GAFD;;AAIA,SAAOQ,WAAWW,IAAX,CAAgB,GAAhB,CAAP;AACD,C;;;;;;;;;;;;;;;;AC1ED;AACA;AACA,mD;;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,E;;;;;;AChBA,uB;;;;;;ACAA;AACA;AACA;AACA,E;;;;;;;ACHA;AACA;AACA;AACA;AACA;;AAEA;AACA,yFAAgF,aAAa,EAAE;;AAE/F;AACA,qDAAqD,0BAA0B;AAC/E;AACA,E;;;;;;ACZA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,E;;;;;;ACZA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,E;;;;;;AChBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,WAAW,eAAe;AAC/B;AACA,KAAK;AACL;AACA,E;;;;;;ACpBA;AACA;AACA;AACA;AACA;AACA;AACA,E;;;;;;ACNA,6E;;;;;;ACAA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,E;;;;;;;ACZA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,wEAA0E,kBAAkB,EAAE;AAC9F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oDAAoD,gCAAgC;AACpF;AACA;AACA,KAAK;AACL;AACA,iCAAiC,gBAAgB;AACjD;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;;;;;;;ACpCD;AACA;AACA;;AAEA;AACA;AACA;AACA,E;;;;;;ACPA;AACA,sD;;;;;;ACDA;AACA;;AAEA,0CAA0C,gCAAoC,E;;;;;;;ACH9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,UAAU,EAAE;AAC9C,mBAAmB,sCAAsC;AACzD,CAAC,oCAAoC;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,CAAC,W;;;;;;AChCD,yC;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA,4C;;;;;;;ACLA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,C;;;;;;ACTA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,wGAAwG,OAAO;AAC/G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,C;;;;;;;ACrBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,gCAAgC;AAChC,cAAc;AACd,iBAAiB;AACjB;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;;AAEA;AACA;AACA,4B;;;;;;ACjCA;AACA;AACA;AACA,0FAAsF;AACtF;AACA;AACA,E;;;;;;;ACNA;AACA;;AAEA;AACA;AACA,wBAAwB,mEAAmE;AAC3F,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC,U;;;;;;;ACXD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,sBAAsB,OAAO;AAC7B;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B,0BAA0B;AAC1B,0BAA0B;AAC1B,qBAAqB;AACrB;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,6DAA6D,OAAO;AACpE;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB,qBAAqB;AACrB,0BAA0B;AAC1B,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA,E;;;;;;;AC7IA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,oBAAoB,aAAa;AACjC,GAAG;AACH,E;;;;;;;ACZA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,OAAO;AACP;AACA,OAAO,kCAAkC,gCAAgC,aAAa;AACtF,6BAA6B,mCAAmC,aAAa;AAC7E;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,2DAA2D;AAC3D;AACA,gDAAgD,iBAAiB,EAAE;AACnE;AACA,0DAA0D,aAAa,EAAE;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,0B;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;;AAEA;AACA,E;;;;;;ACpFA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,E;;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kDAAkD;AAClD;AACA;AACA;AACA;AACA;AACA,OAAO,UAAU,cAAc;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,GAAG;AACR;AACA,E;;;;;;ACxBA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG,UAAU;AACb;AACA,E;;;;;;ACfA;AACA;;AAEA,uCAAuC,uCAAgD,E;;;;;;ACHvF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,E;;;;;;ACRA;;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;ACFA;;;;AACA;;;;AACA;;;;;;AAEAI,OAAOC,OAAP,GAAiB,EAAEC,sBAAF,EAASjC,sBAAT,EAAgBkC,0BAAhB,EAAjB,C,CARA;;;;;;;;;;;;;;kBCcwBA,O;;AARxB;;;;AACA;;;;;;AAEA;;;;;AATA;;;;AAIA;;AAUe,SAASA,OAAT,GAA+B;AAAA,MAAdzB,OAAc,uEAAJ,EAAI;;AAC5C,MAAI,OAAO0B,QAAP,KAAoB,WAAxB,EAAqC;AACnC,UAAM,IAAIzB,KAAJ,CAAU,0DAAV,CAAN;AACD;;AAED,MAAM0B,oBAAoBD,SAASE,gBAAT,CAA0B,gBAA1B,CAA1B;;AAEAZ,QAAMC,IAAN,CAAWU,iBAAX,EAA8BP,OAA9B,CAAsC;AAAA,WAAWS,eAAeC,OAAf,EAAwB9B,OAAxB,CAAX;AAAA,GAAtC;AACD;;AAED;;;;;;AAMA,SAAS6B,cAAT,CAAwBC,OAAxB,EAAiC9B,OAAjC,EAA0C;AACxC,MAAMD,MAAM+B,QAAQC,YAAR,CAAqB,cAArB,CAAZ;;AAEA,MAAI,CAAChC,GAAL,EAAU;AACRiC,YAAQC,KAAR,CAAc,qDAAd;AACA;AACD;;AAED,MAAI,CAAC,gBAAMlC,GAAN,CAAL,EAAiB;AACfiC,YAAQC,KAAR,yBAAmClC,GAAnC;AACA;AACD;;AAED,MAAMmC,mBAAmBJ,QAAQC,YAAR,CAAqB,OAArB,CAAzB;AACA,MAAMtB,aACJT,QAAQK,KAAR,GAAmBL,QAAQK,KAA3B,SAAoC6B,gBAApC,GAAyDA,gBAD3D;;AAIA,MAAMC,YAAY,qBAAMpC,GAAN,EAAWI,OAAOC,MAAP,CAAc,EAAd,EAAkBJ,OAAlB,EAA2B,EAAEK,OAAOI,UAAT,EAA3B,CAAX,CAAlB;AACA,MAAM2B,cAAc,IAAIC,SAAJ,GAAgBC,eAAhB,CAAgCH,SAAhC,EAA2C,eAA3C,CAApB;AACA,MAAMI,aAAaH,YAAYI,aAAZ,CAA0B,KAA1B,CAAnB;;AAEAV,UAAQW,UAAR,CAAmBC,YAAnB,CAAgCH,UAAhC,EAA4CT,OAA5C;AACD,C", "file": "feather.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"feather\"] = factory();\n\telse\n\t\troot[\"feather\"] = factory();\n})(this, function() {\nreturn \n\n\n// WEBPACK FOOTER //\n// webpack/universalModuleDefinition", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, {\n \t\t\t\tconfigurable: false,\n \t\t\t\tenumerable: true,\n \t\t\t\tget: getter\n \t\t\t});\n \t\t}\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 47);\n\n\n\n// WEBPACK FOOTER //\n// webpack/bootstrap 3c0ac6e2b54477142528", "var store      = require('./_shared')('wks')\n  , uid        = require('./_uid')\n  , Symbol     = require('./_global').Symbol\n  , USE_SYMBOL = typeof Symbol == 'function';\n\nvar $exports = module.exports = function(name){\n  return store[name] || (store[name] =\n    USE_SYMBOL && Symbol[name] || (USE_SYMBOL ? Symbol : uid)('Symbol.' + name));\n};\n\n$exports.store = store;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_wks.js\n// module id = 0\n// module chunks = 0", "// https://github.com/zloirock/core-js/issues/86#issuecomment-115759028\nvar global = module.exports = typeof window != 'undefined' && window.Math == Math\n  ? window : typeof self != 'undefined' && self.Math == Math ? self : Function('return this')();\nif(typeof __g == 'number')__g = global; // eslint-disable-line no-undef\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_global.js\n// module id = 1\n// module chunks = 0", "var anObject       = require('./_an-object')\n  , IE8_DOM_DEFINE = require('./_ie8-dom-define')\n  , toPrimitive    = require('./_to-primitive')\n  , dP             = Object.defineProperty;\n\nexports.f = require('./_descriptors') ? Object.defineProperty : function defineProperty(O, P, Attributes){\n  anObject(O);\n  P = toPrimitive(P, true);\n  anObject(Attributes);\n  if(IE8_DOM_DEFINE)try {\n    return dP(O, P, Attributes);\n  } catch(e){ /* empty */ }\n  if('get' in Attributes || 'set' in Attributes)throw TypeError('Accessors not supported!');\n  if('value' in Attributes)O[P] = Attributes.value;\n  return O;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_object-dp.js\n// module id = 2\n// module chunks = 0", "module.exports = function(it){\n  return typeof it === 'object' ? it !== null : typeof it === 'function';\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_is-object.js\n// module id = 3\n// module chunks = 0", "// Thank's IE8 for his funny defineProperty\nmodule.exports = !require('./_fails')(function(){\n  return Object.defineProperty({}, 'a', {get: function(){ return 7; }}).a != 7;\n});\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_descriptors.js\n// module id = 4\n// module chunks = 0", "var hasOwnProperty = {}.hasOwnProperty;\nmodule.exports = function(it, key){\n  return hasOwnProperty.call(it, key);\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_has.js\n// module id = 5\n// module chunks = 0", "var core = module.exports = {version: '2.4.0'};\nif(typeof __e == 'number')__e = core; // eslint-disable-line no-undef\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_core.js\n// module id = 6\n// module chunks = 0", "var dP         = require('./_object-dp')\n  , createDesc = require('./_property-desc');\nmodule.exports = require('./_descriptors') ? function(object, key, value){\n  return dP.f(object, key, createDesc(1, value));\n} : function(object, key, value){\n  object[key] = value;\n  return object;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_hide.js\n// module id = 7\n// module chunks = 0", "var isObject = require('./_is-object');\nmodule.exports = function(it){\n  if(!isObject(it))throw TypeError(it + ' is not an object!');\n  return it;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_an-object.js\n// module id = 8\n// module chunks = 0", "var global    = require('./_global')\n  , hide      = require('./_hide')\n  , has       = require('./_has')\n  , SRC       = require('./_uid')('src')\n  , TO_STRING = 'toString'\n  , $toString = Function[TO_STRING]\n  , TPL       = ('' + $toString).split(TO_STRING);\n\nrequire('./_core').inspectSource = function(it){\n  return $toString.call(it);\n};\n\n(module.exports = function(O, key, val, safe){\n  var isFunction = typeof val == 'function';\n  if(isFunction)has(val, 'name') || hide(val, 'name', key);\n  if(O[key] === val)return;\n  if(isFunction)has(val, SRC) || hide(val, SRC, O[key] ? '' + O[key] : TPL.join(String(key)));\n  if(O === global){\n    O[key] = val;\n  } else {\n    if(!safe){\n      delete O[key];\n      hide(O, key, val);\n    } else {\n      if(O[key])O[key] = val;\n      else hide(O, key, val);\n    }\n  }\n// add fake Function#toString for correct work wrapped methods / constructors with methods like LoDash isNative\n})(Function.prototype, TO_STRING, function toString(){\n  return typeof this == 'function' && this[SRC] || $toString.call(this);\n});\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_redefine.js\n// module id = 9\n// module chunks = 0", "var global    = require('./_global')\n  , core      = require('./_core')\n  , hide      = require('./_hide')\n  , redefine  = require('./_redefine')\n  , ctx       = require('./_ctx')\n  , PROTOTYPE = 'prototype';\n\nvar $export = function(type, name, source){\n  var IS_FORCED = type & $export.F\n    , IS_GLOBAL = type & $export.G\n    , IS_STATIC = type & $export.S\n    , IS_PROTO  = type & $export.P\n    , IS_BIND   = type & $export.B\n    , target    = IS_GLOBAL ? global : IS_STATIC ? global[name] || (global[name] = {}) : (global[name] || {})[PROTOTYPE]\n    , exports   = IS_GLOBAL ? core : core[name] || (core[name] = {})\n    , expProto  = exports[PROTOTYPE] || (exports[PROTOTYPE] = {})\n    , key, own, out, exp;\n  if(IS_GLOBAL)source = name;\n  for(key in source){\n    // contains in native\n    own = !IS_FORCED && target && target[key] !== undefined;\n    // export native or passed\n    out = (own ? target : source)[key];\n    // bind timers to global for call from export context\n    exp = IS_BIND && own ? ctx(out, global) : IS_PROTO && typeof out == 'function' ? ctx(Function.call, out) : out;\n    // extend global\n    if(target)redefine(target, key, out, type & $export.U);\n    // export\n    if(exports[key] != out)hide(exports, key, exp);\n    if(IS_PROTO && expProto[key] != out)expProto[key] = out;\n  }\n};\nglobal.core = core;\n// type bitmap\n$export.F = 1;   // forced\n$export.G = 2;   // global\n$export.S = 4;   // static\n$export.P = 8;   // proto\n$export.B = 16;  // bind\n$export.W = 32;  // wrap\n$export.U = 64;  // safe\n$export.R = 128; // real proto method for `library` \nmodule.exports = $export;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_export.js\n// module id = 10\n// module chunks = 0", "module.exports = function(exec){\n  try {\n    return !!exec();\n  } catch(e){\n    return true;\n  }\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_fails.js\n// module id = 11\n// module chunks = 0", "// optional / simple context binding\nvar aFunction = require('./_a-function');\nmodule.exports = function(fn, that, length){\n  aFunction(fn);\n  if(that === undefined)return fn;\n  switch(length){\n    case 1: return function(a){\n      return fn.call(that, a);\n    };\n    case 2: return function(a, b){\n      return fn.call(that, a, b);\n    };\n    case 3: return function(a, b, c){\n      return fn.call(that, a, b, c);\n    };\n  }\n  return function(/* ...args */){\n    return fn.apply(that, arguments);\n  };\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_ctx.js\n// module id = 12\n// module chunks = 0", "module.exports = {};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_iterators.js\n// module id = 13\n// module chunks = 0", "// 7.2.1 RequireObjectCoercible(argument)\nmodule.exports = function(it){\n  if(it == undefined)throw TypeError(\"Can't call method on  \" + it);\n  return it;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_defined.js\n// module id = 14\n// module chunks = 0", "module.exports = function(bitmap, value){\n  return {\n    enumerable  : !(bitmap & 1),\n    configurable: !(bitmap & 2),\n    writable    : !(bitmap & 4),\n    value       : value\n  };\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_property-desc.js\n// module id = 15\n// module chunks = 0", "var id = 0\n  , px = Math.random();\nmodule.exports = function(key){\n  return 'Symbol('.concat(key === undefined ? '' : key, ')_', (++id + px).toString(36));\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_uid.js\n// module id = 16\n// module chunks = 0", "// to indexed object, toObject with fallback for non-array-like ES3 strings\nvar IObject = require('./_iobject')\n  , defined = require('./_defined');\nmodule.exports = function(it){\n  return IObject(defined(it));\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_to-iobject.js\n// module id = 17\n// module chunks = 0", "// 7.1.4 ToInteger\nvar ceil  = Math.ceil\n  , floor = Math.floor;\nmodule.exports = function(it){\n  return isNaN(it = +it) ? 0 : (it > 0 ? floor : ceil)(it);\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_to-integer.js\n// module id = 18\n// module chunks = 0", "'use strict';\nvar LIBRARY        = require('./_library')\n  , $export        = require('./_export')\n  , redefine       = require('./_redefine')\n  , hide           = require('./_hide')\n  , has            = require('./_has')\n  , Iterators      = require('./_iterators')\n  , $iterCreate    = require('./_iter-create')\n  , setToStringTag = require('./_set-to-string-tag')\n  , getPrototypeOf = require('./_object-gpo')\n  , ITERATOR       = require('./_wks')('iterator')\n  , BUGGY          = !([].keys && 'next' in [].keys()) // <PERSON><PERSON> has buggy iterators w/o `next`\n  , FF_ITERATOR    = '@@iterator'\n  , KEYS           = 'keys'\n  , VALUES         = 'values';\n\nvar returnThis = function(){ return this; };\n\nmodule.exports = function(Base, NAME, Constructor, next, DEFAULT, IS_SET, FORCED){\n  $iterCreate(Constructor, NAME, next);\n  var getMethod = function(kind){\n    if(!BUGGY && kind in proto)return proto[kind];\n    switch(kind){\n      case KEYS: return function keys(){ return new Constructor(this, kind); };\n      case VALUES: return function values(){ return new Constructor(this, kind); };\n    } return function entries(){ return new Constructor(this, kind); };\n  };\n  var TAG        = NAME + ' Iterator'\n    , DEF_VALUES = DEFAULT == VALUES\n    , VALUES_BUG = false\n    , proto      = Base.prototype\n    , $native    = proto[ITERATOR] || proto[FF_ITERATOR] || DEFAULT && proto[DEFAULT]\n    , $default   = $native || getMethod(DEFAULT)\n    , $entries   = DEFAULT ? !DEF_VALUES ? $default : getMethod('entries') : undefined\n    , $anyNative = NAME == 'Array' ? proto.entries || $native : $native\n    , methods, key, IteratorPrototype;\n  // Fix native\n  if($anyNative){\n    IteratorPrototype = getPrototypeOf($anyNative.call(new Base));\n    if(IteratorPrototype !== Object.prototype){\n      // Set @@toStringTag to native iterators\n      setToStringTag(IteratorPrototype, TAG, true);\n      // fix for some old engines\n      if(!LIBRARY && !has(IteratorPrototype, ITERATOR))hide(IteratorPrototype, ITERATOR, returnThis);\n    }\n  }\n  // fix Array#{values, @@iterator}.name in V8 / FF\n  if(DEF_VALUES && $native && $native.name !== VALUES){\n    VALUES_BUG = true;\n    $default = function values(){ return $native.call(this); };\n  }\n  // Define iterator\n  if((!LIBRARY || FORCED) && (BUGGY || VALUES_BUG || !proto[ITERATOR])){\n    hide(proto, ITERATOR, $default);\n  }\n  // Plug for library\n  Iterators[NAME] = $default;\n  Iterators[TAG]  = returnThis;\n  if(DEFAULT){\n    methods = {\n      values:  DEF_VALUES ? $default : getMethod(VALUES),\n      keys:    IS_SET     ? $default : getMethod(KEYS),\n      entries: $entries\n    };\n    if(FORCED)for(key in methods){\n      if(!(key in proto))redefine(proto, key, methods[key]);\n    } else $export($export.P + $export.F * (BUGGY || VALUES_BUG), NAME, methods);\n  }\n  return methods;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_iter-define.js\n// module id = 19\n// module chunks = 0", "// 7.1.15 ToLength\nvar toInteger = require('./_to-integer')\n  , min       = Math.min;\nmodule.exports = function(it){\n  return it > 0 ? min(toInteger(it), 0x1fffffffffffff) : 0; // pow(2, 53) - 1 == 9007199254740991\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_to-length.js\n// module id = 20\n// module chunks = 0", "var shared = require('./_shared')('keys')\n  , uid    = require('./_uid');\nmodule.exports = function(key){\n  return shared[key] || (shared[key] = uid(key));\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_shared-key.js\n// module id = 21\n// module chunks = 0", "var def = require('./_object-dp').f\n  , has = require('./_has')\n  , TAG = require('./_wks')('toStringTag');\n\nmodule.exports = function(it, tag, stat){\n  if(it && !has(it = stat ? it : it.prototype, TAG))def(it, TAG, {configurable: true, value: tag});\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_set-to-string-tag.js\n// module id = 22\n// module chunks = 0", "// 7.1.13 ToObject(argument)\nvar defined = require('./_defined');\nmodule.exports = function(it){\n  return Object(defined(it));\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_to-object.js\n// module id = 23\n// module chunks = 0", "// getting tag from ******** Object.prototype.toString()\nvar cof = require('./_cof')\n  , TAG = require('./_wks')('toStringTag')\n  // ES3 wrong here\n  , ARG = cof(function(){ return arguments; }()) == 'Arguments';\n\n// fallback for IE11 Script Access Denied error\nvar tryGet = function(it, key){\n  try {\n    return it[key];\n  } catch(e){ /* empty */ }\n};\n\nmodule.exports = function(it){\n  var O, T, B;\n  return it === undefined ? 'Undefined' : it === null ? 'Null'\n    // @@toStringTag case\n    : typeof (T = tryGet(O = Object(it), TAG)) == 'string' ? T\n    // builtinTag case\n    : ARG ? cof(O)\n    // ES3 arguments fallback\n    : (B = cof(O)) == 'Object' && typeof O.callee == 'function' ? 'Arguments' : B;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_classof.js\n// module id = 24\n// module chunks = 0", "var ctx         = require('./_ctx')\n  , call        = require('./_iter-call')\n  , isArrayIter = require('./_is-array-iter')\n  , anObject    = require('./_an-object')\n  , toLength    = require('./_to-length')\n  , getIterFn   = require('./core.get-iterator-method')\n  , BREAK       = {}\n  , RETURN      = {};\nvar exports = module.exports = function(iterable, entries, fn, that, ITERATOR){\n  var iterFn = ITERATOR ? function(){ return iterable; } : getIterFn(iterable)\n    , f      = ctx(fn, that, entries ? 2 : 1)\n    , index  = 0\n    , length, step, iterator, result;\n  if(typeof iterFn != 'function')throw TypeError(iterable + ' is not iterable!');\n  // fast case for arrays with default iterator\n  if(isArrayIter(iterFn))for(length = toLength(iterable.length); length > index; index++){\n    result = entries ? f(anObject(step = iterable[index])[0], step[1]) : f(iterable[index]);\n    if(result === BREAK || result === RETURN)return result;\n  } else for(iterator = iterFn.call(iterable); !(step = iterator.next()).done; ){\n    result = call(iterator, f, step.value, entries);\n    if(result === BREAK || result === RETURN)return result;\n  }\n};\nexports.BREAK  = BREAK;\nexports.RETURN = RETURN;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_for-of.js\n// module id = 25\n// module chunks = 0", "module.exports = {\n\t\"activity\": \"<polyline points=\\\"22 12 18 12 15 21 9 3 6 12 2 12\\\"></polyline>\",\n\t\"airplay\": \"<path d=\\\"M5 17H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-1\\\"></path><polygon points=\\\"12 15 17 21 7 21 12 15\\\"></polygon>\",\n\t\"alert-circle\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><line x1=\\\"12\\\" y1=\\\"8\\\" x2=\\\"12\\\" y2=\\\"12\\\"></line><line x1=\\\"12\\\" y1=\\\"16\\\" x2=\\\"12\\\" y2=\\\"16\\\"></line>\",\n\t\"alert-octagon\": \"<polygon points=\\\"7.86 2 16.14 2 22 7.86 22 16.14 16.14 22 7.86 22 2 16.14 2 7.86 7.86 2\\\"></polygon><line x1=\\\"12\\\" y1=\\\"8\\\" x2=\\\"12\\\" y2=\\\"12\\\"></line><line x1=\\\"12\\\" y1=\\\"16\\\" x2=\\\"12\\\" y2=\\\"16\\\"></line>\",\n\t\"alert-triangle\": \"<path d=\\\"M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z\\\"></path><line x1=\\\"12\\\" y1=\\\"9\\\" x2=\\\"12\\\" y2=\\\"13\\\"></line><line x1=\\\"12\\\" y1=\\\"17\\\" x2=\\\"12\\\" y2=\\\"17\\\"></line>\",\n\t\"align-center\": \"<line x1=\\\"18\\\" y1=\\\"10\\\" x2=\\\"6\\\" y2=\\\"10\\\"></line><line x1=\\\"21\\\" y1=\\\"6\\\" x2=\\\"3\\\" y2=\\\"6\\\"></line><line x1=\\\"21\\\" y1=\\\"14\\\" x2=\\\"3\\\" y2=\\\"14\\\"></line><line x1=\\\"18\\\" y1=\\\"18\\\" x2=\\\"6\\\" y2=\\\"18\\\"></line>\",\n\t\"align-justify\": \"<line x1=\\\"21\\\" y1=\\\"10\\\" x2=\\\"3\\\" y2=\\\"10\\\"></line><line x1=\\\"21\\\" y1=\\\"6\\\" x2=\\\"3\\\" y2=\\\"6\\\"></line><line x1=\\\"21\\\" y1=\\\"14\\\" x2=\\\"3\\\" y2=\\\"14\\\"></line><line x1=\\\"21\\\" y1=\\\"18\\\" x2=\\\"3\\\" y2=\\\"18\\\"></line>\",\n\t\"align-left\": \"<line x1=\\\"17\\\" y1=\\\"10\\\" x2=\\\"3\\\" y2=\\\"10\\\"></line><line x1=\\\"21\\\" y1=\\\"6\\\" x2=\\\"3\\\" y2=\\\"6\\\"></line><line x1=\\\"21\\\" y1=\\\"14\\\" x2=\\\"3\\\" y2=\\\"14\\\"></line><line x1=\\\"17\\\" y1=\\\"18\\\" x2=\\\"3\\\" y2=\\\"18\\\"></line>\",\n\t\"align-right\": \"<line x1=\\\"21\\\" y1=\\\"10\\\" x2=\\\"7\\\" y2=\\\"10\\\"></line><line x1=\\\"21\\\" y1=\\\"6\\\" x2=\\\"3\\\" y2=\\\"6\\\"></line><line x1=\\\"21\\\" y1=\\\"14\\\" x2=\\\"3\\\" y2=\\\"14\\\"></line><line x1=\\\"21\\\" y1=\\\"18\\\" x2=\\\"7\\\" y2=\\\"18\\\"></line>\",\n\t\"anchor\": \"<circle cx=\\\"12\\\" cy=\\\"5\\\" r=\\\"3\\\"></circle><line x1=\\\"12\\\" y1=\\\"22\\\" x2=\\\"12\\\" y2=\\\"8\\\"></line><path d=\\\"M5 12H2a10 10 0 0 0 20 0h-3\\\"></path>\",\n\t\"aperture\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><line x1=\\\"14.31\\\" y1=\\\"8\\\" x2=\\\"20.05\\\" y2=\\\"17.94\\\"></line><line x1=\\\"9.69\\\" y1=\\\"8\\\" x2=\\\"21.17\\\" y2=\\\"8\\\"></line><line x1=\\\"7.38\\\" y1=\\\"12\\\" x2=\\\"13.12\\\" y2=\\\"2.06\\\"></line><line x1=\\\"9.69\\\" y1=\\\"16\\\" x2=\\\"3.95\\\" y2=\\\"6.06\\\"></line><line x1=\\\"14.31\\\" y1=\\\"16\\\" x2=\\\"2.83\\\" y2=\\\"16\\\"></line><line x1=\\\"16.62\\\" y1=\\\"12\\\" x2=\\\"10.88\\\" y2=\\\"21.94\\\"></line>\",\n\t\"arrow-down-left\": \"<line x1=\\\"18\\\" y1=\\\"6\\\" x2=\\\"6\\\" y2=\\\"18\\\"></line><polyline points=\\\"15 18 6 18 6 9\\\"></polyline>\",\n\t\"arrow-down-right\": \"<line x1=\\\"6\\\" y1=\\\"6\\\" x2=\\\"18\\\" y2=\\\"18\\\"></line><polyline points=\\\"9 18 18 18 18 9\\\"></polyline>\",\n\t\"arrow-down\": \"<line x1=\\\"12\\\" y1=\\\"4\\\" x2=\\\"12\\\" y2=\\\"20\\\"></line><polyline points=\\\"18 14 12 20 6 14\\\"></polyline>\",\n\t\"arrow-left\": \"<line x1=\\\"20\\\" y1=\\\"12\\\" x2=\\\"4\\\" y2=\\\"12\\\"></line><polyline points=\\\"10 18 4 12 10 6\\\"></polyline>\",\n\t\"arrow-right\": \"<line x1=\\\"4\\\" y1=\\\"12\\\" x2=\\\"20\\\" y2=\\\"12\\\"></line><polyline points=\\\"14 6 20 12 14 18\\\"></polyline>\",\n\t\"arrow-up-left\": \"<line x1=\\\"18\\\" y1=\\\"18\\\" x2=\\\"6\\\" y2=\\\"6\\\"></line><polyline points=\\\"15 6 6 6 6 15\\\"></polyline>\",\n\t\"arrow-up-right\": \"<line x1=\\\"6\\\" y1=\\\"18\\\" x2=\\\"18\\\" y2=\\\"6\\\"></line><polyline points=\\\"9 6 18 6 18 15\\\"></polyline>\",\n\t\"arrow-up\": \"<line x1=\\\"12\\\" y1=\\\"20\\\" x2=\\\"12\\\" y2=\\\"4\\\"></line><polyline points=\\\"6 10 12 4 18 10\\\"></polyline>\",\n\t\"at-sign\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"4\\\"></circle><path d=\\\"M16 12v1a3 3 0 0 0 6 0v-1a10 10 0 1 0-3.92 7.94\\\"></path>\",\n\t\"award\": \"<circle cx=\\\"12\\\" cy=\\\"8\\\" r=\\\"7\\\"></circle><polyline points=\\\"8.21 13.89 7 23 12 20 17 23 15.79 13.88\\\"></polyline>\",\n\t\"bar-chart-2\": \"<rect x=\\\"10\\\" y=\\\"3\\\" width=\\\"4\\\" height=\\\"18\\\"></rect><rect x=\\\"18\\\" y=\\\"8\\\" width=\\\"4\\\" height=\\\"13\\\"></rect><rect x=\\\"2\\\" y=\\\"13\\\" width=\\\"4\\\" height=\\\"8\\\"></rect>\",\n\t\"bar-chart\": \"<rect x=\\\"18\\\" y=\\\"3\\\" width=\\\"4\\\" height=\\\"18\\\"></rect><rect x=\\\"10\\\" y=\\\"8\\\" width=\\\"4\\\" height=\\\"13\\\"></rect><rect x=\\\"2\\\" y=\\\"13\\\" width=\\\"4\\\" height=\\\"8\\\"></rect>\",\n\t\"battery-charging\": \"<path d=\\\"M5 18H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h3.19M15 6h2a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-3.19\\\"></path><line x1=\\\"23\\\" y1=\\\"13\\\" x2=\\\"23\\\" y2=\\\"11\\\"></line><polyline points=\\\"11 6 7 12 13 12 9 18\\\"></polyline>\",\n\t\"battery\": \"<rect x=\\\"1\\\" y=\\\"6\\\" width=\\\"18\\\" height=\\\"12\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><line x1=\\\"23\\\" y1=\\\"13\\\" x2=\\\"23\\\" y2=\\\"11\\\"></line>\",\n\t\"bell-off\": \"<path d=\\\"M8.56 2.9A7 7 0 0 1 19 9v4m-2 4H2a3 3 0 0 0 3-3V9a7 7 0 0 1 .78-3.22M13.73 21a2 2 0 0 1-3.46 0\\\"></path><line x1=\\\"1\\\" y1=\\\"1\\\" x2=\\\"23\\\" y2=\\\"23\\\"></line>\",\n\t\"bell\": \"<path d=\\\"M22 17H2a3 3 0 0 0 3-3V9a7 7 0 0 1 14 0v5a3 3 0 0 0 3 3zm-8.27 4a2 2 0 0 1-3.46 0\\\"></path>\",\n\t\"bluetooth\": \"<polyline points=\\\"6.5 6.5 17.5 17.5 12 23 12 1 17.5 6.5 6.5 17.5\\\"></polyline>\",\n\t\"book\": \"<path d=\\\"M4 19.5A2.5 2.5 0 0 1 6.5 17H20\\\"></path><path d=\\\"M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z\\\"></path>\",\n\t\"bookmark\": \"<path d=\\\"M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z\\\"></path>\",\n\t\"box\": \"<path d=\\\"M12.89 1.45l8 4A2 2 0 0 1 22 7.24v9.53a2 2 0 0 1-1.11 1.79l-8 4a2 2 0 0 1-1.79 0l-8-4a2 2 0 0 1-1.1-1.8V7.24a2 2 0 0 1 1.11-1.79l8-4a2 2 0 0 1 1.78 0z\\\"></path><polyline points=\\\"2.32 6.16 12 11 21.68 6.16\\\"></polyline><line x1=\\\"12\\\" y1=\\\"22.76\\\" x2=\\\"12\\\" y2=\\\"11\\\"></line>\",\n\t\"briefcase\": \"<rect x=\\\"2\\\" y=\\\"7\\\" width=\\\"20\\\" height=\\\"14\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><path d=\\\"M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16\\\"></path>\",\n\t\"calendar\": \"<rect x=\\\"3\\\" y=\\\"4\\\" width=\\\"18\\\" height=\\\"18\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><line x1=\\\"16\\\" y1=\\\"2\\\" x2=\\\"16\\\" y2=\\\"6\\\"></line><line x1=\\\"8\\\" y1=\\\"2\\\" x2=\\\"8\\\" y2=\\\"6\\\"></line><line x1=\\\"3\\\" y1=\\\"10\\\" x2=\\\"21\\\" y2=\\\"10\\\"></line>\",\n\t\"camera-off\": \"<line x1=\\\"1\\\" y1=\\\"1\\\" x2=\\\"23\\\" y2=\\\"23\\\"></line><path d=\\\"M21 21H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h3m3-3h6l2 3h4a2 2 0 0 1 2 2v9.34m-7.72-2.06a4 4 0 1 1-5.56-5.56\\\"></path>\",\n\t\"camera\": \"<path d=\\\"M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z\\\"></path><circle cx=\\\"12\\\" cy=\\\"13\\\" r=\\\"4\\\"></circle>\",\n\t\"cast\": \"<path d=\\\"M2 16.1A5 5 0 0 1 5.9 20M2 12.05A9 9 0 0 1 9.95 20M2 8V6a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2h-6\\\"></path><line x1=\\\"2\\\" y1=\\\"20\\\" x2=\\\"2\\\" y2=\\\"20\\\"></line>\",\n\t\"check-circle\": \"<path d=\\\"M22 11.07V12a10 10 0 1 1-5.93-9.14\\\"></path><polyline points=\\\"23 3 12 14 9 11\\\"></polyline>\",\n\t\"check-square\": \"<polyline points=\\\"9 11 12 14 23 3\\\"></polyline><path d=\\\"M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11\\\"></path>\",\n\t\"check\": \"<polyline points=\\\"20 6 9 17 4 12\\\"></polyline>\",\n\t\"chevron-down\": \"<polyline points=\\\"6 9 12 15 18 9\\\"></polyline>\",\n\t\"chevron-left\": \"<polyline points=\\\"15 18 9 12 15 6\\\"></polyline>\",\n\t\"chevron-right\": \"<polyline points=\\\"9 18 15 12 9 6\\\"></polyline>\",\n\t\"chevron-up\": \"<polyline points=\\\"18 15 12 9 6 15\\\"></polyline>\",\n\t\"chevrons-down\": \"<polyline points=\\\"7 13 12 18 17 13\\\"></polyline><polyline points=\\\"7 6 12 11 17 6\\\"></polyline>\",\n\t\"chevrons-left\": \"<polyline points=\\\"11 17 6 12 11 7\\\"></polyline><polyline points=\\\"18 17 13 12 18 7\\\"></polyline>\",\n\t\"chevrons-right\": \"<polyline points=\\\"13 17 18 12 13 7\\\"></polyline><polyline points=\\\"6 17 11 12 6 7\\\"></polyline>\",\n\t\"chevrons-up\": \"<polyline points=\\\"17 11 12 6 7 11\\\"></polyline><polyline points=\\\"17 18 12 13 7 18\\\"></polyline>\",\n\t\"chrome\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"4\\\"></circle><line x1=\\\"21.17\\\" y1=\\\"8\\\" x2=\\\"12\\\" y2=\\\"8\\\"></line><line x1=\\\"3.95\\\" y1=\\\"6.06\\\" x2=\\\"8.54\\\" y2=\\\"14\\\"></line><line x1=\\\"10.88\\\" y1=\\\"21.94\\\" x2=\\\"15.46\\\" y2=\\\"14\\\"></line>\",\n\t\"circle\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle>\",\n\t\"clipboard\": \"<path d=\\\"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2\\\"></path><rect x=\\\"8\\\" y=\\\"2\\\" width=\\\"8\\\" height=\\\"4\\\" rx=\\\"1\\\" ry=\\\"1\\\"></rect>\",\n\t\"clock\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><polyline points=\\\"12 6 12 12 15 15\\\"></polyline>\",\n\t\"cloud-drizzle\": \"<line x1=\\\"8\\\" y1=\\\"19\\\" x2=\\\"8\\\" y2=\\\"21\\\"></line><line x1=\\\"8\\\" y1=\\\"13\\\" x2=\\\"8\\\" y2=\\\"15\\\"></line><line x1=\\\"16\\\" y1=\\\"19\\\" x2=\\\"16\\\" y2=\\\"21\\\"></line><line x1=\\\"16\\\" y1=\\\"13\\\" x2=\\\"16\\\" y2=\\\"15\\\"></line><line x1=\\\"12\\\" y1=\\\"21\\\" x2=\\\"12\\\" y2=\\\"23\\\"></line><line x1=\\\"12\\\" y1=\\\"15\\\" x2=\\\"12\\\" y2=\\\"17\\\"></line><path d=\\\"M20 16.58A5 5 0 0 0 18 7h-1.26A8 8 0 1 0 4 15.25\\\"></path>\",\n\t\"cloud-lightning\": \"<path d=\\\"M19 16.9A5 5 0 0 0 18 7h-1.26a8 8 0 1 0-11.62 9\\\"></path><polyline points=\\\"13 11 9 17 15 17 11 23\\\"></polyline>\",\n\t\"cloud-off\": \"<path d=\\\"M22.61 16.95A5 5 0 0 0 18 10h-1.26a8 8 0 0 0-7.05-6M5 5a8 8 0 0 0 4 15h9a5 5 0 0 0 1.7-.3\\\"></path><line x1=\\\"1\\\" y1=\\\"1\\\" x2=\\\"23\\\" y2=\\\"23\\\"></line>\",\n\t\"cloud-rain\": \"<line x1=\\\"16\\\" y1=\\\"13\\\" x2=\\\"16\\\" y2=\\\"21\\\"></line><line x1=\\\"8\\\" y1=\\\"13\\\" x2=\\\"8\\\" y2=\\\"21\\\"></line><line x1=\\\"12\\\" y1=\\\"15\\\" x2=\\\"12\\\" y2=\\\"23\\\"></line><path d=\\\"M20 16.58A5 5 0 0 0 18 7h-1.26A8 8 0 1 0 4 15.25\\\"></path>\",\n\t\"cloud-snow\": \"<path d=\\\"M20 17.58A5 5 0 0 0 18 8h-1.26A8 8 0 1 0 4 16.25\\\"></path><line x1=\\\"8\\\" y1=\\\"16\\\" x2=\\\"8\\\" y2=\\\"16\\\"></line><line x1=\\\"8\\\" y1=\\\"20\\\" x2=\\\"8\\\" y2=\\\"20\\\"></line><line x1=\\\"12\\\" y1=\\\"18\\\" x2=\\\"12\\\" y2=\\\"18\\\"></line><line x1=\\\"12\\\" y1=\\\"22\\\" x2=\\\"12\\\" y2=\\\"22\\\"></line><line x1=\\\"16\\\" y1=\\\"16\\\" x2=\\\"16\\\" y2=\\\"16\\\"></line><line x1=\\\"16\\\" y1=\\\"20\\\" x2=\\\"16\\\" y2=\\\"20\\\"></line>\",\n\t\"cloud\": \"<path d=\\\"M18 10h-1.26A8 8 0 1 0 9 20h9a5 5 0 0 0 0-10z\\\"></path>\",\n\t\"codepen\": \"<polygon points=\\\"12 2 22 8.5 22 15.5 12 22 2 15.5 2 8.5 12 2\\\"></polygon><line x1=\\\"12\\\" y1=\\\"22\\\" x2=\\\"12\\\" y2=\\\"15.5\\\"></line><polyline points=\\\"22 8.5 12 15.5 2 8.5\\\"></polyline><polyline points=\\\"2 15.5 12 8.5 22 15.5\\\"></polyline><line x1=\\\"12\\\" y1=\\\"2\\\" x2=\\\"12\\\" y2=\\\"8.5\\\"></line>\",\n\t\"command\": \"<path d=\\\"M18 3a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3 3 3 0 0 0 3-3 3 3 0 0 0-3-3H6a3 3 0 0 0-3 3 3 3 0 0 0 3 3 3 3 0 0 0 3-3V6a3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3h12a3 3 0 0 0 3-3 3 3 0 0 0-3-3z\\\"></path>\",\n\t\"compass\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><polygon points=\\\"16.24 7.76 14.12 14.12 7.76 16.24 9.88 9.88 16.24 7.76\\\"></polygon>\",\n\t\"copy\": \"<rect x=\\\"9\\\" y=\\\"9\\\" width=\\\"13\\\" height=\\\"13\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><path d=\\\"M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1\\\"></path>\",\n\t\"corner-down-left\": \"<polyline points=\\\"9 10 4 15 9 20\\\"></polyline><path d=\\\"M20 4v7a4 4 0 0 1-4 4H4\\\"></path>\",\n\t\"corner-down-right\": \"<polyline points=\\\"15 10 20 15 15 20\\\"></polyline><path d=\\\"M4 4v7a4 4 0 0 0 4 4h12\\\"></path>\",\n\t\"corner-left-down\": \"<polyline points=\\\"14 15 9 20 4 15\\\"></polyline><path d=\\\"M20 4h-7a4 4 0 0 0-4 4v12\\\"></path>\",\n\t\"corner-left-up\": \"<polyline points=\\\"14 9 9 4 4 9\\\"></polyline><path d=\\\"M20 20h-7a4 4 0 0 1-4-4V4\\\"></path>\",\n\t\"corner-right-down\": \"<polyline points=\\\"10 15 15 20 20 15\\\"></polyline><path d=\\\"M4 4h7a4 4 0 0 1 4 4v12\\\"></path>\",\n\t\"corner-right-up\": \"<polyline points=\\\"10 9 15 4 20 9\\\"></polyline><path d=\\\"M4 20h7a4 4 0 0 0 4-4V4\\\"></path>\",\n\t\"corner-up-left\": \"<polyline points=\\\"9 14 4 9 9 4\\\"></polyline><path d=\\\"M20 20v-7a4 4 0 0 0-4-4H4\\\"></path>\",\n\t\"corner-up-right\": \"<polyline points=\\\"15 14 20 9 15 4\\\"></polyline><path d=\\\"M4 20v-7a4 4 0 0 1 4-4h12\\\"></path>\",\n\t\"cpu\": \"<rect x=\\\"4\\\" y=\\\"4\\\" width=\\\"16\\\" height=\\\"16\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><rect x=\\\"9\\\" y=\\\"9\\\" width=\\\"6\\\" height=\\\"6\\\"></rect><line x1=\\\"9\\\" y1=\\\"1\\\" x2=\\\"9\\\" y2=\\\"4\\\"></line><line x1=\\\"15\\\" y1=\\\"1\\\" x2=\\\"15\\\" y2=\\\"4\\\"></line><line x1=\\\"9\\\" y1=\\\"20\\\" x2=\\\"9\\\" y2=\\\"23\\\"></line><line x1=\\\"15\\\" y1=\\\"20\\\" x2=\\\"15\\\" y2=\\\"23\\\"></line><line x1=\\\"20\\\" y1=\\\"9\\\" x2=\\\"23\\\" y2=\\\"9\\\"></line><line x1=\\\"20\\\" y1=\\\"14\\\" x2=\\\"23\\\" y2=\\\"14\\\"></line><line x1=\\\"1\\\" y1=\\\"9\\\" x2=\\\"4\\\" y2=\\\"9\\\"></line><line x1=\\\"1\\\" y1=\\\"14\\\" x2=\\\"4\\\" y2=\\\"14\\\"></line>\",\n\t\"credit-card\": \"<rect x=\\\"1\\\" y=\\\"4\\\" width=\\\"22\\\" height=\\\"16\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><line x1=\\\"1\\\" y1=\\\"10\\\" x2=\\\"23\\\" y2=\\\"10\\\"></line>\",\n\t\"crosshair\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><line x1=\\\"22\\\" y1=\\\"12\\\" x2=\\\"18\\\" y2=\\\"12\\\"></line><line x1=\\\"6\\\" y1=\\\"12\\\" x2=\\\"2\\\" y2=\\\"12\\\"></line><line x1=\\\"12\\\" y1=\\\"6\\\" x2=\\\"12\\\" y2=\\\"2\\\"></line><line x1=\\\"12\\\" y1=\\\"22\\\" x2=\\\"12\\\" y2=\\\"18\\\"></line>\",\n\t\"delete\": \"<path d=\\\"M21 4H8l-7 8 7 8h13a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2z\\\"></path><line x1=\\\"18\\\" y1=\\\"9\\\" x2=\\\"12\\\" y2=\\\"15\\\"></line><line x1=\\\"12\\\" y1=\\\"9\\\" x2=\\\"18\\\" y2=\\\"15\\\"></line>\",\n\t\"disc\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"3\\\"></circle>\",\n\t\"download-cloud\": \"<polyline points=\\\"8 17 12 21 16 17\\\"></polyline><line x1=\\\"12\\\" y1=\\\"12\\\" x2=\\\"12\\\" y2=\\\"21\\\"></line><path d=\\\"M20.88 18.09A5 5 0 0 0 18 9h-1.26A8 8 0 1 0 3 16.29\\\"></path>\",\n\t\"download\": \"<path d=\\\"M3 17v3a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-3\\\"></path><polyline points=\\\"8 12 12 16 16 12\\\"></polyline><line x1=\\\"12\\\" y1=\\\"2\\\" x2=\\\"12\\\" y2=\\\"16\\\"></line>\",\n\t\"droplet\": \"<path d=\\\"M12 2.69l5.66 5.66a8 8 0 1 1-11.31 0z\\\"></path>\",\n\t\"edit-2\": \"<polygon points=\\\"16 3 21 8 8 21 3 21 3 16 16 3\\\"></polygon>\",\n\t\"edit-3\": \"<polygon points=\\\"14 2 18 6 7 17 3 17 3 13 14 2\\\"></polygon><line x1=\\\"3\\\" y1=\\\"22\\\" x2=\\\"21\\\" y2=\\\"22\\\"></line>\",\n\t\"edit\": \"<path d=\\\"M20 14.66V20a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h5.34\\\"></path><polygon points=\\\"18 2 22 6 12 16 8 16 8 12 18 2\\\"></polygon>\",\n\t\"external-link\": \"<path d=\\\"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6\\\"></path><polyline points=\\\"15 3 21 3 21 9\\\"></polyline><line x1=\\\"10\\\" y1=\\\"14\\\" x2=\\\"21\\\" y2=\\\"3\\\"></line>\",\n\t\"eye-off\": \"<path d=\\\"M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24\\\"></path><line x1=\\\"1\\\" y1=\\\"1\\\" x2=\\\"23\\\" y2=\\\"23\\\"></line>\",\n\t\"eye\": \"<path d=\\\"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z\\\"></path><circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"3\\\"></circle>\",\n\t\"facebook\": \"<path d=\\\"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z\\\"></path>\",\n\t\"fast-forward\": \"<polygon points=\\\"13 19 22 12 13 5 13 19\\\"></polygon><polygon points=\\\"2 19 11 12 2 5 2 19\\\"></polygon>\",\n\t\"feather\": \"<path d=\\\"M20.24 12.24a6 6 0 0 0-8.49-8.49L5 10.5V19h8.5z\\\"></path><line x1=\\\"16\\\" y1=\\\"8\\\" x2=\\\"2\\\" y2=\\\"22\\\"></line><line x1=\\\"17\\\" y1=\\\"15\\\" x2=\\\"9\\\" y2=\\\"15\\\"></line>\",\n\t\"file-minus\": \"<path d=\\\"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z\\\"></path><polyline points=\\\"14 2 14 8 20 8\\\"></polyline><line x1=\\\"9\\\" y1=\\\"15\\\" x2=\\\"15\\\" y2=\\\"15\\\"></line>\",\n\t\"file-plus\": \"<path d=\\\"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z\\\"></path><polyline points=\\\"14 2 14 8 20 8\\\"></polyline><line x1=\\\"12\\\" y1=\\\"18\\\" x2=\\\"12\\\" y2=\\\"12\\\"></line><line x1=\\\"9\\\" y1=\\\"15\\\" x2=\\\"15\\\" y2=\\\"15\\\"></line>\",\n\t\"file-text\": \"<path d=\\\"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z\\\"></path><polyline points=\\\"14 2 14 8 20 8\\\"></polyline><line x1=\\\"16\\\" y1=\\\"13\\\" x2=\\\"8\\\" y2=\\\"13\\\"></line><line x1=\\\"16\\\" y1=\\\"17\\\" x2=\\\"8\\\" y2=\\\"17\\\"></line><polyline points=\\\"10 9 9 9 8 9\\\"></polyline>\",\n\t\"file\": \"<path d=\\\"M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z\\\"></path><polyline points=\\\"13 2 13 9 20 9\\\"></polyline>\",\n\t\"film\": \"<rect x=\\\"2\\\" y=\\\"2\\\" width=\\\"20\\\" height=\\\"20\\\" rx=\\\"2.18\\\" ry=\\\"2.18\\\"></rect><line x1=\\\"7\\\" y1=\\\"2\\\" x2=\\\"7\\\" y2=\\\"22\\\"></line><line x1=\\\"17\\\" y1=\\\"2\\\" x2=\\\"17\\\" y2=\\\"22\\\"></line><line x1=\\\"2\\\" y1=\\\"12\\\" x2=\\\"22\\\" y2=\\\"12\\\"></line><line x1=\\\"2\\\" y1=\\\"7\\\" x2=\\\"7\\\" y2=\\\"7\\\"></line><line x1=\\\"2\\\" y1=\\\"17\\\" x2=\\\"7\\\" y2=\\\"17\\\"></line><line x1=\\\"17\\\" y1=\\\"17\\\" x2=\\\"22\\\" y2=\\\"17\\\"></line><line x1=\\\"17\\\" y1=\\\"7\\\" x2=\\\"22\\\" y2=\\\"7\\\"></line>\",\n\t\"filter\": \"<polygon points=\\\"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3\\\"></polygon>\",\n\t\"flag\": \"<path d=\\\"M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z\\\"></path><line x1=\\\"4\\\" y1=\\\"22\\\" x2=\\\"4\\\" y2=\\\"15\\\"></line>\",\n\t\"folder\": \"<path d=\\\"M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z\\\"></path>\",\n\t\"github\": \"<path d=\\\"M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22\\\"></path>\",\n\t\"globe\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><line x1=\\\"2\\\" y1=\\\"12\\\" x2=\\\"22\\\" y2=\\\"12\\\"></line><path d=\\\"M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z\\\"></path>\",\n\t\"grid\": \"<rect x=\\\"3\\\" y=\\\"3\\\" width=\\\"7\\\" height=\\\"7\\\"></rect><rect x=\\\"14\\\" y=\\\"3\\\" width=\\\"7\\\" height=\\\"7\\\"></rect><rect x=\\\"14\\\" y=\\\"14\\\" width=\\\"7\\\" height=\\\"7\\\"></rect><rect x=\\\"3\\\" y=\\\"14\\\" width=\\\"7\\\" height=\\\"7\\\"></rect>\",\n\t\"hash\": \"<line x1=\\\"4\\\" y1=\\\"9\\\" x2=\\\"20\\\" y2=\\\"9\\\"></line><line x1=\\\"4\\\" y1=\\\"15\\\" x2=\\\"20\\\" y2=\\\"15\\\"></line><line x1=\\\"10\\\" y1=\\\"3\\\" x2=\\\"8\\\" y2=\\\"21\\\"></line><line x1=\\\"16\\\" y1=\\\"3\\\" x2=\\\"14\\\" y2=\\\"21\\\"></line>\",\n\t\"headphones\": \"<path d=\\\"M3 18v-6a9 9 0 0 1 18 0v6\\\"></path><path d=\\\"M21 19a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3zM3 19a2 2 0 0 0 2 2h1a2 2 0 0 0 2-2v-3a2 2 0 0 0-2-2H3z\\\"></path>\",\n\t\"heart\": \"<path d=\\\"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z\\\"></path>\",\n\t\"home\": \"<path d=\\\"M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\\\"></path><polyline points=\\\"9 22 9 12 15 12 15 22\\\"></polyline>\",\n\t\"image\": \"<rect x=\\\"3\\\" y=\\\"3\\\" width=\\\"18\\\" height=\\\"18\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><circle cx=\\\"8.5\\\" cy=\\\"8.5\\\" r=\\\"1.5\\\"></circle><polyline points=\\\"21 15 16 10 5 21\\\"></polyline>\",\n\t\"inbox\": \"<polyline points=\\\"22 13 16 13 14 16 10 16 8 13 2 13\\\"></polyline><path d=\\\"M5.47 5.19L2 13v5a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-5l-3.47-7.81A2 2 0 0 0 16.7 4H7.3a2 2 0 0 0-1.83 1.19z\\\"></path>\",\n\t\"info\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><line x1=\\\"12\\\" y1=\\\"16\\\" x2=\\\"12\\\" y2=\\\"12\\\"></line><line x1=\\\"12\\\" y1=\\\"8\\\" x2=\\\"12\\\" y2=\\\"8\\\"></line>\",\n\t\"instagram\": \"<rect x=\\\"2\\\" y=\\\"2\\\" width=\\\"20\\\" height=\\\"20\\\" rx=\\\"5\\\" ry=\\\"5\\\"></rect><path d=\\\"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z\\\"></path><line x1=\\\"17.5\\\" y1=\\\"6.5\\\" x2=\\\"17.5\\\" y2=\\\"6.5\\\"></line>\",\n\t\"layers\": \"<polygon points=\\\"12 2 2 7 12 12 22 7 12 2\\\"></polygon><polyline points=\\\"2 17 12 22 22 17\\\"></polyline><polyline points=\\\"2 12 12 17 22 12\\\"></polyline>\",\n\t\"layout\": \"<rect x=\\\"3\\\" y=\\\"3\\\" width=\\\"18\\\" height=\\\"18\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><line x1=\\\"3\\\" y1=\\\"9\\\" x2=\\\"21\\\" y2=\\\"9\\\"></line><line x1=\\\"9\\\" y1=\\\"21\\\" x2=\\\"9\\\" y2=\\\"9\\\"></line>\",\n\t\"life-buoy\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"4\\\"></circle><line x1=\\\"4.93\\\" y1=\\\"4.93\\\" x2=\\\"9.17\\\" y2=\\\"9.17\\\"></line><line x1=\\\"14.83\\\" y1=\\\"14.83\\\" x2=\\\"19.07\\\" y2=\\\"19.07\\\"></line><line x1=\\\"14.83\\\" y1=\\\"9.17\\\" x2=\\\"19.07\\\" y2=\\\"4.93\\\"></line><line x1=\\\"14.83\\\" y1=\\\"9.17\\\" x2=\\\"18.36\\\" y2=\\\"5.64\\\"></line><line x1=\\\"4.93\\\" y1=\\\"19.07\\\" x2=\\\"9.17\\\" y2=\\\"14.83\\\"></line>\",\n\t\"link-2\": \"<path d=\\\"M15 7h3a5 5 0 0 1 5 5 5 5 0 0 1-5 5h-3m-6 0H6a5 5 0 0 1-5-5 5 5 0 0 1 5-5h3\\\"></path><line x1=\\\"8\\\" y1=\\\"12\\\" x2=\\\"16\\\" y2=\\\"12\\\"></line>\",\n\t\"link\": \"<path d=\\\"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71\\\"></path><path d=\\\"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71\\\"></path>\",\n\t\"list\": \"<line x1=\\\"8\\\" y1=\\\"6\\\" x2=\\\"21\\\" y2=\\\"6\\\"></line><line x1=\\\"8\\\" y1=\\\"12\\\" x2=\\\"21\\\" y2=\\\"12\\\"></line><line x1=\\\"8\\\" y1=\\\"18\\\" x2=\\\"21\\\" y2=\\\"18\\\"></line><line x1=\\\"3\\\" y1=\\\"6\\\" x2=\\\"3\\\" y2=\\\"6\\\"></line><line x1=\\\"3\\\" y1=\\\"12\\\" x2=\\\"3\\\" y2=\\\"12\\\"></line><line x1=\\\"3\\\" y1=\\\"18\\\" x2=\\\"3\\\" y2=\\\"18\\\"></line>\",\n\t\"loader\": \"<line x1=\\\"12\\\" y1=\\\"2\\\" x2=\\\"12\\\" y2=\\\"6\\\"></line><line x1=\\\"12\\\" y1=\\\"18\\\" x2=\\\"12\\\" y2=\\\"22\\\"></line><line x1=\\\"4.93\\\" y1=\\\"4.93\\\" x2=\\\"7.76\\\" y2=\\\"7.76\\\"></line><line x1=\\\"16.24\\\" y1=\\\"16.24\\\" x2=\\\"19.07\\\" y2=\\\"19.07\\\"></line><line x1=\\\"2\\\" y1=\\\"12\\\" x2=\\\"6\\\" y2=\\\"12\\\"></line><line x1=\\\"18\\\" y1=\\\"12\\\" x2=\\\"22\\\" y2=\\\"12\\\"></line><line x1=\\\"4.93\\\" y1=\\\"19.07\\\" x2=\\\"7.76\\\" y2=\\\"16.24\\\"></line><line x1=\\\"16.24\\\" y1=\\\"7.76\\\" x2=\\\"19.07\\\" y2=\\\"4.93\\\"></line>\",\n\t\"lock\": \"<rect x=\\\"3\\\" y=\\\"11\\\" width=\\\"18\\\" height=\\\"11\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><path d=\\\"M7 11V7a5 5 0 0 1 10 0v4\\\"></path>\",\n\t\"log-in\": \"<path d=\\\"M14 22h5a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2h-5\\\"></path><polyline points=\\\"11 16 15 12 11 8\\\"></polyline><line x1=\\\"15\\\" y1=\\\"12\\\" x2=\\\"3\\\" y2=\\\"12\\\"></line>\",\n\t\"log-out\": \"<path d=\\\"M10 22H5a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h5\\\"></path><polyline points=\\\"17 16 21 12 17 8\\\"></polyline><line x1=\\\"21\\\" y1=\\\"12\\\" x2=\\\"9\\\" y2=\\\"12\\\"></line>\",\n\t\"mail\": \"<path d=\\\"M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z\\\"></path><polyline points=\\\"22,6 12,13 2,6\\\"></polyline>\",\n\t\"map-pin\": \"<path d=\\\"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z\\\"></path><circle cx=\\\"12\\\" cy=\\\"10\\\" r=\\\"3\\\"></circle>\",\n\t\"map\": \"<polygon points=\\\"1 6 1 22 8 18 16 22 23 18 23 2 16 6 8 2 1 6\\\"></polygon><line x1=\\\"8\\\" y1=\\\"2\\\" x2=\\\"8\\\" y2=\\\"18\\\"></line><line x1=\\\"16\\\" y1=\\\"6\\\" x2=\\\"16\\\" y2=\\\"22\\\"></line>\",\n\t\"maximize-2\": \"<polyline points=\\\"15 3 21 3 21 9\\\"></polyline><polyline points=\\\"9 21 3 21 3 15\\\"></polyline><line x1=\\\"21\\\" y1=\\\"3\\\" x2=\\\"14\\\" y2=\\\"10\\\"></line><line x1=\\\"3\\\" y1=\\\"21\\\" x2=\\\"10\\\" y2=\\\"14\\\"></line>\",\n\t\"maximize\": \"<path d=\\\"M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3\\\"></path>\",\n\t\"menu\": \"<line x1=\\\"3\\\" y1=\\\"12\\\" x2=\\\"21\\\" y2=\\\"12\\\"></line><line x1=\\\"3\\\" y1=\\\"6\\\" x2=\\\"21\\\" y2=\\\"6\\\"></line><line x1=\\\"3\\\" y1=\\\"18\\\" x2=\\\"21\\\" y2=\\\"18\\\"></line>\",\n\t\"message-circle\": \"<path d=\\\"M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z\\\"></path>\",\n\t\"message-square\": \"<path d=\\\"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z\\\"></path>\",\n\t\"mic-off\": \"<line x1=\\\"1\\\" y1=\\\"1\\\" x2=\\\"23\\\" y2=\\\"23\\\"></line><path d=\\\"M9 9v3a3 3 0 0 0 5.12 2.12M15 9.34V4a3 3 0 0 0-5.94-.6\\\"></path><path d=\\\"M17 16.95A7 7 0 0 1 5 12v-2m14 0v2a7 7 0 0 1-.11 1.23\\\"></path><line x1=\\\"12\\\" y1=\\\"19\\\" x2=\\\"12\\\" y2=\\\"23\\\"></line><line x1=\\\"8\\\" y1=\\\"23\\\" x2=\\\"16\\\" y2=\\\"23\\\"></line>\",\n\t\"mic\": \"<path d=\\\"M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z\\\"></path><path d=\\\"M19 10v2a7 7 0 0 1-14 0v-2\\\"></path><line x1=\\\"12\\\" y1=\\\"19\\\" x2=\\\"12\\\" y2=\\\"23\\\"></line><line x1=\\\"8\\\" y1=\\\"23\\\" x2=\\\"16\\\" y2=\\\"23\\\"></line>\",\n\t\"minimize-2\": \"<polyline points=\\\"4 14 10 14 10 20\\\"></polyline><polyline points=\\\"20 10 14 10 14 4\\\"></polyline><line x1=\\\"14\\\" y1=\\\"10\\\" x2=\\\"21\\\" y2=\\\"3\\\"></line><line x1=\\\"3\\\" y1=\\\"21\\\" x2=\\\"10\\\" y2=\\\"14\\\"></line>\",\n\t\"minimize\": \"<path d=\\\"M8 3v3a2 2 0 0 1-2 2H3m18 0h-3a2 2 0 0 1-2-2V3m0 18v-3a2 2 0 0 1 2-2h3M3 16h3a2 2 0 0 1 2 2v3\\\"></path>\",\n\t\"minus-circle\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><line x1=\\\"8\\\" y1=\\\"12\\\" x2=\\\"16\\\" y2=\\\"12\\\"></line>\",\n\t\"minus-square\": \"<rect x=\\\"3\\\" y=\\\"3\\\" width=\\\"18\\\" height=\\\"18\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><line x1=\\\"8\\\" y1=\\\"12\\\" x2=\\\"16\\\" y2=\\\"12\\\"></line>\",\n\t\"minus\": \"<line x1=\\\"5\\\" y1=\\\"12\\\" x2=\\\"19\\\" y2=\\\"12\\\"></line>\",\n\t\"monitor\": \"<rect x=\\\"2\\\" y=\\\"3\\\" width=\\\"20\\\" height=\\\"14\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><line x1=\\\"8\\\" y1=\\\"21\\\" x2=\\\"16\\\" y2=\\\"21\\\"></line><line x1=\\\"12\\\" y1=\\\"17\\\" x2=\\\"12\\\" y2=\\\"21\\\"></line>\",\n\t\"moon\": \"<path d=\\\"M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z\\\"></path>\",\n\t\"more-horizontal\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"2\\\"></circle><circle cx=\\\"20\\\" cy=\\\"12\\\" r=\\\"2\\\"></circle><circle cx=\\\"4\\\" cy=\\\"12\\\" r=\\\"2\\\"></circle>\",\n\t\"more-vertical\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"2\\\"></circle><circle cx=\\\"12\\\" cy=\\\"4\\\" r=\\\"2\\\"></circle><circle cx=\\\"12\\\" cy=\\\"20\\\" r=\\\"2\\\"></circle>\",\n\t\"move\": \"<polyline points=\\\"5 9 2 12 5 15\\\"></polyline><polyline points=\\\"9 5 12 2 15 5\\\"></polyline><polyline points=\\\"15 19 12 22 9 19\\\"></polyline><polyline points=\\\"19 9 22 12 19 15\\\"></polyline><line x1=\\\"2\\\" y1=\\\"12\\\" x2=\\\"22\\\" y2=\\\"12\\\"></line><line x1=\\\"12\\\" y1=\\\"2\\\" x2=\\\"12\\\" y2=\\\"22\\\"></line>\",\n\t\"music\": \"<path d=\\\"M9 17H5a2 2 0 0 0-2 2 2 2 0 0 0 2 2h2a2 2 0 0 0 2-2zm12-2h-4a2 2 0 0 0-2 2 2 2 0 0 0 2 2h2a2 2 0 0 0 2-2z\\\"></path><polyline points=\\\"9 17 9 5 21 3 21 15\\\"></polyline>\",\n\t\"navigation-2\": \"<polygon points=\\\"12 2 19 21 12 17 5 21 12 2\\\"></polygon>\",\n\t\"navigation\": \"<polygon points=\\\"3 11 22 2 13 21 11 13 3 11\\\"></polygon>\",\n\t\"octagon\": \"<polygon points=\\\"7.86 2 16.14 2 22 7.86 22 16.14 16.14 22 7.86 22 2 16.14 2 7.86 7.86 2\\\"></polygon>\",\n\t\"package\": \"<path d=\\\"M12.89 1.45l8 4A2 2 0 0 1 22 7.24v9.53a2 2 0 0 1-1.11 1.79l-8 4a2 2 0 0 1-1.79 0l-8-4a2 2 0 0 1-1.1-1.8V7.24a2 2 0 0 1 1.11-1.79l8-4a2 2 0 0 1 1.78 0z\\\"></path><polyline points=\\\"2.32 6.16 12 11 21.68 6.16\\\"></polyline><line x1=\\\"12\\\" y1=\\\"22.76\\\" x2=\\\"12\\\" y2=\\\"11\\\"></line><line x1=\\\"7\\\" y1=\\\"3.5\\\" x2=\\\"17\\\" y2=\\\"8.5\\\"></line>\",\n\t\"pause-circle\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><line x1=\\\"10\\\" y1=\\\"15\\\" x2=\\\"10\\\" y2=\\\"9\\\"></line><line x1=\\\"14\\\" y1=\\\"15\\\" x2=\\\"14\\\" y2=\\\"9\\\"></line>\",\n\t\"pause\": \"<rect x=\\\"6\\\" y=\\\"4\\\" width=\\\"4\\\" height=\\\"16\\\"></rect><rect x=\\\"14\\\" y=\\\"4\\\" width=\\\"4\\\" height=\\\"16\\\"></rect>\",\n\t\"percent\": \"<line x1=\\\"19\\\" y1=\\\"5\\\" x2=\\\"5\\\" y2=\\\"19\\\"></line><circle cx=\\\"6.5\\\" cy=\\\"6.5\\\" r=\\\"2.5\\\"></circle><circle cx=\\\"17.5\\\" cy=\\\"17.5\\\" r=\\\"2.5\\\"></circle>\",\n\t\"phone-call\": \"<path d=\\\"M15.05 5A5 5 0 0 1 19 8.95M15.05 1A9 9 0 0 1 23 8.94m-1 7.98v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z\\\"></path>\",\n\t\"phone-forwarded\": \"<polyline points=\\\"19 1 23 5 19 9\\\"></polyline><line x1=\\\"15\\\" y1=\\\"5\\\" x2=\\\"23\\\" y2=\\\"5\\\"></line><path d=\\\"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z\\\"></path>\",\n\t\"phone-incoming\": \"<polyline points=\\\"16 2 16 8 22 8\\\"></polyline><line x1=\\\"23\\\" y1=\\\"1\\\" x2=\\\"16\\\" y2=\\\"8\\\"></line><path d=\\\"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z\\\"></path>\",\n\t\"phone-missed\": \"<line x1=\\\"23\\\" y1=\\\"1\\\" x2=\\\"17\\\" y2=\\\"7\\\"></line><line x1=\\\"17\\\" y1=\\\"1\\\" x2=\\\"23\\\" y2=\\\"7\\\"></line><path d=\\\"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z\\\"></path>\",\n\t\"phone-off\": \"<path d=\\\"M10.68 13.31a16 16 0 0 0 3.41 2.6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7 2 2 0 0 1 1.72 2v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.42 19.42 0 0 1-3.33-2.67m-2.67-3.34a19.79 19.79 0 0 1-3.07-8.63A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91\\\"></path><line x1=\\\"23\\\" y1=\\\"1\\\" x2=\\\"1\\\" y2=\\\"23\\\"></line>\",\n\t\"phone-outgoing\": \"<polyline points=\\\"23 7 23 1 17 1\\\"></polyline><line x1=\\\"16\\\" y1=\\\"8\\\" x2=\\\"23\\\" y2=\\\"1\\\"></line><path d=\\\"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z\\\"></path>\",\n\t\"phone\": \"<path d=\\\"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z\\\"></path>\",\n\t\"pie-chart\": \"<path d=\\\"M21.21 15.89A10 10 0 1 1 8 2.83\\\"></path><path d=\\\"M22 12A10 10 0 0 0 12 2v10z\\\"></path>\",\n\t\"play-circle\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><polygon points=\\\"10 8 16 12 10 16 10 8\\\"></polygon>\",\n\t\"play\": \"<polygon points=\\\"5 3 19 12 5 21 5 3\\\"></polygon>\",\n\t\"plus-circle\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><line x1=\\\"12\\\" y1=\\\"8\\\" x2=\\\"12\\\" y2=\\\"16\\\"></line><line x1=\\\"8\\\" y1=\\\"12\\\" x2=\\\"16\\\" y2=\\\"12\\\"></line>\",\n\t\"plus-square\": \"<rect x=\\\"3\\\" y=\\\"3\\\" width=\\\"18\\\" height=\\\"18\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><line x1=\\\"12\\\" y1=\\\"8\\\" x2=\\\"12\\\" y2=\\\"16\\\"></line><line x1=\\\"8\\\" y1=\\\"12\\\" x2=\\\"16\\\" y2=\\\"12\\\"></line>\",\n\t\"plus\": \"<line x1=\\\"12\\\" y1=\\\"5\\\" x2=\\\"12\\\" y2=\\\"19\\\"></line><line x1=\\\"5\\\" y1=\\\"12\\\" x2=\\\"19\\\" y2=\\\"12\\\"></line>\",\n\t\"pocket\": \"<path d=\\\"M4 3h16a2 2 0 0 1 2 2v6a10 10 0 0 1-10 10A10 10 0 0 1 2 11V5a2 2 0 0 1 2-2z\\\"></path><polyline points=\\\"8 10 12 14 16 10\\\"></polyline>\",\n\t\"power\": \"<path d=\\\"M18.36 6.64a9 9 0 1 1-12.73 0\\\"></path><line x1=\\\"12\\\" y1=\\\"2\\\" x2=\\\"12\\\" y2=\\\"12\\\"></line>\",\n\t\"printer\": \"<polyline points=\\\"6 9 6 2 18 2 18 9\\\"></polyline><path d=\\\"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2\\\"></path><rect x=\\\"6\\\" y=\\\"14\\\" width=\\\"12\\\" height=\\\"8\\\"></rect>\",\n\t\"radio\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"2\\\"></circle><path d=\\\"M16.24 7.76a6 6 0 0 1 0 8.49m-8.48-.01a6 6 0 0 1 0-8.49m11.31-2.82a10 10 0 0 1 0 14.14m-14.14 0a10 10 0 0 1 0-14.14\\\"></path>\",\n\t\"refresh-ccw\": \"<polyline points=\\\"1 4 1 10 7 10\\\"></polyline><polyline points=\\\"23 20 23 14 17 14\\\"></polyline><path d=\\\"M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15\\\"></path>\",\n\t\"refresh-cw\": \"<polyline points=\\\"23 4 23 10 17 10\\\"></polyline><polyline points=\\\"1 20 1 14 7 14\\\"></polyline><path d=\\\"M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15\\\"></path>\",\n\t\"repeat\": \"<polyline points=\\\"17 1 21 5 17 9\\\"></polyline><path d=\\\"M3 11V9a4 4 0 0 1 4-4h14\\\"></path><polyline points=\\\"7 23 3 19 7 15\\\"></polyline><path d=\\\"M21 13v2a4 4 0 0 1-4 4H3\\\"></path>\",\n\t\"rewind\": \"<polygon points=\\\"11 19 2 12 11 5 11 19\\\"></polygon><polygon points=\\\"22 19 13 12 22 5 22 19\\\"></polygon>\",\n\t\"rotate-ccw\": \"<polyline points=\\\"1 4 1 10 7 10\\\"></polyline><path d=\\\"M3.51 15a9 9 0 1 0 2.13-9.36L1 10\\\"></path>\",\n\t\"rotate-cw\": \"<polyline points=\\\"23 4 23 10 17 10\\\"></polyline><path d=\\\"M20.49 15a9 9 0 1 1-2.12-9.36L23 10\\\"></path>\",\n\t\"save\": \"<path d=\\\"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z\\\"></path><polyline points=\\\"17 21 17 13 7 13 7 21\\\"></polyline><polyline points=\\\"7 3 7 8 15 8\\\"></polyline>\",\n\t\"scissors\": \"<circle cx=\\\"6\\\" cy=\\\"6\\\" r=\\\"3\\\"></circle><circle cx=\\\"6\\\" cy=\\\"18\\\" r=\\\"3\\\"></circle><line x1=\\\"20\\\" y1=\\\"4\\\" x2=\\\"8.12\\\" y2=\\\"15.88\\\"></line><line x1=\\\"14.47\\\" y1=\\\"14.48\\\" x2=\\\"20\\\" y2=\\\"20\\\"></line><line x1=\\\"8.12\\\" y1=\\\"8.12\\\" x2=\\\"12\\\" y2=\\\"12\\\"></line>\",\n\t\"search\": \"<circle cx=\\\"10.5\\\" cy=\\\"10.5\\\" r=\\\"7.5\\\"></circle><line x1=\\\"21\\\" y1=\\\"21\\\" x2=\\\"15.8\\\" y2=\\\"15.8\\\"></line>\",\n\t\"server\": \"<rect x=\\\"2\\\" y=\\\"2\\\" width=\\\"20\\\" height=\\\"8\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><rect x=\\\"2\\\" y=\\\"14\\\" width=\\\"20\\\" height=\\\"8\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><line x1=\\\"6\\\" y1=\\\"6\\\" x2=\\\"6\\\" y2=\\\"6\\\"></line><line x1=\\\"6\\\" y1=\\\"18\\\" x2=\\\"6\\\" y2=\\\"18\\\"></line>\",\n\t\"settings\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"3\\\"></circle><path d=\\\"M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z\\\"></path>\",\n\t\"share-2\": \"<circle cx=\\\"18\\\" cy=\\\"5\\\" r=\\\"3\\\"></circle><circle cx=\\\"6\\\" cy=\\\"12\\\" r=\\\"3\\\"></circle><circle cx=\\\"18\\\" cy=\\\"19\\\" r=\\\"3\\\"></circle><line x1=\\\"8.59\\\" y1=\\\"13.51\\\" x2=\\\"15.42\\\" y2=\\\"17.49\\\"></line><line x1=\\\"15.41\\\" y1=\\\"6.51\\\" x2=\\\"8.59\\\" y2=\\\"10.49\\\"></line>\",\n\t\"share\": \"<path d=\\\"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8\\\"></path><polyline points=\\\"16 6 12 2 8 6\\\"></polyline><line x1=\\\"12\\\" y1=\\\"2\\\" x2=\\\"12\\\" y2=\\\"15\\\"></line>\",\n\t\"shield\": \"<path d=\\\"M12 22s8-4 8-10V4l-8-2-8 2v8c0 6 8 10 8 10z\\\"></path>\",\n\t\"shuffle\": \"<polyline points=\\\"16 3 21 3 21 8\\\"></polyline><line x1=\\\"4\\\" y1=\\\"20\\\" x2=\\\"21\\\" y2=\\\"3\\\"></line><polyline points=\\\"21 16 21 21 16 21\\\"></polyline><line x1=\\\"15\\\" y1=\\\"15\\\" x2=\\\"21\\\" y2=\\\"21\\\"></line><line x1=\\\"4\\\" y1=\\\"4\\\" x2=\\\"9\\\" y2=\\\"9\\\"></line>\",\n\t\"sidebar\": \"<rect x=\\\"3\\\" y=\\\"3\\\" width=\\\"18\\\" height=\\\"18\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><line x1=\\\"9\\\" y1=\\\"3\\\" x2=\\\"9\\\" y2=\\\"21\\\"></line>\",\n\t\"skip-back\": \"<polygon points=\\\"19 20 9 12 19 4 19 20\\\"></polygon><line x1=\\\"5\\\" y1=\\\"19\\\" x2=\\\"5\\\" y2=\\\"5\\\"></line>\",\n\t\"skip-forward\": \"<polygon points=\\\"5 4 15 12 5 20 5 4\\\"></polygon><line x1=\\\"19\\\" y1=\\\"5\\\" x2=\\\"19\\\" y2=\\\"19\\\"></line>\",\n\t\"slack\": \"<path d=\\\"M22.08 9C19.81 1.41 16.54-.35 9 1.92S-.35 7.46 1.92 15 7.46 24.35 15 22.08 24.35 16.54 22.08 9z\\\"></path><line x1=\\\"12.57\\\" y1=\\\"5.99\\\" x2=\\\"16.15\\\" y2=\\\"16.39\\\"></line><line x1=\\\"7.85\\\" y1=\\\"7.61\\\" x2=\\\"11.43\\\" y2=\\\"18.01\\\"></line><line x1=\\\"16.39\\\" y1=\\\"7.85\\\" x2=\\\"5.99\\\" y2=\\\"11.43\\\"></line><line x1=\\\"18.01\\\" y1=\\\"12.57\\\" x2=\\\"7.61\\\" y2=\\\"16.15\\\"></line>\",\n\t\"slash\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><line x1=\\\"4.93\\\" y1=\\\"4.93\\\" x2=\\\"19.07\\\" y2=\\\"19.07\\\"></line>\",\n\t\"smartphone\": \"<rect x=\\\"5\\\" y=\\\"2\\\" width=\\\"14\\\" height=\\\"20\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><line x1=\\\"12\\\" y1=\\\"18\\\" x2=\\\"12\\\" y2=\\\"18\\\"></line>\",\n\t\"speaker\": \"<rect x=\\\"4\\\" y=\\\"2\\\" width=\\\"16\\\" height=\\\"20\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><circle cx=\\\"12\\\" cy=\\\"14\\\" r=\\\"4\\\"></circle><line x1=\\\"12\\\" y1=\\\"6\\\" x2=\\\"12\\\" y2=\\\"6\\\"></line>\",\n\t\"square\": \"<rect x=\\\"3\\\" y=\\\"3\\\" width=\\\"18\\\" height=\\\"18\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect>\",\n\t\"star\": \"<polygon points=\\\"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2\\\"></polygon>\",\n\t\"stop-circle\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><rect x=\\\"9\\\" y=\\\"9\\\" width=\\\"6\\\" height=\\\"6\\\"></rect>\",\n\t\"sun\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"5\\\"></circle><line x1=\\\"12\\\" y1=\\\"1\\\" x2=\\\"12\\\" y2=\\\"3\\\"></line><line x1=\\\"12\\\" y1=\\\"21\\\" x2=\\\"12\\\" y2=\\\"23\\\"></line><line x1=\\\"4.22\\\" y1=\\\"4.22\\\" x2=\\\"5.64\\\" y2=\\\"5.64\\\"></line><line x1=\\\"18.36\\\" y1=\\\"18.36\\\" x2=\\\"19.78\\\" y2=\\\"19.78\\\"></line><line x1=\\\"1\\\" y1=\\\"12\\\" x2=\\\"3\\\" y2=\\\"12\\\"></line><line x1=\\\"21\\\" y1=\\\"12\\\" x2=\\\"23\\\" y2=\\\"12\\\"></line><line x1=\\\"4.22\\\" y1=\\\"19.78\\\" x2=\\\"5.64\\\" y2=\\\"18.36\\\"></line><line x1=\\\"18.36\\\" y1=\\\"5.64\\\" x2=\\\"19.78\\\" y2=\\\"4.22\\\"></line>\",\n\t\"sunrise\": \"<path d=\\\"M17 18a5 5 0 0 0-10 0\\\"></path><line x1=\\\"12\\\" y1=\\\"2\\\" x2=\\\"12\\\" y2=\\\"9\\\"></line><line x1=\\\"4.22\\\" y1=\\\"10.22\\\" x2=\\\"5.64\\\" y2=\\\"11.64\\\"></line><line x1=\\\"1\\\" y1=\\\"18\\\" x2=\\\"3\\\" y2=\\\"18\\\"></line><line x1=\\\"21\\\" y1=\\\"18\\\" x2=\\\"23\\\" y2=\\\"18\\\"></line><line x1=\\\"18.36\\\" y1=\\\"11.64\\\" x2=\\\"19.78\\\" y2=\\\"10.22\\\"></line><line x1=\\\"23\\\" y1=\\\"22\\\" x2=\\\"1\\\" y2=\\\"22\\\"></line><polyline points=\\\"8 6 12 2 16 6\\\"></polyline>\",\n\t\"sunset\": \"<path d=\\\"M17 18a5 5 0 0 0-10 0\\\"></path><line x1=\\\"12\\\" y1=\\\"9\\\" x2=\\\"12\\\" y2=\\\"2\\\"></line><line x1=\\\"4.22\\\" y1=\\\"10.22\\\" x2=\\\"5.64\\\" y2=\\\"11.64\\\"></line><line x1=\\\"1\\\" y1=\\\"18\\\" x2=\\\"3\\\" y2=\\\"18\\\"></line><line x1=\\\"21\\\" y1=\\\"18\\\" x2=\\\"23\\\" y2=\\\"18\\\"></line><line x1=\\\"18.36\\\" y1=\\\"11.64\\\" x2=\\\"19.78\\\" y2=\\\"10.22\\\"></line><line x1=\\\"23\\\" y1=\\\"22\\\" x2=\\\"1\\\" y2=\\\"22\\\"></line><polyline points=\\\"16 5 12 9 8 5\\\"></polyline>\",\n\t\"tablet\": \"<rect x=\\\"4\\\" y=\\\"2\\\" width=\\\"16\\\" height=\\\"20\\\" rx=\\\"2\\\" ry=\\\"2\\\" transform=\\\"rotate(180 12 12)\\\"></rect><line x1=\\\"12\\\" y1=\\\"18\\\" x2=\\\"12\\\" y2=\\\"18\\\"></line>\",\n\t\"tag\": \"<path d=\\\"M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z\\\"></path><line x1=\\\"7\\\" y1=\\\"7\\\" x2=\\\"7\\\" y2=\\\"7\\\"></line>\",\n\t\"target\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"6\\\"></circle><circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"2\\\"></circle>\",\n\t\"thermometer\": \"<path d=\\\"M14 14.76V3.5a2.5 2.5 0 0 0-5 0v11.26a4.5 4.5 0 1 0 5 0z\\\"></path>\",\n\t\"thumbs-down\": \"<path d=\\\"M10 15v4a3 3 0 0 0 3 3l4-9V2H5.72a2 2 0 0 0-2 1.7l-1.38 9a2 2 0 0 0 2 2.3zm7-13h2.67A2.31 2.31 0 0 1 22 4v7a2.31 2.31 0 0 1-2.33 2H17\\\"></path>\",\n\t\"thumbs-up\": \"<path d=\\\"M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3\\\"></path>\",\n\t\"toggle-left\": \"<rect x=\\\"1\\\" y=\\\"5\\\" width=\\\"22\\\" height=\\\"14\\\" rx=\\\"7\\\" ry=\\\"7\\\"></rect><circle cx=\\\"8\\\" cy=\\\"12\\\" r=\\\"3\\\"></circle>\",\n\t\"toggle-right\": \"<rect x=\\\"1\\\" y=\\\"5\\\" width=\\\"22\\\" height=\\\"14\\\" rx=\\\"7\\\" ry=\\\"7\\\"></rect><circle cx=\\\"16\\\" cy=\\\"12\\\" r=\\\"3\\\"></circle>\",\n\t\"trash-2\": \"<polyline points=\\\"3 6 5 6 21 6\\\"></polyline><path d=\\\"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2\\\"></path><line x1=\\\"10\\\" y1=\\\"11\\\" x2=\\\"10\\\" y2=\\\"17\\\"></line><line x1=\\\"14\\\" y1=\\\"11\\\" x2=\\\"14\\\" y2=\\\"17\\\"></line>\",\n\t\"trash\": \"<polyline points=\\\"3 6 5 6 21 6\\\"></polyline><path d=\\\"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2\\\"></path>\",\n\t\"trending-down\": \"<polyline points=\\\"23 18 13.5 8.5 8.5 13.5 1 6\\\"></polyline><polyline points=\\\"17 18 23 18 23 12\\\"></polyline>\",\n\t\"trending-up\": \"<polyline points=\\\"23 6 13.5 15.5 8.5 10.5 1 18\\\"></polyline><polyline points=\\\"17 6 23 6 23 12\\\"></polyline>\",\n\t\"triangle\": \"<path d=\\\"M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z\\\"></path>\",\n\t\"twitter\": \"<path d=\\\"M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z\\\"></path>\",\n\t\"type\": \"<polyline points=\\\"4 7 4 4 20 4 20 7\\\"></polyline><line x1=\\\"9\\\" y1=\\\"20\\\" x2=\\\"15\\\" y2=\\\"20\\\"></line><line x1=\\\"12\\\" y1=\\\"4\\\" x2=\\\"12\\\" y2=\\\"20\\\"></line>\",\n\t\"umbrella\": \"<path d=\\\"M23 12a11.05 11.05 0 0 0-22 0zm-5 7a3 3 0 0 1-6 0v-7\\\"></path>\",\n\t\"unlock\": \"<rect x=\\\"3\\\" y=\\\"11\\\" width=\\\"18\\\" height=\\\"11\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><path d=\\\"M7 11V7a5 5 0 0 1 9.9-1\\\"></path>\",\n\t\"upload-cloud\": \"<polyline points=\\\"16 16 12 12 8 16\\\"></polyline><line x1=\\\"12\\\" y1=\\\"12\\\" x2=\\\"12\\\" y2=\\\"21\\\"></line><path d=\\\"M20.39 18.39A5 5 0 0 0 18 9h-1.26A8 8 0 1 0 3 16.3\\\"></path><polyline points=\\\"16 16 12 12 8 16\\\"></polyline>\",\n\t\"upload\": \"<path d=\\\"M3 17v3a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-3\\\"></path><polyline points=\\\"16 6 12 2 8 6\\\"></polyline><line x1=\\\"12\\\" y1=\\\"2\\\" x2=\\\"12\\\" y2=\\\"16\\\"></line>\",\n\t\"user-check\": \"<path d=\\\"M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\\\"></path><circle cx=\\\"8.5\\\" cy=\\\"7\\\" r=\\\"4\\\"></circle><polyline points=\\\"17 11 19 13 23 9\\\"></polyline>\",\n\t\"user-minus\": \"<path d=\\\"M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\\\"></path><circle cx=\\\"8.5\\\" cy=\\\"7\\\" r=\\\"4\\\"></circle><line x1=\\\"23\\\" y1=\\\"11\\\" x2=\\\"17\\\" y2=\\\"11\\\"></line>\",\n\t\"user-plus\": \"<path d=\\\"M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\\\"></path><circle cx=\\\"8.5\\\" cy=\\\"7\\\" r=\\\"4\\\"></circle><line x1=\\\"20\\\" y1=\\\"8\\\" x2=\\\"20\\\" y2=\\\"14\\\"></line><line x1=\\\"23\\\" y1=\\\"11\\\" x2=\\\"17\\\" y2=\\\"11\\\"></line>\",\n\t\"user-x\": \"<path d=\\\"M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\\\"></path><circle cx=\\\"8.5\\\" cy=\\\"7\\\" r=\\\"4\\\"></circle><line x1=\\\"18\\\" y1=\\\"8\\\" x2=\\\"23\\\" y2=\\\"13\\\"></line><line x1=\\\"23\\\" y1=\\\"8\\\" x2=\\\"18\\\" y2=\\\"13\\\"></line>\",\n\t\"user\": \"<path d=\\\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\\\"></path><circle cx=\\\"12\\\" cy=\\\"7\\\" r=\\\"4\\\"></circle>\",\n\t\"users\": \"<path d=\\\"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\\\"></path><circle cx=\\\"9\\\" cy=\\\"7\\\" r=\\\"4\\\"></circle><path d=\\\"M23 21v-2a4 4 0 0 0-3-3.87\\\"></path><path d=\\\"M16 3.13a4 4 0 0 1 0 7.75\\\"></path>\",\n\t\"video-off\": \"<path d=\\\"M16 16v1a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V7a2 2 0 0 1 2-2h2m5.66 0H14a2 2 0 0 1 2 2v3.34l1 1L23 7v10\\\"></path><line x1=\\\"1\\\" y1=\\\"1\\\" x2=\\\"23\\\" y2=\\\"23\\\"></line>\",\n\t\"video\": \"<polygon points=\\\"23 7 16 12 23 17 23 7\\\"></polygon><rect x=\\\"1\\\" y=\\\"5\\\" width=\\\"15\\\" height=\\\"14\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect>\",\n\t\"voicemail\": \"<circle cx=\\\"5.5\\\" cy=\\\"11.5\\\" r=\\\"4.5\\\"></circle><circle cx=\\\"18.5\\\" cy=\\\"11.5\\\" r=\\\"4.5\\\"></circle><line x1=\\\"5.5\\\" y1=\\\"16\\\" x2=\\\"18.5\\\" y2=\\\"16\\\"></line>\",\n\t\"volume-1\": \"<polygon points=\\\"11 5 6 9 2 9 2 15 6 15 11 19 11 5\\\"></polygon><path d=\\\"M15.54 8.46a5 5 0 0 1 0 7.07\\\"></path>\",\n\t\"volume-2\": \"<polygon points=\\\"11 5 6 9 2 9 2 15 6 15 11 19 11 5\\\"></polygon><path d=\\\"M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07\\\"></path>\",\n\t\"volume-x\": \"<polygon points=\\\"11 5 6 9 2 9 2 15 6 15 11 19 11 5\\\"></polygon><line x1=\\\"23\\\" y1=\\\"9\\\" x2=\\\"17\\\" y2=\\\"15\\\"></line><line x1=\\\"17\\\" y1=\\\"9\\\" x2=\\\"23\\\" y2=\\\"15\\\"></line>\",\n\t\"volume\": \"<polygon points=\\\"11 5 6 9 2 9 2 15 6 15 11 19 11 5\\\"></polygon>\",\n\t\"watch\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"7\\\"></circle><polyline points=\\\"12 9 12 12 13.5 13.5\\\"></polyline><path d=\\\"M16.51 17.35l-.35 3.83a2 2 0 0 1-2 1.82H9.83a2 2 0 0 1-2-1.82l-.35-3.83m.01-10.7l.35-3.83A2 2 0 0 1 9.83 1h4.35a2 2 0 0 1 2 1.82l.35 3.83\\\"></path>\",\n\t\"wifi\": \"<path d=\\\"M5 11.55a11 11 0 0 1 14.08 0M1.41 8a16 16 0 0 1 21.17 0M8.52 15.11a6 6 0 0 1 6.95 0\\\"></path><line x1=\\\"12\\\" y1=\\\"19\\\" x2=\\\"12\\\" y2=\\\"19\\\"></line>\",\n\t\"wind\": \"<path d=\\\"M9.59 4.59A2 2 0 1 1 11 8H2m10.59 11.41A2 2 0 1 0 14 16H2m15.73-8.27A2.5 2.5 0 1 1 19.5 12H2\\\"></path>\",\n\t\"x-circle\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><line x1=\\\"15\\\" y1=\\\"9\\\" x2=\\\"9\\\" y2=\\\"15\\\"></line><line x1=\\\"9\\\" y1=\\\"9\\\" x2=\\\"15\\\" y2=\\\"15\\\"></line>\",\n\t\"x-square\": \"<rect x=\\\"3\\\" y=\\\"3\\\" width=\\\"18\\\" height=\\\"18\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><line x1=\\\"9\\\" y1=\\\"9\\\" x2=\\\"15\\\" y2=\\\"15\\\"></line><line x1=\\\"15\\\" y1=\\\"9\\\" x2=\\\"9\\\" y2=\\\"15\\\"></line>\",\n\t\"x\": \"<line x1=\\\"18\\\" y1=\\\"6\\\" x2=\\\"6\\\" y2=\\\"18\\\"></line><line x1=\\\"6\\\" y1=\\\"6\\\" x2=\\\"18\\\" y2=\\\"18\\\"></line>\",\n\t\"zap\": \"<polygon points=\\\"13 2 3 14 12 14 11 22 21 10 12 10 13 2\\\"></polygon>\",\n\t\"zoom-in\": \"<circle cx=\\\"11\\\" cy=\\\"11\\\" r=\\\"8\\\"></circle><line x1=\\\"21\\\" y1=\\\"21\\\" x2=\\\"16.65\\\" y2=\\\"16.65\\\"></line><line x1=\\\"11\\\" y1=\\\"8\\\" x2=\\\"11\\\" y2=\\\"14\\\"></line><line x1=\\\"8\\\" y1=\\\"11\\\" x2=\\\"14\\\" y2=\\\"11\\\"></line>\",\n\t\"zoom-out\": \"<circle cx=\\\"11\\\" cy=\\\"11\\\" r=\\\"8\\\"></circle><line x1=\\\"21\\\" y1=\\\"21\\\" x2=\\\"16.65\\\" y2=\\\"16.65\\\"></line><line x1=\\\"8\\\" y1=\\\"11\\\" x2=\\\"14\\\" y2=\\\"11\\\"></line>\"\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./dist/icons.json\n// module id = 26\n// module chunks = 0", "'use strict';\nvar $at  = require('./_string-at')(true);\n\n// 21.1.3.27 String.prototype[@@iterator]()\nrequire('./_iter-define')(String, 'String', function(iterated){\n  this._t = String(iterated); // target\n  this._i = 0;                // next index\n// ********.1 %StringIteratorPrototype%.next()\n}, function(){\n  var O     = this._t\n    , index = this._i\n    , point;\n  if(index >= O.length)return {value: undefined, done: true};\n  point = $at(O, index);\n  this._i += point.length;\n  return {value: point, done: false};\n});\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/es6.string.iterator.js\n// module id = 27\n// module chunks = 0", "module.exports = !require('./_descriptors') && !require('./_fails')(function(){\n  return Object.defineProperty(require('./_dom-create')('div'), 'a', {get: function(){ return 7; }}).a != 7;\n});\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_ie8-dom-define.js\n// module id = 28\n// module chunks = 0", "var isObject = require('./_is-object')\n  , document = require('./_global').document\n  // in old IE typeof document.createElement is 'object'\n  , is = isObject(document) && isObject(document.createElement);\nmodule.exports = function(it){\n  return is ? document.createElement(it) : {};\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_dom-create.js\n// module id = 29\n// module chunks = 0", "// 7.1.1 ToPrimitive(input [, PreferredType])\nvar isObject = require('./_is-object');\n// instead of the ES6 spec version, we didn't implement @@toPrimitive case\n// and the second argument - flag - preferred type is a string\nmodule.exports = function(it, S){\n  if(!isObject(it))return it;\n  var fn, val;\n  if(S && typeof (fn = it.toString) == 'function' && !isObject(val = fn.call(it)))return val;\n  if(typeof (fn = it.valueOf) == 'function' && !isObject(val = fn.call(it)))return val;\n  if(!S && typeof (fn = it.toString) == 'function' && !isObject(val = fn.call(it)))return val;\n  throw TypeError(\"Can't convert object to primitive value\");\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_to-primitive.js\n// module id = 30\n// module chunks = 0", "// 19.1.2.2 / 15.2.3.5 Object.create(O [, Properties])\nvar anObject    = require('./_an-object')\n  , dPs         = require('./_object-dps')\n  , enumBugKeys = require('./_enum-bug-keys')\n  , IE_PROTO    = require('./_shared-key')('IE_PROTO')\n  , Empty       = function(){ /* empty */ }\n  , PROTOTYPE   = 'prototype';\n\n// Create object with fake `null` prototype: use iframe Object with cleared prototype\nvar createDict = function(){\n  // Thrash, waste and sodomy: IE GC bug\n  var iframe = require('./_dom-create')('iframe')\n    , i      = enumBugKeys.length\n    , lt     = '<'\n    , gt     = '>'\n    , iframeDocument;\n  iframe.style.display = 'none';\n  require('./_html').appendChild(iframe);\n  iframe.src = 'javascript:'; // eslint-disable-line no-script-url\n  // createDict = iframe.contentWindow.Object;\n  // html.removeChild(iframe);\n  iframeDocument = iframe.contentWindow.document;\n  iframeDocument.open();\n  iframeDocument.write(lt + 'script' + gt + 'document.F=Object' + lt + '/script' + gt);\n  iframeDocument.close();\n  createDict = iframeDocument.F;\n  while(i--)delete createDict[PROTOTYPE][enumBugKeys[i]];\n  return createDict();\n};\n\nmodule.exports = Object.create || function create(O, Properties){\n  var result;\n  if(O !== null){\n    Empty[PROTOTYPE] = anObject(O);\n    result = new Empty;\n    Empty[PROTOTYPE] = null;\n    // add \"__proto__\" for Object.getPrototypeOf polyfill\n    result[IE_PROTO] = O;\n  } else result = createDict();\n  return Properties === undefined ? result : dPs(result, Properties);\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_object-create.js\n// module id = 31\n// module chunks = 0", "// ********* / ********* Object.keys(O)\nvar $keys       = require('./_object-keys-internal')\n  , enumBugKeys = require('./_enum-bug-keys');\n\nmodule.exports = Object.keys || function keys(O){\n  return $keys(O, enumBugKeys);\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_object-keys.js\n// module id = 32\n// module chunks = 0", "// fallback for non-array-like ES3 and non-enumerable old V8 strings\nvar cof = require('./_cof');\nmodule.exports = Object('z').propertyIsEnumerable(0) ? Object : function(it){\n  return cof(it) == 'String' ? it.split('') : Object(it);\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_iobject.js\n// module id = 33\n// module chunks = 0", "var toString = {}.toString;\n\nmodule.exports = function(it){\n  return toString.call(it).slice(8, -1);\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_cof.js\n// module id = 34\n// module chunks = 0", "var global = require('./_global')\n  , SHARED = '__core-js_shared__'\n  , store  = global[SHARED] || (global[SHARED] = {});\nmodule.exports = function(key){\n  return store[key] || (store[key] = {});\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_shared.js\n// module id = 35\n// module chunks = 0", "// IE 8- don't enum bug keys\nmodule.exports = (\n  'constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf'\n).split(',');\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_enum-bug-keys.js\n// module id = 36\n// module chunks = 0", "// call something on iterator step with safe closing on error\nvar anObject = require('./_an-object');\nmodule.exports = function(iterator, fn, value, entries){\n  try {\n    return entries ? fn(anObject(value)[0], value[1]) : fn(value);\n  // 7.4.6 IteratorClose(iterator, completion)\n  } catch(e){\n    var ret = iterator['return'];\n    if(ret !== undefined)anObject(ret.call(iterator));\n    throw e;\n  }\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_iter-call.js\n// module id = 37\n// module chunks = 0", "// check on default Array iterator\nvar Iterators  = require('./_iterators')\n  , ITERATOR   = require('./_wks')('iterator')\n  , ArrayProto = Array.prototype;\n\nmodule.exports = function(it){\n  return it !== undefined && (Iterators.Array === it || ArrayProto[ITERATOR] === it);\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_is-array-iter.js\n// module id = 38\n// module chunks = 0", "var classof   = require('./_classof')\n  , ITERATOR  = require('./_wks')('iterator')\n  , Iterators = require('./_iterators');\nmodule.exports = require('./_core').getIteratorMethod = function(it){\n  if(it != undefined)return it[ITERATOR]\n    || it['@@iterator']\n    || Iterators[classof(it)];\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/core.get-iterator-method.js\n// module id = 39\n// module chunks = 0", "var ITERATOR     = require('./_wks')('iterator')\n  , SAFE_CLOSING = false;\n\ntry {\n  var riter = [7][ITERATOR]();\n  riter['return'] = function(){ SAFE_CLOSING = true; };\n  Array.from(riter, function(){ throw 2; });\n} catch(e){ /* empty */ }\n\nmodule.exports = function(exec, skipClosing){\n  if(!skipClosing && !SAFE_CLOSING)return false;\n  var safe = false;\n  try {\n    var arr  = [7]\n      , iter = arr[ITERATOR]();\n    iter.next = function(){ return {done: safe = true}; };\n    arr[ITERATOR] = function(){ return iter; };\n    exec(arr);\n  } catch(e){ /* empty */ }\n  return safe;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_iter-detect.js\n// module id = 40\n// module chunks = 0", "exports.f = {}.propertyIsEnumerable;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_object-pie.js\n// module id = 41\n// module chunks = 0", "module.exports = function(done, value){\n  return {value: value, done: !!done};\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_iter-step.js\n// module id = 42\n// module chunks = 0", "var redefine = require('./_redefine');\nmodule.exports = function(target, src, safe){\n  for(var key in src)redefine(target, key, src[key], safe);\n  return target;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_redefine-all.js\n// module id = 43\n// module chunks = 0", "module.exports = function(it, Constructor, name, forbiddenField){\n  if(!(it instanceof Constructor) || (forbiddenField !== undefined && forbiddenField in it)){\n    throw TypeError(name + ': incorrect invocation!');\n  } return it;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_an-instance.js\n// module id = 44\n// module chunks = 0", "var META     = require('./_uid')('meta')\n  , isObject = require('./_is-object')\n  , has      = require('./_has')\n  , setDesc  = require('./_object-dp').f\n  , id       = 0;\nvar isExtensible = Object.isExtensible || function(){\n  return true;\n};\nvar FREEZE = !require('./_fails')(function(){\n  return isExtensible(Object.preventExtensions({}));\n});\nvar setMeta = function(it){\n  setDesc(it, META, {value: {\n    i: 'O' + ++id, // object ID\n    w: {}          // weak collections IDs\n  }});\n};\nvar fastKey = function(it, create){\n  // return primitive with prefix\n  if(!isObject(it))return typeof it == 'symbol' ? it : (typeof it == 'string' ? 'S' : 'P') + it;\n  if(!has(it, META)){\n    // can't set metadata to uncaught frozen object\n    if(!isExtensible(it))return 'F';\n    // not necessary to add metadata\n    if(!create)return 'E';\n    // add missing metadata\n    setMeta(it);\n  // return object ID\n  } return it[META].i;\n};\nvar getWeak = function(it, create){\n  if(!has(it, META)){\n    // can't set metadata to uncaught frozen object\n    if(!isExtensible(it))return true;\n    // not necessary to add metadata\n    if(!create)return false;\n    // add missing metadata\n    setMeta(it);\n  // return hash weak collections IDs\n  } return it[META].w;\n};\n// add metadata on freeze-family methods calling\nvar onFreeze = function(it){\n  if(FREEZE && meta.NEED && isExtensible(it) && !has(it, META))setMeta(it);\n  return it;\n};\nvar meta = module.exports = {\n  KEY:      META,\n  NEED:     false,\n  fastKey:  fastKey,\n  getWeak:  getWeak,\n  onFreeze: onFreeze\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_meta.js\n// module id = 45\n// module chunks = 0", "/**\n * @file Implements `toSvg` function.\n */\n\nimport icons from '../dist/icons.json';\n\nconst DEFAULT_OPTIONS = {\n  xmlns: 'http://www.w3.org/2000/svg',\n  width: 24,\n  height: 24,\n  viewBox: '0 0 24 24',\n  fill: 'none',\n  stroke: 'currentColor',\n  'stroke-width': 2,\n  'stroke-linecap': 'round',\n  'stroke-linejoin': 'round',\n};\n\n/**\n * Create an SVG string.\n * @param {string} key - Icon name.\n * @param {Object} options\n * @returns {string}\n */\nexport default function toSvg(key, options = {}) {\n  if (!key) {\n    throw new Error('The required `key` (icon name) parameter is missing.');\n  }\n\n  if (!icons[key]) {\n    throw new Error(`No icon matching '${key}'. See the complete list of icons at https://feathericons.com`);\n  }\n\n  const combinedOptions = Object.assign({}, DEFAULT_OPTIONS, options);\n\n  combinedOptions.class = addDefaultClassNames(combinedOptions.class, key);\n\n  const attributes = optionsToAtrributes(combinedOptions);\n\n  return `<svg ${attributes}>${icons[key]}</svg>`;\n}\n\n/**\n * Add default class names.\n * @param {string} classNames - One or more class names seperated by spaces.\n * @param {string} key - Icon name.\n * @returns {string}\n */\nfunction addDefaultClassNames(classNames, key) {\n  // convert class names string into an array\n  const classNamesArray = classNames ? classNames.trim().split(/\\s+/) : [];\n\n  // use Set to avoid duplicate class names\n  const classNamesSet = new Set(classNamesArray);\n\n  // add default class names\n  classNamesSet.add('feather').add(`feather-${key}`);\n\n  return Array.from(classNamesSet).join(' ');\n}\n\n/**\n * Convert options object to string of html attributes.\n * @param {Object} options\n * @returns {string}\n */\nfunction optionsToAtrributes(options) {\n  const attributes = [];\n\n  Object.keys(options).forEach(key => {\n    attributes.push(`${key}=\"${options[key]}\"`);\n  });\n\n  return attributes.join(' ');\n}\n\n\n\n// WEBPACK FOOTER //\n// ./src/to-svg.js", "require('../../modules/es6.string.iterator');\nrequire('../../modules/es6.array.from');\nmodule.exports = require('../../modules/_core').Array.from;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/fn/array/from.js\n// module id = 48\n// module chunks = 0", "var toInteger = require('./_to-integer')\n  , defined   = require('./_defined');\n// true  -> String#at\n// false -> String#codePointAt\nmodule.exports = function(TO_STRING){\n  return function(that, pos){\n    var s = String(defined(that))\n      , i = toInteger(pos)\n      , l = s.length\n      , a, b;\n    if(i < 0 || i >= l)return TO_STRING ? '' : undefined;\n    a = s.charCodeAt(i);\n    return a < 0xd800 || a > 0xdbff || i + 1 === l || (b = s.charCodeAt(i + 1)) < 0xdc00 || b > 0xdfff\n      ? TO_STRING ? s.charAt(i) : a\n      : TO_STRING ? s.slice(i, i + 2) : (a - 0xd800 << 10) + (b - 0xdc00) + 0x10000;\n  };\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_string-at.js\n// module id = 49\n// module chunks = 0", "module.exports = false;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_library.js\n// module id = 50\n// module chunks = 0", "module.exports = function(it){\n  if(typeof it != 'function')throw TypeError(it + ' is not a function!');\n  return it;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_a-function.js\n// module id = 51\n// module chunks = 0", "'use strict';\nvar create         = require('./_object-create')\n  , descriptor     = require('./_property-desc')\n  , setToStringTag = require('./_set-to-string-tag')\n  , IteratorPrototype = {};\n\n// ********.1 %IteratorPrototype%[@@iterator]()\nrequire('./_hide')(IteratorPrototype, require('./_wks')('iterator'), function(){ return this; });\n\nmodule.exports = function(Constructor, NAME, next){\n  Constructor.prototype = create(IteratorPrototype, {next: descriptor(1, next)});\n  setToStringTag(Constructor, NAME + ' Iterator');\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_iter-create.js\n// module id = 52\n// module chunks = 0", "var dP       = require('./_object-dp')\n  , anObject = require('./_an-object')\n  , getKeys  = require('./_object-keys');\n\nmodule.exports = require('./_descriptors') ? Object.defineProperties : function defineProperties(O, Properties){\n  anObject(O);\n  var keys   = getKeys(Properties)\n    , length = keys.length\n    , i = 0\n    , P;\n  while(length > i)dP.f(O, P = keys[i++], Properties[P]);\n  return O;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_object-dps.js\n// module id = 53\n// module chunks = 0", "var has          = require('./_has')\n  , toIObject    = require('./_to-iobject')\n  , arrayIndexOf = require('./_array-includes')(false)\n  , IE_PROTO     = require('./_shared-key')('IE_PROTO');\n\nmodule.exports = function(object, names){\n  var O      = toIObject(object)\n    , i      = 0\n    , result = []\n    , key;\n  for(key in O)if(key != IE_PROTO)has(O, key) && result.push(key);\n  // Don't enum bug & hidden keys\n  while(names.length > i)if(has(O, key = names[i++])){\n    ~arrayIndexOf(result, key) || result.push(key);\n  }\n  return result;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_object-keys-internal.js\n// module id = 54\n// module chunks = 0", "// false -> Array#indexOf\n// true  -> Array#includes\nvar toIObject = require('./_to-iobject')\n  , toLength  = require('./_to-length')\n  , toIndex   = require('./_to-index');\nmodule.exports = function(IS_INCLUDES){\n  return function($this, el, fromIndex){\n    var O      = toIObject($this)\n      , length = toLength(O.length)\n      , index  = toIndex(fromIndex, length)\n      , value;\n    // Array#includes uses SameValueZero equality algorithm\n    if(IS_INCLUDES && el != el)while(length > index){\n      value = O[index++];\n      if(value != value)return true;\n    // Array#toIndex ignores holes, Array#includes - not\n    } else for(;length > index; index++)if(IS_INCLUDES || index in O){\n      if(O[index] === el)return IS_INCLUDES || index || 0;\n    } return !IS_INCLUDES && -1;\n  };\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_array-includes.js\n// module id = 55\n// module chunks = 0", "var toInteger = require('./_to-integer')\n  , max       = Math.max\n  , min       = Math.min;\nmodule.exports = function(index, length){\n  index = toInteger(index);\n  return index < 0 ? max(index + length, 0) : min(index, length);\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_to-index.js\n// module id = 56\n// module chunks = 0", "module.exports = require('./_global').document && document.documentElement;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_html.js\n// module id = 57\n// module chunks = 0", "// 19.1.2.9 / 15.2.3.2 Object.getPrototypeOf(O)\nvar has         = require('./_has')\n  , toObject    = require('./_to-object')\n  , IE_PROTO    = require('./_shared-key')('IE_PROTO')\n  , ObjectProto = Object.prototype;\n\nmodule.exports = Object.getPrototypeOf || function(O){\n  O = toObject(O);\n  if(has(O, IE_PROTO))return O[IE_PROTO];\n  if(typeof O.constructor == 'function' && O instanceof O.constructor){\n    return O.constructor.prototype;\n  } return O instanceof Object ? ObjectProto : null;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_object-gpo.js\n// module id = 58\n// module chunks = 0", "'use strict';\nvar ctx            = require('./_ctx')\n  , $export        = require('./_export')\n  , toObject       = require('./_to-object')\n  , call           = require('./_iter-call')\n  , isArrayIter    = require('./_is-array-iter')\n  , toLength       = require('./_to-length')\n  , createProperty = require('./_create-property')\n  , getIterFn      = require('./core.get-iterator-method');\n\n$export($export.S + $export.F * !require('./_iter-detect')(function(iter){ Array.from(iter); }), 'Array', {\n  // 22.1.2.1 Array.from(arrayLike, mapfn = undefined, thisArg = undefined)\n  from: function from(arrayLike/*, mapfn = undefined, thisArg = undefined*/){\n    var O       = toObject(arrayLike)\n      , C       = typeof this == 'function' ? this : Array\n      , aLen    = arguments.length\n      , mapfn   = aLen > 1 ? arguments[1] : undefined\n      , mapping = mapfn !== undefined\n      , index   = 0\n      , iterFn  = getIterFn(O)\n      , length, result, step, iterator;\n    if(mapping)mapfn = ctx(mapfn, aLen > 2 ? arguments[2] : undefined, 2);\n    // if object isn't iterable or it's array with default iterator - use simple case\n    if(iterFn != undefined && !(C == Array && isArrayIter(iterFn))){\n      for(iterator = iterFn.call(O), result = new C; !(step = iterator.next()).done; index++){\n        createProperty(result, index, mapping ? call(iterator, mapfn, [step.value, index], true) : step.value);\n      }\n    } else {\n      length = toLength(O.length);\n      for(result = new C(length); length > index; index++){\n        createProperty(result, index, mapping ? mapfn(O[index], index) : O[index]);\n      }\n    }\n    result.length = index;\n    return result;\n  }\n});\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/es6.array.from.js\n// module id = 59\n// module chunks = 0", "'use strict';\nvar $defineProperty = require('./_object-dp')\n  , createDesc      = require('./_property-desc');\n\nmodule.exports = function(object, index, value){\n  if(index in object)$defineProperty.f(object, index, createDesc(0, value));\n  else object[index] = value;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_create-property.js\n// module id = 60\n// module chunks = 0", "require('../../modules/es6.object.assign');\nmodule.exports = require('../../modules/_core').Object.assign;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/fn/object/assign.js\n// module id = 61\n// module chunks = 0", "// 19.1.3.1 Object.assign(target, source)\nvar $export = require('./_export');\n\n$export($export.S + $export.F, 'Object', {assign: require('./_object-assign')});\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/es6.object.assign.js\n// module id = 62\n// module chunks = 0", "'use strict';\n// 19.1.2.1 Object.assign(target, source, ...)\nvar getKeys  = require('./_object-keys')\n  , gOPS     = require('./_object-gops')\n  , pIE      = require('./_object-pie')\n  , toObject = require('./_to-object')\n  , IObject  = require('./_iobject')\n  , $assign  = Object.assign;\n\n// should work with symbols and should have deterministic property order (V8 bug)\nmodule.exports = !$assign || require('./_fails')(function(){\n  var A = {}\n    , B = {}\n    , S = Symbol()\n    , K = 'abcdefghijklmnopqrst';\n  A[S] = 7;\n  K.split('').forEach(function(k){ B[k] = k; });\n  return $assign({}, A)[S] != 7 || Object.keys($assign({}, B)).join('') != K;\n}) ? function assign(target, source){ // eslint-disable-line no-unused-vars\n  var T     = toObject(target)\n    , aLen  = arguments.length\n    , index = 1\n    , getSymbols = gOPS.f\n    , isEnum     = pIE.f;\n  while(aLen > index){\n    var S      = IObject(arguments[index++])\n      , keys   = getSymbols ? getKeys(S).concat(getSymbols(S)) : getKeys(S)\n      , length = keys.length\n      , j      = 0\n      , key;\n    while(length > j)if(isEnum.call(S, key = keys[j++]))T[key] = S[key];\n  } return T;\n} : $assign;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_object-assign.js\n// module id = 63\n// module chunks = 0", "exports.f = Object.getOwnPropertySymbols;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_object-gops.js\n// module id = 64\n// module chunks = 0", "require('../modules/es6.object.to-string');\nrequire('../modules/es6.string.iterator');\nrequire('../modules/web.dom.iterable');\nrequire('../modules/es6.set');\nrequire('../modules/es7.set.to-json');\nmodule.exports = require('../modules/_core').Set;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/fn/set.js\n// module id = 65\n// module chunks = 0", "'use strict';\n// ******** Object.prototype.toString()\nvar classof = require('./_classof')\n  , test    = {};\ntest[require('./_wks')('toStringTag')] = 'z';\nif(test + '' != '[object z]'){\n  require('./_redefine')(Object.prototype, 'toString', function toString(){\n    return '[object ' + classof(this) + ']';\n  }, true);\n}\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/es6.object.to-string.js\n// module id = 66\n// module chunks = 0", "var $iterators    = require('./es6.array.iterator')\n  , redefine      = require('./_redefine')\n  , global        = require('./_global')\n  , hide          = require('./_hide')\n  , Iterators     = require('./_iterators')\n  , wks           = require('./_wks')\n  , ITERATOR      = wks('iterator')\n  , TO_STRING_TAG = wks('toStringTag')\n  , ArrayValues   = Iterators.Array;\n\nfor(var collections = ['NodeList', 'DOMTokenList', 'MediaList', 'StyleSheetList', 'CSSRuleList'], i = 0; i < 5; i++){\n  var NAME       = collections[i]\n    , Collection = global[NAME]\n    , proto      = Collection && Collection.prototype\n    , key;\n  if(proto){\n    if(!proto[ITERATOR])hide(proto, ITERATOR, ArrayValues);\n    if(!proto[TO_STRING_TAG])hide(proto, TO_STRING_TAG, NAME);\n    Iterators[NAME] = ArrayValues;\n    for(key in $iterators)if(!proto[key])redefine(proto, key, $iterators[key], true);\n  }\n}\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/web.dom.iterable.js\n// module id = 67\n// module chunks = 0", "'use strict';\nvar addToUnscopables = require('./_add-to-unscopables')\n  , step             = require('./_iter-step')\n  , Iterators        = require('./_iterators')\n  , toIObject        = require('./_to-iobject');\n\n// 22.1.3.4 Array.prototype.entries()\n// 22.1.3.13 Array.prototype.keys()\n// 22.1.3.29 Array.prototype.values()\n// 22.1.3.30 Array.prototype[@@iterator]()\nmodule.exports = require('./_iter-define')(Array, 'Array', function(iterated, kind){\n  this._t = toIObject(iterated); // target\n  this._i = 0;                   // next index\n  this._k = kind;                // kind\n// 22.1.5.2.1 %ArrayIteratorPrototype%.next()\n}, function(){\n  var O     = this._t\n    , kind  = this._k\n    , index = this._i++;\n  if(!O || index >= O.length){\n    this._t = undefined;\n    return step(1);\n  }\n  if(kind == 'keys'  )return step(0, index);\n  if(kind == 'values')return step(0, O[index]);\n  return step(0, [index, O[index]]);\n}, 'values');\n\n// argumentsList[@@iterator] is %ArrayProto_values% (9.4.4.6, 9.4.4.7)\nIterators.Arguments = Iterators.Array;\n\naddToUnscopables('keys');\naddToUnscopables('values');\naddToUnscopables('entries');\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/es6.array.iterator.js\n// module id = 68\n// module chunks = 0", "// 22.1.3.31 Array.prototype[@@unscopables]\nvar UNSCOPABLES = require('./_wks')('unscopables')\n  , ArrayProto  = Array.prototype;\nif(ArrayProto[UNSCOPABLES] == undefined)require('./_hide')(ArrayProto, UNSCOPABLES, {});\nmodule.exports = function(key){\n  ArrayProto[UNSCOPABLES][key] = true;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_add-to-unscopables.js\n// module id = 69\n// module chunks = 0", "'use strict';\nvar strong = require('./_collection-strong');\n\n// 23.2 Set Objects\nmodule.exports = require('./_collection')('Set', function(get){\n  return function Set(){ return get(this, arguments.length > 0 ? arguments[0] : undefined); };\n}, {\n  // 23.2.3.1 Set.prototype.add(value)\n  add: function add(value){\n    return strong.def(this, value = value === 0 ? 0 : value, value);\n  }\n}, strong);\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/es6.set.js\n// module id = 70\n// module chunks = 0", "'use strict';\nvar dP          = require('./_object-dp').f\n  , create      = require('./_object-create')\n  , redefineAll = require('./_redefine-all')\n  , ctx         = require('./_ctx')\n  , anInstance  = require('./_an-instance')\n  , defined     = require('./_defined')\n  , forOf       = require('./_for-of')\n  , $iterDefine = require('./_iter-define')\n  , step        = require('./_iter-step')\n  , setSpecies  = require('./_set-species')\n  , DESCRIPTORS = require('./_descriptors')\n  , fastKey     = require('./_meta').fastKey\n  , SIZE        = DESCRIPTORS ? '_s' : 'size';\n\nvar getEntry = function(that, key){\n  // fast case\n  var index = fastKey(key), entry;\n  if(index !== 'F')return that._i[index];\n  // frozen object case\n  for(entry = that._f; entry; entry = entry.n){\n    if(entry.k == key)return entry;\n  }\n};\n\nmodule.exports = {\n  getConstructor: function(wrapper, NAME, IS_MAP, ADDER){\n    var C = wrapper(function(that, iterable){\n      anInstance(that, C, NAME, '_i');\n      that._i = create(null); // index\n      that._f = undefined;    // first entry\n      that._l = undefined;    // last entry\n      that[SIZE] = 0;         // size\n      if(iterable != undefined)forOf(iterable, IS_MAP, that[ADDER], that);\n    });\n    redefineAll(C.prototype, {\n      // ******** Map.prototype.clear()\n      // ******** Set.prototype.clear()\n      clear: function clear(){\n        for(var that = this, data = that._i, entry = that._f; entry; entry = entry.n){\n          entry.r = true;\n          if(entry.p)entry.p = entry.p.n = undefined;\n          delete data[entry.i];\n        }\n        that._f = that._l = undefined;\n        that[SIZE] = 0;\n      },\n      // 23.1.3.3 Map.prototype.delete(key)\n      // 23.2.3.4 Set.prototype.delete(value)\n      'delete': function(key){\n        var that  = this\n          , entry = getEntry(that, key);\n        if(entry){\n          var next = entry.n\n            , prev = entry.p;\n          delete that._i[entry.i];\n          entry.r = true;\n          if(prev)prev.n = next;\n          if(next)next.p = prev;\n          if(that._f == entry)that._f = next;\n          if(that._l == entry)that._l = prev;\n          that[SIZE]--;\n        } return !!entry;\n      },\n      // 23.2.3.6 Set.prototype.forEach(callbackfn, thisArg = undefined)\n      // 23.1.3.5 Map.prototype.forEach(callbackfn, thisArg = undefined)\n      forEach: function forEach(callbackfn /*, that = undefined */){\n        anInstance(this, C, 'forEach');\n        var f = ctx(callbackfn, arguments.length > 1 ? arguments[1] : undefined, 3)\n          , entry;\n        while(entry = entry ? entry.n : this._f){\n          f(entry.v, entry.k, this);\n          // revert to the last existing entry\n          while(entry && entry.r)entry = entry.p;\n        }\n      },\n      // ******** Map.prototype.has(key)\n      // ******** Set.prototype.has(value)\n      has: function has(key){\n        return !!getEntry(this, key);\n      }\n    });\n    if(DESCRIPTORS)dP(C.prototype, 'size', {\n      get: function(){\n        return defined(this[SIZE]);\n      }\n    });\n    return C;\n  },\n  def: function(that, key, value){\n    var entry = getEntry(that, key)\n      , prev, index;\n    // change existing entry\n    if(entry){\n      entry.v = value;\n    // create new entry\n    } else {\n      that._l = entry = {\n        i: index = fastKey(key, true), // <- index\n        k: key,                        // <- key\n        v: value,                      // <- value\n        p: prev = that._l,             // <- previous entry\n        n: undefined,                  // <- next entry\n        r: false                       // <- removed\n      };\n      if(!that._f)that._f = entry;\n      if(prev)prev.n = entry;\n      that[SIZE]++;\n      // add to index\n      if(index !== 'F')that._i[index] = entry;\n    } return that;\n  },\n  getEntry: getEntry,\n  setStrong: function(C, NAME, IS_MAP){\n    // add .keys, .values, .entries, [@@iterator]\n    // 23.1.3.4, 23.1.3.8, ********1, ********2, 23.2.3.5, 23.2.3.8, 23.2.3.10, 23.2.3.11\n    $iterDefine(C, NAME, function(iterated, kind){\n      this._t = iterated;  // target\n      this._k = kind;      // kind\n      this._l = undefined; // previous\n    }, function(){\n      var that  = this\n        , kind  = that._k\n        , entry = that._l;\n      // revert to the last existing entry\n      while(entry && entry.r)entry = entry.p;\n      // get next entry\n      if(!that._t || !(that._l = entry = entry ? entry.n : that._t._f)){\n        // or finish the iteration\n        that._t = undefined;\n        return step(1);\n      }\n      // return step by kind\n      if(kind == 'keys'  )return step(0, entry.k);\n      if(kind == 'values')return step(0, entry.v);\n      return step(0, [entry.k, entry.v]);\n    }, IS_MAP ? 'entries' : 'values' , !IS_MAP, true);\n\n    // add [@@species], ********, ********\n    setSpecies(NAME);\n  }\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_collection-strong.js\n// module id = 71\n// module chunks = 0", "'use strict';\nvar global      = require('./_global')\n  , dP          = require('./_object-dp')\n  , DESCRIPTORS = require('./_descriptors')\n  , SPECIES     = require('./_wks')('species');\n\nmodule.exports = function(KEY){\n  var C = global[KEY];\n  if(DESCRIPTORS && C && !C[SPECIES])dP.f(C, SPECIES, {\n    configurable: true,\n    get: function(){ return this; }\n  });\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_set-species.js\n// module id = 72\n// module chunks = 0", "'use strict';\nvar global            = require('./_global')\n  , $export           = require('./_export')\n  , redefine          = require('./_redefine')\n  , redefineAll       = require('./_redefine-all')\n  , meta              = require('./_meta')\n  , forOf             = require('./_for-of')\n  , anInstance        = require('./_an-instance')\n  , isObject          = require('./_is-object')\n  , fails             = require('./_fails')\n  , $iterDetect       = require('./_iter-detect')\n  , setToStringTag    = require('./_set-to-string-tag')\n  , inheritIfRequired = require('./_inherit-if-required');\n\nmodule.exports = function(NAME, wrapper, methods, common, IS_MAP, IS_WEAK){\n  var Base  = global[NAME]\n    , C     = Base\n    , ADDER = IS_MAP ? 'set' : 'add'\n    , proto = C && C.prototype\n    , O     = {};\n  var fixMethod = function(KEY){\n    var fn = proto[KEY];\n    redefine(proto, KEY,\n      KEY == 'delete' ? function(a){\n        return IS_WEAK && !isObject(a) ? false : fn.call(this, a === 0 ? 0 : a);\n      } : KEY == 'has' ? function has(a){\n        return IS_WEAK && !isObject(a) ? false : fn.call(this, a === 0 ? 0 : a);\n      } : KEY == 'get' ? function get(a){\n        return IS_WEAK && !isObject(a) ? undefined : fn.call(this, a === 0 ? 0 : a);\n      } : KEY == 'add' ? function add(a){ fn.call(this, a === 0 ? 0 : a); return this; }\n        : function set(a, b){ fn.call(this, a === 0 ? 0 : a, b); return this; }\n    );\n  };\n  if(typeof C != 'function' || !(IS_WEAK || proto.forEach && !fails(function(){\n    new C().entries().next();\n  }))){\n    // create collection constructor\n    C = common.getConstructor(wrapper, NAME, IS_MAP, ADDER);\n    redefineAll(C.prototype, methods);\n    meta.NEED = true;\n  } else {\n    var instance             = new C\n      // early implementations not supports chaining\n      , HASNT_CHAINING       = instance[ADDER](IS_WEAK ? {} : -0, 1) != instance\n      // V8 ~  Chromium 40- weak-collections throws on primitives, but should return false\n      , THROWS_ON_PRIMITIVES = fails(function(){ instance.has(1); })\n      // most early implementations doesn't supports iterables, most modern - not close it correctly\n      , ACCEPT_ITERABLES     = $iterDetect(function(iter){ new C(iter); }) // eslint-disable-line no-new\n      // for early implementations -0 and +0 not the same\n      , BUGGY_ZERO = !IS_WEAK && fails(function(){\n        // V8 ~ Chromium 42- fails only with 5+ elements\n        var $instance = new C()\n          , index     = 5;\n        while(index--)$instance[ADDER](index, index);\n        return !$instance.has(-0);\n      });\n    if(!ACCEPT_ITERABLES){ \n      C = wrapper(function(target, iterable){\n        anInstance(target, C, NAME);\n        var that = inheritIfRequired(new Base, target, C);\n        if(iterable != undefined)forOf(iterable, IS_MAP, that[ADDER], that);\n        return that;\n      });\n      C.prototype = proto;\n      proto.constructor = C;\n    }\n    if(THROWS_ON_PRIMITIVES || BUGGY_ZERO){\n      fixMethod('delete');\n      fixMethod('has');\n      IS_MAP && fixMethod('get');\n    }\n    if(BUGGY_ZERO || HASNT_CHAINING)fixMethod(ADDER);\n    // weak collections should not contains .clear method\n    if(IS_WEAK && proto.clear)delete proto.clear;\n  }\n\n  setToStringTag(C, NAME);\n\n  O[NAME] = C;\n  $export($export.G + $export.W + $export.F * (C != Base), O);\n\n  if(!IS_WEAK)common.setStrong(C, NAME, IS_MAP);\n\n  return C;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_collection.js\n// module id = 73\n// module chunks = 0", "var isObject       = require('./_is-object')\n  , setPrototypeOf = require('./_set-proto').set;\nmodule.exports = function(that, target, C){\n  var P, S = target.constructor;\n  if(S !== C && typeof S == 'function' && (P = S.prototype) !== C.prototype && isObject(P) && setPrototypeOf){\n    setPrototypeOf(that, P);\n  } return that;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_inherit-if-required.js\n// module id = 74\n// module chunks = 0", "// Works with __proto__ only. Old v8 can't work with null proto objects.\n/* eslint-disable no-proto */\nvar isObject = require('./_is-object')\n  , anObject = require('./_an-object');\nvar check = function(O, proto){\n  anObject(O);\n  if(!isObject(proto) && proto !== null)throw TypeError(proto + \": can't set as prototype!\");\n};\nmodule.exports = {\n  set: Object.setPrototypeOf || ('__proto__' in {} ? // eslint-disable-line\n    function(test, buggy, set){\n      try {\n        set = require('./_ctx')(Function.call, require('./_object-gopd').f(Object.prototype, '__proto__').set, 2);\n        set(test, []);\n        buggy = !(test instanceof Array);\n      } catch(e){ buggy = true; }\n      return function setPrototypeOf(O, proto){\n        check(O, proto);\n        if(buggy)O.__proto__ = proto;\n        else set(O, proto);\n        return O;\n      };\n    }({}, false) : undefined),\n  check: check\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_set-proto.js\n// module id = 75\n// module chunks = 0", "var pIE            = require('./_object-pie')\n  , createDesc     = require('./_property-desc')\n  , toIObject      = require('./_to-iobject')\n  , toPrimitive    = require('./_to-primitive')\n  , has            = require('./_has')\n  , IE8_DOM_DEFINE = require('./_ie8-dom-define')\n  , gOPD           = Object.getOwnPropertyDescriptor;\n\nexports.f = require('./_descriptors') ? gOPD : function getOwnPropertyDescriptor(O, P){\n  O = toIObject(O);\n  P = toPrimitive(P, true);\n  if(IE8_DOM_DEFINE)try {\n    return gOPD(O, P);\n  } catch(e){ /* empty */ }\n  if(has(O, P))return createDesc(!pIE.f.call(O, P), O[P]);\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_object-gopd.js\n// module id = 76\n// module chunks = 0", "// https://github.com/DavidBruant/Map-Set.prototype.toJSON\nvar $export  = require('./_export');\n\n$export($export.P + $export.R, 'Set', {toJSON: require('./_collection-to-json')('Set')});\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/es7.set.to-json.js\n// module id = 77\n// module chunks = 0", "// https://github.com/DavidBruant/Map-Set.prototype.toJSON\nvar classof = require('./_classof')\n  , from    = require('./_array-from-iterable');\nmodule.exports = function(NAME){\n  return function toJSON(){\n    if(classof(this) != NAME)throw TypeError(NAME + \"#toJSON isn't generic\");\n    return from(this);\n  };\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_collection-to-json.js\n// module id = 78\n// module chunks = 0", "var forOf = require('./_for-of');\n\nmodule.exports = function(iter, ITERATOR){\n  var result = [];\n  forOf(iter, false, result.push, result, ITERATOR);\n  return result;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_array-from-iterable.js\n// module id = 79\n// module chunks = 0", "/**\n * @file Exposes `feather` object.\n */\n\nimport icons from '../dist/icons.json';\nimport toSvg from './to-svg';\nimport replace from './replace';\n\nmodule.exports = { icons, toSvg, replace };\n\n\n\n// WEBPACK FOOTER //\n// ./src/index.js", "/**\n * @file Implements `replace` function.\n */\n\n/* global document, DOMParser */\n\nimport icons from '../dist/icons.json';\nimport toSvg from './to-svg';\n\n/**\n * Replace all elements that have a `data-feather` attribute with SVG markup\n * corresponding to the element's `data-feather` attribute value.\n * @param {Object} options\n */\nexport default function replace(options = {}) {\n  if (typeof document === 'undefined') {\n    throw new Error('`feather.replace()` only works in a browser environment.');\n  }\n\n  const elementsToReplace = document.querySelectorAll('[data-feather]');\n\n  Array.from(elementsToReplace).forEach(element => replaceElement(element, options));\n}\n\n/**\n * Replace single element with SVG markup\n * corresponding to the element's `data-feather` attribute value.\n * @param {Element} element\n * @param {Object} options\n */\nfunction replaceElement(element, options) {\n  const key = element.getAttribute('data-feather');\n\n  if (!key) {\n    console.error('The required `data-feather` attribute has no value.');\n    return;\n  }\n\n  if (!icons[key]) {\n    console.error(`No icon matching '${key}'. See the complete list of icons at https://feathericons.com`);\n    return;\n  }\n\n  const elementClassAttr = element.getAttribute('class');\n  const classNames = (\n    options.class ? `${options.class} ${elementClassAttr}` : elementClassAttr\n  );\n\n  const svgString = toSvg(key, Object.assign({}, options, { class: classNames }));\n  const svgDocument = new DOMParser().parseFromString(svgString, 'image/svg+xml');\n  const svgElement = svgDocument.querySelector('svg');\n\n  element.parentNode.replaceChild(svgElement, element);\n}\n\n\n\n// WEBPACK FOOTER //\n// ./src/replace.js"], "sourceRoot": ""}