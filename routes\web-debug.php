<?php

// use App\Models\Form;
// use Illuminate\Support\Facades\Route;

// // Debug route to check form structure
// Route::get('/debug/form/{id}', function ($id) {
//     $form = Form::findOrFail($id);
    
//     $decoded = json_decode($form->form_structure, true);
    
//     return response()->json([
//         'id' => $form->id,
//         'title' => $form->title,
//         'form_structure' => $form->form_structure,
//         'form_structure_type' => gettype($form->form_structure),
//         'form_structure_length' => strlen($form->form_structure),
//         'is_json_null' => is_null($form->form_structure),
//         'is_empty' => empty($form->form_structure),
//         'json_decoded' => $decoded,
//         'is_decoded_array' => is_array($decoded),
//         'decoded_keys' => is_array($decoded) ? array_keys($decoded) : null,
//         'json_error' => json_last_error_msg(),
//         'json_error_code' => json_last_error()
//     ]);
// })->name('debug.form');
