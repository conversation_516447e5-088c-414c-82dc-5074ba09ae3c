@extends('layouts.admin')
@section('pageTitle', trans('cruds.qr-decoder-profiles.title'))

@section('styles')
    @parent
@endsection

@section('content')
    <div class="card card-default">
        <div class="card-header separator">
            <div class="card-title mainheading">
                <h4>{{ trans('cruds.qr-decoder-profiles.title') }} {{ trans('global.list') }}</h4>
            </div>
            <div class="card-controls">
                <ul>
                    <li>
                        <x-add-button :url="route('admin.qr-decoder-profiles.create')" :text="trans('cruds.usergroup.title_singular')" />
                    </li>
                </ul>
            </div>
        </div>


        <div class="card-block">
            @php
                $columns = \App\Models\QrDecoderProfile::dataTableColumns();
            @endphp

            <x-datatable :columns="$columns" :ajax="route('admin.qr-decoder-profiles.index')" :order="[[6, 'desc']]" />
        </div>
    </div>
@endsection

@section('scripts')
    @parent

    <script>
        $(function() {


            // Handle select all checkbox
            $('#select-all').on('click', function() {
                var rows = table.rows({
                    'search': 'applied'
                }).nodes();
                $('input[type="checkbox"]', rows).prop('checked', this.checked);
            });
        });
    </script>
@endsection
