<?php $__env->startSection('pageTitle', __('global.create') . ' ' . __('cruds.integrationConfiguration.title_singular')); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .required:after {
        content: " *";
        color: red;
    }
    .help-block {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 0.25rem;
    }
    .form-group {
        margin-bottom: 1.5rem;
    }
    .card-header h4 {
        margin: 0;
        color: #495057;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <div class="card card-default">
        <div class="card-header separator">
            <div class="card-title mainheading">
                <h4><?php echo e(__('global.create')); ?> <?php echo e(__('cruds.integrationConfiguration.title_singular')); ?></h4>
            </div>
            <div class="card-controls">
                <ul>
                    <li>
                        <?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.buttons.back','data' => ['route' => route('admin.integration-configurations.index'),'text' => __('global.back_to_list')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('buttons.back'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['route' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('admin.integration-configurations.index')),'text' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(__('global.back_to_list'))]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>
                    </li>
                </ul>
            </div>
        </div>
        <div class="card-block">
            <?php if($errors->any()): ?>
                <div class="alert alert-danger">
                    <ul>
                        <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li><?php echo e($error); ?></li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                </div>
            <?php endif; ?>

            <form method="POST" action="<?php echo e(route('admin.integration-configurations.store')); ?>" enctype="multipart/form-data" id="integration-form">
                <?php echo csrf_field(); ?>

                <div class="form-group row">
                    <label for="name" class="col-md-2 col-form-label required"><?php echo e(__('cruds.integrationConfiguration.fields.name')); ?></label>
                    <div class="col-md-4">
                        <input type="text" class="form-control <?php echo e($errors->has('name') ? 'is-invalid' : ''); ?>"
                               id="name" name="name" value="<?php echo e(old('name', '')); ?>" required >
                        <?php if($errors->has('name')): ?>
                            <div class="invalid-feedback">
                                <?php echo e($errors->first('name')); ?>

                            </div>
                        <?php endif; ?>
                    </div>
                    <label for="is_active" class="col-md-2 col-form-label required"><?php echo e(trans('cruds.fields.status')); ?></label>
                    <div class="col-md-4">
                        <div class="form-check mt-2">
                            <input type="checkbox" class="form-check-input" id="is_active" name="is_active" value="1"
                                <?php echo e(old('is_active', true) ? 'checked' : ''); ?>>
                            <label class="form-check-label" for="is_active">
                                <?php echo e(trans('cruds.fields.active')); ?>

                            </label>
                        </div>
                        <?php if($errors->has('is_active')): ?>
                            <div class="invalid-feedback d-block">
                                <?php echo e($errors->first('is_active')); ?>

                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="form-group row">
                    <label for="description" class="col-md-2 col-form-label"><?php echo e(__('cruds.integrationConfiguration.fields.description')); ?></label>
                    <div class="col-md-4">
                        <textarea class="form-control <?php echo e($errors->has('description') ? 'is-invalid' : ''); ?>"
                                 id="description" name="description" rows="2"><?php echo e(old('description')); ?></textarea>
                        <?php if($errors->has('description')): ?>
                            <div class="invalid-feedback">
                                <?php echo e($errors->first('description')); ?>

                            </div>
                        <?php endif; ?>
                    </div>
                    <label for="external_system" class="col-md-2 col-form-label required"><?php echo e(__('cruds.integrationConfiguration.fields.external_system')); ?></label>
                    <div class="col-md-4">
                        <select class="form-control <?php echo e($errors->has('external_system') ? 'is-invalid' : ''); ?>"
                                name="external_system" id="external_system" required>
                            <option value=""><?php echo e(__('global.pleaseSelect')); ?></option>
                            <?php $__currentLoopData = $externalSystemOptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($key); ?>" <?php echo e(old('external_system') == $key ? 'selected' : ''); ?>><?php echo e($label); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <?php if($errors->has('external_system')): ?>
                            <div class="invalid-feedback">
                                <?php echo e($errors->first('external_system')); ?>

                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="form-group row">
                    <label for="process_type" class="col-md-2 col-form-label required"><?php echo e(__('cruds.integrationConfiguration.fields.process_type')); ?></label>
                    <div class="col-md-4">
                        <select class="form-control <?php echo e($errors->has('process_type') ? 'is-invalid' : ''); ?>"
                                name="process_type" id="process_type" required>
                            <option value=""><?php echo e(__('global.pleaseSelect')); ?></option>
                            <?php $__currentLoopData = $processTypeOptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($key); ?>" <?php echo e(old('process_type') == $key ? 'selected' : ''); ?>><?php echo e($label); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <?php if($errors->has('process_type')): ?>
                            <div class="invalid-feedback">
                                <?php echo e($errors->first('process_type')); ?>

                            </div>
                        <?php endif; ?>
                    </div>
                    <label for="integration_method" class="col-md-2 col-form-label required"><?php echo e(__('cruds.integrationConfiguration.fields.integration_method')); ?></label>
                    <div class="col-md-4">
                        <select class="form-control <?php echo e($errors->has('integration_method') ? 'is-invalid' : ''); ?>"
                                name="integration_method" id="integration_method" required>
                            <option value=""><?php echo e(__('global.pleaseSelect')); ?></option>
                            <?php $__currentLoopData = $integrationMethodOptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($key); ?>" <?php echo e(old('integration_method') == $key ? 'selected' : ''); ?>><?php echo e($label); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <?php if($errors->has('integration_method')): ?>
                            <div class="invalid-feedback">
                                <?php echo e($errors->first('integration_method')); ?>

                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="form-group row">
                    <label for="endpoint_url" class="col-md-2 col-form-label required"><?php echo e(__('cruds.integrationConfiguration.fields.endpoint_url')); ?></label>
                    <div class="col-md-4">
                        <input type="text" class="form-control <?php echo e($errors->has('endpoint_url') ? 'is-invalid' : ''); ?>"
                               id="endpoint_url" name="endpoint_url" value="<?php echo e(old('endpoint_url', '')); ?>" required
                               placeholder="<?php echo e(__('cruds.integrationConfiguration.fields.endpoint_url_placeholder')); ?>">
                        <?php if($errors->has('endpoint_url')): ?>
                            <div class="invalid-feedback">
                                <?php echo e($errors->first('endpoint_url')); ?>

                            </div>
                        <?php endif; ?>
                    </div>
                    <label for="endpoint_alias" class="col-md-2 col-form-label required"><?php echo e(__('cruds.integrationConfiguration.fields.endpoint_alias')); ?></label>
                    <div class="col-md-4">
                        <input type="text" class="form-control <?php echo e($errors->has('endpoint_alias') ? 'is-invalid' : ''); ?>"
                               id="endpoint_alias" name="endpoint_alias" value="<?php echo e(old('endpoint_alias', '')); ?>" required
                               placeholder="<?php echo e(__('cruds.integrationConfiguration.fields.endpoint_alias_placeholder')); ?>">
                        <?php if($errors->has('endpoint_alias')): ?>
                            <div class="invalid-feedback">
                                <?php echo e($errors->first('endpoint_alias')); ?>

                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="form-group row">
                    <div class="col-12 mb-2">
                        <label class="form-label required"><?php echo e(__('cruds.integrationConfiguration.fields.request_fields')); ?></label>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <small class="text-muted"><?php echo e(__('cruds.integrationConfiguration.fields.request_fields_helper')); ?></small>
                            <div>
                                <button type="button" class="btn btn-secondary btn-sm me-2" id="importJsonBtn" title="<?php echo e(__('global.import_fields_from_json')); ?>">
                                    <i class="fas fa-file-import"></i> <?php echo e(__('global.import')); ?> JSON
                                </button>
                                <button type="button" class="btn btn-secondary btn-sm" id="addFieldBtn" title="<?php echo e(__('global.add_field')); ?>">
                                    <i class="fas fa-plus"></i> <?php echo e(__('global.add_field')); ?>

                                </button>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-bordered table-sm">
                                <thead class="table-light">
                                    <tr>
                                        <th style="width: 25%;"><?php echo e(__('cruds.fields.field_key')); ?></th>
                                        <th style="width: 25%;"><?php echo e(__('cruds.fields.name')); ?></th>
                                        <th style="width: 10%;"><?php echo e(__('cruds.fields.type')); ?></th>
                                        <th style="width: 10%;"><?php echo e(__('cruds.fields.max_length')); ?></th>
                                        <th style="width: 10%;"><?php echo e(__('cruds.fields.required')); ?></th>
                                        <th style="width: 15%; display: none"><?php echo e(__('cruds.fields.default')); ?></th>
                                        <th style="width: 5%;"><?php echo e(__('global.actions')); ?></th>
                                    </tr>
                                </thead>
                                <tbody id="fieldMappingContainer">
                                    <!-- Field mappings will be added here -->
                                </tbody>
                            </table>
                        </div>
                        <div id="noFieldsMessage" class="text-muted text-center py-4 border rounded">
                            <i class="fas fa-inbox fa-2x mb-2"></i>
                            <p class="mb-0"><?php echo e(__('global.no_fields_added')); ?></p>
                            <p class="small"><?php echo e(__('global.click_add_to_get_started')); ?></p>
                        </div>
                        <input type="hidden" name="request_fields" id="request_fields_hidden" value="<?php echo e(old('request_fields', '')); ?>">
                        <?php if($errors->has('request_fields')): ?>
                            <div class="invalid-feedback d-block">
                                <?php echo e($errors->first('request_fields')); ?>

                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="form-group row justify-content-end">
                    <div class="col-auto">
                        <a href="<?php echo e(route('admin.integration-configurations.index')); ?>" class="btn btn-default mr-2">
                            <?php echo e(__('global.cancel')); ?>

                        </a>
                        <button type="submit" class="btn btn-primary">
                            <?php echo e(__('global.save')); ?>

                        </button>
                    </div>
                </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('modal'); ?>
<!-- Field Selector Modal -->
<div class="modal fade" id="fieldSelectorModal" tabindex="-1" role="dialog" aria-labelledby="fieldSelectorModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="fieldSelectorModalLabel">Select or Create Field</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <ul class="nav nav-tabs" id="fieldSelectorTabs" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" id="existing-tab" data-toggle="tab" href="#existing-fields" role="tab" aria-controls="existing-fields" aria-selected="true">Existing Fields</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="custom-tab" data-toggle="tab" href="#custom-field" role="tab" aria-controls="custom-field" aria-selected="false">Custom Field</a>
                    </li>
                </ul>
                <div class="tab-content mt-3" id="fieldSelectorTabsContent">
                    <div class="tab-pane fade show active" id="existing-fields" role="tabpanel" aria-labelledby="existing-tab">
                        <div class="form-group">
                            <label for="field_definition_select">Select Field</label>
                            <select class="form-control" id="field_definition_select">
                                <option value="">-- Select a field --</option>
                                <?php $__currentLoopData = \App\Models\FieldDefinition::all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $field): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value='<?php echo e(json_encode($field->only(['field_key', 'name', 'data_type', 'is_required', 'description']))); ?>'>
                                        <?php echo e($field->name); ?> (<?php echo e($field->field_key); ?>)
                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <button type="button" class="btn btn-primary" id="useSelectedField">Use Selected Field</button>
                    </div>
                    <div class="tab-pane fade" id="custom-field" role="tabpanel" aria-labelledby="custom-tab">
                        <div class="form-group">
                            <label for="custom_field_name">Field Name</label>
                            <input type="text" class="form-control" id="custom_field_name" placeholder="Enter field name">
                        </div>
                        <button type="button" class="btn btn-primary" id="useCustomField">Create Custom Field</button>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>

<!-- Original Content -->
<div class="card card-default">
    <div class="card-header separator">
        <div class="card-title mainheading">
            <h4><?php echo e(__('global.create')); ?> <?php echo e(__('cruds.integrationConfiguration.title_singular')); ?></h4>
        </div>
        <div class="card-controls">
            <ul>
                <li>
                    <?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.buttons.back','data' => ['route' => route('admin.integration-configurations.index'),'text' => __('global.back_to_list')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('buttons.back'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['route' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('admin.integration-configurations.index')),'text' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(__('global.back_to_list'))]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>
                </li>
            </ul>
        </div>
    </div>
    <div class="card-block">
        <?php if($errors->any()): ?>
            <div class="alert alert-danger">
                <ul>
                    <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <li><?php echo e($error); ?></li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>
            </div>
        <?php endif; ?>

        <form method="POST" action="<?php echo e(route('admin.integration-configurations.store')); ?>" enctype="multipart/form-data">
            <?php echo csrf_field(); ?>

            <div class="form-group row">
                <label for="name" class="col-md-3 col-form-label required"><?php echo e(__('cruds.integrationConfiguration.fields.name')); ?></label>
                <div class="col-md-9">
                    <input type="text" class="form-control <?php echo e($errors->has('name') ? 'is-invalid' : ''); ?>" id="name" name="name" value="<?php echo e(old('name', '')); ?>" required>
                    <?php if($errors->has('name')): ?>
                        <div class="invalid-feedback">
                            <?php echo e($errors->first('name')); ?>

                        </div>
                    <?php endif; ?>
                    <!-- <small class="form-text text-muted"><?php echo e(__('cruds.integrationConfiguration.fields.name_helper')); ?></small> -->
                </div>
            </div>

            <div class="form-group row">
                <label for="external_system" class="col-md-3 col-form-label required"><?php echo e(__('cruds.integrationConfiguration.fields.external_system')); ?></label>
                <div class="col-md-9">
                    <select class="form-control <?php echo e($errors->has('external_system') ? 'is-invalid' : ''); ?>" name="external_system" id="external_system" required>
                        <option value=""><?php echo e(__('global.pleaseSelect')); ?></option>
                        <?php $__currentLoopData = $externalSystemOptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($key); ?>" <?php echo e(old('external_system') == $key ? 'selected' : ''); ?>><?php echo e($label); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                    <?php if($errors->has('external_system')): ?>
                        <div class="invalid-feedback">
                            <?php echo e($errors->first('external_system')); ?>

                        </div>
                    <?php endif; ?>
                    <!-- <small class="form-text text-muted">Select the external system to integrate with</small> -->
                </div>
            </div>

            <div class="form-group row">
                <label for="process_type" class="col-md-3 col-form-label required"><?php echo e(__('cruds.integrationConfiguration.fields.process_type')); ?></label>
                <div class="col-md-9">
                    <select class="form-control <?php echo e($errors->has('process_type') ? 'is-invalid' : ''); ?>" name="process_type" id="process_type" required>
                        <option value=""><?php echo e(__('global.pleaseSelect')); ?></option>
                        <?php $__currentLoopData = $processTypeOptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($key); ?>" <?php echo e(old('process_type') == $key ? 'selected' : ''); ?>><?php echo e($label); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                    <?php if($errors->has('process_type')): ?>
                        <div class="invalid-feedback">
                            <?php echo e($errors->first('process_type')); ?>

                        </div>
                    <?php endif; ?>
                    <!-- <small class="form-text text-muted">Select the type of process this integration handles</small> -->
                </div>
            </div>

            <div class="form-group row">
                <label for="integration_method" class="col-md-3 col-form-label required"><?php echo e(__('cruds.integrationConfiguration.fields.integration_method')); ?></label>
                <div class="col-md-9">
                    <select class="form-control <?php echo e($errors->has('integration_method') ? 'is-invalid' : ''); ?>" name="integration_method" id="integration_method" required>
                        <option value=""><?php echo e(__('global.pleaseSelect')); ?></option>
                        <?php $__currentLoopData = $integrationMethodOptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($key); ?>" <?php echo e(old('integration_method') == $key ? 'selected' : ''); ?>><?php echo e($label); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                    <?php if($errors->has('integration_method')): ?>
                        <div class="invalid-feedback">
                            <?php echo e($errors->first('integration_method')); ?>

                        </div>
                    <?php endif; ?>
                    <!-- <small class="form-text text-muted">Select the method used to integrate with the external system</small> -->
                </div>
            </div>

            <div class="form-group row">
                <label for="endpoint_url" class="col-md-3 col-form-label required"><?php echo e(__('cruds.integrationConfiguration.fields.endpoint_url')); ?></label>
                <div class="col-md-9">
                    <input type="url" class="form-control <?php echo e($errors->has('endpoint_url') ? 'is-invalid' : ''); ?>" id="endpoint_url" name="endpoint_url" value="<?php echo e(old('endpoint_url', '')); ?>" required>
                    <?php if($errors->has('endpoint_url')): ?>
                        <div class="invalid-feedback">
                            <?php echo e($errors->first('endpoint_url')); ?>

                        </div>
                    <?php endif; ?>
                    <!-- <small class="form-text text-muted">Enter the URL of the API endpoint</small> -->
                </div>
            </div>

            <div class="form-group row">
                <label for="request_method" class="col-md-3 col-form-label required"><?php echo e(__('cruds.integrationConfiguration.fields.request_method')); ?></label>
                <div class="col-md-9">
                    <select class="form-control <?php echo e($errors->has('request_method') ? 'is-invalid' : ''); ?>" name="request_method" id="request_method" required>
                        <option value=""><?php echo e(__('global.pleaseSelect')); ?></option>
                        <option value="GET" <?php echo e(old('request_method') == 'GET' ? 'selected' : ''); ?>>GET</option>
                        <option value="POST" <?php echo e(old('request_method') == 'POST' ? 'selected' : ''); ?>>POST</option>
                        <option value="PUT" <?php echo e(old('request_method') == 'PUT' ? 'selected' : ''); ?>>PUT</option>
                        <option value="PATCH" <?php echo e(old('request_method') == 'PATCH' ? 'selected' : ''); ?>>PATCH</option>
                        <option value="DELETE" <?php echo e(old('request_method') == 'DELETE' ? 'selected' : ''); ?>>DELETE</option>
                    </select>
                    <?php if($errors->has('request_method')): ?>
                        <div class="invalid-feedback">
                            <?php echo e($errors->first('request_method')); ?>

                        </div>
                    <?php endif; ?>
                    <!-- <small class="form-text text-muted">Select the HTTP method to use for the request</small> -->
                </div>
            </div>

            <div class="form-group row">
                <label for="is_active" class="col-md-3 col-form-label"><?php echo e(__('cruds.integrationConfiguration.fields.is_active')); ?></label>
                <div class="col-md-9">
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="is_active" name="is_active" value="1" <?php echo e(old('is_active', 1) ? 'checked' : ''); ?>>
                        <label class="form-check-label" for="is_active">Active</label>
                    </div>
                    <?php if($errors->has('is_active')): ?>
                        <div class="invalid-feedback">
                            <?php echo e($errors->first('is_active')); ?>

                        </div>
                    <?php endif; ?>
                    <!-- <small class="form-text text-muted">Whether this integration is active</small> -->
                </div>
            </div>

            <div class="form-group row">
                <label class="col-md-3 col-form-label">Field Mappings</label>
                <div class="col-md-9">
                    <div id="fieldMappingContainer" class="mb-3">
                        <!-- Dynamic fields will be added here -->
                    </div>
                    <div id="noFieldsMessage" class="alert alert-info" style="display: none;">
                        No fields have been added yet. Click the "Add Field" button below to get started.
                    </div>
                    <button type="button" id="addFieldBtn" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add Field
                    </button>
                    <button type="button" id="importJsonBtn" class="btn btn-secondary">
                        <i class="fas fa-file-import"></i> Import JSON
                    </button>
                    <input type="hidden" name="request_fields" id="request_fields_hidden" value="">
                </div>
            </div>

            <div class="form-group row">
                <div class="col-12">
                    <div class="float-end">
                        <?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.buttons.cancel','data' => ['route' => route('admin.integration-configurations.index'),'class' => 'me-2']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('buttons.cancel'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['route' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('admin.integration-configurations.index')),'class' => 'me-2']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>
                        <?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.buttons.save','data' => ['route' => route('admin.integration-configurations.store')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('buttons.save'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['route' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('admin.integration-configurations.store'))]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('modal'); ?>
<!-- Field Selector Modal -->
<div class="modal fade" id="fieldSelectorModal" tabindex="-1" role="dialog" aria-labelledby="fieldSelectorModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="fieldSelectorModalLabel">Select or Create Field</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <ul class="nav nav-tabs" id="fieldSelectorTabs" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" id="existing-tab" data-toggle="tab" href="#existing-fields" role="tab" aria-controls="existing-fields" aria-selected="true">Existing Fields</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="custom-tab" data-toggle="tab" href="#custom-field" role="tab" aria-controls="custom-field" aria-selected="false">Custom Field</a>
                    </li>
                </ul>
                <div class="tab-content mt-3" id="fieldSelectorTabsContent">
                    <div class="tab-pane fade show active" id="existing-fields" role="tabpanel" aria-labelledby="existing-tab">
                        <div class="form-group">
                            <label for="field_definition_select">Select Field</label>
                            <select class="form-control" id="field_definition_select">
                                <option value="">-- Select a field --</option>
                                <?php $__currentLoopData = \App\Models\FieldDefinition::all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $field): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value='<?php echo e(json_encode($field->only(['field_key', 'name', 'data_type', 'is_required', 'description']))); ?>'>
                                        <?php echo e($field->name); ?> (<?php echo e($field->field_key); ?>)
                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <button type="button" class="btn btn-primary" id="useSelectedField">Use Selected Field</button>
                    </div>
                    <div class="tab-pane fade" id="custom-field" role="tabpanel" aria-labelledby="custom-tab">
                        <div class="form-group">
                            <label for="custom_field_name">Field Name</label>
                            <input type="text" class="form-control" id="custom_field_name" placeholder="Enter field name">
                        </div>
                        <button type="button" class="btn btn-primary" id="useCustomField">Create Custom Field</button>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>
<!-- End Field Selector Modal -->
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
// Debug: Check if jQuery is loaded
console.log('jQuery version:', $.fn.jquery);

// Check which Bootstrap version is loaded
var bootstrapVersion = typeof bootstrap !== 'undefined' ? 'Bootstrap 5' : 'Bootstrap 3/4';
console.log('Bootstrap Version:', bootstrapVersion);

// Field mapping data store
let fieldMappings = [];

// Initialize when document is ready
$(document).ready(function() {
    // Initialize field mappings from hidden input if it exists
    const fieldMappingInput = $('#fieldMappingInput');
    let fieldMappings = [];

    if (fieldMappingInput.length && fieldMappingInput.val()) {
        try {
            fieldMappings = JSON.parse(fieldMappingInput.val());
        } catch (e) {
            console.error('Error parsing field mappings:', e);
            fieldMappings = [];
        }
        renderFieldMappings();
    }

    // Field search functionality
    $('#fieldSearch').on('input', function() {
        const searchTerm = $(this).val().toLowerCase();
        $('.field-item').each(function() {
            const fieldName = $(this).data('field-name').toLowerCase();
            const fieldKey = $(this).data('field-key').toLowerCase();
            if (fieldName.includes(searchTerm) || fieldKey.includes(searchTerm)) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    });

    // Auto-generate field key from field name
    $('#customFieldName').on('input', function() {
        const fieldName = $(this).val();
        const fieldKey = fieldName
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, '_')
            .replace(/^_+|_+$/g, '');
        $('#customFieldKey').val(fieldKey);
    });

    // Handle selection of existing field
    $(document).on('click', '.field-item', function(e) {
        e.preventDefault();
        const fieldKey = $(this).data('field-key');
        const fieldName = $(this).data('field-name');
        addFieldMapping(fieldKey, fieldName);
        hideModal();
    });

    // Handle add custom field
    $('#addCustomField').on('click', function() {
        const fieldName = $('#customFieldName').val().trim();
        const fieldKey = $('#customFieldKey').val().trim();

        if (!fieldName) {
            alert('Please enter a field name');
            return;
        }

        if (!fieldKey) {
            alert('Please enter a valid field key');
            return;
        }

        addFieldMapping(fieldKey, fieldName);
        hideModal();
    });

    // Handle remove field
    $(document).on('click', '.remove-field', function() {
        const index = $(this).data('index');
        fieldMappings.splice(index, 1);
        renderFieldMappings();
    });
});

// Add a field mapping
function addFieldMapping(fieldKey, fieldName) {
    // Check if field key already exists
    if (fieldMappings.some(field => field.key === fieldKey)) {
        alert('This field has already been added');
        return;
    }

    fieldMappings.push({
        key: fieldKey,
        name: fieldName,
        type: 'string', // Default type, can be changed if needed
        required: false
    });

    renderFieldMappings();
}

// Render field mappings in the UI
function renderFieldMappings() {
    const container = $('#fieldMappingContainer');
    container.empty();

    if (fieldMappings.length === 0) {
        container.html('<div class="text-muted">No fields added yet. Click "Add Field" to get started.</div>');
        $('#fieldMappingInput').val(JSON.stringify([]));
        return;
    }

    const table = $('<table class="table table-bordered"></table>');
    const thead = $('<thead><tr><th>Field Name</th><th>Field Key</th><th>Type</th><th>Required</th><th>Actions</th></tr></thead>');
    const tbody = $('<tbody></tbody>');

    fieldMappings.forEach((field, index) => {
        const row = $('<tr></tr>');

        // Field Name
        const nameCell = $('<td></td>').text(field.name);

        // Field Key
        const keyCell = $('<td></td>').text(field.key);

        // Field Type
        const typeCell = $('<td></td>');
        const typeSelect = $(`<select class="form-control form-control-sm field-type" data-index="${index}">
            <option value="string" ${field.type === 'string' ? 'selected' : ''}>Text</option>
            <option value="number" ${field.type === 'number' ? 'selected' : ''}>Number</option>
            <option value="boolean" ${field.type === 'boolean' ? 'selected' : ''}>True/False</option>
            <option value="date" ${field.type === 'date' ? 'selected' : ''}>Date</option>
            <option value="datetime" ${field.type === 'datetime' ? 'selected' : ''}>Date & Time</option>
        </select>`);
        typeCell.append(typeSelect);

        // Required
        const requiredCell = $('<td class="text-center"></td>');
        const requiredCheckbox = $(`<input type="checkbox" class="form-check-input field-required" data-index="${index}" ${field.required ? 'checked' : ''}>`);
        requiredCell.append(requiredCheckbox);

        // Actions
        const actionsCell = $('<td class="text-center"></td>');
        const removeBtn = $(`<button type="button" class="btn btn-sm btn-danger remove-field" data-index="${index}">
            <i class="fa fa-trash"></i>
        </button>`);
        actionsCell.append(removeBtn);

        row.append(nameCell, keyCell, typeCell, requiredCell, actionsCell);
        tbody.append(row);
    });

    table.append(thead, tbody);
    container.append(table);

    // Update hidden input with current mappings
    updateFieldMappingsInput();
}

// Update field type
$(document).on('change', '.field-type', function() {
    const index = $(this).data('index');
    fieldMappings[index].type = $(this).val();
    updateFieldMappingsInput();
});

// Update required status
$(document).on('change', '.field-required', function() {
    const index = $(this).data('index');
    fieldMappings[index].required = $(this).is(':checked');
    updateFieldMappingsInput();
});

// Update the hidden input with current mappings
function updateFieldMappingsInput() {
    $('#fieldMappingInput').val(JSON.stringify(fieldMappings));
}

// Hide modal and reset form
function hideModal() {
    const modal = bootstrap.Modal.getInstance(document.getElementById('fieldSelectionModal'));
    if (modal) {
        modal.hide();
    } else {
        $('#fieldSelectionModal').modal('hide');
    }

    // Reset form
    $('#fieldSearch').val('');
    $('.field-item').show();
    $('#customFieldName').val('');
    $('#customFieldKey').val('');
}
</script>

<script>
    $(document).ready(function () {
        console.log('Document ready - initializing field mapping');

        // Initialize field mapping
        const fieldMappingContainer = $('#fieldMappingContainer');
        const addFieldBtn = $('#addFieldBtn');
        const importJsonBtn = $('#importJsonBtn');
        const noFieldsMessage = $('#noFieldsMessage');
        const hiddenInput = $('#request_fields_hidden');

        // Data types for dropdown
        const dataTypes = ['string', 'number', 'integer', 'boolean', 'date', 'datetime', 'array', 'object'];

        // Load existing fields if any
        let fields = [];
        try {
            const savedData = hiddenInput.val();
            console.log('Saved data:', savedData);
            if (savedData) {
                fields = JSON.parse(savedData);
                renderFields();
            }
        } catch (e) {
            console.error('Error parsing saved field data:', e);
        }

        // Add new field
        $(document).on('click', '#addFieldBtn', function(e) {
            console.log('Add field button clicked');
            e.preventDefault();

            // Reset modal state
            $('#field_definition_select').val('');
            $('#custom_field_name').val('');
            $('#fieldSelectorTabs a[href="#existing-fields"]').tab('show');

            // Show modal to select or create field
            $('#fieldSelectorModal').modal('show');

            return false;
        });

        // Handle field selection from modal
        $('#useSelectedField').on('click', function() {
            const selectedField = $('#field_definition_select').val();
            if (selectedField) {
                try {
                    const field = JSON.parse(selectedField);
                    fields.push({
                        field_name: field.field_key,
                        field_label: field.name,
                        field_type: field.data_type || 'string',
                        is_required: field.is_required || false,
                        description: field.description || '',
                        example: ''
                    });
                    renderFields();
                    $('#fieldSelectorModal').modal('hide');
                    // Scroll to the newly added field
                    $('html, body').animate({
                        scrollTop: fieldMappingContainer.children().last().offset().top - 100
                    }, 300);
                } catch (e) {
                    console.error('Error parsing field data:', e);
                    alert('Error: Invalid field data');
                }
            } else {
                alert('Please select a field');
            }
        });

        // Handle custom field creation
        $('#useCustomField').on('click', function() {
            const customFieldName = $('#custom_field_name').val().trim();
            if (customFieldName) {
                fields.push({
                    field_name: customFieldName.toLowerCase().replace(/[^a-z0-9_]/g, '_'),
                    field_label: customFieldName,
                    field_type: 'string',
                    is_required: false,
                    description: '',
                    example: ''
                });
                renderFields();
                $('#fieldSelectorModal').modal('hide');
                // Reset custom field input
                $('#custom_field_name').val('');
                // Scroll to the newly added field
                $('html, body').animate({
                    scrollTop: fieldMappingContainer.children().last().offset().top - 100
                }, 300);
            } else {
                alert('Please enter a field name');
            }
        });

        // Import fields from JSON
        importJsonBtn.on('click', function() {
            const jsonString = prompt('Paste your JSON configuration:');
            if (!jsonString) return;

            try {
                const jsonData = JSON.parse(jsonString);
                const importedFields = [];

                if (Array.isArray(jsonData)) {
                    // If it's an array, assume it's already in our field format
                    importedFields.push(...jsonData);
                } else if (typeof jsonData === 'object') {
                    // If it's an object, convert to field format
                    Object.entries(jsonData).forEach(([key, value]) => {
                        const fieldType = determineDataType(value);
                        importedFields.push({
                            field_name: key,
                            field_type: fieldType,
                            is_required: true,
                            description: '',
                            example: fieldType === 'object' || fieldType === 'array'
                                ? JSON.stringify(value, null, 2)
                                : String(value)
                        });
                    });
                }

                if (importedFields.length > 0) {
                    fields = importedFields;
                    renderFields();
                } else {
                    alert('No valid fields found in the provided JSON.');
                }
            } catch (e) {
                alert('Invalid JSON format. Please check your input and try again.');
                console.error('Error parsing JSON:', e);
            }
        });

        // Determine data type from value
        function determineDataType(value) {
            if (value === null || value === undefined) return 'string';
            if (Array.isArray(value)) return 'array';
            if (typeof value === 'object') return 'object';
            if (typeof value === 'number') return Number.isInteger(value) ? 'integer' : 'number';
            if (typeof value === 'boolean') return 'boolean';
            if (typeof value === 'string') {
                // Check for date/datetime
                const date = new Date(value);
                if (!isNaN(date.getTime())) {
                    return value.includes('T') || value.includes(' ') ? 'datetime' : 'date';
                }
            }
            return 'string';
        }

        // Function to flatten object and create field mappings
        function generateFieldMappingsFromSample(data, prefix = '', flatten = true, isRoot = true, depth = 0) {
            console.log(`generateFieldMappingsFromSample called with prefix: '${prefix}', isRoot: ${isRoot}, depth: ${depth}, data type:`, typeof data, 'data:', data);
            const fields = [];
            const indent = '  '.repeat(depth);
            console.log(`${indent}Processing:`, { data, prefix, flatten, isRoot });

            if (data === null || data === undefined) {
                console.log(`${indent}Data is null/undefined, returning empty fields`);
            return Number.isInteger(value) ? 'integer' : 'float';
        }
        if (typeof value === 'boolean') return 'boolean';
        if (typeof value === 'string') {
            // Check for date/datetime
            const date = new Date(value);
            if (!isNaN(date.getTime())) {
                return value.includes('T') || value.includes(' ') ? 'datetime' : 'date';
            }
            // Check for email
            if (value.includes('@') && value.includes('.')) {
                return 'email';
            }
            // Check for URL
            try {
                new URL(value);
                return 'url';
            } catch (e) {}
        }
        return 'string';
    }

    // Function to flatten object and create field mappings
    function generateFieldMappingsFromSample(data, prefix = '', flatten = true, isRoot = true, depth = 0) {
        console.log(`generateFieldMappingsFromSample called with prefix: '${prefix}', isRoot: ${isRoot}, depth: ${depth}, data type:`, typeof data, 'data:', data);
        const fields = [];
        const indent = '  '.repeat(depth);
        console.log(`${indent}Processing:`, { data, prefix, flatten, isRoot });

        if (data === null || data === undefined) {
            console.log(`${indent}Data is null/undefined, returning empty fields`);
            return fields;
        }

        // Handle arrays - process each item and merge unique fields
        if (Array.isArray(data)) {
            console.log(`${indent}Processing array with ${data.length} items`);
            if (data.length === 0) {
                console.log(`${indent}Empty array, returning empty fields`);
                return fields;
            }

            // For arrays of objects, collect all unique fields from all objects
            if (typeof data[0] === 'object' && data[0] !== null) {
                console.log(`${indent}Processing array of objects`);

                // Collect all unique field names
                const allFieldNames = new Set();
                data.forEach(item => {
                    if (item && typeof item === 'object') {
                        Object.keys(item).forEach(key => allFieldNames.add(key));
                    }
                });

                // Create a merged object with all possible fields
                const mergedObject = {};
                allFieldNames.forEach(fieldName => {
                    // Find the first non-null value for this field
                    const value = data.find(item => item && item[fieldName] !== undefined);
                    if (value) {
                        mergedObject[fieldName] = value[fieldName];
                    }
                });

                console.log(`${indent}Merged object with ${Object.keys(mergedObject).length} fields:`, mergedObject);
                return generateFieldMappingsFromSample(mergedObject, prefix, flatten, isRoot, depth + 1);
            }
            // For arrays of primitives, treat as a single field
            else if (data.length > 0) {
                console.log(`${indent}Processing array of primitives`);
                const field = {
                    name: prefix || 'array_field',
                    datatype: 'array',
                    required: false,
                    description: `Array field ${prefix || ''}`,
                    default: JSON.stringify(data)
                };
                console.log(`${indent}Created array field:`, field);
                fields.push(field);
                return fields;
            }
            console.log(`${indent}No items in array, returning empty fields`);
            return fields;
        }

        // Handle objects
        if (typeof data === 'object') {
            console.log(`${indent}Processing object with keys:`, Object.keys(data));
            Object.entries(data).forEach(([key, value]) => {
                const fieldName = prefix ? `${prefix}_${key}` : key;
                console.log(`${indent}Processing key '${key}' with value:`, value);

                // Handle nested objects
                if (value !== null && typeof value === 'object' && !Array.isArray(value)) {
                    console.log(`${indent}Key '${key}' is an object`);
                    if (flatten) {
                        console.log(`${indent}Flattening nested object '${key}'`);
                        const nestedFields = generateFieldMappingsFromSample(value, fieldName, flatten, false, depth + 1);
                        console.log(`${indent}Got ${nestedFields.length} fields from nested object '${key}'`);
                        fields.push(...nestedFields);
                    } else if (isRoot || !flatten) {
                        console.log(`${indent}Creating object field for '${key}'`);
                        const field = {
                            name: fieldName,
                            datatype: 'object',
                            required: false,
                            description: `Object field ${fieldName}`,
                            default: '{}'
                        };
                        fields.push(field);
                    }
                }
                // Handle arrays
                else if (Array.isArray(value)) {
                    console.log(`${indent}Key '${key}' is an array`);
                    if (flatten) {
                        console.log(`${indent}Processing array items for '${key}'`);
                        const arrayFields = generateFieldMappingsFromSample(value, fieldName, flatten, false, depth + 1);
                        console.log(`${indent}Got ${arrayFields.length} fields from array '${key}'`);
                        fields.push(...arrayFields);
                    } else {
                        console.log(`${indent}Creating array field for '${key}'`);
                        const field = {
                            name: fieldName,
                            datatype: 'array',
                            required: false,
                            description: `Array field ${fieldName}`,
                            default: '[]'
                        };
                        fields.push(field);
                    }
                }
                // Handle primitive values
                else {
                    console.log(`${indent}Key '${key}' is a primitive (${typeof value}):`, value);
                    const field = {
                        name: fieldName,
                        datatype: determineDataType(value),
                        required: false,
                        description: `Field ${fieldName}`
                    };

                    // Set reasonable defaults based on data type
                    if (field.datatype === 'string') {
                        field.maxlength = 255;
                        field.default = value !== null && value !== undefined ? String(value) : '';
                    } else if (field.datatype === 'integer' || field.datatype === 'float') {
                        field.maxlength = 20;
                        field.default = value !== null && value !== undefined ? String(value) : '0';
                    } else if (field.datatype === 'boolean') {
                        field.default = value ? 'true' : 'false';
                    }

                    console.log(`${indent}Created field:`, field);
                    fields.push(field);
                }
            });
        } else {
            console.log(`${indent}Data is not an object or array:`, typeof data);
        }

        console.log(`${indent}Returning ${fields.length} fields`);
        return fields;
    }

    // Function to show/hide no fields message
    function updateNoFieldsMessage() {
        const fieldRows = $('.field-row');
        if (fieldRows.length === 0) {
            $('#noFieldsMessage').show();
        } else {
            $('#noFieldsMessage').hide();
        }
    }

    // Function to add a new field row
    function addFieldRow(fieldData = {}) {
        const fieldId = 'field-' + Date.now();
        const fieldRow = `
            <tr class="field-row" data-field-id="${fieldId}">
                <td style="width: 25%;">
                    <input type="text" class="form-control form-control-sm field-key"
                           placeholder="e.g., item_code" value="${fieldData.key || fieldData.name || ''}" required>
                </td>
                <td style="width: 25%;">
                    <input type="text" class="form-control form-control-sm field-name"
                           placeholder="e.g., Item Code" value="${fieldData.field_name || fieldData.name || ''}" required>
                </td>
                <td style="width: 10%;">
                    <select class="form-control form-control-sm field-datatype">
                        ${dataTypes.map(type =>
                            `<option value="${type}" ${fieldData.datatype === type ? 'selected' : ''}>${type}</option>`
                        ).join('')}
                    </select>
                </td>
                <td style="width: 10%;">
                    <input type="number" class="form-control form-control-sm field-maxlength"
                           placeholder="255" value="${fieldData.maxlength !== null && fieldData.maxlength !== undefined ? fieldData.maxlength : ''}" min="1">
                </td>
                <td style="width: 10%;">
                    <div class="form-check d-flex justify-content-center align-items-center h-100">
                        <input type="checkbox" class="form-check-input field-required" ${fieldData.required ? 'checked' : ''}>
                    </div>
                </td>
                <td style="width: 15%; display: none">
                    <input type="text" class="form-control form-control-sm field-default"
                           placeholder="Optional" value="${fieldData.default !== null && fieldData.default !== undefined ? fieldData.default : ''}">
                </td>
                <td class="text-center" style="width: 5%;">
                    <?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.buttons.delete','data' => ['route' => '#','size' => 'sm','title' => __('global.delete'),'confirm' => __('global.are_you_sure_delete', ['name' => 'this field']),'iconOnly' => true,'class' => 'remove-field','style' => 'border: none; background: none;']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('buttons.delete'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['route' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute('#'),'size' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute('sm'),'title' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(__('global.delete')),'confirm' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(__('global.are_you_sure_delete', ['name' => 'this field'])),'iconOnly' => true,'class' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute('remove-field'),'style' => 'border: none; background: none;']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>
                </td>
            </tr>
        `;

        $('#fieldMappingContainer').append(fieldRow);
        updateNoFieldsMessage();
        updateHiddenField();
    }

    // Remove field
    $(document).on('click', '.remove-field', function() {
        $(this).closest('.field-row').remove();
        updateNoFieldsMessage();
        updateHiddenField();
    });

    // Update hidden field when any input changes
    $(document).on('input change', '.field-row input, .field-row select', function() {
        updateHiddenField();
    });

    // Function to update the hidden field with JSON data
    function updateHiddenField() {
        const fields = [];

        $('.field-row').each(function() {
            const row = $(this);
            const field = {
                key: row.find('.field-key').val().trim(),
                name: row.find('.field-name').val().trim(),
                datatype: row.find('.field-datatype').val(),
                maxlength: row.find('.field-maxlength').val() ? parseInt(row.find('.field-maxlength').val()) : null,
                required: row.find('.field-required').val() === 'true',
                default: row.find('.field-default').val().trim() || null
            };

            // Only add the field if it has a name and key
            if (field.name && field.key) {
                fields.push(field);
            }
        });

        // Update both hidden fields if they exist
        const jsonData = JSON.stringify(fields);
        $('#request_fields_hidden').val(jsonData);
        if ($('#body_data_field_hidden').length) {
            $('#body_data_field_hidden').val(jsonData);
        }

        console.log('Updated hidden fields with:', fields);
    }

    // Initial state
    updateNoFieldsMessage();

    // JSON Import Modal
    const importModal = `
    <div class="modal fade" id="importJsonModal" tabindex="-1" role="dialog" aria-labelledby="importJsonModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="importJsonModalLabel">Import Fields from Sample Data</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="sampleDataInput">Paste sample JSON response from your endpoint:</label>
                        <textarea class="form-control" id="sampleDataInput" rows="10" placeholder='{
                            "id": 123,
                            "name": "Example Product",
                            "price": 99.99,
                            "in_stock": true,
                            "tags": ["electronics", "new"],
                            "details": {
                                "weight": 2.5,
                                "dimensions": "10x15x5cm"
                            },
                            "created_at": "2023-01-01T12:00:00Z"
                        }'></textarea>
                        <small class="form-text text-muted">
                            Paste a sample JSON response to automatically generate field mappings
                        </small>
                    </div>
                    <div class="form-group">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="flattenNested" checked>
                            <label class="custom-control-label" for="flattenNested">Flatten nested objects (e.g., details.weight → details_weight)</label>
                        </div>
                    </div>
                    <div id="jsonError" class="alert alert-danger mt-3" style="display: none;"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fa fa-times"></i> Cancel
                    </button>
                    <button type="button" class="btn btn-primary" id="importJsonSubmit">
                        <i class="fa fa-file-import"></i> Import JSON
                    </button>
                </div>
            </div>
        </div>
    </div>`;

    // Debug: Log when script loads
    console.log('Endpoint configuration form script loaded');

    // Add modal to DOM
    $('body').append(importModal);
    console.log('Import modal added to DOM');

    // Debug: Check if button exists
    const $importButton = $('#importJsonBtn');
    console.log('Import button found:', $importButton.length > 0);

    // Open import modal
    $importButton.on('click', function(e) {
        console.log('Import button clicked', e);
        $('#jsonInput').val('');
        $('#jsonError').hide();
        console.log('Showing import modal');
        $('#importJsonModal').modal('show');
    });

    // Debug: Check event binding
    console.log('Event listeners for import button:', $._data($importButton[0], 'events'));

    // Debug: Check if modal is properly initialized
    console.log('Import modal initialized');

    // Handle JSON import
    $('#importJsonSubmit').on('click', function(e) {
        console.log('Import submit button clicked', e);

        // Prevent form submission
        e.preventDefault();
        e.stopPropagation();

        try {
            let fields = [];
            console.log('Processing sample data import');
            const sampleDataInput = $('#sampleDataInput').val().trim();
            console.log('Raw sample data input:', sampleDataInput);

            if (!sampleDataInput) {
                showJsonError('Please paste sample JSON data');
                return;
            }

            console.log('Attempting to parse sample data...');
            let sampleData;
            try {
                sampleData = JSON.parse(sampleDataInput);
                console.log('Successfully parsed sample data:', sampleData);
            } catch (e) {
                console.error('Error parsing sample data:', e);
                throw new Error('Invalid JSON format: ' + e.message);
            }

            const flattenNested = $('#flattenNested').is(':checked');
            console.log('Flatten nested objects:', flattenNested);

            console.log('Generating field mappings...');
            fields = generateFieldMappingsFromSample(sampleData, '', flattenNested, true);
            console.log('Generated fields:', fields);

            if (fields.length === 0) {
                console.warn('No fields were generated from the sample data');
                throw new Error('Could not generate any fields from the sample data');
            }

            console.log(`Successfully generated ${fields.length} fields from sample data`);

            // Clear existing fields
            $('.field-row').remove();

            // Add each field from the generated fields
            fields.forEach(field => {
                if (field && typeof field === 'object') {
                    // Normalize field property names for compatibility
                    const normalizedField = {
                        key: field.key || field.field_key || field.name || '',
                        field_name: field.field_name || field.name || '',
                        name: field.name || field.field_name || '',
                        datatype: field.datatype || field.data_type || field.type || 'string',
                        maxlength: field.maxlength !== undefined ? field.maxlength : (field.max_length !== undefined ? field.max_length : ''),
                        required: typeof field.required === 'boolean' ? field.required : (field.required === 'true' ? true : false),
                        default: field.default !== undefined ? field.default : (field.default_value !== undefined ? field.default_value : '')
                    };
                    addFieldRow(normalizedField);
                }
            });

            updateNoFieldsMessage();
            updateHiddenField();

            $('#importJsonModal').modal('hide');
            showToast('success', `Successfully imported ${fields.length} fields`);

        } catch (error) {
            showJsonError('Error: ' + error.message);
        }
    });

    // Show JSON error message
    function showJsonError(message) {
        const $errorDiv = $('#jsonError');
        $errorDiv.text(message).show();
        $('html, body').animate({
            scrollTop: $errorDiv.offset().top - 100
        }, 500);
    }

    // Show toast notification
    function showToast(type, message) {
        // You can implement a toast notification here or use alert for now
        alert(message);
    }
});
</script>

<style>
    .field-row {
        background-color: #f8f9fa;
        border-radius: 4px;
        margin-bottom: 15px;
        transition: all 0.2s;
    }
    .field-row:hover {
        background-color: #f1f3f5;
    }
    .form-control-sm {
        height: calc(1.5em + 0.5rem + 2px);
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        line-height: 1.5;
        border-radius: 0.2rem;
    }
    .modal-body textarea {
        font-family: monospace;
        font-size: 0.9em;
    }
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Git Data Capture\application\resources\views/admin/integration-configurations/create.blade.php ENDPATH**/ ?>