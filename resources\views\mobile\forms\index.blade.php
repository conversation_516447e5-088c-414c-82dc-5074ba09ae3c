@extends('layouts.mobile')
@section('pageTitle', __("global.menu.$menuName"))


@section('styles')
    @parent

@endsection
@section('content')
    <!--<div class="content">-->

    <div class="card card-default">
        {{-- <div class="card-header">
            <div class="card-title mainheading">
                <h4>Home</h4>
            </div>
        </div> --}}
        <div class="card-body">

            <div class="row">
                <div class="col-md-4">
                    @foreach ($forms as $form)
                        <a href="{{ route('forms.render', $form->id) }}" type="button"
                            class="btn btn-block btn-default btn-lg btn-icon-right w-100 mt-2">
                            <span>{{ $form->title }}</span>
                            {{-- <i class="fa {{ $menu['icon'] }}"></i> --}}
                        </a>
                    @endforeach
                </div>
            </div>

        </div>

    </div>

    <!--</div>-->
@endsection
@section('scripts')
    @parent

    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.5.0/Chart.min.js"></script>
@endsection
