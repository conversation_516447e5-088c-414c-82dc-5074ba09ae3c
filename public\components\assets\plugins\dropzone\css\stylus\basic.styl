/* The MIT License */
// Copyright (c) 2012 <PERSON><PERSON> <<EMAIL>>
// Permission is hereby granted, free of charge, to any person obtaining a copy of
// this software and associated documentation files (the "Software"), to deal in
// the Software without restriction, including without limitation the rights to
// use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies
// of the Software, and to permit persons to whom the Software is furnished to do
// so, subject to the following conditions:

// The above copyright notice and this permission notice shall be included in all
// copies or substantial portions of the Software.

// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
// SOFTWARE.

@import "nib"

.dropzone
.dropzone *
.dropzone-previews
.dropzone-previews *
  box-sizing border-box


.dropzone
  position relative
  border 1px solid rgba(0, 0, 0, 0.08)
  background rgba(0, 0, 0, 0.02)
  padding 1em

  &.dz-clickable
    cursor pointer
    .dz-message
    .dz-message span
      cursor pointer
    *
      cursor default

  .dz-message
    opacity 1

  &.dz-drag-hover
    border-color rgba(0, 0, 0, 0.15)
    background rgba(0, 0, 0, 0.04)

  &.dz-started
    .dz-message
      display none

.dropzone
.dropzone-previews
  .dz-preview
    background rgba(255, 255, 255, 0.8)
    position relative
    display inline-block
    margin 17px

    vertical-align top

    border 1px solid #acacac

    padding 6px 6px 6px 6px

    &.dz-file-preview
      [data-dz-thumbnail]
        display none

    .dz-details
      width 100px
      height @width
      position relative
      background #ebebeb
      padding 5px
      margin-bottom 22px

      .dz-filename
        overflow hidden
        height 100%


      img
        absolute top left
        width @width
        height @width

      .dz-size
        absolute bottom -28px left 3px
        height 28px
        line-height @height


    &.dz-error
      .dz-error-mark
        display block
    &.dz-success
      .dz-success-mark
        display block


    &:hover
      .dz-details
        img
          display none


    .dz-success-mark
    .dz-error-mark
      display none
      position absolute

      width 40px
      height 40px

      font-size 30px
      text-align center
      right -10px
      top -10px

    .dz-success-mark
      color #8CC657
    .dz-error-mark
      color #EE162D


    .dz-progress
      position absolute
      top 100px
      left 6px
      right 6px
      height 6px
      background #d7d7d7
      display none

      .dz-upload
        position absolute
        top 0
        bottom 0
        left 0
        width 0%
        background-color #8CC657


    &.dz-processing
      .dz-progress
        display block


    .dz-error-message
      display none
      absolute top -5px left -20px
      background rgba(245, 245, 245, 0.8)
      padding 8px 10px
      color #800
      min-width 140px
      max-width 500px
      z-index 500
    &:hover.dz-error
      .dz-error-message
        display block
