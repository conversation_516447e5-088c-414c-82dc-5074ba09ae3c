{"__meta": {"id": "01K4VAVBS0BP9A3S5G26ZKMZQ6", "datetime": "2025-09-11 02:49:36", "utime": **********.28902, "method": "GET", "uri": "/admin/field-mapping-configurations?draw=1&columns%5B0%5D%5Bdata%5D=DT_RowIndex&columns%5B0%5D%5Bname%5D=DT_RowIndex&columns%5B0%5D%5Bsearchable%5D=false&columns%5B0%5D%5Borderable%5D=true&columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B1%5D%5Bdata%5D=name&columns%5B1%5D%5Bname%5D=name&columns%5B1%5D%5Bsearchable%5D=true&columns%5B1%5D%5Borderable%5D=true&columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B2%5D%5Bdata%5D=form.title&columns%5B2%5D%5Bname%5D=form.title&columns%5B2%5D%5Bsearchable%5D=true&columns%5B2%5D%5Borderable%5D=true&columns%5B2%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B2%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B3%5D%5Bdata%5D=endpoint_configuration&columns%5B3%5D%5Bname%5D=endpoint_configuration&columns%5B3%5D%5Bsearchable%5D=true&columns%5B3%5D%5Borderable%5D=true&columns%5B3%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B3%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B4%5D%5Bdata%5D=external_system&columns%5B4%5D%5Bname%5D=external_system&columns%5B4%5D%5Bsearchable%5D=true&columns%5B4%5D%5Borderable%5D=true&columns%5B4%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B4%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B5%5D%5Bdata%5D=integration_method&columns%5B5%5D%5Bname%5D=integration_method&columns%5B5%5D%5Bsearchable%5D=true&columns%5B5%5D%5Borderable%5D=true&columns%5B5%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B5%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B6%5D%5Bdata%5D=process_type&columns%5B6%5D%5Bname%5D=process_type&columns%5B6%5D%5Bsearchable%5D=true&columns%5B6%5D%5Borderable%5D=true&columns%5B6%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B6%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B7%5D%5Bdata%5D=status&columns%5B7%5D%5Bname%5D=status&columns%5B7%5D%5Bsearchable%5D=false&columns%5B7%5D%5Borderable%5D=true&columns%5B7%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B7%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B8%5D%5Bdata%5D=updated_by&columns%5B8%5D%5Bname%5D=updatedBy.name&columns%5B8%5D%5Bsearchable%5D=true&columns%5B8%5D%5Borderable%5D=true&columns%5B8%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B8%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B9%5D%5Bdata%5D=updated_at&columns%5B9%5D%5Bname%5D=updated_at&columns%5B9%5D%5Bsearchable%5D=false&columns%5B9%5D%5Borderable%5D=true&columns%5B9%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B9%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B10%5D%5Bdata%5D=actions&columns%5B10%5D%5Bname%5D=actions&columns%5B10%5D%5Bsearchable%5D=false&columns%5B10%5D%5Borderable%5D=false&columns%5B10%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B10%5D%5Bsearch%5D%5Bregex%5D=false&order%5B0%5D%5Bcolumn%5D=9&order%5B0%5D%5Bdir%5D=desc&start=0&length=25&search%5Bvalue%5D=&search%5Bregex%5D=false&_=**********133", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 24, "start": **********.747962, "end": **********.289034, "duration": 0.541071891784668, "duration_str": "541ms", "measures": [{"label": "Booting", "start": **********.747962, "relative_start": 0, "end": **********.955499, "relative_end": **********.955499, "duration": 0.*****************, "duration_str": "208ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.955512, "relative_start": 0.***************, "end": **********.289036, "relative_end": 2.1457672119140625e-06, "duration": 0.****************, "duration_str": "334ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.979244, "relative_start": 0.*****************, "end": **********.986908, "relative_end": **********.986908, "duration": 0.0076639652252197266, "duration_str": "7.66ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: partials.datatablesActions", "start": **********.168348, "relative_start": 0.*****************, "end": **********.168348, "relative_end": **********.168348, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.view", "start": **********.186634, "relative_start": 0.****************, "end": **********.186634, "relative_end": **********.186634, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.button", "start": **********.188632, "relative_start": 0.****************, "end": **********.188632, "relative_end": **********.188632, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.edit", "start": **********.189505, "relative_start": 0.4415431022644043, "end": **********.189505, "relative_end": **********.189505, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.button", "start": **********.215667, "relative_start": 0.46770501136779785, "end": **********.215667, "relative_end": **********.215667, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.delete", "start": **********.218123, "relative_start": 0.47016096115112305, "end": **********.218123, "relative_end": **********.218123, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.button", "start": **********.219654, "relative_start": 0.4716920852661133, "end": **********.219654, "relative_end": **********.219654, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.datatablesActions", "start": **********.220872, "relative_start": 0.47290992736816406, "end": **********.220872, "relative_end": **********.220872, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.view", "start": **********.221991, "relative_start": 0.4740290641784668, "end": **********.221991, "relative_end": **********.221991, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.button", "start": **********.22293, "relative_start": 0.47496795654296875, "end": **********.22293, "relative_end": **********.22293, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.edit", "start": **********.224085, "relative_start": 0.4761230945587158, "end": **********.224085, "relative_end": **********.224085, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.button", "start": **********.225041, "relative_start": 0.477078914642334, "end": **********.225041, "relative_end": **********.225041, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.delete", "start": **********.226334, "relative_start": 0.47837209701538086, "end": **********.226334, "relative_end": **********.226334, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.button", "start": **********.227492, "relative_start": 0.47953009605407715, "end": **********.227492, "relative_end": **********.227492, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.datatablesActions", "start": **********.228724, "relative_start": 0.4807620048522949, "end": **********.228724, "relative_end": **********.228724, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.view", "start": **********.229809, "relative_start": 0.48184704780578613, "end": **********.229809, "relative_end": **********.229809, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.button", "start": **********.231374, "relative_start": 0.4834120273590088, "end": **********.231374, "relative_end": **********.231374, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.edit", "start": **********.233082, "relative_start": 0.4851200580596924, "end": **********.233082, "relative_end": **********.233082, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.button", "start": **********.234259, "relative_start": 0.4862968921661377, "end": **********.234259, "relative_end": **********.234259, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.delete", "start": **********.235578, "relative_start": 0.48761606216430664, "end": **********.235578, "relative_end": **********.235578, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.button", "start": **********.236829, "relative_start": 0.48886704444885254, "end": **********.236829, "relative_end": **********.236829, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 23540336, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "9.x", "tooltip": {"Laravel Version": "9.52.20", "PHP Version": "8.1.15", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 21, "nb_templates": 21, "templates": [{"name": "partials.datatablesActions", "param_count": null, "params": [], "start": **********.168283, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/partials/datatablesActions.blade.phppartials.datatablesActions", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fpartials%2FdatatablesActions.blade.php:1", "ajax": false, "filename": "datatablesActions.blade.php", "line": "?"}}, {"name": "components.buttons.view", "param_count": null, "params": [], "start": **********.186584, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/view.blade.phpcomponents.buttons.view", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fview.blade.php:1", "ajax": false, "filename": "view.blade.php", "line": "?"}}, {"name": "components.buttons.button", "param_count": null, "params": [], "start": **********.188581, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/button.blade.phpcomponents.buttons.button", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fbutton.blade.php:1", "ajax": false, "filename": "button.blade.php", "line": "?"}}, {"name": "components.buttons.edit", "param_count": null, "params": [], "start": **********.189422, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/edit.blade.phpcomponents.buttons.edit", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fedit.blade.php:1", "ajax": false, "filename": "edit.blade.php", "line": "?"}}, {"name": "components.buttons.button", "param_count": null, "params": [], "start": **********.215488, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/button.blade.phpcomponents.buttons.button", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fbutton.blade.php:1", "ajax": false, "filename": "button.blade.php", "line": "?"}}, {"name": "components.buttons.delete", "param_count": null, "params": [], "start": **********.218009, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/delete.blade.phpcomponents.buttons.delete", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fdelete.blade.php:1", "ajax": false, "filename": "delete.blade.php", "line": "?"}}, {"name": "components.buttons.button", "param_count": null, "params": [], "start": **********.21955, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/button.blade.phpcomponents.buttons.button", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fbutton.blade.php:1", "ajax": false, "filename": "button.blade.php", "line": "?"}}, {"name": "partials.datatablesActions", "param_count": null, "params": [], "start": **********.220771, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/partials/datatablesActions.blade.phppartials.datatablesActions", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fpartials%2FdatatablesActions.blade.php:1", "ajax": false, "filename": "datatablesActions.blade.php", "line": "?"}}, {"name": "components.buttons.view", "param_count": null, "params": [], "start": **********.22189, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/view.blade.phpcomponents.buttons.view", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fview.blade.php:1", "ajax": false, "filename": "view.blade.php", "line": "?"}}, {"name": "components.buttons.button", "param_count": null, "params": [], "start": **********.222831, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/button.blade.phpcomponents.buttons.button", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fbutton.blade.php:1", "ajax": false, "filename": "button.blade.php", "line": "?"}}, {"name": "components.buttons.edit", "param_count": null, "params": [], "start": **********.223984, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/edit.blade.phpcomponents.buttons.edit", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fedit.blade.php:1", "ajax": false, "filename": "edit.blade.php", "line": "?"}}, {"name": "components.buttons.button", "param_count": null, "params": [], "start": **********.22494, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/button.blade.phpcomponents.buttons.button", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fbutton.blade.php:1", "ajax": false, "filename": "button.blade.php", "line": "?"}}, {"name": "components.buttons.delete", "param_count": null, "params": [], "start": **********.226233, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/delete.blade.phpcomponents.buttons.delete", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fdelete.blade.php:1", "ajax": false, "filename": "delete.blade.php", "line": "?"}}, {"name": "components.buttons.button", "param_count": null, "params": [], "start": **********.227391, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/button.blade.phpcomponents.buttons.button", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fbutton.blade.php:1", "ajax": false, "filename": "button.blade.php", "line": "?"}}, {"name": "partials.datatablesActions", "param_count": null, "params": [], "start": **********.228623, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/partials/datatablesActions.blade.phppartials.datatablesActions", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fpartials%2FdatatablesActions.blade.php:1", "ajax": false, "filename": "datatablesActions.blade.php", "line": "?"}}, {"name": "components.buttons.view", "param_count": null, "params": [], "start": **********.229709, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/view.blade.phpcomponents.buttons.view", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fview.blade.php:1", "ajax": false, "filename": "view.blade.php", "line": "?"}}, {"name": "components.buttons.button", "param_count": null, "params": [], "start": **********.231252, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/button.blade.phpcomponents.buttons.button", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fbutton.blade.php:1", "ajax": false, "filename": "button.blade.php", "line": "?"}}, {"name": "components.buttons.edit", "param_count": null, "params": [], "start": **********.232965, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/edit.blade.phpcomponents.buttons.edit", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fedit.blade.php:1", "ajax": false, "filename": "edit.blade.php", "line": "?"}}, {"name": "components.buttons.button", "param_count": null, "params": [], "start": **********.234156, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/button.blade.phpcomponents.buttons.button", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fbutton.blade.php:1", "ajax": false, "filename": "button.blade.php", "line": "?"}}, {"name": "components.buttons.delete", "param_count": null, "params": [], "start": **********.235475, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/delete.blade.phpcomponents.buttons.delete", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fdelete.blade.php:1", "ajax": false, "filename": "delete.blade.php", "line": "?"}}, {"name": "components.buttons.button", "param_count": null, "params": [], "start": **********.236728, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/button.blade.phpcomponents.buttons.button", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fbutton.blade.php:1", "ajax": false, "filename": "button.blade.php", "line": "?"}}]}, "queries": {"count": 10, "nb_statements": 9, "nb_visible_statements": 10, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.015519999999999999, "accumulated_duration_str": "15.52ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 15, "namespace": "middleware", "name": "auth", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}], "start": **********.011784, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php:168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "dc_local", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.020364, "duration": 0.0028399999999999996, "duration_str": "2.84ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php:59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "dc_local", "explain": null, "start_percent": 0, "width_percent": 18.299}, {"sql": "select count(*) as aggregate from `field_mapping_configurations` where `field_mapping_configurations`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\Git Data Capture\\application\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 205}, {"index": 16, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/DataTableAbstract.php", "file": "D:\\Git Data Capture\\application\\vendor\\yajra\\laravel-datatables-oracle\\src\\DataTableAbstract.php", "line": 848}, {"index": 17, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\Git Data Capture\\application\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 186}, {"index": 18, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\Git Data Capture\\application\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 158}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/FieldMappingConfigurationsController.php", "file": "D:\\Git Data Capture\\application\\app\\Http\\Controllers\\Admin\\FieldMappingConfigurationsController.php", "line": 100}], "start": **********.045005, "duration": 0.00239, "duration_str": "2.39ms", "memory": 0, "memory_str": null, "filename": "QueryDataTable.php:205", "source": {"index": 15, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\Git Data Capture\\application\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 205}, "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fvendor%2Fyajra%2Flaravel-datatables-oracle%2Fsrc%2FQueryDataTable.php:205", "ajax": false, "filename": "QueryDataTable.php", "line": "205"}, "connection": "dc_local", "explain": null, "start_percent": 18.299, "width_percent": 15.399}, {"sql": "select * from `field_mapping_configurations` where `field_mapping_configurations`.`deleted_at` is null limit 25 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\Git Data Capture\\application\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 175}, {"index": 15, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\Git Data Capture\\application\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 158}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/FieldMappingConfigurationsController.php", "file": "D:\\Git Data Capture\\application\\app\\Http\\Controllers\\Admin\\FieldMappingConfigurationsController.php", "line": 100}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/FieldMappingConfigurationsController.php", "file": "D:\\Git Data Capture\\application\\app\\Http\\Controllers\\Admin\\FieldMappingConfigurationsController.php", "line": 33}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.0508628, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "QueryDataTable.php:175", "source": {"index": 14, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\Git Data Capture\\application\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 175}, "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fvendor%2Fyajra%2Flaravel-datatables-oracle%2Fsrc%2FQueryDataTable.php:175", "ajax": false, "filename": "QueryDataTable.php", "line": "175"}, "connection": "dc_local", "explain": null, "start_percent": 33.698, "width_percent": 3.866}, {"sql": "select * from `forms` where `forms`.`id` in (2, 3, 4) and `forms`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\Git Data Capture\\application\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 175}, {"index": 20, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\Git Data Capture\\application\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 158}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/FieldMappingConfigurationsController.php", "file": "D:\\Git Data Capture\\application\\app\\Http\\Controllers\\Admin\\FieldMappingConfigurationsController.php", "line": 100}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Admin/FieldMappingConfigurationsController.php", "file": "D:\\Git Data Capture\\application\\app\\Http\\Controllers\\Admin\\FieldMappingConfigurationsController.php", "line": 33}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.095211, "duration": 0.0060999999999999995, "duration_str": "6.1ms", "memory": 0, "memory_str": null, "filename": "QueryDataTable.php:175", "source": {"index": 19, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\Git Data Capture\\application\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 175}, "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fvendor%2Fyajra%2Flaravel-datatables-oracle%2Fsrc%2FQueryDataTable.php:175", "ajax": false, "filename": "QueryDataTable.php", "line": "175"}, "connection": "dc_local", "explain": null, "start_percent": 37.564, "width_percent": 39.304}, {"sql": "select * from `integration_configurations` where `integration_configurations`.`id` in (2, 3, 4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\Git Data Capture\\application\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 175}, {"index": 20, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\Git Data Capture\\application\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 158}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/FieldMappingConfigurationsController.php", "file": "D:\\Git Data Capture\\application\\app\\Http\\Controllers\\Admin\\FieldMappingConfigurationsController.php", "line": 100}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Admin/FieldMappingConfigurationsController.php", "file": "D:\\Git Data Capture\\application\\app\\Http\\Controllers\\Admin\\FieldMappingConfigurationsController.php", "line": 33}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.104559, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "QueryDataTable.php:175", "source": {"index": 19, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\Git Data Capture\\application\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 175}, "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fvendor%2Fyajra%2Flaravel-datatables-oracle%2Fsrc%2FQueryDataTable.php:175", "ajax": false, "filename": "QueryDataTable.php", "line": "175"}, "connection": "dc_local", "explain": null, "start_percent": 76.869, "width_percent": 5.606}, {"sql": "select * from `users` where `users`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\Git Data Capture\\application\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 175}, {"index": 20, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\Git Data Capture\\application\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 158}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Admin/FieldMappingConfigurationsController.php", "file": "D:\\Git Data Capture\\application\\app\\Http\\Controllers\\Admin\\FieldMappingConfigurationsController.php", "line": 100}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Admin/FieldMappingConfigurationsController.php", "file": "D:\\Git Data Capture\\application\\app\\Http\\Controllers\\Admin\\FieldMappingConfigurationsController.php", "line": 33}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.1075351, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "QueryDataTable.php:175", "source": {"index": 19, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\Git Data Capture\\application\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 175}, "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fvendor%2Fyajra%2Flaravel-datatables-oracle%2Fsrc%2FQueryDataTable.php:175", "ajax": false, "filename": "QueryDataTable.php", "line": "175"}, "connection": "dc_local", "explain": null, "start_percent": 82.474, "width_percent": 3.222}, {"sql": "select * from `users` where `users`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/FieldMappingConfigurationsController.php", "file": "D:\\Git Data Capture\\application\\app\\Http\\Controllers\\Admin\\FieldMappingConfigurationsController.php", "line": 80}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 37}], "start": **********.148794, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "FieldMappingConfigurationsController.php:80", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/FieldMappingConfigurationsController.php", "file": "D:\\Git Data Capture\\application\\app\\Http\\Controllers\\Admin\\FieldMappingConfigurationsController.php", "line": 80}, "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fapp%2FHttp%2FControllers%2FAdmin%2FFieldMappingConfigurationsController.php:80", "ajax": false, "filename": "FieldMappingConfigurationsController.php", "line": "80"}, "connection": "dc_local", "explain": null, "start_percent": 85.696, "width_percent": 4.639}, {"sql": "select * from `users` where `users`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/FieldMappingConfigurationsController.php", "file": "D:\\Git Data Capture\\application\\app\\Http\\Controllers\\Admin\\FieldMappingConfigurationsController.php", "line": 80}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 37}], "start": **********.160348, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "FieldMappingConfigurationsController.php:80", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/FieldMappingConfigurationsController.php", "file": "D:\\Git Data Capture\\application\\app\\Http\\Controllers\\Admin\\FieldMappingConfigurationsController.php", "line": 80}, "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fapp%2FHttp%2FControllers%2FAdmin%2FFieldMappingConfigurationsController.php:80", "ajax": false, "filename": "FieldMappingConfigurationsController.php", "line": "80"}, "connection": "dc_local", "explain": null, "start_percent": 90.335, "width_percent": 3.479}, {"sql": "select * from `users` where `users`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/FieldMappingConfigurationsController.php", "file": "D:\\Git Data Capture\\application\\app\\Http\\Controllers\\Admin\\FieldMappingConfigurationsController.php", "line": 80}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 37}], "start": **********.1643589, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "FieldMappingConfigurationsController.php:80", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/FieldMappingConfigurationsController.php", "file": "D:\\Git Data Capture\\application\\app\\Http\\Controllers\\Admin\\FieldMappingConfigurationsController.php", "line": 80}, "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fapp%2FHttp%2FControllers%2FAdmin%2FFieldMappingConfigurationsController.php:80", "ajax": false, "filename": "FieldMappingConfigurationsController.php", "line": "80"}, "connection": "dc_local", "explain": null, "start_percent": 93.814, "width_percent": 6.186}]}, "models": {"data": {"App\\Models\\User": {"retrieved": 5, "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fapp%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\FieldMappingConfiguration": {"retrieved": 3, "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fapp%2FModels%2FFieldMappingConfiguration.php:1", "ajax": false, "filename": "FieldMappingConfiguration.php", "line": "?"}}, "App\\Models\\Form": {"retrieved": 3, "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fapp%2FModels%2FForm.php:1", "ajax": false, "filename": "Form.php", "line": "?"}}, "App\\Models\\IntegrationConfiguration": {"retrieved": 3, "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fapp%2FModels%2FIntegrationConfiguration.php:1", "ajax": false, "filename": "IntegrationConfiguration.php", "line": "?"}}}, "count": 14, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 14}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8001/admin/field-mapping-configurations?_=**********133&columns%5B0%5D%5Bdata%5D=DT...", "action_name": "admin.field-mapping-configurations.index", "controller_action": "App\\Http\\Controllers\\Admin\\FieldMappingConfigurationsController@index", "uri": "GET admin/field-mapping-configurations", "controller": "App\\Http\\Controllers\\Admin\\FieldMappingConfigurationsController@index<a href=\"vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fapp%2FHttp%2FControllers%2FAdmin%2FFieldMappingConfigurationsController.php:30\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "/admin", "file": "<a href=\"vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fapp%2FHttp%2FControllers%2FAdmin%2FFieldMappingConfigurationsController.php:30\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/FieldMappingConfigurationsController.php:30-38</a>", "middleware": "web, auth, user_type:admin", "duration": "544ms", "peak_memory": "24MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2083666932 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>draw</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>columns</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"11 characters\">DT_RowIndex</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">DT_RowIndex</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"10 characters\">form.title</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">form.title</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"22 characters\">endpoint_configuration</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"22 characters\">endpoint_configuration</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"15 characters\">external_system</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"15 characters\">external_system</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"18 characters\">integration_method</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">integration_method</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"12 characters\">process_type</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">process_type</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"10 characters\">updated_by</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">updatedBy.name</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"10 characters\">updated_at</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">updated_at</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"7 characters\">actions</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">actions</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>column</span>\" => \"<span class=sf-dump-str>9</span>\"\n      \"<span class=sf-dump-key>dir</span>\" => \"<span class=sf-dump-str title=\"4 characters\">desc</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>start</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>length</span>\" => \"<span class=sf-dump-str title=\"2 characters\">25</span>\"\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_</span>\" => \"<span class=sf-dump-str title=\"13 characters\">**********133</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2083666932\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2102934120 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2102934120\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-592930132 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko/20100101 Firefox/142.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">http://localhost:8001/admin/field-mapping-configurations</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IitDdHNqOGlGVXphUlFEK3kwWUVSUGc9PSIsInZhbHVlIjoiVnpvanZMbXVLaTdJYnFpeVRCZEpHekVTUnp6UjY3ZHlLL0hJTkNOczBNTlBIL0R0UVRWQ1lrSlBwbFBQWTIvbFVkNEQ2aG4vaVRCYTBQQ0NUWEprSzZqN3NmT0xxVjVVNUxjL01wTlpqLzFtbk1KQVNRZ05WdC9PNENFL1FzV20iLCJtYWMiOiJkMTA5MDllZmExMDVlMmE2YTY0N2ZjOTBmNjZmNWQ2MGIzMzE2OWVkNmI3OWY0Yzg1OGY0NjM0ZTRlNTc0ZGQyIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjYvbXJJYjh2ZVJCQlFQZ1ZDalBPbWc9PSIsInZhbHVlIjoiNTdYd0YrSGRSaDY0YTA1MkRQM2Z6WkpBY3Ztc0hBS2VFeGJLUE9aT1l0OGpwWW5uYTBDMzliM0d1ZkZNQXByN0FGVzhOTEpnVlJzQ2JoN3Q3K1VBelFIU29ZNTlPTHhzdXU3UWlQbDRpNk83c0Y2UGhlMmM2c1psb3hLenZKUjMiLCJtYWMiOiJhNWVlZmZjNzRkOWE3Y2M2YjZlYjMyYzk5OTBhZDA1ODgzMjhhNDU2ZTc5NTU5YjM5NzdjZDQyMzA5YTJiZDY5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-592930132\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1583380160 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4RbyFb2oU1pjc4UNp6CmU470XASLVRa4SNAgSVgT</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3qWAZMAIgZlFqvqcm0sdt0aBVP4ghvpst3qVJvce</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1583380160\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-40624751 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 11 Sep 2025 02:49:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-40624751\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2078213904 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4RbyFb2oU1pjc4UNp6CmU470XASLVRa4SNAgSVgT</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"56 characters\">http://localhost:8001/admin/field-mapping-configurations</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1757556567</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2078213904\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8001/admin/field-mapping-configurations?_=**********133&columns%5B0%5D%5Bdata%5D=DT...", "action_name": "admin.field-mapping-configurations.index", "controller_action": "App\\Http\\Controllers\\Admin\\FieldMappingConfigurationsController@index"}, "badge": null}}