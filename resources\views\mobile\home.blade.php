@extends('layouts.mobile')
@section('pageTitle', 'Home')


@section('styles')
    @parent

@endsection
@section('content')
    <!--<div class="content">-->

    <div class="card card-default">
        {{-- <div class="card-header">
            <div class="card-title mainheading">
                <h4>Home</h4>
            </div>
        </div> --}}
        <div class="card-body">

            <div class="row">
                <div class="col-md-4">
                    @foreach ($submenu as $menu)
                        <a href="{{ $menu['route'] }}" type="button"
                            class="btn btn-block btn-primary btn-lg btn-icon-right w-100 m-b-10">
                            <span>{{ $menu['title'] }}</span>
                            @if(isset($menu['icon']))
                            <i class="fa {{ $menu['icon'] }}"></i>
                            @endif
                        </a>
                    @endforeach
                </div>
            </div>

        </div>

    </div>

    <!--</div>-->
@endsection
@section('scripts')
    @parent

    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.5.0/Chart.min.js"></script>
@endsection
