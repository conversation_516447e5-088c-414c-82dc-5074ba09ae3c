@extends('layouts.app')

@section('title', $form->title)

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ $form->title }}</h3>
                    <div class="card-tools">
                        <a href="{{ route('forms.index') }}" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Forms
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Loading indicator -->
                    <div id="form-loading" class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading form...</span>
                        </div>
                        <p class="mt-2 text-muted">Loading form...</p>
                    </div>

                    <!-- Form container -->
                    <div id="form-container" style="display: none;">
                        <div id="formio-form"></div>
                    </div>

                    <!-- Error container -->
                    <div id="form-error" style="display: none;" class="alert alert-danger">
                        <h5><i class="fas fa-exclamation-triangle"></i> Error Loading Form</h5>
                        <p id="error-message">An error occurred while loading the form. Please try again.</p>
                        <button type="button" class="btn btn-outline-danger" onclick="loadForm()">
                            <i class="fas fa-redo"></i> Retry
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Submission Modal -->
<div class="modal fade" id="submissionModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-paper-plane text-primary"></i>
                    Submitting Form
                </h5>
            </div>
            <div class="modal-body text-center">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Submitting...</span>
                </div>
                <p>Please wait while we submit your form...</p>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/formiojs@latest/dist/formio.full.min.css">
<style>
.formio-form {
    margin-top: 20px;
}

.formio-component-submit .btn {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
    padding: 10px 30px;
    font-size: 16px;
}

.formio-component-submit .btn:hover {
    background-color: #0056b3;
    border-color: #0056b3;
}

.alert-validation {
    margin-top: 15px;
}
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/formiojs@latest/dist/formio.full.min.js"></script>
<script>
let formInstance = null;
const formDefinition = {!! json_encode($form->content) !!};
const formId = {{ $form->id }};
const submitUrl = "{{ route('forms.submit', $form->id) }}";
const csrfToken = "{{ csrf_token() }}";

document.addEventListener('DOMContentLoaded', function() {
    loadForm();
});

function loadForm() {
    // Hide error and show loading
    document.getElementById('form-error').style.display = 'none';
    document.getElementById('form-loading').style.display = 'block';
    document.getElementById('form-container').style.display = 'none';

    // Create FormIO form
    Formio.createForm(document.getElementById('formio-form'), formDefinition)
        .then(function(form) {
            formInstance = form;
            
            // Hide loading and show form
            document.getElementById('form-loading').style.display = 'none';
            document.getElementById('form-container').style.display = 'block';

            // Handle form submission
            form.on('submit', function(submission) {
                handleFormSubmission(submission.data);
            });

            // Handle form changes for validation
            form.on('change', function(changed) {
                // Clear any previous validation errors
                clearValidationErrors();
            });

            console.log('Form loaded successfully');
        })
        .catch(function(error) {
            console.error('Error loading form:', error);
            showFormError('Failed to load form: ' + error.message);
        });
}

function handleFormSubmission(formData) {
    // Show submission modal
    const modal = new bootstrap.Modal(document.getElementById('submissionModal'));
    modal.show();

    // Prepare form data
    const submitData = new FormData();
    submitData.append('_token', csrfToken);
    submitData.append('form_data', JSON.stringify(formData));

    // Submit form
    fetch(submitUrl, {
        method: 'POST',
        body: submitData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (response.redirected) {
            // Handle redirect (success case)
            window.location.href = response.url;
            return;
        }
        return response.json();
    })
    .then(data => {
        modal.hide();
        
        if (data && data.errors) {
            // Handle validation errors
            showValidationErrors(data.errors);
        } else if (data && data.message) {
            // Handle other errors
            showAlert('error', data.message);
        }
    })
    .catch(error => {
        modal.hide();
        console.error('Submission error:', error);
        showAlert('error', 'An error occurred while submitting the form. Please try again.');
    });
}

function showFormError(message) {
    document.getElementById('form-loading').style.display = 'none';
    document.getElementById('form-container').style.display = 'none';
    document.getElementById('error-message').textContent = message;
    document.getElementById('form-error').style.display = 'block';
}

function showValidationErrors(errors) {
    // Clear previous errors
    clearValidationErrors();

    // Create error alert
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-validation';
    alertDiv.innerHTML = `
        <h6><i class="fas fa-exclamation-triangle"></i> Please correct the following errors:</h6>
        <ul class="mb-0">
            ${Object.entries(errors).map(([field, messages]) => 
                `<li><strong>${field}:</strong> ${Array.isArray(messages) ? messages.join(', ') : messages}</li>`
            ).join('')}
        </ul>
    `;

    // Insert after form
    const formContainer = document.getElementById('formio-form');
    formContainer.parentNode.insertBefore(alertDiv, formContainer.nextSibling);

    // Scroll to errors
    alertDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
}

function clearValidationErrors() {
    const errorAlerts = document.querySelectorAll('.alert-validation');
    errorAlerts.forEach(alert => alert.remove());
}

function showAlert(type, message) {
    const alertClass = type === 'error' ? 'alert-danger' : 'alert-success';
    const icon = type === 'error' ? 'fas fa-exclamation-circle' : 'fas fa-check-circle';
    
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert ${alertClass} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        <i class="${icon}"></i> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Insert at top of card body
    const cardBody = document.querySelector('.card-body');
    cardBody.insertBefore(alertDiv, cardBody.firstChild);

    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
@endpush
