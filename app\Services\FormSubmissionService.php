<?php

namespace App\Services;

use App\Models\Form;
use App\Models\FormSubmission;
use App\Models\AuditLog;
use App\Models\ErrorLog;
use App\Models\FormIntegrationSetting;
use App\Models\User;
use App\Jobs\ProcessFormSubmissionJob;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class FormSubmissionService
{
    protected AuditLoggerService $auditLogger;
    protected ErrorLoggerService $errorLogger;

    public function __construct(
        AuditLoggerService $auditLogger,
        ErrorLoggerService $errorLogger
    ) {
        $this->auditLogger = $auditLogger;
        $this->errorLogger = $errorLogger;
    }

    /**
     * Submit a form with the provided data
     */
    public function submitForm(
        array $formData,
        Form $form,
        ?User $user = null,
        array $metadata = []
    ): FormSubmission {
        try {
            DB::beginTransaction();

            // Validate form data
            $this->validateFormData($formData, $form);

            // Create form submission
            $submission = FormSubmission::create([
                'form_id' => $form->id,
                'user_id' => $user?->id,
                'submission_data' => $formData,
                'status' => FormSubmission::STATUS_PENDING,
                'submitted_at' => now(),
                'metadata' => $metadata,
            ]);

            // Log submission creation
            $this->auditLogger->logSubmissionEvent(
                $submission,
                AuditLog::EVENT_SUBMISSION_CREATED,
                [
                    'form_title' => $form->title,
                    'user_id' => $user?->id,
                    'data_fields_count' => count($formData),
                ]
            );

            // Get active integrations for this form
            $integrations = FormIntegrationSetting::where('form_id', $form->id)
                ->where('is_active', true)
                ->with('endpointConfiguration')
                ->get();

            $submission->update([
                'total_integrations' => $integrations->count(),
            ]);

            // Queue processing job
            ProcessFormSubmissionJob::dispatch($submission);

            DB::commit();

            Log::info('Form submission created successfully', [
                'submission_uuid' => $submission->uuid,
                'form_id' => $form->id,
                'user_id' => $user?->id,
                'integrations_count' => $integrations->count(),
            ]);

            return $submission;

        } catch (\Exception $e) {
            DB::rollBack();

            $this->errorLogger->logSubmissionError(
                null,
                $e,
                ErrorLog::LEVEL_ERROR,
                ErrorLog::TYPE_SYSTEM_ERROR,
                [
                    'form_id' => $form->id,
                    'user_id' => $user?->id,
                    'form_data_keys' => array_keys($formData),
                ]
            );

            throw $e;
        }
    }

    /**
     * Process a form submission through all configured integrations
     */
    public function processSubmission(FormSubmission $submission): void
    {
        try {
            $submission->update([
                'status' => FormSubmission::STATUS_PROCESSING,
                'processed_at' => now(),
            ]);

            $this->auditLogger->logSubmissionEvent(
                $submission,
                AuditLog::EVENT_SUBMISSION_VALIDATED,
                ['status_change' => 'pending -> processing']
            );

            // Get active integrations for this form
            $integrations = FormIntegrationSetting::where('form_id', $submission->form_id)
                ->where('is_active', true)
                ->with('endpointConfiguration')
                ->get();

            if ($integrations->isEmpty()) {
                $submission->update(['status' => FormSubmission::STATUS_COMPLETED]);

                $this->auditLogger->logSubmissionEvent(
                    $submission,
                    AuditLog::EVENT_SYNC_COMPLETED,
                    ['message' => 'No active integrations found']
                );

                return;
            }

            // Create sync records for each integration
            foreach ($integrations as $integration) {
                $submission->syncs()->create([
                    'form_integration_setting_id' => $integration->id,
                    'status' => FormSubmissionSync::STATUS_PENDING,
                ]);
            }

            // Dispatch sync jobs
            app(FormSyncEngineService::class)->syncToAllEndpoints($submission);

        } catch (\Exception $e) {
            $submission->update(['status' => FormSubmission::STATUS_FAILED]);

            $this->errorLogger->logSubmissionError(
                $submission,
                $e,
                ErrorLog::LEVEL_ERROR,
                ErrorLog::TYPE_SYSTEM_ERROR
            );

            throw $e;
        }
    }

    /**
     * Retry failed syncs for a submission
     */
    public function retryFailedSyncs(FormSubmission $submission): void
    {
        $failedSyncs = $submission->syncs()
            ->where('status', FormSubmissionSync::STATUS_FAILED)
            ->where('retry_count', '<', 'max_retries')
            ->get();

        foreach ($failedSyncs as $sync) {
            $sync->scheduleRetry();

            $this->auditLogger->logSyncEvent(
                $sync,
                AuditLog::EVENT_SYNC_RETRIED,
                ['retry_count' => $sync->retry_count]
            );
        }

        if ($failedSyncs->isNotEmpty()) {
            app(FormSyncEngineService::class)->syncToAllEndpoints($submission);
        }
    }

    /**
     * Get submission status with detailed information
     */
    public function getSubmissionStatus(string $uuid): array
    {
        $submission = FormSubmission::where('uuid', $uuid)
            ->with(['form', 'user', 'syncs.formIntegrationSetting.endpointConfiguration'])
            ->firstOrFail();

        return [
            'uuid' => $submission->uuid,
            'status' => $submission->status,
            'status_label' => $submission->getStatusLabel(),
            'submitted_at' => $submission->submitted_at,
            'processed_at' => $submission->processed_at,
            'completed_at' => $submission->completed_at,
            'total_integrations' => $submission->total_integrations,
            'successful_integrations' => $submission->successful_integrations,
            'failed_integrations' => $submission->failed_integrations,
            'progress_percentage' => $submission->getProgressPercentage(),
            'form' => [
                'id' => $submission->form->id,
                'title' => $submission->form->title,
            ],
            'syncs' => $submission->syncs->map(function ($sync) {
                return [
                    'uuid' => $sync->uuid,
                    'integration_name' => $sync->getIntegrationName(),
                    'target_system' => $sync->getTargetSystem(),
                    'status' => $sync->status,
                    'status_label' => $sync->getStatusLabel(),
                    'attempted_at' => $sync->attempted_at,
                    'completed_at' => $sync->completed_at,
                    'retry_count' => $sync->retry_count,
                    'error_message' => $sync->error_message,
                ];
            }),
        ];
    }

    /**
     * Validate form data against form definition
     */
    protected function validateFormData(array $formData, Form $form): void
    {
        // Extract validation rules from form content
        $rules = $this->extractValidationRules($form->content);

        $validator = Validator::make($formData, $rules);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }
    }

    /**
     * Extract validation rules from FormIO form definition
     */
    protected function extractValidationRules(array $formContent): array
    {
        $rules = [];

        if (isset($formContent['components'])) {
            $this->extractRulesFromComponents($formContent['components'], $rules);
        }

        return $rules;
    }

    /**
     * Recursively extract validation rules from form components
     */
    protected function extractRulesFromComponents(array $components, array &$rules): void
    {
        foreach ($components as $component) {
            if (isset($component['key'])) {
                $fieldRules = [];

                // Required validation
                if (!empty($component['validate']['required'])) {
                    $fieldRules[] = 'required';
                }

                // Type-specific validations
                switch ($component['type']) {
                    case 'email':
                        $fieldRules[] = 'email';
                        break;
                    case 'number':
                        $fieldRules[] = 'numeric';
                        break;
                    case 'textfield':
                    case 'textarea':
                        if (!empty($component['validate']['maxLength'])) {
                            $fieldRules[] = 'max:' . $component['validate']['maxLength'];
                        }
                        if (!empty($component['validate']['minLength'])) {
                            $fieldRules[] = 'min:' . $component['validate']['minLength'];
                        }
                        break;
                }

                if (!empty($fieldRules)) {
                    $rules[$component['key']] = $fieldRules;
                }
            }

            // Handle nested components (like panels, columns, etc.)
            if (isset($component['components'])) {
                $this->extractRulesFromComponents($component['components'], $rules);
            }
        }
    }
}
