@extends('layouts.admin')
@section('pageTitle', trans('global.view') . ' ' . trans('cruds.usergroup.title_singular'))

@section('content')
<div class="card card-default">
    <div class="card-header separator">
        <div class="card-title mainheading">
            <h4>{{ trans('global.view') }} {{ trans('cruds.usergroup.title_singular') }}</h4>
        </div>
        <div class="card-controls">
            <ul>
                <li>
                    <a class="btn btn-secondary" href="{{ route('admin.usergroups.index') }}">
                        {{ trans('global.back_to_list') }}
                    </a>
                </li>
                <li>
                    <a class="btn btn-info" href="{{ route('admin.usergroups.edit', $usergroup->id) }}">
                        {{ trans('global.edit') }}
                    </a>
                </li>
            </ul>
        </div>
    </div>

    <div class="card-block">
        <div class="row">
            <div class="col-md-6">
                <table class="table table-bordered">
                    <tr>
                        <th width="30%">{{ trans('cruds.fields.name') }}</th>
                        <td>{{ $usergroup->name }}</td>
                    </tr>
                    <tr>
                        <th>{{ trans('cruds.fields.description') }}</th>
                        <td>{{ $usergroup->description ?? 'N/A' }}</td>
                    </tr>
                    <tr>
                        <th>{{ trans('cruds.fields.status') }}</th>
                        <td>
                            @if($usergroup->is_active)
                                <span class="badge badge-success">{{ trans('cruds.fields.active') }}</span>
                            @else
                                <span class="badge badge-danger">{{ trans('cruds.fields.inactive') }}</span>
                            @endif
                        </td>
                    </tr>
                    <tr>
                        <th>{{ trans('cruds.fields.users_count') }}</th>
                        <td>{{ $usergroup->users->count() }}</td>
                    </tr>
                    <tr>
                        <th>{{ trans('cruds.fields.created_at') }}</th>
                        <td>{{ $usergroup->created_at->format('Y-m-d H:i:s') }}</td>
                    </tr>
                    <tr>
                        <th>{{ trans('cruds.fields.updated_at') }}</th>
                        <td>{{ $usergroup->updated_at->format('Y-m-d H:i:s') }}</td>
                    </tr>
                </table>
            </div>
        </div>

        @if($usergroup->users->count() > 0)
        <div class="mt-4">
            <h5>{{ trans('cruds.user.title') }} in this {{ trans('cruds.usergroup.title_singular') }}</h5>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>{{ trans('cruds.fields.name') }}</th>
                            <th>{{ trans('cruds.fields.email') }}</th>
                            <th>{{ trans('cruds.fields.created_at') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($usergroup->users as $user)
                        <tr>
                            <td>{{ $user->name }}</td>
                            <td>{{ $user->email }}</td>
                            <td>{{ $user->created_at->format('Y-m-d H:i:s') }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
        @endif
    </div>
</div>
@endsection
