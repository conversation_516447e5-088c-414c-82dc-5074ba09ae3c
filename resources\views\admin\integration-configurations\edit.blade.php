@extends('layouts.admin')
@section('pageTitle', __('global.edit') . ' ' . __('cruds.integrationConfiguration.title_singular'))

@push('styles')
<style>
    .required:after {
        content: " *";
        color: red;
    }
    .help-block {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 0.25rem;
        display: none;
    }
    .form-group {
        margin-bottom: 1.5rem;
    }
    .card-header h4 {
        margin: 0;
        color: #495057;
    }
    .field-mapping-container {
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 15px;
        margin-bottom: 20px;
    }
    .field-row {
        background: #f8f9fa;
        padding: 10px;
        margin-bottom: 10px;
        border-radius: 4px;
        border-left: 3px solid #007bff;
    }
    .field-actions {
        display: flex;
        gap: 10px;
        margin-top: 10px;
    }
</style>
@endpush

@section('content')
<div class="card card-default">
    <div class="card-header separator">
        <div class="card-title mainheading">
            <h4>{{ __('global.edit') }} {{ __('cruds.integrationConfiguration.title_singular') }}</h4>
        </div>
        <div class="card-controls">
            <ul>
                <li>
                    <x-buttons.back :route="route('admin.integration-configurations.index')" :text="__('global.back_to_list')" />
                </li>
            </ul>
        </div>
    </div>
    <div class="card-block">
        @if ($errors->any())
            <div class="alert alert-danger">
                <ul>
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <form method="POST" action="{{ route('admin.integration-configurations.update', $integrationConfiguration) }}" enctype="multipart/form-data" id="integration-form">
            @method('PUT')
            @csrf

            <div class="form-group row">
                <label for="name" class="col-md-2 col-form-label required">{{ __('cruds.integrationConfiguration.fields.name') }}</label>
                <div class="col-md-4">
                    <input type="text" class="form-control {{ $errors->has('name') ? 'is-invalid' : '' }}" 
                           id="name" name="name" value="{{ old('name', $integrationConfiguration->name) }}" required
                           placeholder="{{ __('cruds.integrationConfiguration.fields.name_placeholder') }}">
                    @if($errors->has('name'))
                        <div class="invalid-feedback">
                            {{ $errors->first('name') }}
                        </div>
                    @endif
                    <small class="help-block">{{ __('cruds.integrationConfiguration.fields.name_helper') }}</small>
                </div>

                <label for="is_active" class="col-md-2 col-form-label required">{{ trans('cruds.fields.status') }}</label>
                <div class="col-md-4">
                    <div class="form-check mt-2">
                        <input type="checkbox" class="form-check-input" id="is_active" name="is_active" value="1"
                            {{ old('is_active', $integrationConfiguration->is_active) ? 'checked' : '' }}>
                        <label class="form-check-label" for="is_active">
                            {{ trans('cruds.fields.active') }}
                        </label>
                    </div>
                    @if($errors->has('is_active'))
                        <div class="invalid-feedback d-block">
                            {{ $errors->first('is_active') }}
                        </div>
                    @endif
                    <small class="help-block">{{ trans('cruds.fields.status_helper') }}</small>
                </div>
            </div>

            <div class="form-group row">
                <label for="description" class="col-md-2 col-form-label">{{ __('cruds.integrationConfiguration.fields.description') }}</label>
                <div class="col-md-4">
                    <textarea class="form-control {{ $errors->has('description') ? 'is-invalid' : '' }}" 
                             id="description" name="description" rows="2"
                             placeholder="{{ __('cruds.integrationConfiguration.fields.description_placeholder') }}">{{ old('description', $integrationConfiguration->description) }}</textarea>
                    @if($errors->has('description'))
                        <div class="invalid-feedback">
                            {{ $errors->first('description') }}
                        </div>
                    @endif
                    <small class="help-block">{{ __('cruds.integrationConfiguration.fields.description_helper') }}</small>
                </div>

                <label for="target_type" class="col-md-2 col-form-label required">{{ __('cruds.integrationConfiguration.fields.external_system') }}</label>
                <div class="col-md-4">
                    <select class="form-control select2 {{ $errors->has('target_type') ? 'is-invalid' : '' }}" 
                            name="target_type" id="target_type" required>
                        <option value="">{{ __('global.pleaseSelect') }}</option>
                        @foreach($externalSystemOptions as $key => $label)
                            <option value="{{ $key }}" {{ old('target_type', $integrationConfiguration->target_type) == $key ? 'selected' : '' }}>{{ $label }}</option>
                        @endforeach
                    </select>
                    @if($errors->has('target_type'))
                        <div class="invalid-feedback">
                            {{ $errors->first('target_type') }}
                        </div>
                    @endif
                    <small class="help-block">{{ __('cruds.integrationConfiguration.fields.external_system_helper') }}</small>
                </div>
            </div>

                <div class="form-group row">
                    <label for="process_type" class="col-md-2 col-form-label required">{{ __('cruds.integrationConfiguration.fields.process_type') }}</label>
                    <div class="col-md-4">
                        <select class="form-control {{ $errors->has('process_type') ? 'is-invalid' : '' }}"
                                name="process_type" id="process_type" required>
                            <option value="">{{ trans('global.pleaseSelect') }}</option>
                            @foreach($processTypeOptions as $key => $label)
                                <option value="{{ $key }}" {{ old('process_selection', $integrationConfiguration->process_selection) == $key ? 'selected' : '' }}>{{ $label }}</option>
                            @endforeach
                        </select>
                        @if($errors->has('process_selection'))
                            <div class="invalid-feedback">
                                {{ $errors->first('process_selection') }}
                            </div>
                        @endif
                        <small class="help-block">{{ trans('cruds.integrationConfiguration.fields.process_selection_helper') }}</small>
                    </div>
                </div>

                <div class="form-group row">
                    <label for="endpoint_type" class="col-md-2 col-form-label required">{{ trans('cruds.integrationConfiguration.fields.endpoint_type') }}</label>
                    <div class="col-md-4">
                        <select class="form-control select2 {{ $errors->has('endpoint_type') ? 'is-invalid' : '' }}" 
                                name="endpoint_type" id="endpoint_type" required>
                            <option value="">{{ trans('global.pleaseSelect') }}</option>
                            @foreach($integrationMethodOptions as $key => $label)
                                <option value="{{ $key }}" {{ old('endpoint_type', $integrationConfiguration->endpoint_type) == $key ? 'selected' : '' }}>{{ $label }}</option>
                            @endforeach
                        </select>
                        @if($errors->has('endpoint_type'))
                            <div class="invalid-feedback">
                                {{ $errors->first('endpoint_type') }}
                            </div>
                        @endif
                        <small class="help-block">{{ trans('cruds.integrationConfiguration.fields.endpoint_type_helper') }}</small>
                    </div>
                </div>

                <div class="form-group row">
                    <label for="endpoint_url" class="col-md-2 col-form-label required">{{ __('cruds.integrationConfiguration.fields.endpoint_url') }}</label>
                    <div class="col-md-4">
                        <input type="url" class="form-control {{ $errors->has('endpoint_url') ? 'is-invalid' : '' }}" 
                               id="endpoint_url" name="endpoint_url" value="{{ old('endpoint_url', $integrationConfiguration->endpoint_url) }}" required
                               placeholder="{{ trans('cruds.integrationConfiguration.fields.endpoint_url_placeholder') }}">
                        @if($errors->has('endpoint_url'))
                            <div class="invalid-feedback">
                                {{ $errors->first('endpoint_url') }}
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <small class="text-muted">{{ __('cruds.integrationConfiguration.fields.request_fields_helper') }}</small>
                                    <div>
                                        <button type="button" class="btn btn-sm btn-outline-primary mr-2" id="addFieldBtn">
                                            <i class="fa fa-plus"></i> {{ __('global.add_field') }}
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" id="importJsonBtn" data-toggle="modal" data-target="#importJsonModal">
                                            <i class="fa fa-file-import"></i> {{ __('global.import_json') }}
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover" id="fieldMappingTable">
                                        <thead class="thead-light">
                                            <tr>
                                                <th style="width: 25%;">{{ __('cruds.integrationConfiguration.fields.field_name') }}</th>
                                                <th style="width: 15%;">{{ __('cruds.integrationConfiguration.fields.data_type') }}</th>
                                                <th style="width: 10%;">{{ __('cruds.integrationConfiguration.fields.required') }}</th>
                                                <th style="width: 40%;">{{ __('cruds.integrationConfiguration.fields.description') }}</th>
                                                <th style="width: 10%;">{{ __('global.actions') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody id="fieldMappingContainer">
                                            <!-- Dynamic fields will be added here -->
                                        </tbody>
                                    </table>
                                </div>
                                
                                <div id="noFieldsMessage" class="text-center text-muted py-3">
                                    <i class="fa fa-info-circle"></i> {{ __('global.no_fields_added') }}
                                </div>
                            </div>
                            
                            <input type="hidden" name="request_fields" id="fieldMappingInput" value="{{ old('request_fields', $integrationConfiguration->request_fields) }}">
                            
                            @if($errors->has('request_fields'))
                                <div class="invalid-feedback d-block">
                                    {{ $errors->first('request_fields') }}
                                </div>
                            @endif
                            @endif
                            <small class="help-block">{{ trans('cruds.integrationConfiguration.fields.body_data_field_helper') }}</small>
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-checkbox">
                                <input type="hidden" name="is_active" value="0">
                                <input class="custom-control-input" type="checkbox" name="is_active" id="is_active" value="1" {{ old('is_active', $integrationConfiguration->is_active) == 1 ? 'checked' : '' }}>
                                <label class="custom-control-label" for="is_active">
                                    {{ trans('cruds.integrationConfiguration.fields.is_active') }}
                                </label>
                                @if($errors->has('is_active'))
                                    <div class="invalid-feedback d-block">
                                        {{ $errors->first('is_active') }}
                                    </div>
                                @endif
                                <small class="help-block d-block">{{ trans('cruds.integrationConfiguration.fields.is_active_helper') }}</small>
                            </div>
                        </div>

                        <div class="form-group row">
                            <div class="col-md-9 offset-md-3">
                                <div class="d-flex justify-content-end gap-2">
                                    <x-buttons.cancel :route="route('admin.integration-configurations.index')" class="me-2"/>
                                    <x-buttons.save :route="route('admin.integration-configurations.update', $integrationConfiguration)" :text="__('global.save')" icon="save"/>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    // Initialize field mappings from hidden input if it exists
    const fieldMappingInput = $('#fieldMappingInput');
    let fieldMappings = [];

    // Initialize field counter
    let fieldCounter = 0;

    // Data types options
    const dataTypes = [
        'string', 'integer', 'float', 'boolean', 'date', 'datetime', 'email', 'url', 'text', 'json'
    ];

    // Load existing field mappings if editing
    @if(!empty($integrationConfiguration->request_fields))
        try {
            @php
                $savedMappings = is_string($integrationConfiguration->request_fields) 
                    ? json_decode($integrationConfiguration->request_fields, true) 
                    : $integrationConfiguration->request_fields;
                $savedMappings = is_array($savedMappings) ? $savedMappings : [];
            @endphp
            const savedMappings = @json($savedMappings);
            if (savedMappings && savedMappings.length > 0) {
                fieldMappings = savedMappings;
                renderFieldMappings();
            } else {
                // Add one empty row by default if no existing mappings
                addFieldRow();
            }
        } catch (e) {
            console.error('Error loading saved field mappings:', e);
            // Add one empty row by default if there's an error
            addFieldRow();
        }
    @else
        // Add one empty row by default for new forms
        addFieldRow();
    @endif

    // Initialize field mappings on page load
    $(document).ready(function() {
        // Add field button click
        $('#addFieldBtn').on('click', function(e) {
            e.preventDefault();
            addFieldRow();
        });
        
        // Initialize field mappings
        renderFieldMappings();
    });

    // Function to add a new field row
    function addFieldRow(fieldData = {}) {
        fieldCounter++;

        const fieldRow = `
            <div class="field-row border rounded p-3 mb-3" data-field-id="${fieldCounter}">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group mb-2">
                            <label class="small font-weight-bold">Field Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control form-control-sm field-name"
                                   placeholder="e.g., item_code" value="${fieldData.name || ''}" required>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group mb-2">
                            <label class="small font-weight-bold">Data Type</label>
                            <select class="form-control form-control-sm field-datatype">
                                ${dataTypes.map(type =>
                                    `<option value="${type}" ${fieldData.datatype === type ? 'selected' : ''}>${type}</option>`
                                ).join('')}
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group mb-2">
                            <label class="small font-weight-bold">Max Length</label>
                            <input type="number" class="form-control form-control-sm field-maxlength"
                                   placeholder="255" value="${fieldData.maxlength !== null && fieldData.maxlength !== undefined ? fieldData.maxlength : ''}" min="1">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group mb-2">
                            <label class="small font-weight-bold">Required</label>
                            <select class="form-control form-control-sm field-required">
                                <option value="false" ${fieldData.required === false ? 'selected' : ''}>No</option>
                                <option value="true" ${fieldData.required === true ? 'selected' : ''}>Yes</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group mb-2">
                            <label class="small font-weight-bold">Default Value</label>
                            <input type="text" class="form-control form-control-sm field-default"
                                   placeholder="Optional" value="${fieldData.default !== null && fieldData.default !== undefined ? fieldData.default : ''}">
                        </div>
                    </div>
                    <div class="col-md-1">
                        <div class="form-group mb-2">
                            <label class="small font-weight-bold">&nbsp;</label>
                            <button type="button" class="btn btn-sm btn-danger btn-block remove-field">
                                <i class="fa fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-11">
                        <div class="form-group mb-0">
                            <label class="small font-weight-bold">Description</label>
                            <input type="text" class="form-control form-control-sm field-description"
                                   placeholder="Optional field description" value="${fieldData.description || ''}">
                        </div>
                    </div>
                </div>
            </div>
        `;

        $('#fieldMappingContainer').append(fieldRow);
        updateNoFieldsMessage();
        updateHiddenField();
    }

    // Remove field
    // Add new field
    $('#addFieldBtn').on('click', function() {
        addFieldRow();
        updateNoFieldsMessage();
        updateHiddenField();
    });

    // Remove field
    $(document).on('click', '.remove-field', function() {
        const row = $(this).closest('tr');
        const fieldName = row.find('input[name$="[field]"]').val();
        
        if (confirm(`Are you sure you want to remove the field "${fieldName}"?`)) {
            row.remove();
            updateNoFieldsMessage();
            updateHiddenField();
        }
    });
    
    // Add field row
    function addFieldRow(field = {}) {
        fieldCounter++;
        const rowId = `field-row-${fieldCounter}`;
        const fieldName = field.field || '';
        const dataType = field.data_type || 'string';
        const isRequired = field.required || false;
        const description = field.description || '';
        
        const row = `
            <tr class="field-row" id="${rowId}">
                <td>
                    <input type="text" class="form-control form-control-sm field-name" 
                           name="fields[${fieldCounter}][field]" 
                           value="${fieldName}" 
                           placeholder="field_name" 
                           required>
                </td>
                <td>
                    <select class="form-control form-control-sm field-type" 
                            name="fields[${fieldCounter}][data_type]" required>
                        ${dataTypes.map(type => 
                            `<option value="${type}" ${type === dataType ? 'selected' : ''}>${type}</option>`
                        ).join('')}
                    </select>
                </td>
                <td class="text-center">
                    <div class="form-check d-inline-block">
                        <input type="checkbox" class="form-check-input field-required" 
                               name="fields[${fieldCounter}][required]" 
                               ${isRequired ? 'checked' : ''}>
                    </div>
                </td>
                <td>
                    <input type="text" class="form-control form-control-sm field-description" 
                           name="fields[${fieldCounter}][description]" 
                           value="${description}" 
                           placeholder="Field description">
                </td>
                <td class="text-center">
                    <button type="button" class="btn btn-sm btn-outline-danger remove-field" 
                            title="{{ __('global.delete') }}">
                        <i class="fa fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
        
        $('#fieldMappingContainer').append(row);
        return rowId;
    }
    
    // Update hidden field with JSON data
    function updateHiddenField() {
        const fields = [];
        $('.field-row').each(function() {
            fields.push({
                field: $(this).find('.field-name').val(),
                data_type: $(this).find('.field-type').val(),
                required: $(this).find('.field-required').is(':checked'),
                description: $(this).find('.field-description').val()
            });
        });
        
        fieldMappingInput.val(JSON.stringify(fields));
    }
    
    // Update no fields message visibility
    function updateNoFieldsMessage() {
        const hasFields = $('.field-row').length > 0;
        $('#noFieldsMessage').toggle(!hasFields);
    }
    
    // Handle form submission
    $('#integration-form').on('submit', function(e) {
        updateHiddenField();
        
        // Additional validation can be added here
        const hasInvalidFields = $('.is-invalid').length > 0;
        
        if (hasInvalidFields) {
            e.preventDefault();
            alert('Please fix the validation errors before submitting.');
        }
    });
    
    // Initialize field mapping on page load
    function renderFieldMappings() {
        if (fieldMappings && fieldMappings.length > 0) {
            fieldMappings.forEach(field => {
                addFieldRow(field);
            });
        } else {
            // Add one empty row by default if no existing mappings
            addFieldRow();
        }
        updateNoFieldsMessage();
    }

    // Update hidden field when any input changes
    $(document).on('input change', '.field-row input, .field-row select, .field-row textarea', function() {
        updateHiddenField();
    });
    
    // Handle form submission
    $('#integration-form').on('submit', function() {
        updateHiddenField();
        return true;
    });
    
    // Function to update the hidden field with field mappings
    function updateHiddenField() {
        const fields = [];
        $('.field-row').each(function() {
            const $row = $(this);
            const field = {
                field: $row.find('.field-name').val(),
                data_type: $row.find('.field-type').val(),
                required: $row.find('.field-required').is(':checked'),
                description: $row.find('.field-description').val() || ''
            };
            fields.push(field);
        });
        
        $('#fieldMappingInput').val(JSON.stringify(fields));
    }

    // Function to show/hide no fields message
    function updateNoFieldsMessage() {
        const fieldRows = $('.field-row');
        if (fieldRows.length === 0) {
            $('#noFieldsMessage').show();
        } else {
            $('#noFieldsMessage').hide();
        }
    }

    // Function to determine data type from value
    function determineDataType(value) {
        if (value === null || value === undefined) return 'string';
        if (Array.isArray(value)) return 'array';
        if (typeof value === 'object') return 'object';
        if (typeof value === 'number') {
            return Number.isInteger(value) ? 'integer' : 'float';
        }
        if (typeof value === 'boolean') return 'boolean';
        if (typeof value === 'string') {
            // Check for date/datetime
            const date = new Date(value);
            if (!isNaN(date.getTime())) {
                return value.includes('T') || value.includes(' ') ? 'datetime' : 'date';
            }
            // Check for email
            if (value.includes('@') && value.includes('.')) {
                return 'email';
            }
            // Check for URL
            try {
                new URL(value);
                return 'url';
            } catch (e) {}
        }
        return 'string';
    }

    // Function to flatten object and create field mappings
    function generateFieldMappingsFromSample(data, prefix = '', flatten = true) {
        const fields = [];
        
        if (Array.isArray(data) && data.length > 0) {
            // If it's an array, use the first item to determine structure
            return generateFieldMappingsFromSample(data[0], prefix, flatten);
        } else if (data && typeof data === 'object') {
            Object.entries(data).forEach(([key, value]) => {
                const fieldName = prefix ? `${prefix}_${key}` : key;
                
                if (value !== null && typeof value === 'object' && !Array.isArray(value) && flatten) {
                    // Recursively process nested objects if flattening is enabled
                    fields.push(...generateFieldMappingsFromSample(value, fieldName, flatten));
                } else {
                    const field = {
                        name: fieldName,
                        datatype: determineDataType(value),
                        required: false,
                        description: `Field ${fieldName} from sample data`
                    };
                    
                    // Set reasonable defaults based on data type
                    if (field.datatype === 'string') {
                        field.maxlength = 255;
                        field.default = '';
                    } else if (field.datatype === 'integer' || field.datatype === 'float') {
                        field.maxlength = 20;
                        field.default = '0';
                    } else if (field.datatype === 'boolean') {
                        field.default = 'false';
                    } else if (field.datatype === 'array' || field.datatype === 'object') {
                        field.datatype = 'json';
                        field.default = field.datatype === 'array' ? '[]' : '{}';
                    }
                    
                    fields.push(field);
                }
            });
        }
        
        return fields;
    }

    // Initial state
    updateNoFieldsMessage();

    // JSON Import Modal
    const importModal = `
    <div class="modal fade" id="importJsonModal" tabindex="-1" role="dialog" aria-labelledby="importJsonModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="importJsonModalLabel">Import Field Mappings</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs mb-3" id="importTabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="json-tab" data-toggle="tab" href="#jsonTab" role="tab" aria-controls="jsonTab" aria-selected="true">Field Mappings</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="sample-tab" data-toggle="tab" href="#sampleTab" role="tab" aria-controls="sampleTab" aria-selected="false">From Sample Data</a>
                        </li>
                    </ul>
                    
                    <div class="tab-content" id="importTabsContent">
                        <!-- Field Mappings Tab -->
                        <div class="tab-pane fade show active" id="jsonTab" role="tabpanel" aria-labelledby="json-tab">
                            <div class="form-group">
                                <label for="jsonInput">Paste your JSON array of field mappings:</label>
                                <textarea class="form-control" id="jsonInput" rows="10" placeholder='[
  {
    "name": "field1",
    "datatype": "string",
    "maxlength": 255,
    "required": true,
    "default": "",
    "description": "Field description"
  },
  {
    "name": "field2",
    "datatype": "integer",
    "required": false,
    "description": "Numeric field"
  }
]'></textarea>
                                <small class="form-text text-muted">
                                    Format: An array of objects with field properties (name, datatype, maxlength, required, default, description)
                                </small>
                            </div>
                        </div>
                        
                        <!-- Sample Data Tab -->
                        <div class="tab-pane fade" id="sampleTab" role="tabpanel" aria-labelledby="sample-tab">
                            <div class="form-group">
                                <label for="sampleDataInput">Paste sample JSON response from your endpoint:</label>
                                <textarea class="form-control" id="sampleDataInput" rows="10" placeholder='{
  "id": 123,
  "name": "Example Product",
  "price": 99.99,
  "in_stock": true,
  "tags": ["electronics", "new"],
  "details": {
    "weight": 2.5,
    "dimensions": "10x15x5cm"
  },
  "created_at": "2023-01-01T12:00:00Z"
}'></textarea>
                                <small class="form-text text-muted">
                                    Paste a sample JSON response to automatically generate field mappings
                                </small>
                            </div>
                            <div class="form-group">
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" class="custom-control-input" id="flattenNested" checked>
                                    <label class="custom-control-label" for="flattenNested">Flatten nested objects (e.g., details.weight → details_weight)</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div id="jsonError" class="alert alert-danger mt-3" style="display: none;"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="importJsonSubmit">Import Fields</button>
                </div>
            </div>
        </div>
    </div>`;

    // Add modal to DOM
    $('body').append(importModal);

    // Open import modal
    $('#importJsonBtn').on('click', function() {
        $('#jsonInput').val('');
        $('#sampleDataInput').val('');
        $('#jsonError').hide();
        $('#importJsonModal').modal('show');
    });

    // Handle JSON import
    $('#importJsonSubmit').on('click', function() {
        const activeTab = $('#importTabs .tab-pane.active').attr('id');
        
        try {
            let fields = [];
            
            if (activeTab === 'jsonTab') {
                // Handle direct field mappings
                const jsonInput = $('#jsonInput').val().trim();
                if (!jsonInput) {
                    showJsonError('Please enter JSON data');
                    return;
                }
                
                const parsed = JSON.parse(jsonInput);
                if (!Array.isArray(parsed)) {
                    throw new Error('Invalid format. Expected an array of field objects.');
                }
                fields = parsed;
            } 
            else if (activeTab === 'sampleTab') {
                // Handle sample data import
                const sampleDataInput = $('#sampleDataInput').val().trim();
                if (!sampleDataInput) {
                    showJsonError('Please paste sample JSON data');
                    return;
                }
                
                const sampleData = JSON.parse(sampleDataInput);
                const flattenNested = $('#flattenNested').is(':checked');
                fields = generateFieldMappingsFromSample(sampleData, '', flattenNested);
                
                if (fields.length === 0) {
                    throw new Error('Could not generate any fields from the sample data');
                }
            }

            // Clear existing fields
            $('.field-row').remove();
            
            // Add each field from the JSON
            fields.forEach(field => {
                if (field && typeof field === 'object' && field.name) {
                    addFieldRow(field);
                }
            });
            
            updateNoFieldsMessage();
            updateHiddenField();
            
            $('#importJsonModal').modal('hide');
            showToast('success', `Successfully imported ${fields.length} fields`);
            
        } catch (error) {
            showJsonError('Invalid JSON: ' + error.message);
        }
    });
    
    // Show JSON error message
    function showJsonError(message) {
        const $errorDiv = $('#jsonError');
        $errorDiv.text(message).show();
        $('html, body').animate({
            scrollTop: $errorDiv.offset().top - 100
        }, 500);
    }
    
    // Show toast notification
    function showToast(type, message) {
        // You can implement a toast notification here or use alert for now
        alert(message);
    }
});
</script>

<style>
    .field-row {
        background-color: #f8f9fa;
        border-radius: 4px;
        margin-bottom: 15px;
        transition: all 0.2s;
    }
    .field-row:hover {
        background-color: #f1f3f5;
    }
    .form-control-sm {
        height: calc(1.5em + 0.5rem + 2px);
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        line-height: 1.5;
        border-radius: 0.2rem;
    }
    .modal-body textarea {
        font-family: monospace;
        font-size: 0.9em;
    }
</style>
@endsection
