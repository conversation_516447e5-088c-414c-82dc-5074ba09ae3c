<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\FormIntegrationSetting;
use App\Models\Form;
use App\Models\IntegrationConfiguration;
use App\Http\Requests\StoreFormIntegrationSettingRequest;
use App\Http\Requests\UpdateFormIntegrationSettingRequest;
use App\Services\FormIntegrationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Yajra\DataTables\Facades\DataTables;

class FormIntegrationSettingController extends Controller
{
    protected FormIntegrationService $formIntegrationService;

    public function __construct()
    {
        $this->formIntegrationService = app(FormIntegrationService::class);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // TODO: Re-enable permission checking when user management system is ready
        // abort_if(auth()->user()->type !== 1, Response::HTTP_FORBIDDEN, '403 Forbidden');

        try {
            if ($request->ajax()) {
                $user = Auth::user();
                // Temporarily bypass scope for testing - TODO: Fix authentication
                $query = FormIntegrationSetting::with(['form', 'integrationConfiguration', 'createdBy'])
                    ->orderBy('created_at', 'desc');



                return DataTables::of($query)
                    ->addColumn('form_name', function ($setting) {
                        return $setting->form ? $setting->form->title : 'N/A';
                    })
                    ->addColumn('integration_name', function ($setting) {
                        return $setting->integrationConfiguration ? $setting->integrationConfiguration->name : 'N/A';
                    })
                    ->addColumn('target_system', function ($setting) {
                        return $setting->integrationConfiguration ? $setting->integrationConfiguration->external_system : 'N/A';
                    })
                    ->addColumn('process_type', function ($setting) {
                        return $setting->integrationConfiguration ? $setting->integrationConfiguration->process_type : 'N/A';
                    })
                    ->addColumn('status', function ($setting) {
                        return $setting->is_active
                            ? '<span class="badge badge-success">Active</span>'
                            : '<span class="badge badge-secondary">Inactive</span>';
                    })
                    ->addColumn('created_by_name', function ($setting) {
                        return $setting->createdBy ? $setting->createdBy->name : 'System';
                    })
                    ->addColumn('actions', function ($setting) {
                        $editUrl = route('admin.form-integration-settings.edit', $setting->id);
                        $showUrl = route('admin.form-integration-settings.show', $setting->id);
                        $deleteUrl = route('admin.form-integration-settings.destroy', $setting->id);

                        return '
                            <div class="btn-group" role="group">
                                <a href="'.$showUrl.'" class="btn btn-sm btn-info" title="View Details">
                                    <i class="fa fa-eye"></i>
                                </a>
                                <a href="'.$editUrl.'" class="btn btn-sm btn-primary" title="Edit">
                                    <i class="fa fa-edit"></i>
                                </a>
                                <form action="'.$deleteUrl.'" method="POST" style="display: inline-block;">
                                    '.csrf_field().'
                                    '.method_field('DELETE').'
                                    <button type="button" class="btn btn-sm btn-danger delete-btn" title="Delete">
                                        <i class="fa fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        ';
                    })
                    ->rawColumns(['status', 'actions'])
                    ->make(true);
            }

            // Get data for debugging
            $user = Auth::user();
            $integrationSettings = FormIntegrationSetting::with(['form', 'integrationConfiguration', 'createdBy'])
                ->orderBy('created_at', 'desc')
                ->get();

            return view('admin.form-integration-settings.index', compact('integrationSettings'));
        } catch (\Exception $e) {
            Log::error('Form Integration Settings Index Error: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'request' => $request->all()
            ]);

            if ($request->ajax()) {
                return response()->json(['error' => 'An error occurred: ' . $e->getMessage()], 500);
            }

            return redirect()->back()->with('error', 'An error occurred: ' . $e->getMessage());
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // TODO: Re-enable permission checking when user management system is ready
        // abort_if(auth()->user()->type !== 1, Response::HTTP_FORBIDDEN, '403 Forbidden');

        $forms = Form::active()->get(['id', 'title']);
        $integrations = IntegrationConfiguration::active()->get(['id', 'name', 'external_system', 'process_type', 'integration_method']);
        $targetOptions = IntegrationConfiguration::getExternalSystemOptions();
        $processOptions = IntegrationConfiguration::getProcessTypeOptions();
        $endpointTypeOptions = IntegrationConfiguration::getIntegrationMethodOptions();

        return view('admin.form-integration-settings.create', compact(
            'forms',
            'integrations',
            'targetOptions',
            'processOptions',
            'endpointTypeOptions'
        ));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreFormIntegrationSettingRequest $request)
    {
        try {
            $data = $request->validated();
            $data['created_by'] = Auth::id();
            $data['updated_by'] = Auth::id();

            $integrationSetting = FormIntegrationSetting::create($data);

            // Validate the integration setting
            $validationErrors = $this->formIntegrationService->validateIntegrationSetting($integrationSetting);

            if (!empty($validationErrors)) {
                Log::warning('Form integration setting created with validation warnings', [
                    'integration_setting_id' => $integrationSetting->id,
                    'warnings' => $validationErrors
                ]);
            }

            return redirect()
                ->route('admin.form-integration-settings.index')
                ->with('success', 'Form integration setting created successfully.');

        } catch (\Exception $e) {
            Log::error('Failed to create form integration setting', [
                'error' => $e->getMessage(),
                'request_data' => $request->validated()
            ]);

            return redirect()
                ->back()
                ->withInput()
                ->with('error', 'Failed to create form integration setting: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(FormIntegrationSetting $formIntegrationSetting)
    {
        // TODO: Re-enable permission checking when user management system is ready
        // abort_if(auth()->user()->type !== 1, Response::HTTP_FORBIDDEN, '403 Forbidden');

        $formIntegrationSetting->load(['form', 'integrationConfiguration', 'createdBy', 'updatedBy']);

        // Get validation status
        $validationErrors = $this->formIntegrationService->validateIntegrationSetting($formIntegrationSetting);

        return view('admin.form-integration-settings.show', compact('formIntegrationSetting', 'validationErrors'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(FormIntegrationSetting $formIntegrationSetting)
    {
        // TODO: Re-enable permission checking when user management system is ready
        // abort_if(auth()->user()->type !== 1, Response::HTTP_FORBIDDEN, '403 Forbidden');

        $formIntegrationSetting->load(['form', 'integrationConfiguration']);

        $forms = Form::active()->get(['id', 'title']);
        $integrations = IntegrationConfiguration::active()->get(['id', 'name', 'external_system', 'process_type', 'integration_method']);
        $targetOptions = IntegrationConfiguration::getExternalSystemOptions();
        $processOptions = IntegrationConfiguration::getProcessTypeOptions();
        $endpointTypeOptions = IntegrationConfiguration::getIntegrationMethodOptions();

        return view('admin.form-integration-settings.edit', compact(
            'formIntegrationSetting',
            'forms',
            'integrations',
            'targetOptions',
            'processOptions',
            'endpointTypeOptions'
        ));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateFormIntegrationSettingRequest $request, FormIntegrationSetting $formIntegrationSetting)
    {
        try {
            $data = $request->validated();
            $data['updated_by'] = Auth::id();

            $formIntegrationSetting->update($data);

            // Validate the updated integration setting
            $validationErrors = $this->formIntegrationService->validateIntegrationSetting($formIntegrationSetting);

            if (!empty($validationErrors)) {
                Log::warning('Form integration setting updated with validation warnings', [
                    'integration_setting_id' => $formIntegrationSetting->id,
                    'warnings' => $validationErrors
                ]);
            }

            return redirect()
                ->route('admin.form-integration-settings.index')
                ->with('success', 'Form integration setting updated successfully.');

        } catch (\Exception $e) {
            Log::error('Failed to update form integration setting', [
                'integration_setting_id' => $formIntegrationSetting->id,
                'error' => $e->getMessage(),
                'request_data' => $request->validated()
            ]);

            return redirect()
                ->back()
                ->withInput()
                ->with('error', 'Failed to update form integration setting: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(FormIntegrationSetting $formIntegrationSetting)
    {
        // TODO: Re-enable permission checking when user management system is ready
        // abort_if(auth()->user()->type !== 1, Response::HTTP_FORBIDDEN, '403 Forbidden');

        try {
            $formIntegrationSetting->delete();

            return redirect()
                ->route('admin.form-integration-settings.index')
                ->with('success', 'Form integration setting deleted successfully.');

        } catch (\Exception $e) {
            Log::error('Failed to delete form integration setting', [
                'integration_setting_id' => $formIntegrationSetting->id,
                'error' => $e->getMessage()
            ]);

            return redirect()
                ->back()
                ->with('error', 'Failed to delete form integration setting: ' . $e->getMessage());
        }
    }

    /**
     * Get form fields for AJAX request
     */
    public function getFormFields(Request $request)
    {
        $formId = $request->input('form_id');

        if (!$formId) {
            return response()->json(['error' => 'Form ID is required'], 400);
        }

        try {
            $formFields = $this->formIntegrationService->getFormFields($formId);
            return response()->json(['fields' => $formFields]);

        } catch (\Exception $e) {
            Log::error('Failed to get form fields', [
                'form_id' => $formId,
                'error' => $e->getMessage()
            ]);

            return response()->json(['error' => 'Failed to get form fields'], 500);
        }
    }

    /**
     * Get integration fields for AJAX request
     */
    public function getIntegrationFields(Request $request)
    {
        $integrationId = $request->input('integration_id');

        if (!$integrationId) {
            return response()->json(['error' => 'Integration ID is required'], 400);
        }

        try {
            $integrationFields = $this->formIntegrationService->getIntegrationFields($integrationId);
            return response()->json(['fields' => $integrationFields]);

        } catch (\Exception $e) {
            Log::error('Failed to get integration fields', [
                'integration_id' => $integrationId,
                'error' => $e->getMessage()
            ]);

            return response()->json(['error' => 'Failed to get integration fields'], 500);
        }
    }

    /**
     * Get field mapping suggestions for AJAX request
     */
    public function getFieldMappingSuggestions(Request $request)
    {
        try {
            $formId = $request->input('form_id');
            $integrationId = $request->input('endpoint_configuration_id');

            if (!$formId || !$integrationId) {
                return response()->json(['error' => 'Form ID and Integration ID are required'], 400);
            }

            // Get form fields
            $formFields = $this->formIntegrationService->getFormFields($formId);
            
            // Get integration fields
            $integrationFields = $this->formIntegrationService->getIntegrationFields($integrationId);
            
            // Get suggestions
            $suggestionService = new \App\Services\FieldMappingSuggestionService();
            $suggestions = $suggestionService->generateSuggestions($formFields, $integrationFields);
            
            // Prepare detailed suggestions with additional context
            $suggestionService = new \App\Services\FieldMappingSuggestionService();
            $detailedSuggestions = [];

            foreach ($suggestions as $formField => $integrationField) {
                $formFieldData = collect($formFields)->firstWhere('key', $formField);
                $integrationFieldData = collect($integrationFields)->firstWhere('name', $integrationField);

                if ($formFieldData && $integrationFieldData) {
                    $matchInfo = $suggestionService->generateSuggestions([$formFieldData], [$integrationFieldData]);
                    $detailedSuggestions[] = [
                        'form_field' => $formField,
                        'form_label' => $formFieldData['label'] ?? $formField,
                        'integration_field' => $integrationField,
                        'integration_description' => $integrationFieldData['description'] ?? '',
                        'confidence' => 0.9, // Default high confidence for suggested matches
                        'reason' => 'Intelligent matching algorithm'
                    ];
                }
            }

            return response()->json([
                'success' => true,
                'form_fields' => $formFields,
                'integration_fields' => $integrationFields,
                'suggestions' => $suggestions,
                'detailed_suggestions' => $detailedSuggestions,
                'total_suggestions' => count($suggestions),
                'unmapped_form_fields' => array_filter($formFields, function($field) use ($suggestions) {
                    return !isset($suggestions[$field['key']]);
                }),
                'unmapped_integration_fields' => array_filter($integrationFields, function($field) use ($suggestions) {
                    return !in_array($field['name'], array_values($suggestions));
                })
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get field mapping suggestions', [
                'form_id' => $formId ?? null,
                'integration_id' => $integrationId ?? null,
                'error' => $e->getMessage()
            ]);

            return response()->json(['error' => 'Failed to get field mapping suggestions'], 500);
        }
    }

    /**
     * Get endpoint fields for the given endpoint configuration ID
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getEndpointFields(Request $request)
    {
        try {
            $request->validate([
                'endpoint_id' => 'required|exists:integration_configurations,id',
            ]);

            $endpointId = $request->input('endpoint_id');
            $endpoint = IntegrationConfiguration::findOrFail($endpointId);

            // Parse the request fields from the endpoint configuration
            $requestFields = json_decode($endpoint->request_fields, true) ?? [];

            // Format the fields for the response
            $fields = array_map(function($field) {
                return [
                    'name' => $field['name'] ?? '',
                    'type' => $field['type'] ?? 'string',
                    'required' => $field['required'] ?? false,
                    'description' => $field['description'] ?? '',
                    'example' => $field['example'] ?? ''
                ];
            }, $requestFields);

            return response()->json([
                'success' => true,
                'fields' => $fields
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get endpoint fields: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to load endpoint fields: ' . $e->getMessage()
            ], 500);
        }
    }
}
