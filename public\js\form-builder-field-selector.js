class FieldNameSelector {
    constructor(builder, fieldDefinitions = []) {
        this.builder = builder;
        this.fieldDefinitions = fieldDefinitions;
        this.initialize();
    }

    initialize() {
        // Store original create method
        const originalCreate = this.builder.addComponent;
        
        // Override addComponent to inject our custom modal
        this.builder.addComponent = (component, parent, element, data, before) => {
            // Only handle field components, not layout components
            const isFieldComponent = [
                'textfield', 'textarea', 'number', 'checkbox', 'select', 'radio'
            ].includes(component.type);

            if (isFieldComponent) {
                this.showFieldNameModal(component, parent, element, data, before, originalCreate);
            } else {
                originalCreate.call(this.builder, component, parent, element, data, before);
            }
        };
    }

    showFieldNameModal(component, parent, element, data, before, originalCreate) {
        // Create modal
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = 'fieldNameModal';
        modal.tabIndex = '-1';
        modal.role = 'dialog';
        modal.innerHTML = `
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Select Field Name</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label>Select from existing fields or create new:</label>
                            <select class="form-control mb-2" id="fieldNameSelect">
                                <option value="">-- Select a field --</option>
                                ${this.fieldDefinitions.map(field => 
                                    `<option value="${field.field_key}">${field.label} (${field.field_key})</option>`
                                ).join('')}
                                <option value="_custom">+ Create New Field</option>
                            </select>
                            <input type="text" class="form-control mt-2" id="customFieldName" placeholder="Enter field name" style="display: none;">
                            <input type="text" class="form-control mt-2" id="fieldLabel" placeholder="Enter field label">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" id="saveFieldBtn">Save</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        
        // Initialize modal
        const modalEl = new bootstrap.Modal(modal);
        
        // Show/hide custom field input
        const fieldSelect = modal.querySelector('#fieldNameSelect');
        const customFieldInput = modal.querySelector('#customFieldName');
        
        fieldSelect.addEventListener('change', (e) => {
            customFieldInput.style.display = e.target.value === '_custom' ? 'block' : 'none';
            
            // Auto-fill label if selecting existing field
            if (e.target.value && e.target.value !== '_custom') {
                const selectedField = this.fieldDefinitions.find(f => f.field_key === e.target.value);
                if (selectedField) {
                    modal.querySelector('#fieldLabel').value = selectedField.label || '';
                }
            }
        });

        // Handle save
        const saveBtn = modal.querySelector('#saveFieldBtn');
        saveBtn.addEventListener('click', () => {
            const fieldKey = fieldSelect.value === '_custom' 
                ? customFieldInput.value.trim() 
                : fieldSelect.value;
                
            const fieldLabel = modal.querySelector('#fieldLabel').value.trim();
            
            if (!fieldKey) {
                alert('Please enter a field name');
                return;
            }
            
            if (!fieldLabel) {
                alert('Please enter a field label');
                return;
            }
            
            // Update component with field name and label
            component.key = fieldKey;
            component.label = fieldLabel;
            
            // Close modal
            modalEl.hide();
            
            // Add the component with the original method
            originalCreate.call(this.builder, component, parent, element, data, before);
            
            // Clean up
            modal.remove();
        });
        
        // Show modal
        modalEl.show();
    }
}

// Export for use in other files
if (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {
    module.exports = FieldNameSelector;
} else {
    window.FieldNameSelector = FieldNameSelector;
}
