<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('qr_decoder_profiles', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->string('process_type')->index();
            $table->enum('mode', ['delimiter', 'fixed-length', 'gs1']);
            $table->string('delimiter')->nullable();
            $table->text('sample_qr')->nullable();
            $table->string('vendor_num')->nullable()->index();
            $table->tinyInteger('is_active')->default(0);
            $table->unsignedBigInteger('updated_by')->default(0)->comment('User who last updated this setting');

            $table->timestamps();
        });
        // Schema::create('qr_decoder_fields', function (Blueprint $table) {
        //     $table->id();
        //     $table->foreignId('profile_id')->constrained('qr_decoder_profiles')->cascadeOnDelete();
        //     // unified columns for different modes
        //     $table->unsignedInteger('segment')->nullable();     // delimiter mode: position (1-based)
        //     $table->unsignedInteger('start')->nullable();       // fixed-length: start index (0-based)
        //     $table->unsignedInteger('length')->nullable();      // fixed-length: length
        //     $table->string('ai')->nullable();                   // GS1 AI code e.g., '01', '17'

        //     $table->string('field_name');                       // mapped form field key
        //     $table->string('transform_rule')->nullable();       // e.g., 'date:YYYYMMDD>DD/MM/YYYY'
        //     $table->tinyInteger('optional')->default(0);
        //     $table->string('notes')->nullable();
        //     $table->unsignedInteger('order')->default(0);
        //     $table->timestamps();

        //     $table->index(['profile_id', 'order']);
        // });
        // Schema::create('qr_decoder_vendor_mappings', function (Blueprint $table) {
        //     $table->id();
        //     $table->string('process_type')->index();
        //     $table->string('vendor_number')->index();
        //     $table->foreignId('profile_id')->constrained('qr_decoder_profiles')->cascadeOnDelete();
        //     $table->timestamps();

        //     $table->unique(['process_type', 'vendor_number']);
        // });
    }

    public function down(): void
    {
        Schema::dropIfExists('qr_decoder_profiles');
    }
};
