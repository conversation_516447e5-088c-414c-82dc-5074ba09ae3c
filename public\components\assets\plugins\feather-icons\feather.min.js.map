{"version": 3, "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///feather.min.js", "webpack:///webpack/bootstrap 6069794b43f7bf4ca7d0", "webpack:///./node_modules/core-js/modules/_wks.js", "webpack:///./node_modules/core-js/modules/_global.js", "webpack:///./node_modules/core-js/modules/_object-dp.js", "webpack:///./node_modules/core-js/modules/_is-object.js", "webpack:///./node_modules/core-js/modules/_descriptors.js", "webpack:///./node_modules/core-js/modules/_has.js", "webpack:///./node_modules/core-js/modules/_core.js", "webpack:///./node_modules/core-js/modules/_hide.js", "webpack:///./node_modules/core-js/modules/_an-object.js", "webpack:///./node_modules/core-js/modules/_redefine.js", "webpack:///./node_modules/core-js/modules/_export.js", "webpack:///./node_modules/core-js/modules/_fails.js", "webpack:///./node_modules/core-js/modules/_ctx.js", "webpack:///./node_modules/core-js/modules/_iterators.js", "webpack:///./node_modules/core-js/modules/_defined.js", "webpack:///./node_modules/core-js/modules/_property-desc.js", "webpack:///./node_modules/core-js/modules/_uid.js", "webpack:///./node_modules/core-js/modules/_to-iobject.js", "webpack:///./node_modules/core-js/modules/_to-integer.js", "webpack:///./node_modules/core-js/modules/_iter-define.js", "webpack:///./node_modules/core-js/modules/_to-length.js", "webpack:///./node_modules/core-js/modules/_shared-key.js", "webpack:///./node_modules/core-js/modules/_set-to-string-tag.js", "webpack:///./node_modules/core-js/modules/_to-object.js", "webpack:///./node_modules/core-js/modules/_classof.js", "webpack:///./node_modules/core-js/modules/_for-of.js", "webpack:///./dist/icons.json", "webpack:///./node_modules/core-js/modules/es6.string.iterator.js", "webpack:///./node_modules/core-js/modules/_ie8-dom-define.js", "webpack:///./node_modules/core-js/modules/_dom-create.js", "webpack:///./node_modules/core-js/modules/_to-primitive.js", "webpack:///./node_modules/core-js/modules/_object-create.js", "webpack:///./node_modules/core-js/modules/_object-keys.js", "webpack:///./node_modules/core-js/modules/_iobject.js", "webpack:///./node_modules/core-js/modules/_cof.js", "webpack:///./node_modules/core-js/modules/_shared.js", "webpack:///./node_modules/core-js/modules/_enum-bug-keys.js", "webpack:///./node_modules/core-js/modules/_iter-call.js", "webpack:///./node_modules/core-js/modules/_is-array-iter.js", "webpack:///./node_modules/core-js/modules/core.get-iterator-method.js", "webpack:///./node_modules/core-js/modules/_iter-detect.js", "webpack:///./node_modules/core-js/modules/_object-pie.js", "webpack:///./node_modules/core-js/modules/_iter-step.js", "webpack:///./node_modules/core-js/modules/_redefine-all.js", "webpack:///./node_modules/core-js/modules/_an-instance.js", "webpack:///./node_modules/core-js/modules/_meta.js", "webpack:///./src/to-svg.js", "webpack:///./node_modules/core-js/fn/array/from.js", "webpack:///./node_modules/core-js/modules/_string-at.js", "webpack:///./node_modules/core-js/modules/_library.js", "webpack:///./node_modules/core-js/modules/_a-function.js", "webpack:///./node_modules/core-js/modules/_iter-create.js", "webpack:///./node_modules/core-js/modules/_object-dps.js", "webpack:///./node_modules/core-js/modules/_object-keys-internal.js", "webpack:///./node_modules/core-js/modules/_array-includes.js", "webpack:///./node_modules/core-js/modules/_to-index.js", "webpack:///./node_modules/core-js/modules/_html.js", "webpack:///./node_modules/core-js/modules/_object-gpo.js", "webpack:///./node_modules/core-js/modules/es6.array.from.js", "webpack:///./node_modules/core-js/modules/_create-property.js", "webpack:///./node_modules/core-js/fn/object/assign.js", "webpack:///./node_modules/core-js/modules/es6.object.assign.js", "webpack:///./node_modules/core-js/modules/_object-assign.js", "webpack:///./node_modules/core-js/modules/_object-gops.js", "webpack:///./node_modules/core-js/fn/set.js", "webpack:///./node_modules/core-js/modules/es6.object.to-string.js", "webpack:///./node_modules/core-js/modules/web.dom.iterable.js", "webpack:///./node_modules/core-js/modules/es6.array.iterator.js", "webpack:///./node_modules/core-js/modules/_add-to-unscopables.js", "webpack:///./node_modules/core-js/modules/es6.set.js", "webpack:///./node_modules/core-js/modules/_collection-strong.js", "webpack:///./node_modules/core-js/modules/_set-species.js", "webpack:///./node_modules/core-js/modules/_collection.js", "webpack:///./node_modules/core-js/modules/_inherit-if-required.js", "webpack:///./node_modules/core-js/modules/_set-proto.js", "webpack:///./node_modules/core-js/modules/_object-gopd.js", "webpack:///./node_modules/core-js/modules/es7.set.to-json.js", "webpack:///./node_modules/core-js/modules/_collection-to-json.js", "webpack:///./node_modules/core-js/modules/_array-from-iterable.js", "webpack:///./src/index.js", "webpack:///./src/replace.js"], "names": ["root", "factory", "exports", "module", "define", "amd", "this", "modules", "__webpack_require__", "moduleId", "installedModules", "i", "l", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "configurable", "enumerable", "get", "n", "__esModule", "object", "property", "prototype", "hasOwnProperty", "p", "s", "store", "uid", "Symbol", "USE_SYMBOL", "global", "window", "Math", "self", "Function", "__g", "anObject", "IE8_DOM_DEFINE", "toPrimitive", "dP", "f", "O", "P", "Attributes", "e", "TypeError", "value", "it", "a", "key", "core", "version", "__e", "createDesc", "isObject", "hide", "has", "SRC", "$toString", "TPL", "split", "inspectSource", "val", "safe", "isFunction", "join", "String", "redefine", "ctx", "$export", "type", "source", "own", "out", "exp", "IS_FORCED", "F", "IS_GLOBAL", "G", "IS_STATIC", "S", "IS_PROTO", "IS_BIND", "B", "target", "expProto", "undefined", "U", "W", "R", "exec", "aFunction", "fn", "that", "length", "b", "apply", "arguments", "bitmap", "writable", "id", "px", "random", "concat", "toString", "IObject", "defined", "ceil", "floor", "isNaN", "LIBRARY", "Iterators", "$iterCreate", "setToStringTag", "getPrototypeOf", "ITERATOR", "BUGGY", "keys", "returnThis", "Base", "NAME", "<PERSON><PERSON><PERSON><PERSON>", "next", "DEFAULT", "IS_SET", "FORCED", "methods", "IteratorPrototype", "getMethod", "kind", "proto", "TAG", "DEF_VALUES", "VALUES_BUG", "$native", "$default", "$entries", "$anyNative", "entries", "values", "toInteger", "min", "shared", "def", "tag", "stat", "cof", "ARG", "tryGet", "T", "callee", "isArrayIter", "to<PERSON><PERSON><PERSON>", "getIterFn", "BREAK", "RETURN", "iterable", "step", "iterator", "result", "iterFn", "index", "done", "activity", "airplay", "alert-circle", "alert-octagon", "alert-triangle", "align-center", "align-justify", "align-left", "align-right", "anchor", "aperture", "arrow-down-left", "arrow-down-right", "arrow-down", "arrow-left", "arrow-right", "arrow-up-left", "arrow-up-right", "arrow-up", "at-sign", "award", "bar-chart-2", "bar-chart", "battery-charging", "battery", "bell-off", "bell", "bluetooth", "book", "bookmark", "box", "briefcase", "calendar", "camera-off", "camera", "cast", "check-circle", "check-square", "check", "chevron-down", "chevron-left", "chevron-right", "chevron-up", "chevrons-down", "chevrons-left", "chevrons-right", "chevrons-up", "chrome", "circle", "clipboard", "clock", "cloud-drizzle", "cloud-lightning", "cloud-off", "cloud-rain", "cloud-snow", "cloud", "codepen", "command", "compass", "copy", "corner-down-left", "corner-down-right", "corner-left-down", "corner-left-up", "corner-right-down", "corner-right-up", "corner-up-left", "corner-up-right", "cpu", "credit-card", "crosshair", "delete", "disc", "download-cloud", "download", "droplet", "edit-2", "edit-3", "edit", "external-link", "eye-off", "eye", "facebook", "fast-forward", "feather", "file-minus", "file-plus", "file-text", "file", "film", "filter", "flag", "folder", "github", "globe", "grid", "hash", "headphones", "heart", "home", "image", "inbox", "info", "instagram", "layers", "layout", "life-buoy", "link-2", "link", "list", "loader", "lock", "log-in", "log-out", "mail", "map-pin", "map", "maximize-2", "maximize", "menu", "message-circle", "message-square", "mic-off", "mic", "minimize-2", "minimize", "minus-circle", "minus-square", "minus", "monitor", "moon", "more-horizontal", "more-vertical", "move", "music", "navigation-2", "navigation", "octagon", "package", "pause-circle", "pause", "percent", "phone-call", "phone-forwarded", "phone-incoming", "phone-missed", "phone-off", "phone-outgoing", "phone", "pie-chart", "play-circle", "play", "plus-circle", "plus-square", "plus", "pocket", "power", "printer", "radio", "refresh-ccw", "refresh-cw", "repeat", "rewind", "rotate-ccw", "rotate-cw", "save", "scissors", "search", "server", "settings", "share-2", "share", "shield", "shuffle", "sidebar", "skip-back", "skip-forward", "slack", "slash", "smartphone", "speaker", "square", "star", "stop-circle", "sun", "sunrise", "sunset", "tablet", "thermometer", "thumbs-down", "thumbs-up", "toggle-left", "toggle-right", "trash-2", "trash", "trending-down", "trending-up", "triangle", "twitter", "umbrella", "unlock", "upload-cloud", "upload", "user-check", "user-minus", "user-plus", "user-x", "user", "users", "video-off", "video", "voicemail", "volume-1", "volume-2", "volume-x", "volume", "watch", "wifi", "wind", "x-circle", "x-square", "x", "zap", "zoom-in", "zoom-out", "$at", "iterated", "_t", "_i", "point", "document", "is", "createElement", "valueOf", "dPs", "enumBugKeys", "IE_PROTO", "Empty", "createDict", "iframeDocument", "iframe", "style", "display", "append<PERSON><PERSON><PERSON>", "src", "contentWindow", "open", "write", "lt", "close", "create", "Properties", "$keys", "propertyIsEnumerable", "slice", "ret", "ArrayProto", "Array", "classof", "getIteratorMethod", "SAFE_CLOSING", "riter", "from", "skipClosing", "arr", "iter", "forbiddenField", "META", "setDesc", "isExtensible", "FREEZE", "preventExtensions", "setMeta", "w", "<PERSON><PERSON><PERSON>", "getWeak", "onFreeze", "meta", "NEED", "KEY", "toSvg", "options", "Error", "_icons2", "default", "combinedOptions", "assign", "DEFAULT_OPTIONS", "class", "addDefaultClassNames", "optionsToAtrributes", "classNames", "classNamesArray", "trim", "classNamesSet", "Set", "add", "attributes", "for<PERSON>ach", "push", "_icons", "obj", "xmlns", "width", "height", "viewBox", "fill", "stroke", "stroke-width", "stroke-linecap", "stroke-linejoin", "TO_STRING", "pos", "charCodeAt", "char<PERSON>t", "descriptor", "get<PERSON><PERSON><PERSON>", "defineProperties", "toIObject", "arrayIndexOf", "names", "toIndex", "IS_INCLUDES", "$this", "el", "fromIndex", "max", "documentElement", "toObject", "ObjectProto", "constructor", "createProperty", "arrayLike", "C", "aLen", "mapfn", "mapping", "$defineProperty", "gOPS", "pIE", "$assign", "A", "K", "k", "getSymbols", "isEnum", "j", "getOwnPropertySymbols", "test", "$iterators", "wks", "TO_STRING_TAG", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "collections", "Collection", "addToUnscopables", "_k", "Arguments", "UNSCOPABLES", "strong", "redefineAll", "anInstance", "forOf", "$iterDefine", "setSpecies", "DESCRIPTORS", "SIZE", "getEntry", "entry", "_f", "getConstructor", "wrapper", "IS_MAP", "ADDER", "_l", "clear", "data", "r", "prev", "callbackfn", "v", "setStrong", "SPECIES", "fails", "$iterDetect", "inheritIfRequired", "common", "IS_WEAK", "fixMethod", "instance", "HASNT_CHAINING", "THROWS_ON_PRIMITIVES", "ACCEPT_ITERABLES", "BUGGY_ZERO", "$instance", "setPrototypeOf", "set", "buggy", "__proto__", "gOPD", "getOwnPropertyDescriptor", "toJSON", "_interopRequireDefault", "_toSvg", "_toSvg2", "_replace", "_replace2", "icons", "replace", "elementsToReplace", "querySelectorAll", "element", "replaceElement", "getAttribute", "console", "error", "elementClassAttr", "svgString", "svgDocument", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "svgElement", "querySelector", "parentNode", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": "CAAA,SAAAA,EAAAC,GACA,gBAAAC,UAAA,gBAAAC,QACAA,OAAAD,QAAAD,IACA,kBAAAG,gBAAAC,IACAD,UAAAH,GACA,gBAAAC,SACAA,QAAA,QAAAD,IAEAD,EAAA,QAAAC,KACCK,KAAA,WACD,MCAgB,UAAUC,GCN1B,QAAAC,GAAAC,GAGA,GAAAC,EAAAD,GACA,MAAAC,GAAAD,GAAAP,OAGA,IAAAC,GAAAO,EAAAD,IACAE,EAAAF,EACAG,GAAA,EACAV,WAUA,OANAK,GAAAE,GAAAI,KAAAV,EAAAD,QAAAC,IAAAD,QAAAM,GAGAL,EAAAS,GAAA,EAGAT,EAAAD,QAvBA,GAAAQ,KA4DA,OAhCAF,GAAAM,EAAAP,EAGAC,EAAAO,EAAAL,EAGAF,EAAAQ,EAAA,SAAAd,EAAAe,EAAAC,GACAV,EAAAW,EAAAjB,EAAAe,IACAG,OAAAC,eAAAnB,EAAAe,GACAK,cAAA,EACAC,YAAA,EACAC,IAAAN,KAMAV,EAAAiB,EAAA,SAAAtB,GACA,GAAAe,GAAAf,KAAAuB,WACA,WAA2B,MAAAvB,GAAA,SAC3B,WAAiC,MAAAA,GAEjC,OADAK,GAAAQ,EAAAE,EAAA,IAAAA,GACAA,GAIAV,EAAAW,EAAA,SAAAQ,EAAAC,GAAsD,MAAAR,QAAAS,UAAAC,eAAAjB,KAAAc,EAAAC,IAGtDpB,EAAAuB,EAAA,GAGAvB,IAAAwB,EAAA,MDgBM,SAAU7B,EAAQD,EAASM,GE7EjC,GAAAyB,GAAAzB,EAAA,WACA0B,EAAA1B,EAAA,IACA2B,EAAA3B,EAAA,GAAA2B,OACAC,EAAA,kBAAAD,IAEAhC,EAAAD,QAAA,SAAAe,GACA,MAAAgB,GAAAhB,KAAAgB,EAAAhB,GACAmB,GAAAD,EAAAlB,KAAAmB,EAAAD,EAAAD,GAAA,UAAAjB,MAGAgB,SFmFM,SAAU9B,EAAQD,GG5FxB,GAAAmC,GAAAlC,EAAAD,QAAA,mBAAAoC,gBAAAC,WACAD,OAAA,mBAAAE,YAAAD,WAAAC,KAAAC,SAAA,gBACA,iBAAAC,WAAAL,IHmGM,SAAUlC,EAAQD,EAASM,GItGjC,GAAAmC,GAAAnC,EAAA,GACAoC,EAAApC,EAAA,IACAqC,EAAArC,EAAA,IACAsC,EAAA1B,OAAAC,cAEAnB,GAAA6C,EAAAvC,EAAA,GAAAY,OAAAC,eAAA,SAAA2B,EAAAC,EAAAC,GAIA,GAHAP,EAAAK,GACAC,EAAAJ,EAAAI,GAAA,GACAN,EAAAO,GACAN,EAAA,IACA,MAAAE,GAAAE,EAAAC,EAAAC,GACG,MAAAC,IACH,UAAAD,IAAA,OAAAA,GAAA,KAAAE,WAAA,2BAEA,OADA,SAAAF,KAAAF,EAAAC,GAAAC,EAAAG,OACAL,IJ6GM,SAAU7C,EAAQD,GK3HxBC,EAAAD,QAAA,SAAAoD,GACA,sBAAAA,GAAA,OAAAA,EAAA,kBAAAA,KLkIM,SAAUnD,EAAQD,EAASM,GMlIjCL,EAAAD,SAAAM,EAAA,eACA,MAAsE,IAAtEY,OAAAC,kBAAiC,KAAQG,IAAA,WAAgB,YAAa+B,KN0IhE,SAAUpD,EAAQD,GO5IxB,GAAA4B,MAAuBA,cACvB3B,GAAAD,QAAA,SAAAoD,EAAAE,GACA,MAAA1B,GAAAjB,KAAAyC,EAAAE,KPmJM,SAAUrD,EAAQD,GQrJxB,GAAAuD,GAAAtD,EAAAD,SAA6BwD,QAAA,QAC7B,iBAAAC,WAAAF,IR2JM,SAAUtD,EAAQD,EAASM,GS5JjC,GAAAsC,GAAAtC,EAAA,GACAoD,EAAApD,EAAA,GACAL,GAAAD,QAAAM,EAAA,YAAAmB,EAAA6B,EAAAH,GACA,MAAAP,GAAAC,EAAApB,EAAA6B,EAAAI,EAAA,EAAAP,KACC,SAAA1B,EAAA6B,EAAAH,GAED,MADA1B,GAAA6B,GAAAH,EACA1B,ITmKM,SAAUxB,EAAQD,EAASM,GUzKjC,GAAAqD,GAAArD,EAAA,EACAL,GAAAD,QAAA,SAAAoD,GACA,IAAAO,EAAAP,GAAA,KAAAF,WAAAE,EAAA,qBACA,OAAAA,KVgLM,SAAUnD,EAAQD,EAASM,GWnLjC,GAAA6B,GAAA7B,EAAA,GACAsD,EAAAtD,EAAA,GACAuD,EAAAvD,EAAA,GACAwD,EAAAxD,EAAA,WAEAyD,EAAAxB,SAAA,SACAyB,GAAA,GAAAD,GAAAE,MAFA,WAIA3D,GAAA,GAAA4D,cAAA,SAAAd,GACA,MAAAW,GAAApD,KAAAyC,KAGAnD,EAAAD,QAAA,SAAA8C,EAAAQ,EAAAa,EAAAC,GACA,GAAAC,GAAA,kBAAAF,EACAE,KAAAR,EAAAM,EAAA,SAAAP,EAAAO,EAAA,OAAAb,IACAR,EAAAQ,KAAAa,IACAE,IAAAR,EAAAM,EAAAL,IAAAF,EAAAO,EAAAL,EAAAhB,EAAAQ,GAAA,GAAAR,EAAAQ,GAAAU,EAAAM,KAAAC,OAAAjB,MACAR,IAAAX,EACAW,EAAAQ,GAAAa,EAEAC,EAIAtB,EAAAQ,GAAAR,EAAAQ,GAAAa,EACAP,EAAAd,EAAAQ,EAAAa,UAJArB,GAAAQ,GACAM,EAAAd,EAAAQ,EAAAa,OAOC5B,SAAAZ,UAzBD,WAyBC,WACD,wBAAAvB,YAAA0D,IAAAC,EAAApD,KAAAP,SX0LM,SAAUH,EAAQD,EAASM,GYxNjC,GAAA6B,GAAA7B,EAAA,GACAiD,EAAAjD,EAAA,GACAsD,EAAAtD,EAAA,GACAkE,EAAAlE,EAAA,GACAmE,EAAAnE,EAAA,IAGAoE,EAAA,SAAAC,EAAA5D,EAAA6D,GACA,GAQAtB,GAAAuB,EAAAC,EAAAC,EARAC,EAAAL,EAAAD,EAAAO,EACAC,EAAAP,EAAAD,EAAAS,EACAC,EAAAT,EAAAD,EAAAW,EACAC,EAAAX,EAAAD,EAAA3B,EACAwC,EAAAZ,EAAAD,EAAAc,EACAC,EAAAP,EAAA/C,EAAAiD,EAAAjD,EAAApB,KAAAoB,EAAApB,QAAqFoB,EAAApB,QAAuB,UAC5Gf,EAAAkF,EAAA3B,IAAAxC,KAAAwC,EAAAxC,OACA2E,EAAA1F,EAAA,YAAAA,EAAA,aAEAkF,KAAAN,EAAA7D,EACA,KAAAuC,IAAAsB,GAEAC,GAAAG,GAAAS,OAAAE,KAAAF,EAAAnC,GAEAwB,GAAAD,EAAAY,EAAAb,GAAAtB,GAEAyB,EAAAQ,GAAAV,EAAAJ,EAAAK,EAAA3C,GAAAmD,GAAA,kBAAAR,GAAAL,EAAAlC,SAAA5B,KAAAmE,KAEAW,GAAAjB,EAAAiB,EAAAnC,EAAAwB,EAAAH,EAAAD,EAAAkB,GAEA5F,EAAAsD,IAAAwB,GAAAlB,EAAA5D,EAAAsD,EAAAyB,GACAO,GAAAI,EAAApC,IAAAwB,IAAAY,EAAApC,GAAAwB,GAGA3C,GAAAoB,OAEAmB,EAAAO,EAAA,EACAP,EAAAS,EAAA,EACAT,EAAAW,EAAA,EACAX,EAAA3B,EAAA,EACA2B,EAAAc,EAAA,GACAd,EAAAmB,EAAA,GACAnB,EAAAkB,EAAA,GACAlB,EAAAoB,EAAA,IACA7F,EAAAD,QAAA0E,GZ8NM,SAAUzE,EAAQD,GaxQxBC,EAAAD,QAAA,SAAA+F,GACA,IACA,QAAAA,IACG,MAAA9C,GACH,YbgRM,SAAUhD,EAAQD,EAASM,GcnRjC,GAAA0F,GAAA1F,EAAA,GACAL,GAAAD,QAAA,SAAAiG,EAAAC,EAAAC,GAEA,GADAH,EAAAC,OACAN,KAAAO,EAAA,MAAAD,EACA,QAAAE,GACA,uBAAA9C,GACA,MAAA4C,GAAAtF,KAAAuF,EAAA7C,GAEA,wBAAAA,EAAA+C,GACA,MAAAH,GAAAtF,KAAAuF,EAAA7C,EAAA+C,GAEA,wBAAA/C,EAAA+C,EAAAvF,GACA,MAAAoF,GAAAtF,KAAAuF,EAAA7C,EAAA+C,EAAAvF,IAGA,kBACA,MAAAoF,GAAAI,MAAAH,EAAAI,cd4RM,SAAUrG,EAAQD,Ge7SxBC,EAAAD,YfmTM,SAAUC,EAAQD,GgBlTxBC,EAAAD,QAAA,SAAAoD,GACA,OAAAuC,IAAAvC,EAAA,KAAAF,WAAA,yBAAAE,EACA,OAAAA,KhB0TM,SAAUnD,EAAQD,GiB7TxBC,EAAAD,QAAA,SAAAuG,EAAApD,GACA,OACA9B,aAAA,EAAAkF,GACAnF,eAAA,EAAAmF,GACAC,WAAA,EAAAD,GACApD,WjBqUM,SAAUlD,EAAQD,GkB1UxB,GAAAyG,GAAA,EACAC,EAAArE,KAAAsE,QACA1G,GAAAD,QAAA,SAAAsD,GACA,gBAAAsD,WAAAjB,KAAArC,EAAA,GAAAA,EAAA,QAAAmD,EAAAC,GAAAG,SAAA,OlBiVM,SAAU5G,EAAQD,EAASM,GmBnVjC,GAAAwG,GAAAxG,EAAA,IACAyG,EAAAzG,EAAA,GACAL,GAAAD,QAAA,SAAAoD,GACA,MAAA0D,GAAAC,EAAA3D,MnB2VM,SAAUnD,EAAQD,GoB9VxB,GAAAgH,GAAA3E,KAAA2E,KACAC,EAAA5E,KAAA4E,KACAhH,GAAAD,QAAA,SAAAoD,GACA,MAAA8D,OAAA9D,MAAA,GAAAA,EAAA,EAAA6D,EAAAD,GAAA5D,KpBsWM,SAAUnD,EAAQD,EAASM,GAEjC,YqB3WA,IAAA6G,GAAA7G,EAAA,IACAoE,EAAApE,EAAA,IACAkE,EAAAlE,EAAA,GACAsD,EAAAtD,EAAA,GACAuD,EAAAvD,EAAA,GACA8G,EAAA9G,EAAA,IACA+G,EAAA/G,EAAA,IACAgH,EAAAhH,EAAA,IACAiH,EAAAjH,EAAA,IACAkH,EAAAlH,EAAA,eACAmH,OAAAC,MAAA,WAAAA,QAKAC,EAAA,WAA4B,MAAAvH,MAE5BH,GAAAD,QAAA,SAAA4H,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,GACAb,EAAAS,EAAAD,EAAAE,EACA,IAeAI,GAAA7E,EAAA8E,EAfAC,EAAA,SAAAC,GACA,IAAAb,GAAAa,IAAAC,GAAA,MAAAA,GAAAD,EACA,QAAAA,GACA,IAVA,OAWA,IAVA,SAUA,kBAA4C,UAAAR,GAAA1H,KAAAkI,IACvC,kBAA2B,UAAAR,GAAA1H,KAAAkI,KAEhCE,EAAAX,EAAA,YACAY,EAdA,UAcAT,EACAU,GAAA,EACAH,EAAAX,EAAAjG,UACAgH,EAAAJ,EAAAf,IAAAe,EAnBA,eAmBAP,GAAAO,EAAAP,GACAY,EAAAD,GAAAN,EAAAL,GACAa,EAAAb,EAAAS,EAAAJ,EAAA,WAAAO,MAAAjD,GACAmD,EAAA,SAAAjB,EAAAU,EAAAQ,SAAAJ,GAwBA,IArBAG,IACAV,EAAAb,EAAAuB,EAAAnI,KAAA,GAAAiH,QACA1G,OAAAS,YAEA2F,EAAAc,EAAAI,GAAA,GAEArB,GAAAtD,EAAAuE,EAAAZ,IAAA5D,EAAAwE,EAAAZ,EAAAG,IAIAc,GAAAE,GAjCA,WAiCAA,EAAA5H,OACA2H,GAAA,EACAE,EAAA,WAAiC,MAAAD,GAAAhI,KAAAP,QAGjC+G,IAAAe,IAAAT,IAAAiB,GAAAH,EAAAf,IACA5D,EAAA2E,EAAAf,EAAAoB,GAGAxB,EAAAS,GAAAe,EACAxB,EAAAoB,GAAAb,EACAK,EAMA,GALAG,GACAa,OAAAP,EAAAG,EAAAP,EA9CA,UA+CAX,KAAAO,EAAAW,EAAAP,EAhDA,QAiDAU,QAAAF,GAEAX,EAAA,IAAA5E,IAAA6E,GACA7E,IAAAiF,IAAA/D,EAAA+D,EAAAjF,EAAA6E,EAAA7E,QACKoB,KAAA3B,EAAA2B,EAAAO,GAAAwC,GAAAiB,GAAAb,EAAAM,EAEL,OAAAA,KrBkXM,SAAUlI,EAAQD,EAASM,GsBrbjC,GAAA2I,GAAA3I,EAAA,IACA4I,EAAA7G,KAAA6G,GACAjJ,GAAAD,QAAA,SAAAoD,GACA,MAAAA,GAAA,EAAA8F,EAAAD,EAAA7F,GAAA,sBtB6bM,SAAUnD,EAAQD,EAASM,GuBjcjC,GAAA6I,GAAA7I,EAAA,YACA0B,EAAA1B,EAAA,GACAL,GAAAD,QAAA,SAAAsD,GACA,MAAA6F,GAAA7F,KAAA6F,EAAA7F,GAAAtB,EAAAsB,MvBwcM,SAAUrD,EAAQD,EAASM,GwB3cjC,GAAA8I,GAAA9I,EAAA,GAAAuC,EACAgB,EAAAvD,EAAA,GACAkI,EAAAlI,EAAA,iBAEAL,GAAAD,QAAA,SAAAoD,EAAAiG,EAAAC,GACAlG,IAAAS,EAAAT,EAAAkG,EAAAlG,IAAAzB,UAAA6G,IAAAY,EAAAhG,EAAAoF,GAAkEpH,cAAA,EAAA+B,MAAAkG,MxBkd5D,SAAUpJ,EAAQD,EAASM,GyBtdjC,GAAAyG,GAAAzG,EAAA,GACAL,GAAAD,QAAA,SAAAoD,GACA,MAAAlC,QAAA6F,EAAA3D,MzB8dM,SAAUnD,EAAQD,EAASM,G0BhejC,GAAAiJ,GAAAjJ,EAAA,IACAkI,EAAAlI,EAAA,kBAEAkJ,EAA6C,aAA7CD,EAAA,WAAyB,MAAAjD,eAGzBmD,EAAA,SAAArG,EAAAE,GACA,IACA,MAAAF,GAAAE,GACG,MAAAL,KAGHhD,GAAAD,QAAA,SAAAoD,GACA,GAAAN,GAAA4G,EAAAlE,CACA,YAAAG,KAAAvC,EAAA,mBAAAA,EAAA,OAEA,iBAAAsG,EAAAD,EAAA3G,EAAA5B,OAAAkC,GAAAoF,IAAAkB,EAEAF,EAAAD,EAAAzG,GAEA,WAAA0C,EAAA+D,EAAAzG,KAAA,kBAAAA,GAAA6G,OAAA,YAAAnE,I1BweM,SAAUvF,EAAQD,EAASM,G2B7fjC,GAAAmE,GAAAnE,EAAA,IACAK,EAAAL,EAAA,IACAsJ,EAAAtJ,EAAA,IACAmC,EAAAnC,EAAA,GACAuJ,EAAAvJ,EAAA,IACAwJ,EAAAxJ,EAAA,IACAyJ,KACAC,KACAhK,EAAAC,EAAAD,QAAA,SAAAiK,EAAAlB,EAAA9C,EAAAC,EAAAsB,GACA,GAGArB,GAAA+D,EAAAC,EAAAC,EAHAC,EAAA7C,EAAA,WAAqC,MAAAyC,IAAmBH,EAAAG,GACxDpH,EAAA4B,EAAAwB,EAAAC,EAAA6C,EAAA,KACAuB,EAAA,CAEA,sBAAAD,GAAA,KAAAnH,WAAA+G,EAAA,oBAEA,IAAAL,EAAAS,IAAA,IAAAlE,EAAA0D,EAAAI,EAAA9D,QAAgEA,EAAAmE,EAAgBA,IAEhF,IADAF,EAAArB,EAAAlG,EAAAJ,EAAAyH,EAAAD,EAAAK,IAAA,GAAAJ,EAAA,IAAArH,EAAAoH,EAAAK,OACAP,GAAAK,IAAAJ,EAAA,MAAAI,OACG,KAAAD,EAAAE,EAAA1J,KAAAsJ,KAA2CC,EAAAC,EAAApC,QAAAwC,MAE9C,IADAH,EAAAzJ,EAAAwJ,EAAAtH,EAAAqH,EAAA/G,MAAA4F,MACAgB,GAAAK,IAAAJ,EAAA,MAAAI,GAGApK,GAAA+J,QACA/J,EAAAgK,U3BmgBM,SAAU/J,EAAQD,G4B3hBxBC,EAAAD,SACAwK,SAAA,iEACAC,QAAA,kJACAC,eAAA,oIACAC,gBAAA,+LACAC,iBAAA,sMACAC,eAAA,iLACAC,gBAAA,iLACAC,aAAA,iLACAC,cAAA,iLACAC,OAAA,kIACAC,SAAA,+VACAC,kBAAA,2FACAC,mBAAA,4FACAC,aAAA,8FACAC,aAAA,6FACAC,cAAA,8FACAC,gBAAA,0FACAC,iBAAA,2FACAC,WAAA,6FACAC,UAAA,2GACAC,MAAA,+GACAC,cAAA,kJACAC,YAAA,kJACAC,mBAAA,0MACAC,QAAA,8GACAC,WAAA,8JACAC,KAAA,sGACAC,UAAA,gFACAC,KAAA,oIACAC,SAAA,sEACAC,IAAA,oRACAC,UAAA,6HACAC,SAAA,iMACAC,aAAA,sKACAC,OAAA,6IACAC,KAAA,2KACAC,eAAA,qGACAC,eAAA,4HACAC,MAAA,gDACAC,eAAA,gDACAC,eAAA,iDACAC,gBAAA,gDACAC,aAAA,iDACAC,gBAAA,+FACAC,gBAAA,gGACAC,iBAAA,+FACAC,cAAA,gGACAC,OAAA,yOACAC,OAAA,2CACAC,UAAA,yJACAC,MAAA,0FACAC,gBAAA,+UACAC,kBAAA,yHACAC,YAAA,yJACAC,aAAA,0MACAC,aAAA,+UACAC,MAAA,kEACAC,QAAA,8QACAC,QAAA,6MACAC,QAAA,8HACAC,KAAA,0IACAC,mBAAA,yFACAC,oBAAA,4FACAC,mBAAA,4FACAC,iBAAA,yFACAC,oBAAA,4FACAC,kBAAA,yFACAC,iBAAA,yFACAC,kBAAA,4FACAC,IAAA,ucACAC,cAAA,6GACAC,UAAA,2NACAC,OAAA,+JACAC,KAAA,kFACAC,iBAAA,oKACAC,SAAA,yJACAC,QAAA,0DACAC,SAAA,6DACAC,SAAA,yGACAC,KAAA,+IACAC,gBAAA,sKACAC,UAAA,oPACAC,IAAA,wGACAC,SAAA,sFACAC,eAAA,sGACAC,QAAA,2JACAC,aAAA,wKACAC,YAAA,qNACAC,YAAA,+PACAC,KAAA,4HACAC,KAAA,qXACAC,OAAA,2EACAC,KAAA,yHACAC,OAAA,gGACAC,OAAA,wTACAC,MAAA,mMACAC,KAAA,+LACAC,KAAA,gLACAC,WAAA,gLACAC,MAAA,6JACAC,KAAA,uHACAC,MAAA,2JACAC,MAAA,6LACAC,KAAA,mIACAC,UAAA,qLACAC,OAAA,sJACAC,OAAA,qJACAC,YAAA,qWACAC,SAAA,4IACAC,KAAA,8JACAC,KAAA,oQACAC,OAAA,+YACAC,KAAA,4GACAC,SAAA,yJACAC,UAAA,wJACAC,KAAA,6IACAC,UAAA,0GACAC,IAAA,iKACAC,aAAA,qLACAC,SAAA,kHACAC,KAAA,qIACAC,iBAAA,6MACAC,iBAAA,kFACAC,UAAA,sRACAC,IAAA,8MACAC,aAAA,yLACAC,SAAA,kHACAC,eAAA,uFACAC,eAAA,6GACAC,MAAA,+CACAC,QAAA,0JACAC,KAAA,oEACAC,kBAAA,uHACAC,gBAAA,uHACAC,KAAA,iRACAC,MAAA,gLACAC,eAAA,0DACAC,WAAA,0DACAC,QAAA,sGACAC,QAAA,kUACAC,eAAA,mIACAC,MAAA,kGACAC,QAAA,sIACAC,aAAA,qWACAC,kBAAA,0YACAC,iBAAA,0YACAC,eAAA,wYACAC,YAAA,qXACAC,iBAAA,0YACAC,MAAA,kTACAC,YAAA,iGACAC,cAAA,6FACAC,KAAA,kDACAC,cAAA,mIACAC,cAAA,yJACAC,KAAA,2FACAC,OAAA,+IACAC,MAAA,8FACAC,QAAA,+LACAC,MAAA,+KACAC,cAAA,oLACAC,aAAA,qLACAC,OAAA,iLACAC,OAAA,wGACAC,aAAA,kGACAC,YAAA,uGACAC,KAAA,mLACAC,SAAA,mOACAC,OAAA,iGACAC,OAAA,kNACAC,SAAA,0xBACAC,UAAA,qOACAC,MAAA,sJACAC,OAAA,gEACAC,QAAA,iOACAC,QAAA,2GACAC,YAAA,+FACAC,eAAA,8FACAC,MAAA,kVACAC,MAAA,kGACAC,WAAA,8GACAC,QAAA,mJACAC,OAAA,iEACAC,KAAA,8HACAC,cAAA,yFACAC,IAAA,sbACAC,QAAA,qXACAC,OAAA,qXACAC,OAAA,4IACA/M,IAAA,4IACA5D,OAAA,yHACA4Q,YAAA,6EACAC,cAAA,0JACAC,YAAA,wIACAC,cAAA,uGACAC,eAAA,wGACAC,UAAA,wOACAC,MAAA,8IACAC,gBAAA,6GACAC,cAAA,4GACAC,SAAA,6GACAC,QAAA,gMACApS,KAAA,2IACAqS,SAAA,yEACAC,OAAA,2GACAC,eAAA,kNACAC,OAAA,sJACAC,aAAA,oJACAC,aAAA,kJACAC,YAAA,8LACAC,SAAA,6LACAC,KAAA,oGACAC,MAAA,0LACAC,YAAA,iKACAC,MAAA,mHACAC,UAAA,4IACAC,WAAA,+GACAC,WAAA,8IACAC,WAAA,yJACAC,OAAA,iEACAC,MAAA,wPACAC,KAAA,qJACAC,KAAA,iHACAC,WAAA,iIACAC,WAAA,uJACAC,EAAA,yFACAC,IAAA,sEACAC,UAAA,qLACAC,WAAA,2I5BkiBM,SAAUxY,EAAQD,EAASM,GAEjC,Y6BxwBA,IAAAoY,GAAApY,EAAA,OAGAA,GAAA,IAAAiE,OAAA,kBAAAoU,GACAvY,KAAAwY,GAAArU,OAAAoU,GACAvY,KAAAyY,GAAA,GAEC,WACD,GAEAC,GAFAhW,EAAA1C,KAAAwY,GACAtO,EAAAlK,KAAAyY,EAEA,OAAAvO,IAAAxH,EAAAqD,QAA+BhD,UAAAwC,GAAA4E,MAAA,IAC/BuO,EAAAJ,EAAA5V,EAAAwH,GACAlK,KAAAyY,IAAAC,EAAA3S,QACUhD,MAAA2V,EAAAvO,MAAA,O7B+wBJ,SAAUtK,EAAQD,EAASM,G8B9xBjCL,EAAAD,SAAAM,EAAA,KAAAA,EAAA,eACA,MAAmG,IAAnGY,OAAAC,eAAAb,EAAA,gBAAsEgB,IAAA,WAAgB,YAAa+B,K9BqyB7F,SAAUpD,EAAQD,EAASM,G+BtyBjC,GAAAqD,GAAArD,EAAA,GACAyY,EAAAzY,EAAA,GAAAyY,SAEAC,EAAArV,EAAAoV,IAAApV,EAAAoV,EAAAE,cACAhZ,GAAAD,QAAA,SAAAoD,GACA,MAAA4V,GAAAD,EAAAE,cAAA7V,Q/B6yBM,SAAUnD,EAAQD,EAASM,GgCjzBjC,GAAAqD,GAAArD,EAAA,EAGAL,GAAAD,QAAA,SAAAoD,EAAAiC,GACA,IAAA1B,EAAAP,GAAA,MAAAA,EACA,IAAA6C,GAAA9B,CACA,IAAAkB,GAAA,mBAAAY,EAAA7C,EAAAyD,YAAAlD,EAAAQ,EAAA8B,EAAAtF,KAAAyC,IAAA,MAAAe,EACA,uBAAA8B,EAAA7C,EAAA8V,WAAAvV,EAAAQ,EAAA8B,EAAAtF,KAAAyC,IAAA,MAAAe,EACA,KAAAkB,GAAA,mBAAAY,EAAA7C,EAAAyD,YAAAlD,EAAAQ,EAAA8B,EAAAtF,KAAAyC,IAAA,MAAAe,EACA,MAAAjB,WAAA,6ChCyzBM,SAAUjD,EAAQD,EAASM,GiCl0BjC,GAAAmC,GAAAnC,EAAA,GACA6Y,EAAA7Y,EAAA,IACA8Y,EAAA9Y,EAAA,IACA+Y,EAAA/Y,EAAA,gBACAgZ,EAAA,aAIAC,EAAA,WAEA,GAIAC,GAJAC,EAAAnZ,EAAA,cACAG,EAAA2Y,EAAAjT,MAcA,KAVAsT,EAAAC,MAAAC,QAAA,OACArZ,EAAA,IAAAsZ,YAAAH,GACAA,EAAAI,IAAA,cAGAL,EAAAC,EAAAK,cAAAf,SACAS,EAAAO,OACAP,EAAAQ,MAAAC,uCACAT,EAAAU,QACAX,EAAAC,EAAAvU,EACAxE,WAAA8Y,GAAA,UAAAH,EAAA3Y,GACA,OAAA8Y,KAGAtZ,GAAAD,QAAAkB,OAAAiZ,QAAA,SAAArX,EAAAsX,GACA,GAAAhQ,EAQA,OAPA,QAAAtH,GACAwW,EAAA,UAAA7W,EAAAK,GACAsH,EAAA,GAAAkP,GACAA,EAAA,eAEAlP,EAAAiP,GAAAvW,GACGsH,EAAAmP,QACH5T,KAAAyU,EAAAhQ,EAAA+O,EAAA/O,EAAAgQ,KjC20BM,SAAUna,EAAQD,EAASM,GkCj3BjC,GAAA+Z,GAAA/Z,EAAA,IACA8Y,EAAA9Y,EAAA,GAEAL,GAAAD,QAAAkB,OAAAwG,MAAA,SAAA5E,GACA,MAAAuX,GAAAvX,EAAAsW,KlCy3BM,SAAUnZ,EAAQD,EAASM,GmC73BjC,GAAAiJ,GAAAjJ,EAAA,GACAL,GAAAD,QAAAkB,OAAA,KAAAoZ,qBAAA,GAAApZ,OAAA,SAAAkC,GACA,gBAAAmG,EAAAnG,KAAAa,MAAA,IAAA/C,OAAAkC,KnCq4BM,SAAUnD,EAAQD,GoCx4BxB,GAAA6G,MAAiBA,QAEjB5G,GAAAD,QAAA,SAAAoD,GACA,MAAAyD,GAAAlG,KAAAyC,GAAAmX,MAAA,QpC+4BM,SAAUta,EAAQD,EAASM,GqCl5BjC,GAAA6B,GAAA7B,EAAA,GAEAyB,EAAAI,EADA,wBACAA,EADA,yBAEAlC,GAAAD,QAAA,SAAAsD,GACA,MAAAvB,GAAAuB,KAAAvB,EAAAuB,SrCy5BM,SAAUrD,EAAQD,GsC55BxBC,EAAAD,QAAA,gGAEAiE,MAAA,MtCm6BM,SAAUhE,EAAQD,EAASM,GuCr6BjC,GAAAmC,GAAAnC,EAAA,EACAL,GAAAD,QAAA,SAAAmK,EAAAlE,EAAA9C,EAAA4F,GACA,IACA,MAAAA,GAAA9C,EAAAxD,EAAAU,GAAA,GAAAA,EAAA,IAAA8C,EAAA9C,GAEG,MAAAF,GACH,GAAAuX,GAAArQ,EAAA,MAEA,WADAxE,KAAA6U,GAAA/X,EAAA+X,EAAA7Z,KAAAwJ,IACAlH,KvC86BM,SAAUhD,EAAQD,EAASM,GwCt7BjC,GAAA8G,GAAA9G,EAAA,IACAkH,EAAAlH,EAAA,eACAma,EAAAC,MAAA/Y,SAEA1B,GAAAD,QAAA,SAAAoD,GACA,WAAAuC,KAAAvC,IAAAgE,EAAAsT,QAAAtX,GAAAqX,EAAAjT,KAAApE,KxC87BM,SAAUnD,EAAQD,EAASM,GyCp8BjC,GAAAqa,GAAAra,EAAA,IACAkH,EAAAlH,EAAA,eACA8G,EAAA9G,EAAA,GACAL,GAAAD,QAAAM,EAAA,GAAAsa,kBAAA,SAAAxX,GACA,OAAAuC,IAAAvC,EAAA,MAAAA,GAAAoE,IACApE,EAAA,eACAgE,EAAAuT,EAAAvX,MzC28BM,SAAUnD,EAAQD,EAASM,G0Cj9BjC,GAAAkH,GAAAlH,EAAA,eACAua,GAAA,CAEA,KACA,GAAAC,IAAA,GAAAtT,IACAsT,GAAA,kBAA+BD,GAAA,GAC/BH,MAAAK,KAAAD,EAAA,WAA+B,UAC9B,MAAA7X,IAEDhD,EAAAD,QAAA,SAAA+F,EAAAiV,GACA,IAAAA,IAAAH,EAAA,QACA,IAAAzW,IAAA,CACA,KACA,GAAA6W,IAAA,GACAC,EAAAD,EAAAzT,IACA0T,GAAAnT,KAAA,WAA2B,OAASwC,KAAAnG,GAAA,IACpC6W,EAAAzT,GAAA,WAA+B,MAAA0T,IAC/BnV,EAAAkV,GACG,MAAAhY,IACH,MAAAmB,K1Cw9BM,SAAUnE,EAAQD,G2C3+BxBA,EAAA6C,KAAcyX,sB3Ci/BR,SAAUra,EAAQD,G4Cj/BxBC,EAAAD,QAAA,SAAAuK,EAAApH,GACA,OAAUA,QAAAoH,Y5Cw/BJ,SAAUtK,EAAQD,EAASM,G6Cz/BjC,GAAAkE,GAAAlE,EAAA,EACAL,GAAAD,QAAA,SAAAyF,EAAAoU,EAAAzV,GACA,OAAAd,KAAAuW,GAAArV,EAAAiB,EAAAnC,EAAAuW,EAAAvW,GAAAc,EACA,OAAAqB,K7CggCM,SAAUxF,EAAQD,G8CngCxBC,EAAAD,QAAA,SAAAoD,EAAA0E,EAAA/G,EAAAoa,GACA,KAAA/X,YAAA0E,SAAAnC,KAAAwV,OAAA/X,GACA,KAAAF,WAAAnC,EAAA,0BACG,OAAAqC,K9C0gCG,SAAUnD,EAAQD,EAASM,G+C7gCjC,GAAA8a,GAAA9a,EAAA,YACAqD,EAAArD,EAAA,GACAuD,EAAAvD,EAAA,GACA+a,EAAA/a,EAAA,GAAAuC,EACA4D,EAAA,EACA6U,EAAApa,OAAAoa,cAAA,WACA,UAEAC,GAAAjb,EAAA,eACA,MAAAgb,GAAApa,OAAAsa,yBAEAC,EAAA,SAAArY,GACAiY,EAAAjY,EAAAgY,GAAqBjY,OACrB1C,EAAA,OAAAgG,EACAiV,SAGAC,EAAA,SAAAvY,EAAA+W,GAEA,IAAAxW,EAAAP,GAAA,sBAAAA,MAAA,gBAAAA,GAAA,SAAAA,CACA,KAAAS,EAAAT,EAAAgY,GAAA,CAEA,IAAAE,EAAAlY,GAAA,SAEA,KAAA+W,EAAA,SAEAsB,GAAArY,GAEG,MAAAA,GAAAgY,GAAA3a,GAEHmb,EAAA,SAAAxY,EAAA+W,GACA,IAAAtW,EAAAT,EAAAgY,GAAA,CAEA,IAAAE,EAAAlY,GAAA,QAEA,KAAA+W,EAAA,QAEAsB,GAAArY,GAEG,MAAAA,GAAAgY,GAAAM,GAGHG,EAAA,SAAAzY,GAEA,MADAmY,IAAAO,EAAAC,MAAAT,EAAAlY,KAAAS,EAAAT,EAAAgY,IAAAK,EAAArY,GACAA,GAEA0Y,EAAA7b,EAAAD,SACAgc,IAAAZ,EACAW,MAAA,EACAJ,UACAC,UACAC,a/CohCM,SAAU5b,EAAQD,EAASM,GAEjC,YgDjjCe,SAAS2b,GAAM3Y,GAAmB,GAAd4Y,GAAc5V,UAAAH,OAAA,OAAAR,KAAAW,UAAA,GAAAA,UAAA,KAC/C,KAAKhD,EACH,KAAM,IAAI6Y,OAAM,uDAGlB,KAAKC,EAAAC,QAAM/Y,GACT,KAAM,IAAI6Y,OAAJ,qBAA+B7Y,EAA/B,gEAGR,IAAMgZ,GAAkBpb,OAAOqb,UAAWC,EAAiBN,EAM3D,OAJAI,GAAgBG,MAAQC,EAAqBJ,EAAgBG,MAAOnZ,GAIpE,QAFmBqZ,EAAoBL,GAEvC,IAA6BF,EAAAC,QAAM/Y,GAAnC,SASF,QAASoZ,GAAqBE,EAAYtZ,GAExC,GAAMuZ,GAAkBD,EAAaA,EAAWE,OAAO7Y,MAAM,UAGvD8Y,EAAgB,GAAIC,KAAIH,EAK9B,OAFAE,GAAcE,IAAI,WAAWA,IAA7B,WAA4C3Z,GAErCoX,MAAMK,KAAKgC,GAAezY,KAAK,KAQxC,QAASqY,GAAoBT,GAC3B,GAAMgB,KAMN,OAJAhc,QAAOwG,KAAKwU,GAASiB,QAAQ,SAAA7Z,GAC3B4Z,EAAWE,KAAQ9Z,EAAnB,KAA2B4Y,EAAQ5Y,GAAnC,OAGK4Z,EAAW5Y,KAAK,KhDmgCzBpD,OAAOC,eAAenB,EAAS,cAC7BmD,OAAO,IAETnD,EAAQqc,QgDvjCgBJ,CApBxB,IAAAoB,GAAA/c,EAAA,IhD+kCI8b,EAEJ,SAAgCkB,GAAO,MAAOA,IAAOA,EAAI9b,WAAa8b,GAAQjB,QAASiB,IAFlDD,GgD7kC/Bb,GACJe,MAAO,6BACPC,MAAO,GACPC,OAAQ,GACRC,QAAS,YACTC,KAAM,OACNC,OAAQ,eACRC,eAAgB,EAChBC,iBAAkB,QAClBC,kBAAmB,UhDspCf,SAAU9d,EAAQD,EAASM,GAEjCA,EAAoB,IACpBA,EAAoB,IACpBA,EAAoB,IACpBL,EAAOD,QAAUM,EAAoB,KAK/B,SAAUL,EAAQD,EAASM,GiD/qCjCA,EAAA,IACAA,EAAA,IACAL,EAAAD,QAAAM,EAAA,GAAAoa,MAAAK,MjDqrCM,SAAU9a,EAAQD,EAASM,GkDvrCjC,GAAA2I,GAAA3I,EAAA,IACAyG,EAAAzG,EAAA,GAGAL,GAAAD,QAAA,SAAAge,GACA,gBAAA9X,EAAA+X,GACA,GAGA5a,GAAA+C,EAHAtE,EAAAyC,OAAAwC,EAAAb,IACAzF,EAAAwI,EAAAgV,GACAvd,EAAAoB,EAAAqE,MAEA,OAAA1F,GAAA,GAAAA,GAAAC,EAAAsd,EAAA,OAAArY,IACAtC,EAAAvB,EAAAoc,WAAAzd,GACA4C,EAAA,OAAAA,EAAA,OAAA5C,EAAA,IAAAC,IAAA0F,EAAAtE,EAAAoc,WAAAzd,EAAA,WAAA2F,EAAA,MACA4X,EAAAlc,EAAAqc,OAAA1d,GAAA4C,EACA2a,EAAAlc,EAAAyY,MAAA9Z,IAAA,GAAA2F,EAAA,OAAA/C,EAAA,qBlD+rCM,SAAUpD,EAAQD,GmD7sCxBC,EAAAD,SAAA,GnDmtCM,SAAUC,EAAQD,GoDntCxBC,EAAAD,QAAA,SAAAoD,GACA,qBAAAA,GAAA,KAAAF,WAAAE,EAAA,sBACA,OAAAA,KpD0tCM,SAAUnD,EAAQD,EAASM,GAEjC,YqD7tCA,IAAA6Z,GAAA7Z,EAAA,IACA8d,EAAA9d,EAAA,IACAgH,EAAAhH,EAAA,IACA8H,IAGA9H,GAAA,GAAA8H,EAAA9H,EAAA,0BAAgF,MAAAF,QAEhFH,EAAAD,QAAA,SAAA8H,EAAAD,EAAAE,GACAD,EAAAnG,UAAAwY,EAAA/R,GAAqDL,KAAAqW,EAAA,EAAArW,KACrDT,EAAAQ,EAAAD,EAAA,erDouCM,SAAU5H,EAAQD,EAASM,GsD/uCjC,GAAAsC,GAAAtC,EAAA,GACAmC,EAAAnC,EAAA,GACA+d,EAAA/d,EAAA,GAEAL,GAAAD,QAAAM,EAAA,GAAAY,OAAAod,iBAAA,SAAAxb,EAAAsX,GACA3X,EAAAK,EAKA,KAJA,GAGAC,GAHA2E,EAAA2W,EAAAjE,GACAjU,EAAAuB,EAAAvB,OACA1F,EAAA,EAEA0F,EAAA1F,GAAAmC,EAAAC,EAAAC,EAAAC,EAAA2E,EAAAjH,KAAA2Z,EAAArX,GACA,OAAAD,KtDsvCM,SAAU7C,EAAQD,EAASM,GuDjwCjC,GAAAuD,GAAAvD,EAAA,GACAie,EAAAje,EAAA,IACAke,EAAAle,EAAA,QACA+Y,EAAA/Y,EAAA,eAEAL,GAAAD,QAAA,SAAAyB,EAAAgd,GACA,GAGAnb,GAHAR,EAAAyb,EAAA9c,GACAhB,EAAA,EACA2J,IAEA,KAAA9G,IAAAR,GAAAQ,GAAA+V,GAAAxV,EAAAf,EAAAQ,IAAA8G,EAAAgT,KAAA9Z,EAEA,MAAAmb,EAAAtY,OAAA1F,GAAAoD,EAAAf,EAAAQ,EAAAmb,EAAAhe,SACA+d,EAAApU,EAAA9G,IAAA8G,EAAAgT,KAAA9Z,GAEA,OAAA8G,KvDwwCM,SAAUnK,EAAQD,EAASM,GwDrxCjC,GAAAie,GAAAje,EAAA,IACAuJ,EAAAvJ,EAAA,IACAoe,EAAApe,EAAA,GACAL,GAAAD,QAAA,SAAA2e,GACA,gBAAAC,EAAAC,EAAAC,GACA,GAGA3b,GAHAL,EAAAyb,EAAAK,GACAzY,EAAA0D,EAAA/G,EAAAqD,QACAmE,EAAAoU,EAAAI,EAAA3Y,EAGA,IAAAwY,GAAAE,MAAA,KAAA1Y,EAAAmE,GAEA,IADAnH,EAAAL,EAAAwH,OACAnH,EAAA,aAEK,MAAWgD,EAAAmE,EAAeA,IAAA,IAAAqU,GAAArU,IAAAxH,KAC/BA,EAAAwH,KAAAuU,EAAA,MAAAF,IAAArU,GAAA,CACK,QAAAqU,IAAA,KxD+xCC,SAAU1e,EAAQD,EAASM,GyDjzCjC,GAAA2I,GAAA3I,EAAA,IACAye,EAAA1c,KAAA0c,IACA7V,EAAA7G,KAAA6G,GACAjJ,GAAAD,QAAA,SAAAsK,EAAAnE,GAEA,MADAmE,GAAArB,EAAAqB,GACAA,EAAA,EAAAyU,EAAAzU,EAAAnE,EAAA,GAAA+C,EAAAoB,EAAAnE,KzDwzCM,SAAUlG,EAAQD,EAASM,G0D7zCjCL,EAAAD,QAAAM,EAAA,GAAAyY,mBAAAiG,iB1Dm0CM,SAAU/e,EAAQD,EAASM,G2Dl0CjC,GAAAuD,GAAAvD,EAAA,GACA2e,EAAA3e,EAAA,IACA+Y,EAAA/Y,EAAA,gBACA4e,EAAAhe,OAAAS,SAEA1B,GAAAD,QAAAkB,OAAAqG,gBAAA,SAAAzE,GAEA,MADAA,GAAAmc,EAAAnc,GACAe,EAAAf,EAAAuW,GAAAvW,EAAAuW,GACA,kBAAAvW,GAAAqc,aAAArc,eAAAqc,YACArc,EAAAqc,YAAAxd,UACGmB,YAAA5B,QAAAge,EAAA,O3D00CG,SAAUjf,EAAQD,EAASM,GAEjC,Y4Dt1CA,IAAAmE,GAAAnE,EAAA,IACAoE,EAAApE,EAAA,IACA2e,EAAA3e,EAAA,IACAK,EAAAL,EAAA,IACAsJ,EAAAtJ,EAAA,IACAuJ,EAAAvJ,EAAA,IACA8e,EAAA9e,EAAA,IACAwJ,EAAAxJ,EAAA,GAEAoE,KAAAW,EAAAX,EAAAO,GAAA3E,EAAA,aAAA4a,GAA0ER,MAAAK,KAAAG,KAAoB,SAE9FH,KAAA,SAAAsE,GACA,GAOAlZ,GAAAiE,EAAAF,EAAAC,EAPArH,EAAAmc,EAAAI,GACAC,EAAA,kBAAAlf,WAAAsa,MACA6E,EAAAjZ,UAAAH,OACAqZ,EAAAD,EAAA,EAAAjZ,UAAA,OAAAX,GACA8Z,MAAA9Z,KAAA6Z,EACAlV,EAAA,EACAD,EAAAP,EAAAhH,EAIA,IAFA2c,IAAAD,EAAA/a,EAAA+a,EAAAD,EAAA,EAAAjZ,UAAA,OAAAX,GAAA,QAEAA,IAAA0E,GAAAiV,GAAA5E,OAAA9Q,EAAAS,GAMA,IADAlE,EAAA0D,EAAA/G,EAAAqD,QACAiE,EAAA,GAAAkV,GAAAnZ,GAAiCA,EAAAmE,EAAgBA,IACjD8U,EAAAhV,EAAAE,EAAAmV,EAAAD,EAAA1c,EAAAwH,MAAAxH,EAAAwH,QANA,KAAAH,EAAAE,EAAA1J,KAAAmC,GAAAsH,EAAA,GAAAkV,KAAoDpV,EAAAC,EAAApC,QAAAwC,KAAgCD,IACpF8U,EAAAhV,EAAAE,EAAAmV,EAAA9e,EAAAwJ,EAAAqV,GAAAtV,EAAA/G,MAAAmH,IAAA,GAAAJ,EAAA/G,MASA,OADAiH,GAAAjE,OAAAmE,EACAF,M5D+1CM,SAAUnK,EAAQD,EAASM,GAEjC,Y6Dl4CA,IAAAof,GAAApf,EAAA,GACAoD,EAAApD,EAAA,GAEAL,GAAAD,QAAA,SAAAyB,EAAA6I,EAAAnH,GACAmH,IAAA7I,GAAAie,EAAA7c,EAAApB,EAAA6I,EAAA5G,EAAA,EAAAP,IACA1B,EAAA6I,GAAAnH,I7Dy4CM,SAAUlD,EAAQD,EAASM,G8D/4CjCA,EAAA,IACAL,EAAAD,QAAAM,EAAA,GAAAY,OAAAqb,Q9Dq5CM,SAAUtc,EAAQD,EAASM,G+Dr5CjC,GAAAoE,GAAApE,EAAA,GAEAoE,KAAAW,EAAAX,EAAAO,EAAA,UAA0CsX,OAAAjc,EAAA,O/D45CpC,SAAUL,EAAQD,EAASM,GAEjC,YgE/5CA,IAAA+d,GAAA/d,EAAA,IACAqf,EAAArf,EAAA,IACAsf,EAAAtf,EAAA,IACA2e,EAAA3e,EAAA,IACAwG,EAAAxG,EAAA,IACAuf,EAAA3e,OAAAqb,MAGAtc,GAAAD,SAAA6f,GAAAvf,EAAA,eACA,GAAAwf,MACAta,KACAH,EAAApD,SACA8d,EAAA,sBAGA,OAFAD,GAAAza,GAAA,EACA0a,EAAA9b,MAAA,IAAAkZ,QAAA,SAAA6C,GAAkCxa,EAAAwa,OACf,GAAnBH,KAAmBC,GAAAza,IAAAnE,OAAAwG,KAAAmY,KAAsCra,IAAAlB,KAAA,KAAAyb,IACxD,SAAAta,EAAAb,GAMD,IALA,GAAA8E,GAAAuV,EAAAxZ,GACA8Z,EAAAjZ,UAAAH,OACAmE,EAAA,EACA2V,EAAAN,EAAA9c,EACAqd,EAAAN,EAAA/c,EACA0c,EAAAjV,GAMA,IALA,GAIAhH,GAJA+B,EAAAyB,EAAAR,UAAAgE,MACA5C,EAAAuY,EAAA5B,EAAAhZ,GAAAuB,OAAAqZ,EAAA5a,IAAAgZ,EAAAhZ,GACAc,EAAAuB,EAAAvB,OACAga,EAAA,EAEAha,EAAAga,GAAAD,EAAAvf,KAAA0E,EAAA/B,EAAAoE,EAAAyY,QAAAzW,EAAApG,GAAA+B,EAAA/B,GACG,OAAAoG,IACFmW,GhEs6CK,SAAU5f,EAAQD,GiEt8CxBA,EAAA6C,EAAA3B,OAAAkf,uBjE48CM,SAAUngB,EAAQD,EAASM,GkE58CjCA,EAAA,IACAA,EAAA,IACAA,EAAA,IACAA,EAAA,IACAA,EAAA,IACAL,EAAAD,QAAAM,EAAA,GAAA0c,KlEk9CM,SAAU/c,EAAQD,EAASM,GAEjC,YmEv9CA,IAAAqa,GAAAra,EAAA,IACA+f,IACAA,GAAA/f,EAAA,uBACA+f,EAAA,kBACA/f,EAAA,GAAAY,OAAAS,UAAA,sBACA,iBAAAgZ,EAAAva,MAAA,MACG,InE+9CG,SAAUH,EAAQD,EAASM,GoE79CjC,OAVAggB,GAAAhgB,EAAA,IACAkE,EAAAlE,EAAA,GACA6B,EAAA7B,EAAA,GACAsD,EAAAtD,EAAA,GACA8G,EAAA9G,EAAA,IACAigB,EAAAjgB,EAAA,GACAkH,EAAA+Y,EAAA,YACAC,EAAAD,EAAA,eACAE,EAAArZ,EAAAsT,MAEAgG,GAAA,sEAAAjgB,EAAA,EAAwGA,EAAA,EAAOA,IAAA,CAC/G,GAGA6C,GAHAuE,EAAA6Y,EAAAjgB,GACAkgB,EAAAxe,EAAA0F,GACAU,EAAAoY,KAAAhf,SAEA,IAAA4G,EAAA,CACAA,EAAAf,IAAA5D,EAAA2E,EAAAf,EAAAiZ,GACAlY,EAAAiY,IAAA5c,EAAA2E,EAAAiY,EAAA3Y,GACAT,EAAAS,GAAA4Y,CACA,KAAAnd,IAAAgd,GAAA/X,EAAAjF,IAAAkB,EAAA+D,EAAAjF,EAAAgd,EAAAhd,IAAA,MpE++CM,SAAUrD,EAAQD,EAASM,GAEjC,YqEngDA,IAAAsgB,GAAAtgB,EAAA,IACA4J,EAAA5J,EAAA,IACA8G,EAAA9G,EAAA,IACAie,EAAAje,EAAA,GAMAL,GAAAD,QAAAM,EAAA,IAAAoa,MAAA,iBAAA/B,EAAArQ,GACAlI,KAAAwY,GAAA2F,EAAA5F,GACAvY,KAAAyY,GAAA,EACAzY,KAAAygB,GAAAvY,GAEC,WACD,GAAAxF,GAAA1C,KAAAwY,GACAtQ,EAAAlI,KAAAygB,GACAvW,EAAAlK,KAAAyY,IACA,QAAA/V,GAAAwH,GAAAxH,EAAAqD,QACA/F,KAAAwY,OAAAjT,GACAuE,EAAA,IAEA,QAAA5B,EAAA4B,EAAA,EAAAI,GACA,UAAAhC,EAAA4B,EAAA,EAAApH,EAAAwH,IACAJ,EAAA,GAAAI,EAAAxH,EAAAwH,MACC,UAGDlD,EAAA0Z,UAAA1Z,EAAAsT,MAEAkG,EAAA,QACAA,EAAA,UACAA,EAAA,YrEygDM,SAAU3gB,EAAQD,EAASM,GsEziDjC,GAAAygB,GAAAzgB,EAAA,kBACAma,EAAAC,MAAA/Y,cACAgE,IAAA8U,EAAAsG,IAAAzgB,EAAA,GAAAma,EAAAsG,MACA9gB,EAAAD,QAAA,SAAAsD,GACAmX,EAAAsG,GAAAzd,IAAA,ItEijDM,SAAUrD,EAAQD,EAASM,GAEjC,YuEvjDA,IAAA0gB,GAAA1gB,EAAA,GAGAL,GAAAD,QAAAM,EAAA,mBAAAgB,GACA,kBAAwB,MAAAA,GAAAlB,KAAAkG,UAAAH,OAAA,EAAAG,UAAA,OAAAX,OAGxBsX,IAAA,SAAA9Z,GACA,MAAA6d,GAAA5X,IAAAhJ,KAAA+C,EAAA,IAAAA,EAAA,EAAAA,OAEC6d,IvE6jDK,SAAU/gB,EAAQD,EAASM,GAEjC,YwEzkDA,IAAAsC,GAAAtC,EAAA,GAAAuC,EACAsX,EAAA7Z,EAAA,IACA2gB,EAAA3gB,EAAA,IACAmE,EAAAnE,EAAA,IACA4gB,EAAA5gB,EAAA,IACAyG,EAAAzG,EAAA,IACA6gB,EAAA7gB,EAAA,IACA8gB,EAAA9gB,EAAA,IACA4J,EAAA5J,EAAA,IACA+gB,EAAA/gB,EAAA,IACAghB,EAAAhhB,EAAA,GACAqb,EAAArb,EAAA,IAAAqb,QACA4F,EAAAD,EAAA,YAEAE,EAAA,SAAAtb,EAAA5C,GAEA,GAAAme,GAAAnX,EAAAqR,EAAArY,EACA,UAAAgH,EAAA,MAAApE,GAAA2S,GAAAvO,EAEA,KAAAmX,EAAAvb,EAAAwb,GAAsBD,EAAOA,IAAAlgB,EAC7B,GAAAkgB,EAAAzB,GAAA1c,EAAA,MAAAme,GAIAxhB,GAAAD,SACA2hB,eAAA,SAAAC,EAAA/Z,EAAAga,EAAAC,GACA,GAAAxC,GAAAsC,EAAA,SAAA1b,EAAA+D,GACAiX,EAAAhb,EAAAoZ,EAAAzX,EAAA,MACA3B,EAAA2S,GAAAsB,EAAA,MACAjU,EAAAwb,OAAA/b,GACAO,EAAA6b,OAAApc,GACAO,EAAAqb,GAAA,MACA5b,IAAAsE,GAAAkX,EAAAlX,EAAA4X,EAAA3b,EAAA4b,GAAA5b,IAsDA,OApDA+a,GAAA3B,EAAA3d,WAGAqgB,MAAA,WACA,OAAA9b,GAAA9F,KAAA6hB,EAAA/b,EAAA2S,GAAA4I,EAAAvb,EAAAwb,GAA6DD,EAAOA,IAAAlgB,EACpEkgB,EAAAS,GAAA,EACAT,EAAA5f,IAAA4f,EAAA5f,EAAA4f,EAAA5f,EAAAN,MAAAoE,UACAsc,GAAAR,EAAAhhB,EAEAyF,GAAAwb,GAAAxb,EAAA6b,OAAApc,GACAO,EAAAqb,GAAA,GAIAvS,OAAA,SAAA1L,GACA,GAAA4C,GAAA9F,KACAqhB,EAAAD,EAAAtb,EAAA5C,EACA,IAAAme,EAAA,CACA,GAAA1Z,GAAA0Z,EAAAlgB,EACA4gB,EAAAV,EAAA5f,QACAqE,GAAA2S,GAAA4I,EAAAhhB,GACAghB,EAAAS,GAAA,EACAC,MAAA5gB,EAAAwG,GACAA,MAAAlG,EAAAsgB,GACAjc,EAAAwb,IAAAD,IAAAvb,EAAAwb,GAAA3Z,GACA7B,EAAA6b,IAAAN,IAAAvb,EAAA6b,GAAAI,GACAjc,EAAAqb,KACS,QAAAE,GAITtE,QAAA,SAAAiF,GACAlB,EAAA9gB,KAAAkf,EAAA,UAGA,KAFA,GACAmC,GADA5e,EAAA4B,EAAA2d,EAAA9b,UAAAH,OAAA,EAAAG,UAAA,OAAAX,GAAA,GAEA8b,MAAAlgB,EAAAnB,KAAAshB,IAGA,IAFA7e,EAAA4e,EAAAY,EAAAZ,EAAAzB,EAAA5f,MAEAqhB,KAAAS,GAAAT,IAAA5f,GAKAgC,IAAA,SAAAP,GACA,QAAAke,EAAAphB,KAAAkD,MAGAge,GAAA1e,EAAA0c,EAAA3d,UAAA,QACAL,IAAA,WACA,MAAAyF,GAAA3G,KAAAmhB,OAGAjC,GAEAlW,IAAA,SAAAlD,EAAA5C,EAAAH,GACA,GACAgf,GAAA7X,EADAmX,EAAAD,EAAAtb,EAAA5C,EAoBK,OAjBLme,GACAA,EAAAY,EAAAlf,GAGA+C,EAAA6b,GAAAN,GACAhhB,EAAA6J,EAAAqR,EAAArY,GAAA,GACA0c,EAAA1c,EACA+e,EAAAlf,EACAtB,EAAAsgB,EAAAjc,EAAA6b,GACAxgB,MAAAoE,GACAuc,GAAA,GAEAhc,EAAAwb,KAAAxb,EAAAwb,GAAAD,GACAU,MAAA5gB,EAAAkgB,GACAvb,EAAAqb,KAEA,MAAAjX,IAAApE,EAAA2S,GAAAvO,GAAAmX,IACKvb,GAELsb,WACAc,UAAA,SAAAhD,EAAAzX,EAAAga,GAGAT,EAAA9B,EAAAzX,EAAA,SAAA8Q,EAAArQ,GACAlI,KAAAwY,GAAAD,EACAvY,KAAAygB,GAAAvY,EACAlI,KAAA2hB,OAAApc,IACK,WAKL,IAJA,GAAAO,GAAA9F,KACAkI,EAAApC,EAAA2a,GACAY,EAAAvb,EAAA6b,GAEAN,KAAAS,GAAAT,IAAA5f,CAEA,OAAAqE,GAAA0S,KAAA1S,EAAA6b,GAAAN,MAAAlgB,EAAA2E,EAAA0S,GAAA8I,IAMA,QAAApZ,EAAA4B,EAAA,EAAAuX,EAAAzB,GACA,UAAA1X,EAAA4B,EAAA,EAAAuX,EAAAY,GACAnY,EAAA,GAAAuX,EAAAzB,EAAAyB,EAAAY,KANAnc,EAAA0S,OAAAjT,GACAuE,EAAA,KAMK2X,EAAA,oBAAAA,GAAA,GAGLR,EAAAxZ,MxEilDM,SAAU5H,EAAQD,EAASM,GAEjC,YyE7tDA,IAAA6B,GAAA7B,EAAA,GACAsC,EAAAtC,EAAA,GACAghB,EAAAhhB,EAAA,GACAiiB,EAAAjiB,EAAA,aAEAL,GAAAD,QAAA,SAAAgc,GACA,GAAAsD,GAAAnd,EAAA6Z,EACAsF,IAAAhC,MAAAiD,IAAA3f,EAAAC,EAAAyc,EAAAiD,GACAnhB,cAAA,EACAE,IAAA,WAAoB,MAAAlB,WzEquDd,SAAUH,EAAQD,EAASM,GAEjC,Y0EhvDA,IAAA6B,GAAA7B,EAAA,GACAoE,EAAApE,EAAA,IACAkE,EAAAlE,EAAA,GACA2gB,EAAA3gB,EAAA,IACAwb,EAAAxb,EAAA,IACA6gB,EAAA7gB,EAAA,IACA4gB,EAAA5gB,EAAA,IACAqD,EAAArD,EAAA,GACAkiB,EAAAliB,EAAA,IACAmiB,EAAAniB,EAAA,IACAgH,EAAAhH,EAAA,IACAoiB,EAAApiB,EAAA,GAEAL,GAAAD,QAAA,SAAA6H,EAAA+Z,EAAAzZ,EAAAwa,EAAAd,EAAAe,GACA,GAAAhb,GAAAzF,EAAA0F,GACAyX,EAAA1X,EACAka,EAAAD,EAAA,YACAtZ,EAAA+W,KAAA3d,UACAmB,KACA+f,EAAA,SAAA7G,GACA,GAAA/V,GAAAsC,EAAAyT,EACAxX,GAAA+D,EAAAyT,EACA,UAAAA,EAAA,SAAA3Y,GACA,QAAAuf,IAAAjf,EAAAN,KAAA4C,EAAAtF,KAAAP,KAAA,IAAAiD,EAAA,EAAAA,IACO,OAAA2Y,EAAA,SAAA3Y,GACP,QAAAuf,IAAAjf,EAAAN,KAAA4C,EAAAtF,KAAAP,KAAA,IAAAiD,EAAA,EAAAA,IACO,OAAA2Y,EAAA,SAAA3Y,GACP,MAAAuf,KAAAjf,EAAAN,OAAAsC,GAAAM,EAAAtF,KAAAP,KAAA,IAAAiD,EAAA,EAAAA,IACO,OAAA2Y,EAAA,SAAA3Y,GAAkE,MAAhC4C,GAAAtF,KAAAP,KAAA,IAAAiD,EAAA,EAAAA,GAAgCjD,MACzE,SAAAiD,EAAA+C,GAAgE,MAAnCH,GAAAtF,KAAAP,KAAA,IAAAiD,EAAA,EAAAA,EAAA+C,GAAmChG,OAGhE,sBAAAkf,KAAAsD,GAAAra,EAAA4U,UAAAqF,EAAA,YACA,GAAAlD,IAAAvW,UAAAhB,UAMG,CACH,GAAA+a,GAAA,GAAAxD,GAEAyD,EAAAD,EAAAhB,GAAAc,MAA2D,MAAAE,EAE3DE,EAAAR,EAAA,WAAgDM,EAAAjf,IAAA,KAEhDof,EAAAR,EAAA,SAAAvH,GAA0D,GAAAoE,GAAApE,KAE1DgI,GAAAN,GAAAJ,EAAA,WAIA,IAFA,GAAAW,GAAA,GAAA7D,GACAhV,EAAA,EACAA,KAAA6Y,EAAArB,GAAAxX,IACA,QAAA6Y,EAAAtf,KAAA,IAEAof,KACA3D,EAAAsC,EAAA,SAAAnc,EAAAwE,GACAiX,EAAAzb,EAAA6Z,EAAAzX,EACA,IAAA3B,GAAAwc,EAAA,GAAA9a,GAAAnC,EAAA6Z,EAEA,YADA3Z,IAAAsE,GAAAkX,EAAAlX,EAAA4X,EAAA3b,EAAA4b,GAAA5b,GACAA,IAEAoZ,EAAA3d,UAAA4G,EACAA,EAAA4W,YAAAG,IAEA0D,GAAAE,KACAL,EAAA,UACAA,EAAA,OACAhB,GAAAgB,EAAA,SAEAK,GAAAH,IAAAF,EAAAf,GAEAc,GAAAra,EAAAyZ,aAAAzZ,GAAAyZ,UApCA1C,GAAAqD,EAAAhB,eAAAC,EAAA/Z,EAAAga,EAAAC,GACAb,EAAA3B,EAAA3d,UAAAwG,GACA2T,EAAAC,MAAA,CA4CA,OAPAzU,GAAAgY,EAAAzX,GAEA/E,EAAA+E,GAAAyX,EACA5a,IAAAS,EAAAT,EAAAmB,EAAAnB,EAAAO,GAAAqa,GAAA1X,GAAA9E,GAEA8f,GAAAD,EAAAL,UAAAhD,EAAAzX,EAAAga,GAEAvC,I1EuvDM,SAAUrf,EAAQD,EAASM,G2E10DjC,GAAAqD,GAAArD,EAAA,GACA8iB,EAAA9iB,EAAA,IAAA+iB,GACApjB,GAAAD,QAAA,SAAAkG,EAAAT,EAAA6Z,GACA,GAAAvc,GAAAsC,EAAAI,EAAA0Z,WAGG,OAFH9Z,KAAAia,GAAA,kBAAAja,KAAAtC,EAAAsC,EAAA1D,aAAA2d,EAAA3d,WAAAgC,EAAAZ,IAAAqgB,GACAA,EAAAld,EAAAnD,GACGmD,I3Ei1DG,SAAUjG,EAAQD,EAASM,G4Er1DjC,GAAAqD,GAAArD,EAAA,GACAmC,EAAAnC,EAAA,GACAwM,EAAA,SAAAhK,EAAAyF,GAEA,GADA9F,EAAAK,IACAa,EAAA4E,IAAA,OAAAA,EAAA,KAAArF,WAAAqF,EAAA,6BAEAtI,GAAAD,SACAqjB,IAAAniB,OAAAkiB,iBAAA,gBACA,SAAA/C,EAAAiD,EAAAD,GACA,IACAA,EAAA/iB,EAAA,IAAAiC,SAAA5B,KAAAL,EAAA,IAAAuC,EAAA3B,OAAAS,UAAA,aAAA0hB,IAAA,GACAA,EAAAhD,MACAiD,IAAAjD,YAAA3F,QACO,MAAAzX,GAAUqgB,GAAA,EACjB,gBAAAxgB,EAAAyF,GAIA,MAHAuE,GAAAhK,EAAAyF,GACA+a,EAAAxgB,EAAAygB,UAAAhb,EACA8a,EAAAvgB,EAAAyF,GACAzF,QAEQ,OAAA6C,IACRmH,U5E81DM,SAAU7M,EAAQD,EAASM,G6Er3DjC,GAAAsf,GAAAtf,EAAA,IACAoD,EAAApD,EAAA,IACAie,EAAAje,EAAA,IACAqC,EAAArC,EAAA,IACAuD,EAAAvD,EAAA,GACAoC,EAAApC,EAAA,IACAkjB,EAAAtiB,OAAAuiB,wBAEAzjB,GAAA6C,EAAAvC,EAAA,GAAAkjB,EAAA,SAAA1gB,EAAAC,GAGA,GAFAD,EAAAyb,EAAAzb,GACAC,EAAAJ,EAAAI,GAAA,GACAL,EAAA,IACA,MAAA8gB,GAAA1gB,EAAAC,GACG,MAAAE,IACH,GAAAY,EAAAf,EAAAC,GAAA,MAAAW,IAAAkc,EAAA/c,EAAAlC,KAAAmC,EAAAC,GAAAD,EAAAC,M7E43DM,SAAU9C,EAAQD,EAASM,G8Ez4DjC,GAAAoE,GAAApE,EAAA,GAEAoE,KAAA3B,EAAA2B,EAAAoB,EAAA,OAAuC4d,OAAApjB,EAAA,c9Eg5DjC,SAAUL,EAAQD,EAASM,G+El5DjC,GAAAqa,GAAAra,EAAA,IACAya,EAAAza,EAAA,GACAL,GAAAD,QAAA,SAAA6H,GACA,kBACA,GAAA8S,EAAAva,OAAAyH,EAAA,KAAA3E,WAAA2E,EAAA,wBACA,OAAAkT,GAAA3a,S/E25DM,SAAUH,EAAQD,EAASM,GgFj6DjC,GAAA6gB,GAAA7gB,EAAA,GAEAL,GAAAD,QAAA,SAAAkb,EAAA1T,GACA,GAAA4C,KAEA,OADA+W,GAAAjG,GAAA,EAAA9Q,EAAAgT,KAAAhT,EAAA5C,GACA4C,IhFy6DM,SAAUnK,EAAQD,EAASM,GAEjC,YAeA,SAASqjB,GAAuBrG,GAAO,MAAOA,IAAOA,EAAI9b,WAAa8b,GAAQjB,QAASiB,GiF37DvF,GAAAD,GAAA/c,EAAA,IjFi7DI8b,EAAUuH,EAAuBtG,GiFh7DrCuG,EAAAtjB,EAAA,IjFo7DIujB,EAAUF,EAAuBC,GiFn7DrCE,EAAAxjB,EAAA,IjFu7DIyjB,EAAYJ,EAAuBG,EiFr7DvC7jB,GAAOD,SAAYgkB,gBAAO/H,gBAAOgI,oBjF+7D3B,SAAUhkB,EAAQD,EAASM,GAEjC,YAgBA,SAASqjB,GAAuBrG,GAAO,MAAOA,IAAOA,EAAI9b,WAAa8b,GAAQjB,QAASiB,GkF38DxE,QAAS2G,KAAsB,GAAd/H,GAAc5V,UAAAH,OAAA,OAAAR,KAAAW,UAAA,GAAAA,UAAA,KAC5C,IAAwB,mBAAbyS,UACT,KAAM,IAAIoD,OAAM,2DAGlB,IAAM+H,GAAoBnL,SAASoL,iBAAiB,iBAEpDzJ,OAAMK,KAAKmJ,GAAmB/G,QAAQ,SAAAiH,GAAA,MAAWC,GAAeD,EAASlI,KAS3E,QAASmI,GAAeD,EAASlI,GAC/B,GAAM5Y,GAAM8gB,EAAQE,aAAa,eAEjC,KAAKhhB,EAEH,WADAihB,SAAQC,MAAM,sDAIhB,KAAKpI,EAAAC,QAAM/Y,GAET,WADAihB,SAAQC,MAAR,qBAAmClhB,EAAnC,gEAIF,IAAMmhB,GAAmBL,EAAQE,aAAa,SACxC1H,EACJV,EAAQO,MAAWP,EAAQO,MAA3B,IAAoCgI,EAAqBA,EAGrDC,GAAY,EAAAb,EAAAxH,SAAM/Y,EAAKpC,OAAOqb,UAAWL,GAAWO,MAAOG,KAC3D+H,GAAc,GAAIC,YAAYC,gBAAgBH,EAAW,iBACzDI,EAAaH,EAAYI,cAAc,MAE7CX,GAAQY,WAAWC,aAAaH,EAAYV,GlFw5D9CljB,OAAOC,eAAenB,EAAS,cAC7BmD,OAAO,IAETnD,EAAQqc,QkFj8DgB4H,CARxB,IAAA5G,GAAA/c,EAAA,IlF68DI8b,EAAUuH,EAAuBtG,GkF58DrCuG,EAAAtjB,EAAA,IlFg9DIujB,EAAUF,EAAuBC", "file": "feather.min.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"feather\"] = factory();\n\telse\n\t\troot[\"feather\"] = factory();\n})(this, function() {\nreturn \n\n\n// WEBPACK FOOTER //\n// webpack/universalModuleDefinition", "(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"feather\"] = factory();\n\telse\n\t\troot[\"feather\"] = factory();\n})(this, function() {\nreturn /******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n/******/\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId]) {\n/******/ \t\t\treturn installedModules[moduleId].exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\ti: moduleId,\n/******/ \t\t\tl: false,\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.l = true;\n/******/\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/\n/******/\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__webpack_require__.m = modules;\n/******/\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = installedModules;\n/******/\n/******/ \t// define getter function for harmony exports\n/******/ \t__webpack_require__.d = function(exports, name, getter) {\n/******/ \t\tif(!__webpack_require__.o(exports, name)) {\n/******/ \t\t\tObject.defineProperty(exports, name, {\n/******/ \t\t\t\tconfigurable: false,\n/******/ \t\t\t\tenumerable: true,\n/******/ \t\t\t\tget: getter\n/******/ \t\t\t});\n/******/ \t\t}\n/******/ \t};\n/******/\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = function(module) {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\tfunction getDefault() { return module['default']; } :\n/******/ \t\t\tfunction getModuleExports() { return module; };\n/******/ \t\t__webpack_require__.d(getter, 'a', getter);\n/******/ \t\treturn getter;\n/******/ \t};\n/******/\n/******/ \t// Object.prototype.hasOwnProperty.call\n/******/ \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n/******/\n/******/ \t// __webpack_public_path__\n/******/ \t__webpack_require__.p = \"\";\n/******/\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(__webpack_require__.s = 47);\n/******/ })\n/************************************************************************/\n/******/ ([\n/* 0 */\n/***/ (function(module, exports, __webpack_require__) {\n\nvar store      = __webpack_require__(35)('wks')\n  , uid        = __webpack_require__(16)\n  , Symbol     = __webpack_require__(1).Symbol\n  , USE_SYMBOL = typeof Symbol == 'function';\n\nvar $exports = module.exports = function(name){\n  return store[name] || (store[name] =\n    USE_SYMBOL && Symbol[name] || (USE_SYMBOL ? Symbol : uid)('Symbol.' + name));\n};\n\n$exports.store = store;\n\n/***/ }),\n/* 1 */\n/***/ (function(module, exports) {\n\n// https://github.com/zloirock/core-js/issues/86#issuecomment-115759028\nvar global = module.exports = typeof window != 'undefined' && window.Math == Math\n  ? window : typeof self != 'undefined' && self.Math == Math ? self : Function('return this')();\nif(typeof __g == 'number')__g = global; // eslint-disable-line no-undef\n\n/***/ }),\n/* 2 */\n/***/ (function(module, exports, __webpack_require__) {\n\nvar anObject       = __webpack_require__(8)\n  , IE8_DOM_DEFINE = __webpack_require__(28)\n  , toPrimitive    = __webpack_require__(30)\n  , dP             = Object.defineProperty;\n\nexports.f = __webpack_require__(4) ? Object.defineProperty : function defineProperty(O, P, Attributes){\n  anObject(O);\n  P = toPrimitive(P, true);\n  anObject(Attributes);\n  if(IE8_DOM_DEFINE)try {\n    return dP(O, P, Attributes);\n  } catch(e){ /* empty */ }\n  if('get' in Attributes || 'set' in Attributes)throw TypeError('Accessors not supported!');\n  if('value' in Attributes)O[P] = Attributes.value;\n  return O;\n};\n\n/***/ }),\n/* 3 */\n/***/ (function(module, exports) {\n\nmodule.exports = function(it){\n  return typeof it === 'object' ? it !== null : typeof it === 'function';\n};\n\n/***/ }),\n/* 4 */\n/***/ (function(module, exports, __webpack_require__) {\n\n// Thank's IE8 for his funny defineProperty\nmodule.exports = !__webpack_require__(11)(function(){\n  return Object.defineProperty({}, 'a', {get: function(){ return 7; }}).a != 7;\n});\n\n/***/ }),\n/* 5 */\n/***/ (function(module, exports) {\n\nvar hasOwnProperty = {}.hasOwnProperty;\nmodule.exports = function(it, key){\n  return hasOwnProperty.call(it, key);\n};\n\n/***/ }),\n/* 6 */\n/***/ (function(module, exports) {\n\nvar core = module.exports = {version: '2.4.0'};\nif(typeof __e == 'number')__e = core; // eslint-disable-line no-undef\n\n/***/ }),\n/* 7 */\n/***/ (function(module, exports, __webpack_require__) {\n\nvar dP         = __webpack_require__(2)\n  , createDesc = __webpack_require__(15);\nmodule.exports = __webpack_require__(4) ? function(object, key, value){\n  return dP.f(object, key, createDesc(1, value));\n} : function(object, key, value){\n  object[key] = value;\n  return object;\n};\n\n/***/ }),\n/* 8 */\n/***/ (function(module, exports, __webpack_require__) {\n\nvar isObject = __webpack_require__(3);\nmodule.exports = function(it){\n  if(!isObject(it))throw TypeError(it + ' is not an object!');\n  return it;\n};\n\n/***/ }),\n/* 9 */\n/***/ (function(module, exports, __webpack_require__) {\n\nvar global    = __webpack_require__(1)\n  , hide      = __webpack_require__(7)\n  , has       = __webpack_require__(5)\n  , SRC       = __webpack_require__(16)('src')\n  , TO_STRING = 'toString'\n  , $toString = Function[TO_STRING]\n  , TPL       = ('' + $toString).split(TO_STRING);\n\n__webpack_require__(6).inspectSource = function(it){\n  return $toString.call(it);\n};\n\n(module.exports = function(O, key, val, safe){\n  var isFunction = typeof val == 'function';\n  if(isFunction)has(val, 'name') || hide(val, 'name', key);\n  if(O[key] === val)return;\n  if(isFunction)has(val, SRC) || hide(val, SRC, O[key] ? '' + O[key] : TPL.join(String(key)));\n  if(O === global){\n    O[key] = val;\n  } else {\n    if(!safe){\n      delete O[key];\n      hide(O, key, val);\n    } else {\n      if(O[key])O[key] = val;\n      else hide(O, key, val);\n    }\n  }\n// add fake Function#toString for correct work wrapped methods / constructors with methods like LoDash isNative\n})(Function.prototype, TO_STRING, function toString(){\n  return typeof this == 'function' && this[SRC] || $toString.call(this);\n});\n\n/***/ }),\n/* 10 */\n/***/ (function(module, exports, __webpack_require__) {\n\nvar global    = __webpack_require__(1)\n  , core      = __webpack_require__(6)\n  , hide      = __webpack_require__(7)\n  , redefine  = __webpack_require__(9)\n  , ctx       = __webpack_require__(12)\n  , PROTOTYPE = 'prototype';\n\nvar $export = function(type, name, source){\n  var IS_FORCED = type & $export.F\n    , IS_GLOBAL = type & $export.G\n    , IS_STATIC = type & $export.S\n    , IS_PROTO  = type & $export.P\n    , IS_BIND   = type & $export.B\n    , target    = IS_GLOBAL ? global : IS_STATIC ? global[name] || (global[name] = {}) : (global[name] || {})[PROTOTYPE]\n    , exports   = IS_GLOBAL ? core : core[name] || (core[name] = {})\n    , expProto  = exports[PROTOTYPE] || (exports[PROTOTYPE] = {})\n    , key, own, out, exp;\n  if(IS_GLOBAL)source = name;\n  for(key in source){\n    // contains in native\n    own = !IS_FORCED && target && target[key] !== undefined;\n    // export native or passed\n    out = (own ? target : source)[key];\n    // bind timers to global for call from export context\n    exp = IS_BIND && own ? ctx(out, global) : IS_PROTO && typeof out == 'function' ? ctx(Function.call, out) : out;\n    // extend global\n    if(target)redefine(target, key, out, type & $export.U);\n    // export\n    if(exports[key] != out)hide(exports, key, exp);\n    if(IS_PROTO && expProto[key] != out)expProto[key] = out;\n  }\n};\nglobal.core = core;\n// type bitmap\n$export.F = 1;   // forced\n$export.G = 2;   // global\n$export.S = 4;   // static\n$export.P = 8;   // proto\n$export.B = 16;  // bind\n$export.W = 32;  // wrap\n$export.U = 64;  // safe\n$export.R = 128; // real proto method for `library` \nmodule.exports = $export;\n\n/***/ }),\n/* 11 */\n/***/ (function(module, exports) {\n\nmodule.exports = function(exec){\n  try {\n    return !!exec();\n  } catch(e){\n    return true;\n  }\n};\n\n/***/ }),\n/* 12 */\n/***/ (function(module, exports, __webpack_require__) {\n\n// optional / simple context binding\nvar aFunction = __webpack_require__(51);\nmodule.exports = function(fn, that, length){\n  aFunction(fn);\n  if(that === undefined)return fn;\n  switch(length){\n    case 1: return function(a){\n      return fn.call(that, a);\n    };\n    case 2: return function(a, b){\n      return fn.call(that, a, b);\n    };\n    case 3: return function(a, b, c){\n      return fn.call(that, a, b, c);\n    };\n  }\n  return function(/* ...args */){\n    return fn.apply(that, arguments);\n  };\n};\n\n/***/ }),\n/* 13 */\n/***/ (function(module, exports) {\n\nmodule.exports = {};\n\n/***/ }),\n/* 14 */\n/***/ (function(module, exports) {\n\n// 7.2.1 RequireObjectCoercible(argument)\nmodule.exports = function(it){\n  if(it == undefined)throw TypeError(\"Can't call method on  \" + it);\n  return it;\n};\n\n/***/ }),\n/* 15 */\n/***/ (function(module, exports) {\n\nmodule.exports = function(bitmap, value){\n  return {\n    enumerable  : !(bitmap & 1),\n    configurable: !(bitmap & 2),\n    writable    : !(bitmap & 4),\n    value       : value\n  };\n};\n\n/***/ }),\n/* 16 */\n/***/ (function(module, exports) {\n\nvar id = 0\n  , px = Math.random();\nmodule.exports = function(key){\n  return 'Symbol('.concat(key === undefined ? '' : key, ')_', (++id + px).toString(36));\n};\n\n/***/ }),\n/* 17 */\n/***/ (function(module, exports, __webpack_require__) {\n\n// to indexed object, toObject with fallback for non-array-like ES3 strings\nvar IObject = __webpack_require__(33)\n  , defined = __webpack_require__(14);\nmodule.exports = function(it){\n  return IObject(defined(it));\n};\n\n/***/ }),\n/* 18 */\n/***/ (function(module, exports) {\n\n// 7.1.4 ToInteger\nvar ceil  = Math.ceil\n  , floor = Math.floor;\nmodule.exports = function(it){\n  return isNaN(it = +it) ? 0 : (it > 0 ? floor : ceil)(it);\n};\n\n/***/ }),\n/* 19 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\nvar LIBRARY        = __webpack_require__(50)\n  , $export        = __webpack_require__(10)\n  , redefine       = __webpack_require__(9)\n  , hide           = __webpack_require__(7)\n  , has            = __webpack_require__(5)\n  , Iterators      = __webpack_require__(13)\n  , $iterCreate    = __webpack_require__(52)\n  , setToStringTag = __webpack_require__(22)\n  , getPrototypeOf = __webpack_require__(58)\n  , ITERATOR       = __webpack_require__(0)('iterator')\n  , BUGGY          = !([].keys && 'next' in [].keys()) // Safari has buggy iterators w/o `next`\n  , FF_ITERATOR    = '@@iterator'\n  , KEYS           = 'keys'\n  , VALUES         = 'values';\n\nvar returnThis = function(){ return this; };\n\nmodule.exports = function(Base, NAME, Constructor, next, DEFAULT, IS_SET, FORCED){\n  $iterCreate(Constructor, NAME, next);\n  var getMethod = function(kind){\n    if(!BUGGY && kind in proto)return proto[kind];\n    switch(kind){\n      case KEYS: return function keys(){ return new Constructor(this, kind); };\n      case VALUES: return function values(){ return new Constructor(this, kind); };\n    } return function entries(){ return new Constructor(this, kind); };\n  };\n  var TAG        = NAME + ' Iterator'\n    , DEF_VALUES = DEFAULT == VALUES\n    , VALUES_BUG = false\n    , proto      = Base.prototype\n    , $native    = proto[ITERATOR] || proto[FF_ITERATOR] || DEFAULT && proto[DEFAULT]\n    , $default   = $native || getMethod(DEFAULT)\n    , $entries   = DEFAULT ? !DEF_VALUES ? $default : getMethod('entries') : undefined\n    , $anyNative = NAME == 'Array' ? proto.entries || $native : $native\n    , methods, key, IteratorPrototype;\n  // Fix native\n  if($anyNative){\n    IteratorPrototype = getPrototypeOf($anyNative.call(new Base));\n    if(IteratorPrototype !== Object.prototype){\n      // Set @@toStringTag to native iterators\n      setToStringTag(IteratorPrototype, TAG, true);\n      // fix for some old engines\n      if(!LIBRARY && !has(IteratorPrototype, ITERATOR))hide(IteratorPrototype, ITERATOR, returnThis);\n    }\n  }\n  // fix Array#{values, @@iterator}.name in V8 / FF\n  if(DEF_VALUES && $native && $native.name !== VALUES){\n    VALUES_BUG = true;\n    $default = function values(){ return $native.call(this); };\n  }\n  // Define iterator\n  if((!LIBRARY || FORCED) && (BUGGY || VALUES_BUG || !proto[ITERATOR])){\n    hide(proto, ITERATOR, $default);\n  }\n  // Plug for library\n  Iterators[NAME] = $default;\n  Iterators[TAG]  = returnThis;\n  if(DEFAULT){\n    methods = {\n      values:  DEF_VALUES ? $default : getMethod(VALUES),\n      keys:    IS_SET     ? $default : getMethod(KEYS),\n      entries: $entries\n    };\n    if(FORCED)for(key in methods){\n      if(!(key in proto))redefine(proto, key, methods[key]);\n    } else $export($export.P + $export.F * (BUGGY || VALUES_BUG), NAME, methods);\n  }\n  return methods;\n};\n\n/***/ }),\n/* 20 */\n/***/ (function(module, exports, __webpack_require__) {\n\n// 7.1.15 ToLength\nvar toInteger = __webpack_require__(18)\n  , min       = Math.min;\nmodule.exports = function(it){\n  return it > 0 ? min(toInteger(it), 0x1fffffffffffff) : 0; // pow(2, 53) - 1 == 9007199254740991\n};\n\n/***/ }),\n/* 21 */\n/***/ (function(module, exports, __webpack_require__) {\n\nvar shared = __webpack_require__(35)('keys')\n  , uid    = __webpack_require__(16);\nmodule.exports = function(key){\n  return shared[key] || (shared[key] = uid(key));\n};\n\n/***/ }),\n/* 22 */\n/***/ (function(module, exports, __webpack_require__) {\n\nvar def = __webpack_require__(2).f\n  , has = __webpack_require__(5)\n  , TAG = __webpack_require__(0)('toStringTag');\n\nmodule.exports = function(it, tag, stat){\n  if(it && !has(it = stat ? it : it.prototype, TAG))def(it, TAG, {configurable: true, value: tag});\n};\n\n/***/ }),\n/* 23 */\n/***/ (function(module, exports, __webpack_require__) {\n\n// 7.1.13 ToObject(argument)\nvar defined = __webpack_require__(14);\nmodule.exports = function(it){\n  return Object(defined(it));\n};\n\n/***/ }),\n/* 24 */\n/***/ (function(module, exports, __webpack_require__) {\n\n// getting tag from ******** Object.prototype.toString()\nvar cof = __webpack_require__(34)\n  , TAG = __webpack_require__(0)('toStringTag')\n  // ES3 wrong here\n  , ARG = cof(function(){ return arguments; }()) == 'Arguments';\n\n// fallback for IE11 Script Access Denied error\nvar tryGet = function(it, key){\n  try {\n    return it[key];\n  } catch(e){ /* empty */ }\n};\n\nmodule.exports = function(it){\n  var O, T, B;\n  return it === undefined ? 'Undefined' : it === null ? 'Null'\n    // @@toStringTag case\n    : typeof (T = tryGet(O = Object(it), TAG)) == 'string' ? T\n    // builtinTag case\n    : ARG ? cof(O)\n    // ES3 arguments fallback\n    : (B = cof(O)) == 'Object' && typeof O.callee == 'function' ? 'Arguments' : B;\n};\n\n/***/ }),\n/* 25 */\n/***/ (function(module, exports, __webpack_require__) {\n\nvar ctx         = __webpack_require__(12)\n  , call        = __webpack_require__(37)\n  , isArrayIter = __webpack_require__(38)\n  , anObject    = __webpack_require__(8)\n  , toLength    = __webpack_require__(20)\n  , getIterFn   = __webpack_require__(39)\n  , BREAK       = {}\n  , RETURN      = {};\nvar exports = module.exports = function(iterable, entries, fn, that, ITERATOR){\n  var iterFn = ITERATOR ? function(){ return iterable; } : getIterFn(iterable)\n    , f      = ctx(fn, that, entries ? 2 : 1)\n    , index  = 0\n    , length, step, iterator, result;\n  if(typeof iterFn != 'function')throw TypeError(iterable + ' is not iterable!');\n  // fast case for arrays with default iterator\n  if(isArrayIter(iterFn))for(length = toLength(iterable.length); length > index; index++){\n    result = entries ? f(anObject(step = iterable[index])[0], step[1]) : f(iterable[index]);\n    if(result === BREAK || result === RETURN)return result;\n  } else for(iterator = iterFn.call(iterable); !(step = iterator.next()).done; ){\n    result = call(iterator, f, step.value, entries);\n    if(result === BREAK || result === RETURN)return result;\n  }\n};\nexports.BREAK  = BREAK;\nexports.RETURN = RETURN;\n\n/***/ }),\n/* 26 */\n/***/ (function(module, exports) {\n\nmodule.exports = {\n\t\"activity\": \"<polyline points=\\\"22 12 18 12 15 21 9 3 6 12 2 12\\\"></polyline>\",\n\t\"airplay\": \"<path d=\\\"M5 17H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-1\\\"></path><polygon points=\\\"12 15 17 21 7 21 12 15\\\"></polygon>\",\n\t\"alert-circle\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><line x1=\\\"12\\\" y1=\\\"8\\\" x2=\\\"12\\\" y2=\\\"12\\\"></line><line x1=\\\"12\\\" y1=\\\"16\\\" x2=\\\"12\\\" y2=\\\"16\\\"></line>\",\n\t\"alert-octagon\": \"<polygon points=\\\"7.86 2 16.14 2 22 7.86 22 16.14 16.14 22 7.86 22 2 16.14 2 7.86 7.86 2\\\"></polygon><line x1=\\\"12\\\" y1=\\\"8\\\" x2=\\\"12\\\" y2=\\\"12\\\"></line><line x1=\\\"12\\\" y1=\\\"16\\\" x2=\\\"12\\\" y2=\\\"16\\\"></line>\",\n\t\"alert-triangle\": \"<path d=\\\"M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z\\\"></path><line x1=\\\"12\\\" y1=\\\"9\\\" x2=\\\"12\\\" y2=\\\"13\\\"></line><line x1=\\\"12\\\" y1=\\\"17\\\" x2=\\\"12\\\" y2=\\\"17\\\"></line>\",\n\t\"align-center\": \"<line x1=\\\"18\\\" y1=\\\"10\\\" x2=\\\"6\\\" y2=\\\"10\\\"></line><line x1=\\\"21\\\" y1=\\\"6\\\" x2=\\\"3\\\" y2=\\\"6\\\"></line><line x1=\\\"21\\\" y1=\\\"14\\\" x2=\\\"3\\\" y2=\\\"14\\\"></line><line x1=\\\"18\\\" y1=\\\"18\\\" x2=\\\"6\\\" y2=\\\"18\\\"></line>\",\n\t\"align-justify\": \"<line x1=\\\"21\\\" y1=\\\"10\\\" x2=\\\"3\\\" y2=\\\"10\\\"></line><line x1=\\\"21\\\" y1=\\\"6\\\" x2=\\\"3\\\" y2=\\\"6\\\"></line><line x1=\\\"21\\\" y1=\\\"14\\\" x2=\\\"3\\\" y2=\\\"14\\\"></line><line x1=\\\"21\\\" y1=\\\"18\\\" x2=\\\"3\\\" y2=\\\"18\\\"></line>\",\n\t\"align-left\": \"<line x1=\\\"17\\\" y1=\\\"10\\\" x2=\\\"3\\\" y2=\\\"10\\\"></line><line x1=\\\"21\\\" y1=\\\"6\\\" x2=\\\"3\\\" y2=\\\"6\\\"></line><line x1=\\\"21\\\" y1=\\\"14\\\" x2=\\\"3\\\" y2=\\\"14\\\"></line><line x1=\\\"17\\\" y1=\\\"18\\\" x2=\\\"3\\\" y2=\\\"18\\\"></line>\",\n\t\"align-right\": \"<line x1=\\\"21\\\" y1=\\\"10\\\" x2=\\\"7\\\" y2=\\\"10\\\"></line><line x1=\\\"21\\\" y1=\\\"6\\\" x2=\\\"3\\\" y2=\\\"6\\\"></line><line x1=\\\"21\\\" y1=\\\"14\\\" x2=\\\"3\\\" y2=\\\"14\\\"></line><line x1=\\\"21\\\" y1=\\\"18\\\" x2=\\\"7\\\" y2=\\\"18\\\"></line>\",\n\t\"anchor\": \"<circle cx=\\\"12\\\" cy=\\\"5\\\" r=\\\"3\\\"></circle><line x1=\\\"12\\\" y1=\\\"22\\\" x2=\\\"12\\\" y2=\\\"8\\\"></line><path d=\\\"M5 12H2a10 10 0 0 0 20 0h-3\\\"></path>\",\n\t\"aperture\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><line x1=\\\"14.31\\\" y1=\\\"8\\\" x2=\\\"20.05\\\" y2=\\\"17.94\\\"></line><line x1=\\\"9.69\\\" y1=\\\"8\\\" x2=\\\"21.17\\\" y2=\\\"8\\\"></line><line x1=\\\"7.38\\\" y1=\\\"12\\\" x2=\\\"13.12\\\" y2=\\\"2.06\\\"></line><line x1=\\\"9.69\\\" y1=\\\"16\\\" x2=\\\"3.95\\\" y2=\\\"6.06\\\"></line><line x1=\\\"14.31\\\" y1=\\\"16\\\" x2=\\\"2.83\\\" y2=\\\"16\\\"></line><line x1=\\\"16.62\\\" y1=\\\"12\\\" x2=\\\"10.88\\\" y2=\\\"21.94\\\"></line>\",\n\t\"arrow-down-left\": \"<line x1=\\\"18\\\" y1=\\\"6\\\" x2=\\\"6\\\" y2=\\\"18\\\"></line><polyline points=\\\"15 18 6 18 6 9\\\"></polyline>\",\n\t\"arrow-down-right\": \"<line x1=\\\"6\\\" y1=\\\"6\\\" x2=\\\"18\\\" y2=\\\"18\\\"></line><polyline points=\\\"9 18 18 18 18 9\\\"></polyline>\",\n\t\"arrow-down\": \"<line x1=\\\"12\\\" y1=\\\"4\\\" x2=\\\"12\\\" y2=\\\"20\\\"></line><polyline points=\\\"18 14 12 20 6 14\\\"></polyline>\",\n\t\"arrow-left\": \"<line x1=\\\"20\\\" y1=\\\"12\\\" x2=\\\"4\\\" y2=\\\"12\\\"></line><polyline points=\\\"10 18 4 12 10 6\\\"></polyline>\",\n\t\"arrow-right\": \"<line x1=\\\"4\\\" y1=\\\"12\\\" x2=\\\"20\\\" y2=\\\"12\\\"></line><polyline points=\\\"14 6 20 12 14 18\\\"></polyline>\",\n\t\"arrow-up-left\": \"<line x1=\\\"18\\\" y1=\\\"18\\\" x2=\\\"6\\\" y2=\\\"6\\\"></line><polyline points=\\\"15 6 6 6 6 15\\\"></polyline>\",\n\t\"arrow-up-right\": \"<line x1=\\\"6\\\" y1=\\\"18\\\" x2=\\\"18\\\" y2=\\\"6\\\"></line><polyline points=\\\"9 6 18 6 18 15\\\"></polyline>\",\n\t\"arrow-up\": \"<line x1=\\\"12\\\" y1=\\\"20\\\" x2=\\\"12\\\" y2=\\\"4\\\"></line><polyline points=\\\"6 10 12 4 18 10\\\"></polyline>\",\n\t\"at-sign\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"4\\\"></circle><path d=\\\"M16 12v1a3 3 0 0 0 6 0v-1a10 10 0 1 0-3.92 7.94\\\"></path>\",\n\t\"award\": \"<circle cx=\\\"12\\\" cy=\\\"8\\\" r=\\\"7\\\"></circle><polyline points=\\\"8.21 13.89 7 23 12 20 17 23 15.79 13.88\\\"></polyline>\",\n\t\"bar-chart-2\": \"<rect x=\\\"10\\\" y=\\\"3\\\" width=\\\"4\\\" height=\\\"18\\\"></rect><rect x=\\\"18\\\" y=\\\"8\\\" width=\\\"4\\\" height=\\\"13\\\"></rect><rect x=\\\"2\\\" y=\\\"13\\\" width=\\\"4\\\" height=\\\"8\\\"></rect>\",\n\t\"bar-chart\": \"<rect x=\\\"18\\\" y=\\\"3\\\" width=\\\"4\\\" height=\\\"18\\\"></rect><rect x=\\\"10\\\" y=\\\"8\\\" width=\\\"4\\\" height=\\\"13\\\"></rect><rect x=\\\"2\\\" y=\\\"13\\\" width=\\\"4\\\" height=\\\"8\\\"></rect>\",\n\t\"battery-charging\": \"<path d=\\\"M5 18H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h3.19M15 6h2a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-3.19\\\"></path><line x1=\\\"23\\\" y1=\\\"13\\\" x2=\\\"23\\\" y2=\\\"11\\\"></line><polyline points=\\\"11 6 7 12 13 12 9 18\\\"></polyline>\",\n\t\"battery\": \"<rect x=\\\"1\\\" y=\\\"6\\\" width=\\\"18\\\" height=\\\"12\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><line x1=\\\"23\\\" y1=\\\"13\\\" x2=\\\"23\\\" y2=\\\"11\\\"></line>\",\n\t\"bell-off\": \"<path d=\\\"M8.56 2.9A7 7 0 0 1 19 9v4m-2 4H2a3 3 0 0 0 3-3V9a7 7 0 0 1 .78-3.22M13.73 21a2 2 0 0 1-3.46 0\\\"></path><line x1=\\\"1\\\" y1=\\\"1\\\" x2=\\\"23\\\" y2=\\\"23\\\"></line>\",\n\t\"bell\": \"<path d=\\\"M22 17H2a3 3 0 0 0 3-3V9a7 7 0 0 1 14 0v5a3 3 0 0 0 3 3zm-8.27 4a2 2 0 0 1-3.46 0\\\"></path>\",\n\t\"bluetooth\": \"<polyline points=\\\"6.5 6.5 17.5 17.5 12 23 12 1 17.5 6.5 6.5 17.5\\\"></polyline>\",\n\t\"book\": \"<path d=\\\"M4 19.5A2.5 2.5 0 0 1 6.5 17H20\\\"></path><path d=\\\"M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z\\\"></path>\",\n\t\"bookmark\": \"<path d=\\\"M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z\\\"></path>\",\n\t\"box\": \"<path d=\\\"M12.89 1.45l8 4A2 2 0 0 1 22 7.24v9.53a2 2 0 0 1-1.11 1.79l-8 4a2 2 0 0 1-1.79 0l-8-4a2 2 0 0 1-1.1-1.8V7.24a2 2 0 0 1 1.11-1.79l8-4a2 2 0 0 1 1.78 0z\\\"></path><polyline points=\\\"2.32 6.16 12 11 21.68 6.16\\\"></polyline><line x1=\\\"12\\\" y1=\\\"22.76\\\" x2=\\\"12\\\" y2=\\\"11\\\"></line>\",\n\t\"briefcase\": \"<rect x=\\\"2\\\" y=\\\"7\\\" width=\\\"20\\\" height=\\\"14\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><path d=\\\"M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16\\\"></path>\",\n\t\"calendar\": \"<rect x=\\\"3\\\" y=\\\"4\\\" width=\\\"18\\\" height=\\\"18\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><line x1=\\\"16\\\" y1=\\\"2\\\" x2=\\\"16\\\" y2=\\\"6\\\"></line><line x1=\\\"8\\\" y1=\\\"2\\\" x2=\\\"8\\\" y2=\\\"6\\\"></line><line x1=\\\"3\\\" y1=\\\"10\\\" x2=\\\"21\\\" y2=\\\"10\\\"></line>\",\n\t\"camera-off\": \"<line x1=\\\"1\\\" y1=\\\"1\\\" x2=\\\"23\\\" y2=\\\"23\\\"></line><path d=\\\"M21 21H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h3m3-3h6l2 3h4a2 2 0 0 1 2 2v9.34m-7.72-2.06a4 4 0 1 1-5.56-5.56\\\"></path>\",\n\t\"camera\": \"<path d=\\\"M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z\\\"></path><circle cx=\\\"12\\\" cy=\\\"13\\\" r=\\\"4\\\"></circle>\",\n\t\"cast\": \"<path d=\\\"M2 16.1A5 5 0 0 1 5.9 20M2 12.05A9 9 0 0 1 9.95 20M2 8V6a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2h-6\\\"></path><line x1=\\\"2\\\" y1=\\\"20\\\" x2=\\\"2\\\" y2=\\\"20\\\"></line>\",\n\t\"check-circle\": \"<path d=\\\"M22 11.07V12a10 10 0 1 1-5.93-9.14\\\"></path><polyline points=\\\"23 3 12 14 9 11\\\"></polyline>\",\n\t\"check-square\": \"<polyline points=\\\"9 11 12 14 23 3\\\"></polyline><path d=\\\"M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11\\\"></path>\",\n\t\"check\": \"<polyline points=\\\"20 6 9 17 4 12\\\"></polyline>\",\n\t\"chevron-down\": \"<polyline points=\\\"6 9 12 15 18 9\\\"></polyline>\",\n\t\"chevron-left\": \"<polyline points=\\\"15 18 9 12 15 6\\\"></polyline>\",\n\t\"chevron-right\": \"<polyline points=\\\"9 18 15 12 9 6\\\"></polyline>\",\n\t\"chevron-up\": \"<polyline points=\\\"18 15 12 9 6 15\\\"></polyline>\",\n\t\"chevrons-down\": \"<polyline points=\\\"7 13 12 18 17 13\\\"></polyline><polyline points=\\\"7 6 12 11 17 6\\\"></polyline>\",\n\t\"chevrons-left\": \"<polyline points=\\\"11 17 6 12 11 7\\\"></polyline><polyline points=\\\"18 17 13 12 18 7\\\"></polyline>\",\n\t\"chevrons-right\": \"<polyline points=\\\"13 17 18 12 13 7\\\"></polyline><polyline points=\\\"6 17 11 12 6 7\\\"></polyline>\",\n\t\"chevrons-up\": \"<polyline points=\\\"17 11 12 6 7 11\\\"></polyline><polyline points=\\\"17 18 12 13 7 18\\\"></polyline>\",\n\t\"chrome\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"4\\\"></circle><line x1=\\\"21.17\\\" y1=\\\"8\\\" x2=\\\"12\\\" y2=\\\"8\\\"></line><line x1=\\\"3.95\\\" y1=\\\"6.06\\\" x2=\\\"8.54\\\" y2=\\\"14\\\"></line><line x1=\\\"10.88\\\" y1=\\\"21.94\\\" x2=\\\"15.46\\\" y2=\\\"14\\\"></line>\",\n\t\"circle\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle>\",\n\t\"clipboard\": \"<path d=\\\"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2\\\"></path><rect x=\\\"8\\\" y=\\\"2\\\" width=\\\"8\\\" height=\\\"4\\\" rx=\\\"1\\\" ry=\\\"1\\\"></rect>\",\n\t\"clock\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><polyline points=\\\"12 6 12 12 15 15\\\"></polyline>\",\n\t\"cloud-drizzle\": \"<line x1=\\\"8\\\" y1=\\\"19\\\" x2=\\\"8\\\" y2=\\\"21\\\"></line><line x1=\\\"8\\\" y1=\\\"13\\\" x2=\\\"8\\\" y2=\\\"15\\\"></line><line x1=\\\"16\\\" y1=\\\"19\\\" x2=\\\"16\\\" y2=\\\"21\\\"></line><line x1=\\\"16\\\" y1=\\\"13\\\" x2=\\\"16\\\" y2=\\\"15\\\"></line><line x1=\\\"12\\\" y1=\\\"21\\\" x2=\\\"12\\\" y2=\\\"23\\\"></line><line x1=\\\"12\\\" y1=\\\"15\\\" x2=\\\"12\\\" y2=\\\"17\\\"></line><path d=\\\"M20 16.58A5 5 0 0 0 18 7h-1.26A8 8 0 1 0 4 15.25\\\"></path>\",\n\t\"cloud-lightning\": \"<path d=\\\"M19 16.9A5 5 0 0 0 18 7h-1.26a8 8 0 1 0-11.62 9\\\"></path><polyline points=\\\"13 11 9 17 15 17 11 23\\\"></polyline>\",\n\t\"cloud-off\": \"<path d=\\\"M22.61 16.95A5 5 0 0 0 18 10h-1.26a8 8 0 0 0-7.05-6M5 5a8 8 0 0 0 4 15h9a5 5 0 0 0 1.7-.3\\\"></path><line x1=\\\"1\\\" y1=\\\"1\\\" x2=\\\"23\\\" y2=\\\"23\\\"></line>\",\n\t\"cloud-rain\": \"<line x1=\\\"16\\\" y1=\\\"13\\\" x2=\\\"16\\\" y2=\\\"21\\\"></line><line x1=\\\"8\\\" y1=\\\"13\\\" x2=\\\"8\\\" y2=\\\"21\\\"></line><line x1=\\\"12\\\" y1=\\\"15\\\" x2=\\\"12\\\" y2=\\\"23\\\"></line><path d=\\\"M20 16.58A5 5 0 0 0 18 7h-1.26A8 8 0 1 0 4 15.25\\\"></path>\",\n\t\"cloud-snow\": \"<path d=\\\"M20 17.58A5 5 0 0 0 18 8h-1.26A8 8 0 1 0 4 16.25\\\"></path><line x1=\\\"8\\\" y1=\\\"16\\\" x2=\\\"8\\\" y2=\\\"16\\\"></line><line x1=\\\"8\\\" y1=\\\"20\\\" x2=\\\"8\\\" y2=\\\"20\\\"></line><line x1=\\\"12\\\" y1=\\\"18\\\" x2=\\\"12\\\" y2=\\\"18\\\"></line><line x1=\\\"12\\\" y1=\\\"22\\\" x2=\\\"12\\\" y2=\\\"22\\\"></line><line x1=\\\"16\\\" y1=\\\"16\\\" x2=\\\"16\\\" y2=\\\"16\\\"></line><line x1=\\\"16\\\" y1=\\\"20\\\" x2=\\\"16\\\" y2=\\\"20\\\"></line>\",\n\t\"cloud\": \"<path d=\\\"M18 10h-1.26A8 8 0 1 0 9 20h9a5 5 0 0 0 0-10z\\\"></path>\",\n\t\"codepen\": \"<polygon points=\\\"12 2 22 8.5 22 15.5 12 22 2 15.5 2 8.5 12 2\\\"></polygon><line x1=\\\"12\\\" y1=\\\"22\\\" x2=\\\"12\\\" y2=\\\"15.5\\\"></line><polyline points=\\\"22 8.5 12 15.5 2 8.5\\\"></polyline><polyline points=\\\"2 15.5 12 8.5 22 15.5\\\"></polyline><line x1=\\\"12\\\" y1=\\\"2\\\" x2=\\\"12\\\" y2=\\\"8.5\\\"></line>\",\n\t\"command\": \"<path d=\\\"M18 3a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3 3 3 0 0 0 3-3 3 3 0 0 0-3-3H6a3 3 0 0 0-3 3 3 3 0 0 0 3 3 3 3 0 0 0 3-3V6a3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3h12a3 3 0 0 0 3-3 3 3 0 0 0-3-3z\\\"></path>\",\n\t\"compass\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><polygon points=\\\"16.24 7.76 14.12 14.12 7.76 16.24 9.88 9.88 16.24 7.76\\\"></polygon>\",\n\t\"copy\": \"<rect x=\\\"9\\\" y=\\\"9\\\" width=\\\"13\\\" height=\\\"13\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><path d=\\\"M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1\\\"></path>\",\n\t\"corner-down-left\": \"<polyline points=\\\"9 10 4 15 9 20\\\"></polyline><path d=\\\"M20 4v7a4 4 0 0 1-4 4H4\\\"></path>\",\n\t\"corner-down-right\": \"<polyline points=\\\"15 10 20 15 15 20\\\"></polyline><path d=\\\"M4 4v7a4 4 0 0 0 4 4h12\\\"></path>\",\n\t\"corner-left-down\": \"<polyline points=\\\"14 15 9 20 4 15\\\"></polyline><path d=\\\"M20 4h-7a4 4 0 0 0-4 4v12\\\"></path>\",\n\t\"corner-left-up\": \"<polyline points=\\\"14 9 9 4 4 9\\\"></polyline><path d=\\\"M20 20h-7a4 4 0 0 1-4-4V4\\\"></path>\",\n\t\"corner-right-down\": \"<polyline points=\\\"10 15 15 20 20 15\\\"></polyline><path d=\\\"M4 4h7a4 4 0 0 1 4 4v12\\\"></path>\",\n\t\"corner-right-up\": \"<polyline points=\\\"10 9 15 4 20 9\\\"></polyline><path d=\\\"M4 20h7a4 4 0 0 0 4-4V4\\\"></path>\",\n\t\"corner-up-left\": \"<polyline points=\\\"9 14 4 9 9 4\\\"></polyline><path d=\\\"M20 20v-7a4 4 0 0 0-4-4H4\\\"></path>\",\n\t\"corner-up-right\": \"<polyline points=\\\"15 14 20 9 15 4\\\"></polyline><path d=\\\"M4 20v-7a4 4 0 0 1 4-4h12\\\"></path>\",\n\t\"cpu\": \"<rect x=\\\"4\\\" y=\\\"4\\\" width=\\\"16\\\" height=\\\"16\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><rect x=\\\"9\\\" y=\\\"9\\\" width=\\\"6\\\" height=\\\"6\\\"></rect><line x1=\\\"9\\\" y1=\\\"1\\\" x2=\\\"9\\\" y2=\\\"4\\\"></line><line x1=\\\"15\\\" y1=\\\"1\\\" x2=\\\"15\\\" y2=\\\"4\\\"></line><line x1=\\\"9\\\" y1=\\\"20\\\" x2=\\\"9\\\" y2=\\\"23\\\"></line><line x1=\\\"15\\\" y1=\\\"20\\\" x2=\\\"15\\\" y2=\\\"23\\\"></line><line x1=\\\"20\\\" y1=\\\"9\\\" x2=\\\"23\\\" y2=\\\"9\\\"></line><line x1=\\\"20\\\" y1=\\\"14\\\" x2=\\\"23\\\" y2=\\\"14\\\"></line><line x1=\\\"1\\\" y1=\\\"9\\\" x2=\\\"4\\\" y2=\\\"9\\\"></line><line x1=\\\"1\\\" y1=\\\"14\\\" x2=\\\"4\\\" y2=\\\"14\\\"></line>\",\n\t\"credit-card\": \"<rect x=\\\"1\\\" y=\\\"4\\\" width=\\\"22\\\" height=\\\"16\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><line x1=\\\"1\\\" y1=\\\"10\\\" x2=\\\"23\\\" y2=\\\"10\\\"></line>\",\n\t\"crosshair\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><line x1=\\\"22\\\" y1=\\\"12\\\" x2=\\\"18\\\" y2=\\\"12\\\"></line><line x1=\\\"6\\\" y1=\\\"12\\\" x2=\\\"2\\\" y2=\\\"12\\\"></line><line x1=\\\"12\\\" y1=\\\"6\\\" x2=\\\"12\\\" y2=\\\"2\\\"></line><line x1=\\\"12\\\" y1=\\\"22\\\" x2=\\\"12\\\" y2=\\\"18\\\"></line>\",\n\t\"delete\": \"<path d=\\\"M21 4H8l-7 8 7 8h13a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2z\\\"></path><line x1=\\\"18\\\" y1=\\\"9\\\" x2=\\\"12\\\" y2=\\\"15\\\"></line><line x1=\\\"12\\\" y1=\\\"9\\\" x2=\\\"18\\\" y2=\\\"15\\\"></line>\",\n\t\"disc\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"3\\\"></circle>\",\n\t\"download-cloud\": \"<polyline points=\\\"8 17 12 21 16 17\\\"></polyline><line x1=\\\"12\\\" y1=\\\"12\\\" x2=\\\"12\\\" y2=\\\"21\\\"></line><path d=\\\"M20.88 18.09A5 5 0 0 0 18 9h-1.26A8 8 0 1 0 3 16.29\\\"></path>\",\n\t\"download\": \"<path d=\\\"M3 17v3a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-3\\\"></path><polyline points=\\\"8 12 12 16 16 12\\\"></polyline><line x1=\\\"12\\\" y1=\\\"2\\\" x2=\\\"12\\\" y2=\\\"16\\\"></line>\",\n\t\"droplet\": \"<path d=\\\"M12 2.69l5.66 5.66a8 8 0 1 1-11.31 0z\\\"></path>\",\n\t\"edit-2\": \"<polygon points=\\\"16 3 21 8 8 21 3 21 3 16 16 3\\\"></polygon>\",\n\t\"edit-3\": \"<polygon points=\\\"14 2 18 6 7 17 3 17 3 13 14 2\\\"></polygon><line x1=\\\"3\\\" y1=\\\"22\\\" x2=\\\"21\\\" y2=\\\"22\\\"></line>\",\n\t\"edit\": \"<path d=\\\"M20 14.66V20a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h5.34\\\"></path><polygon points=\\\"18 2 22 6 12 16 8 16 8 12 18 2\\\"></polygon>\",\n\t\"external-link\": \"<path d=\\\"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6\\\"></path><polyline points=\\\"15 3 21 3 21 9\\\"></polyline><line x1=\\\"10\\\" y1=\\\"14\\\" x2=\\\"21\\\" y2=\\\"3\\\"></line>\",\n\t\"eye-off\": \"<path d=\\\"M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24\\\"></path><line x1=\\\"1\\\" y1=\\\"1\\\" x2=\\\"23\\\" y2=\\\"23\\\"></line>\",\n\t\"eye\": \"<path d=\\\"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z\\\"></path><circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"3\\\"></circle>\",\n\t\"facebook\": \"<path d=\\\"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z\\\"></path>\",\n\t\"fast-forward\": \"<polygon points=\\\"13 19 22 12 13 5 13 19\\\"></polygon><polygon points=\\\"2 19 11 12 2 5 2 19\\\"></polygon>\",\n\t\"feather\": \"<path d=\\\"M20.24 12.24a6 6 0 0 0-8.49-8.49L5 10.5V19h8.5z\\\"></path><line x1=\\\"16\\\" y1=\\\"8\\\" x2=\\\"2\\\" y2=\\\"22\\\"></line><line x1=\\\"17\\\" y1=\\\"15\\\" x2=\\\"9\\\" y2=\\\"15\\\"></line>\",\n\t\"file-minus\": \"<path d=\\\"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z\\\"></path><polyline points=\\\"14 2 14 8 20 8\\\"></polyline><line x1=\\\"9\\\" y1=\\\"15\\\" x2=\\\"15\\\" y2=\\\"15\\\"></line>\",\n\t\"file-plus\": \"<path d=\\\"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z\\\"></path><polyline points=\\\"14 2 14 8 20 8\\\"></polyline><line x1=\\\"12\\\" y1=\\\"18\\\" x2=\\\"12\\\" y2=\\\"12\\\"></line><line x1=\\\"9\\\" y1=\\\"15\\\" x2=\\\"15\\\" y2=\\\"15\\\"></line>\",\n\t\"file-text\": \"<path d=\\\"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z\\\"></path><polyline points=\\\"14 2 14 8 20 8\\\"></polyline><line x1=\\\"16\\\" y1=\\\"13\\\" x2=\\\"8\\\" y2=\\\"13\\\"></line><line x1=\\\"16\\\" y1=\\\"17\\\" x2=\\\"8\\\" y2=\\\"17\\\"></line><polyline points=\\\"10 9 9 9 8 9\\\"></polyline>\",\n\t\"file\": \"<path d=\\\"M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z\\\"></path><polyline points=\\\"13 2 13 9 20 9\\\"></polyline>\",\n\t\"film\": \"<rect x=\\\"2\\\" y=\\\"2\\\" width=\\\"20\\\" height=\\\"20\\\" rx=\\\"2.18\\\" ry=\\\"2.18\\\"></rect><line x1=\\\"7\\\" y1=\\\"2\\\" x2=\\\"7\\\" y2=\\\"22\\\"></line><line x1=\\\"17\\\" y1=\\\"2\\\" x2=\\\"17\\\" y2=\\\"22\\\"></line><line x1=\\\"2\\\" y1=\\\"12\\\" x2=\\\"22\\\" y2=\\\"12\\\"></line><line x1=\\\"2\\\" y1=\\\"7\\\" x2=\\\"7\\\" y2=\\\"7\\\"></line><line x1=\\\"2\\\" y1=\\\"17\\\" x2=\\\"7\\\" y2=\\\"17\\\"></line><line x1=\\\"17\\\" y1=\\\"17\\\" x2=\\\"22\\\" y2=\\\"17\\\"></line><line x1=\\\"17\\\" y1=\\\"7\\\" x2=\\\"22\\\" y2=\\\"7\\\"></line>\",\n\t\"filter\": \"<polygon points=\\\"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3\\\"></polygon>\",\n\t\"flag\": \"<path d=\\\"M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z\\\"></path><line x1=\\\"4\\\" y1=\\\"22\\\" x2=\\\"4\\\" y2=\\\"15\\\"></line>\",\n\t\"folder\": \"<path d=\\\"M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z\\\"></path>\",\n\t\"github\": \"<path d=\\\"M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22\\\"></path>\",\n\t\"globe\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><line x1=\\\"2\\\" y1=\\\"12\\\" x2=\\\"22\\\" y2=\\\"12\\\"></line><path d=\\\"M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z\\\"></path>\",\n\t\"grid\": \"<rect x=\\\"3\\\" y=\\\"3\\\" width=\\\"7\\\" height=\\\"7\\\"></rect><rect x=\\\"14\\\" y=\\\"3\\\" width=\\\"7\\\" height=\\\"7\\\"></rect><rect x=\\\"14\\\" y=\\\"14\\\" width=\\\"7\\\" height=\\\"7\\\"></rect><rect x=\\\"3\\\" y=\\\"14\\\" width=\\\"7\\\" height=\\\"7\\\"></rect>\",\n\t\"hash\": \"<line x1=\\\"4\\\" y1=\\\"9\\\" x2=\\\"20\\\" y2=\\\"9\\\"></line><line x1=\\\"4\\\" y1=\\\"15\\\" x2=\\\"20\\\" y2=\\\"15\\\"></line><line x1=\\\"10\\\" y1=\\\"3\\\" x2=\\\"8\\\" y2=\\\"21\\\"></line><line x1=\\\"16\\\" y1=\\\"3\\\" x2=\\\"14\\\" y2=\\\"21\\\"></line>\",\n\t\"headphones\": \"<path d=\\\"M3 18v-6a9 9 0 0 1 18 0v6\\\"></path><path d=\\\"M21 19a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3zM3 19a2 2 0 0 0 2 2h1a2 2 0 0 0 2-2v-3a2 2 0 0 0-2-2H3z\\\"></path>\",\n\t\"heart\": \"<path d=\\\"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z\\\"></path>\",\n\t\"home\": \"<path d=\\\"M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\\\"></path><polyline points=\\\"9 22 9 12 15 12 15 22\\\"></polyline>\",\n\t\"image\": \"<rect x=\\\"3\\\" y=\\\"3\\\" width=\\\"18\\\" height=\\\"18\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><circle cx=\\\"8.5\\\" cy=\\\"8.5\\\" r=\\\"1.5\\\"></circle><polyline points=\\\"21 15 16 10 5 21\\\"></polyline>\",\n\t\"inbox\": \"<polyline points=\\\"22 13 16 13 14 16 10 16 8 13 2 13\\\"></polyline><path d=\\\"M5.47 5.19L2 13v5a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-5l-3.47-7.81A2 2 0 0 0 16.7 4H7.3a2 2 0 0 0-1.83 1.19z\\\"></path>\",\n\t\"info\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><line x1=\\\"12\\\" y1=\\\"16\\\" x2=\\\"12\\\" y2=\\\"12\\\"></line><line x1=\\\"12\\\" y1=\\\"8\\\" x2=\\\"12\\\" y2=\\\"8\\\"></line>\",\n\t\"instagram\": \"<rect x=\\\"2\\\" y=\\\"2\\\" width=\\\"20\\\" height=\\\"20\\\" rx=\\\"5\\\" ry=\\\"5\\\"></rect><path d=\\\"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z\\\"></path><line x1=\\\"17.5\\\" y1=\\\"6.5\\\" x2=\\\"17.5\\\" y2=\\\"6.5\\\"></line>\",\n\t\"layers\": \"<polygon points=\\\"12 2 2 7 12 12 22 7 12 2\\\"></polygon><polyline points=\\\"2 17 12 22 22 17\\\"></polyline><polyline points=\\\"2 12 12 17 22 12\\\"></polyline>\",\n\t\"layout\": \"<rect x=\\\"3\\\" y=\\\"3\\\" width=\\\"18\\\" height=\\\"18\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><line x1=\\\"3\\\" y1=\\\"9\\\" x2=\\\"21\\\" y2=\\\"9\\\"></line><line x1=\\\"9\\\" y1=\\\"21\\\" x2=\\\"9\\\" y2=\\\"9\\\"></line>\",\n\t\"life-buoy\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"4\\\"></circle><line x1=\\\"4.93\\\" y1=\\\"4.93\\\" x2=\\\"9.17\\\" y2=\\\"9.17\\\"></line><line x1=\\\"14.83\\\" y1=\\\"14.83\\\" x2=\\\"19.07\\\" y2=\\\"19.07\\\"></line><line x1=\\\"14.83\\\" y1=\\\"9.17\\\" x2=\\\"19.07\\\" y2=\\\"4.93\\\"></line><line x1=\\\"14.83\\\" y1=\\\"9.17\\\" x2=\\\"18.36\\\" y2=\\\"5.64\\\"></line><line x1=\\\"4.93\\\" y1=\\\"19.07\\\" x2=\\\"9.17\\\" y2=\\\"14.83\\\"></line>\",\n\t\"link-2\": \"<path d=\\\"M15 7h3a5 5 0 0 1 5 5 5 5 0 0 1-5 5h-3m-6 0H6a5 5 0 0 1-5-5 5 5 0 0 1 5-5h3\\\"></path><line x1=\\\"8\\\" y1=\\\"12\\\" x2=\\\"16\\\" y2=\\\"12\\\"></line>\",\n\t\"link\": \"<path d=\\\"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71\\\"></path><path d=\\\"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71\\\"></path>\",\n\t\"list\": \"<line x1=\\\"8\\\" y1=\\\"6\\\" x2=\\\"21\\\" y2=\\\"6\\\"></line><line x1=\\\"8\\\" y1=\\\"12\\\" x2=\\\"21\\\" y2=\\\"12\\\"></line><line x1=\\\"8\\\" y1=\\\"18\\\" x2=\\\"21\\\" y2=\\\"18\\\"></line><line x1=\\\"3\\\" y1=\\\"6\\\" x2=\\\"3\\\" y2=\\\"6\\\"></line><line x1=\\\"3\\\" y1=\\\"12\\\" x2=\\\"3\\\" y2=\\\"12\\\"></line><line x1=\\\"3\\\" y1=\\\"18\\\" x2=\\\"3\\\" y2=\\\"18\\\"></line>\",\n\t\"loader\": \"<line x1=\\\"12\\\" y1=\\\"2\\\" x2=\\\"12\\\" y2=\\\"6\\\"></line><line x1=\\\"12\\\" y1=\\\"18\\\" x2=\\\"12\\\" y2=\\\"22\\\"></line><line x1=\\\"4.93\\\" y1=\\\"4.93\\\" x2=\\\"7.76\\\" y2=\\\"7.76\\\"></line><line x1=\\\"16.24\\\" y1=\\\"16.24\\\" x2=\\\"19.07\\\" y2=\\\"19.07\\\"></line><line x1=\\\"2\\\" y1=\\\"12\\\" x2=\\\"6\\\" y2=\\\"12\\\"></line><line x1=\\\"18\\\" y1=\\\"12\\\" x2=\\\"22\\\" y2=\\\"12\\\"></line><line x1=\\\"4.93\\\" y1=\\\"19.07\\\" x2=\\\"7.76\\\" y2=\\\"16.24\\\"></line><line x1=\\\"16.24\\\" y1=\\\"7.76\\\" x2=\\\"19.07\\\" y2=\\\"4.93\\\"></line>\",\n\t\"lock\": \"<rect x=\\\"3\\\" y=\\\"11\\\" width=\\\"18\\\" height=\\\"11\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><path d=\\\"M7 11V7a5 5 0 0 1 10 0v4\\\"></path>\",\n\t\"log-in\": \"<path d=\\\"M14 22h5a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2h-5\\\"></path><polyline points=\\\"11 16 15 12 11 8\\\"></polyline><line x1=\\\"15\\\" y1=\\\"12\\\" x2=\\\"3\\\" y2=\\\"12\\\"></line>\",\n\t\"log-out\": \"<path d=\\\"M10 22H5a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h5\\\"></path><polyline points=\\\"17 16 21 12 17 8\\\"></polyline><line x1=\\\"21\\\" y1=\\\"12\\\" x2=\\\"9\\\" y2=\\\"12\\\"></line>\",\n\t\"mail\": \"<path d=\\\"M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z\\\"></path><polyline points=\\\"22,6 12,13 2,6\\\"></polyline>\",\n\t\"map-pin\": \"<path d=\\\"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z\\\"></path><circle cx=\\\"12\\\" cy=\\\"10\\\" r=\\\"3\\\"></circle>\",\n\t\"map\": \"<polygon points=\\\"1 6 1 22 8 18 16 22 23 18 23 2 16 6 8 2 1 6\\\"></polygon><line x1=\\\"8\\\" y1=\\\"2\\\" x2=\\\"8\\\" y2=\\\"18\\\"></line><line x1=\\\"16\\\" y1=\\\"6\\\" x2=\\\"16\\\" y2=\\\"22\\\"></line>\",\n\t\"maximize-2\": \"<polyline points=\\\"15 3 21 3 21 9\\\"></polyline><polyline points=\\\"9 21 3 21 3 15\\\"></polyline><line x1=\\\"21\\\" y1=\\\"3\\\" x2=\\\"14\\\" y2=\\\"10\\\"></line><line x1=\\\"3\\\" y1=\\\"21\\\" x2=\\\"10\\\" y2=\\\"14\\\"></line>\",\n\t\"maximize\": \"<path d=\\\"M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3\\\"></path>\",\n\t\"menu\": \"<line x1=\\\"3\\\" y1=\\\"12\\\" x2=\\\"21\\\" y2=\\\"12\\\"></line><line x1=\\\"3\\\" y1=\\\"6\\\" x2=\\\"21\\\" y2=\\\"6\\\"></line><line x1=\\\"3\\\" y1=\\\"18\\\" x2=\\\"21\\\" y2=\\\"18\\\"></line>\",\n\t\"message-circle\": \"<path d=\\\"M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z\\\"></path>\",\n\t\"message-square\": \"<path d=\\\"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z\\\"></path>\",\n\t\"mic-off\": \"<line x1=\\\"1\\\" y1=\\\"1\\\" x2=\\\"23\\\" y2=\\\"23\\\"></line><path d=\\\"M9 9v3a3 3 0 0 0 5.12 2.12M15 9.34V4a3 3 0 0 0-5.94-.6\\\"></path><path d=\\\"M17 16.95A7 7 0 0 1 5 12v-2m14 0v2a7 7 0 0 1-.11 1.23\\\"></path><line x1=\\\"12\\\" y1=\\\"19\\\" x2=\\\"12\\\" y2=\\\"23\\\"></line><line x1=\\\"8\\\" y1=\\\"23\\\" x2=\\\"16\\\" y2=\\\"23\\\"></line>\",\n\t\"mic\": \"<path d=\\\"M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z\\\"></path><path d=\\\"M19 10v2a7 7 0 0 1-14 0v-2\\\"></path><line x1=\\\"12\\\" y1=\\\"19\\\" x2=\\\"12\\\" y2=\\\"23\\\"></line><line x1=\\\"8\\\" y1=\\\"23\\\" x2=\\\"16\\\" y2=\\\"23\\\"></line>\",\n\t\"minimize-2\": \"<polyline points=\\\"4 14 10 14 10 20\\\"></polyline><polyline points=\\\"20 10 14 10 14 4\\\"></polyline><line x1=\\\"14\\\" y1=\\\"10\\\" x2=\\\"21\\\" y2=\\\"3\\\"></line><line x1=\\\"3\\\" y1=\\\"21\\\" x2=\\\"10\\\" y2=\\\"14\\\"></line>\",\n\t\"minimize\": \"<path d=\\\"M8 3v3a2 2 0 0 1-2 2H3m18 0h-3a2 2 0 0 1-2-2V3m0 18v-3a2 2 0 0 1 2-2h3M3 16h3a2 2 0 0 1 2 2v3\\\"></path>\",\n\t\"minus-circle\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><line x1=\\\"8\\\" y1=\\\"12\\\" x2=\\\"16\\\" y2=\\\"12\\\"></line>\",\n\t\"minus-square\": \"<rect x=\\\"3\\\" y=\\\"3\\\" width=\\\"18\\\" height=\\\"18\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><line x1=\\\"8\\\" y1=\\\"12\\\" x2=\\\"16\\\" y2=\\\"12\\\"></line>\",\n\t\"minus\": \"<line x1=\\\"5\\\" y1=\\\"12\\\" x2=\\\"19\\\" y2=\\\"12\\\"></line>\",\n\t\"monitor\": \"<rect x=\\\"2\\\" y=\\\"3\\\" width=\\\"20\\\" height=\\\"14\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><line x1=\\\"8\\\" y1=\\\"21\\\" x2=\\\"16\\\" y2=\\\"21\\\"></line><line x1=\\\"12\\\" y1=\\\"17\\\" x2=\\\"12\\\" y2=\\\"21\\\"></line>\",\n\t\"moon\": \"<path d=\\\"M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z\\\"></path>\",\n\t\"more-horizontal\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"2\\\"></circle><circle cx=\\\"20\\\" cy=\\\"12\\\" r=\\\"2\\\"></circle><circle cx=\\\"4\\\" cy=\\\"12\\\" r=\\\"2\\\"></circle>\",\n\t\"more-vertical\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"2\\\"></circle><circle cx=\\\"12\\\" cy=\\\"4\\\" r=\\\"2\\\"></circle><circle cx=\\\"12\\\" cy=\\\"20\\\" r=\\\"2\\\"></circle>\",\n\t\"move\": \"<polyline points=\\\"5 9 2 12 5 15\\\"></polyline><polyline points=\\\"9 5 12 2 15 5\\\"></polyline><polyline points=\\\"15 19 12 22 9 19\\\"></polyline><polyline points=\\\"19 9 22 12 19 15\\\"></polyline><line x1=\\\"2\\\" y1=\\\"12\\\" x2=\\\"22\\\" y2=\\\"12\\\"></line><line x1=\\\"12\\\" y1=\\\"2\\\" x2=\\\"12\\\" y2=\\\"22\\\"></line>\",\n\t\"music\": \"<path d=\\\"M9 17H5a2 2 0 0 0-2 2 2 2 0 0 0 2 2h2a2 2 0 0 0 2-2zm12-2h-4a2 2 0 0 0-2 2 2 2 0 0 0 2 2h2a2 2 0 0 0 2-2z\\\"></path><polyline points=\\\"9 17 9 5 21 3 21 15\\\"></polyline>\",\n\t\"navigation-2\": \"<polygon points=\\\"12 2 19 21 12 17 5 21 12 2\\\"></polygon>\",\n\t\"navigation\": \"<polygon points=\\\"3 11 22 2 13 21 11 13 3 11\\\"></polygon>\",\n\t\"octagon\": \"<polygon points=\\\"7.86 2 16.14 2 22 7.86 22 16.14 16.14 22 7.86 22 2 16.14 2 7.86 7.86 2\\\"></polygon>\",\n\t\"package\": \"<path d=\\\"M12.89 1.45l8 4A2 2 0 0 1 22 7.24v9.53a2 2 0 0 1-1.11 1.79l-8 4a2 2 0 0 1-1.79 0l-8-4a2 2 0 0 1-1.1-1.8V7.24a2 2 0 0 1 1.11-1.79l8-4a2 2 0 0 1 1.78 0z\\\"></path><polyline points=\\\"2.32 6.16 12 11 21.68 6.16\\\"></polyline><line x1=\\\"12\\\" y1=\\\"22.76\\\" x2=\\\"12\\\" y2=\\\"11\\\"></line><line x1=\\\"7\\\" y1=\\\"3.5\\\" x2=\\\"17\\\" y2=\\\"8.5\\\"></line>\",\n\t\"pause-circle\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><line x1=\\\"10\\\" y1=\\\"15\\\" x2=\\\"10\\\" y2=\\\"9\\\"></line><line x1=\\\"14\\\" y1=\\\"15\\\" x2=\\\"14\\\" y2=\\\"9\\\"></line>\",\n\t\"pause\": \"<rect x=\\\"6\\\" y=\\\"4\\\" width=\\\"4\\\" height=\\\"16\\\"></rect><rect x=\\\"14\\\" y=\\\"4\\\" width=\\\"4\\\" height=\\\"16\\\"></rect>\",\n\t\"percent\": \"<line x1=\\\"19\\\" y1=\\\"5\\\" x2=\\\"5\\\" y2=\\\"19\\\"></line><circle cx=\\\"6.5\\\" cy=\\\"6.5\\\" r=\\\"2.5\\\"></circle><circle cx=\\\"17.5\\\" cy=\\\"17.5\\\" r=\\\"2.5\\\"></circle>\",\n\t\"phone-call\": \"<path d=\\\"M15.05 5A5 5 0 0 1 19 8.95M15.05 1A9 9 0 0 1 23 8.94m-1 7.98v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z\\\"></path>\",\n\t\"phone-forwarded\": \"<polyline points=\\\"19 1 23 5 19 9\\\"></polyline><line x1=\\\"15\\\" y1=\\\"5\\\" x2=\\\"23\\\" y2=\\\"5\\\"></line><path d=\\\"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z\\\"></path>\",\n\t\"phone-incoming\": \"<polyline points=\\\"16 2 16 8 22 8\\\"></polyline><line x1=\\\"23\\\" y1=\\\"1\\\" x2=\\\"16\\\" y2=\\\"8\\\"></line><path d=\\\"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z\\\"></path>\",\n\t\"phone-missed\": \"<line x1=\\\"23\\\" y1=\\\"1\\\" x2=\\\"17\\\" y2=\\\"7\\\"></line><line x1=\\\"17\\\" y1=\\\"1\\\" x2=\\\"23\\\" y2=\\\"7\\\"></line><path d=\\\"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z\\\"></path>\",\n\t\"phone-off\": \"<path d=\\\"M10.68 13.31a16 16 0 0 0 3.41 2.6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7 2 2 0 0 1 1.72 2v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.42 19.42 0 0 1-3.33-2.67m-2.67-3.34a19.79 19.79 0 0 1-3.07-8.63A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91\\\"></path><line x1=\\\"23\\\" y1=\\\"1\\\" x2=\\\"1\\\" y2=\\\"23\\\"></line>\",\n\t\"phone-outgoing\": \"<polyline points=\\\"23 7 23 1 17 1\\\"></polyline><line x1=\\\"16\\\" y1=\\\"8\\\" x2=\\\"23\\\" y2=\\\"1\\\"></line><path d=\\\"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z\\\"></path>\",\n\t\"phone\": \"<path d=\\\"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z\\\"></path>\",\n\t\"pie-chart\": \"<path d=\\\"M21.21 15.89A10 10 0 1 1 8 2.83\\\"></path><path d=\\\"M22 12A10 10 0 0 0 12 2v10z\\\"></path>\",\n\t\"play-circle\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><polygon points=\\\"10 8 16 12 10 16 10 8\\\"></polygon>\",\n\t\"play\": \"<polygon points=\\\"5 3 19 12 5 21 5 3\\\"></polygon>\",\n\t\"plus-circle\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><line x1=\\\"12\\\" y1=\\\"8\\\" x2=\\\"12\\\" y2=\\\"16\\\"></line><line x1=\\\"8\\\" y1=\\\"12\\\" x2=\\\"16\\\" y2=\\\"12\\\"></line>\",\n\t\"plus-square\": \"<rect x=\\\"3\\\" y=\\\"3\\\" width=\\\"18\\\" height=\\\"18\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><line x1=\\\"12\\\" y1=\\\"8\\\" x2=\\\"12\\\" y2=\\\"16\\\"></line><line x1=\\\"8\\\" y1=\\\"12\\\" x2=\\\"16\\\" y2=\\\"12\\\"></line>\",\n\t\"plus\": \"<line x1=\\\"12\\\" y1=\\\"5\\\" x2=\\\"12\\\" y2=\\\"19\\\"></line><line x1=\\\"5\\\" y1=\\\"12\\\" x2=\\\"19\\\" y2=\\\"12\\\"></line>\",\n\t\"pocket\": \"<path d=\\\"M4 3h16a2 2 0 0 1 2 2v6a10 10 0 0 1-10 10A10 10 0 0 1 2 11V5a2 2 0 0 1 2-2z\\\"></path><polyline points=\\\"8 10 12 14 16 10\\\"></polyline>\",\n\t\"power\": \"<path d=\\\"M18.36 6.64a9 9 0 1 1-12.73 0\\\"></path><line x1=\\\"12\\\" y1=\\\"2\\\" x2=\\\"12\\\" y2=\\\"12\\\"></line>\",\n\t\"printer\": \"<polyline points=\\\"6 9 6 2 18 2 18 9\\\"></polyline><path d=\\\"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2\\\"></path><rect x=\\\"6\\\" y=\\\"14\\\" width=\\\"12\\\" height=\\\"8\\\"></rect>\",\n\t\"radio\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"2\\\"></circle><path d=\\\"M16.24 7.76a6 6 0 0 1 0 8.49m-8.48-.01a6 6 0 0 1 0-8.49m11.31-2.82a10 10 0 0 1 0 14.14m-14.14 0a10 10 0 0 1 0-14.14\\\"></path>\",\n\t\"refresh-ccw\": \"<polyline points=\\\"1 4 1 10 7 10\\\"></polyline><polyline points=\\\"23 20 23 14 17 14\\\"></polyline><path d=\\\"M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15\\\"></path>\",\n\t\"refresh-cw\": \"<polyline points=\\\"23 4 23 10 17 10\\\"></polyline><polyline points=\\\"1 20 1 14 7 14\\\"></polyline><path d=\\\"M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15\\\"></path>\",\n\t\"repeat\": \"<polyline points=\\\"17 1 21 5 17 9\\\"></polyline><path d=\\\"M3 11V9a4 4 0 0 1 4-4h14\\\"></path><polyline points=\\\"7 23 3 19 7 15\\\"></polyline><path d=\\\"M21 13v2a4 4 0 0 1-4 4H3\\\"></path>\",\n\t\"rewind\": \"<polygon points=\\\"11 19 2 12 11 5 11 19\\\"></polygon><polygon points=\\\"22 19 13 12 22 5 22 19\\\"></polygon>\",\n\t\"rotate-ccw\": \"<polyline points=\\\"1 4 1 10 7 10\\\"></polyline><path d=\\\"M3.51 15a9 9 0 1 0 2.13-9.36L1 10\\\"></path>\",\n\t\"rotate-cw\": \"<polyline points=\\\"23 4 23 10 17 10\\\"></polyline><path d=\\\"M20.49 15a9 9 0 1 1-2.12-9.36L23 10\\\"></path>\",\n\t\"save\": \"<path d=\\\"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z\\\"></path><polyline points=\\\"17 21 17 13 7 13 7 21\\\"></polyline><polyline points=\\\"7 3 7 8 15 8\\\"></polyline>\",\n\t\"scissors\": \"<circle cx=\\\"6\\\" cy=\\\"6\\\" r=\\\"3\\\"></circle><circle cx=\\\"6\\\" cy=\\\"18\\\" r=\\\"3\\\"></circle><line x1=\\\"20\\\" y1=\\\"4\\\" x2=\\\"8.12\\\" y2=\\\"15.88\\\"></line><line x1=\\\"14.47\\\" y1=\\\"14.48\\\" x2=\\\"20\\\" y2=\\\"20\\\"></line><line x1=\\\"8.12\\\" y1=\\\"8.12\\\" x2=\\\"12\\\" y2=\\\"12\\\"></line>\",\n\t\"search\": \"<circle cx=\\\"10.5\\\" cy=\\\"10.5\\\" r=\\\"7.5\\\"></circle><line x1=\\\"21\\\" y1=\\\"21\\\" x2=\\\"15.8\\\" y2=\\\"15.8\\\"></line>\",\n\t\"server\": \"<rect x=\\\"2\\\" y=\\\"2\\\" width=\\\"20\\\" height=\\\"8\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><rect x=\\\"2\\\" y=\\\"14\\\" width=\\\"20\\\" height=\\\"8\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><line x1=\\\"6\\\" y1=\\\"6\\\" x2=\\\"6\\\" y2=\\\"6\\\"></line><line x1=\\\"6\\\" y1=\\\"18\\\" x2=\\\"6\\\" y2=\\\"18\\\"></line>\",\n\t\"settings\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"3\\\"></circle><path d=\\\"M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z\\\"></path>\",\n\t\"share-2\": \"<circle cx=\\\"18\\\" cy=\\\"5\\\" r=\\\"3\\\"></circle><circle cx=\\\"6\\\" cy=\\\"12\\\" r=\\\"3\\\"></circle><circle cx=\\\"18\\\" cy=\\\"19\\\" r=\\\"3\\\"></circle><line x1=\\\"8.59\\\" y1=\\\"13.51\\\" x2=\\\"15.42\\\" y2=\\\"17.49\\\"></line><line x1=\\\"15.41\\\" y1=\\\"6.51\\\" x2=\\\"8.59\\\" y2=\\\"10.49\\\"></line>\",\n\t\"share\": \"<path d=\\\"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8\\\"></path><polyline points=\\\"16 6 12 2 8 6\\\"></polyline><line x1=\\\"12\\\" y1=\\\"2\\\" x2=\\\"12\\\" y2=\\\"15\\\"></line>\",\n\t\"shield\": \"<path d=\\\"M12 22s8-4 8-10V4l-8-2-8 2v8c0 6 8 10 8 10z\\\"></path>\",\n\t\"shuffle\": \"<polyline points=\\\"16 3 21 3 21 8\\\"></polyline><line x1=\\\"4\\\" y1=\\\"20\\\" x2=\\\"21\\\" y2=\\\"3\\\"></line><polyline points=\\\"21 16 21 21 16 21\\\"></polyline><line x1=\\\"15\\\" y1=\\\"15\\\" x2=\\\"21\\\" y2=\\\"21\\\"></line><line x1=\\\"4\\\" y1=\\\"4\\\" x2=\\\"9\\\" y2=\\\"9\\\"></line>\",\n\t\"sidebar\": \"<rect x=\\\"3\\\" y=\\\"3\\\" width=\\\"18\\\" height=\\\"18\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><line x1=\\\"9\\\" y1=\\\"3\\\" x2=\\\"9\\\" y2=\\\"21\\\"></line>\",\n\t\"skip-back\": \"<polygon points=\\\"19 20 9 12 19 4 19 20\\\"></polygon><line x1=\\\"5\\\" y1=\\\"19\\\" x2=\\\"5\\\" y2=\\\"5\\\"></line>\",\n\t\"skip-forward\": \"<polygon points=\\\"5 4 15 12 5 20 5 4\\\"></polygon><line x1=\\\"19\\\" y1=\\\"5\\\" x2=\\\"19\\\" y2=\\\"19\\\"></line>\",\n\t\"slack\": \"<path d=\\\"M22.08 9C19.81 1.41 16.54-.35 9 1.92S-.35 7.46 1.92 15 7.46 24.35 15 22.08 24.35 16.54 22.08 9z\\\"></path><line x1=\\\"12.57\\\" y1=\\\"5.99\\\" x2=\\\"16.15\\\" y2=\\\"16.39\\\"></line><line x1=\\\"7.85\\\" y1=\\\"7.61\\\" x2=\\\"11.43\\\" y2=\\\"18.01\\\"></line><line x1=\\\"16.39\\\" y1=\\\"7.85\\\" x2=\\\"5.99\\\" y2=\\\"11.43\\\"></line><line x1=\\\"18.01\\\" y1=\\\"12.57\\\" x2=\\\"7.61\\\" y2=\\\"16.15\\\"></line>\",\n\t\"slash\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><line x1=\\\"4.93\\\" y1=\\\"4.93\\\" x2=\\\"19.07\\\" y2=\\\"19.07\\\"></line>\",\n\t\"smartphone\": \"<rect x=\\\"5\\\" y=\\\"2\\\" width=\\\"14\\\" height=\\\"20\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><line x1=\\\"12\\\" y1=\\\"18\\\" x2=\\\"12\\\" y2=\\\"18\\\"></line>\",\n\t\"speaker\": \"<rect x=\\\"4\\\" y=\\\"2\\\" width=\\\"16\\\" height=\\\"20\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><circle cx=\\\"12\\\" cy=\\\"14\\\" r=\\\"4\\\"></circle><line x1=\\\"12\\\" y1=\\\"6\\\" x2=\\\"12\\\" y2=\\\"6\\\"></line>\",\n\t\"square\": \"<rect x=\\\"3\\\" y=\\\"3\\\" width=\\\"18\\\" height=\\\"18\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect>\",\n\t\"star\": \"<polygon points=\\\"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2\\\"></polygon>\",\n\t\"stop-circle\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><rect x=\\\"9\\\" y=\\\"9\\\" width=\\\"6\\\" height=\\\"6\\\"></rect>\",\n\t\"sun\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"5\\\"></circle><line x1=\\\"12\\\" y1=\\\"1\\\" x2=\\\"12\\\" y2=\\\"3\\\"></line><line x1=\\\"12\\\" y1=\\\"21\\\" x2=\\\"12\\\" y2=\\\"23\\\"></line><line x1=\\\"4.22\\\" y1=\\\"4.22\\\" x2=\\\"5.64\\\" y2=\\\"5.64\\\"></line><line x1=\\\"18.36\\\" y1=\\\"18.36\\\" x2=\\\"19.78\\\" y2=\\\"19.78\\\"></line><line x1=\\\"1\\\" y1=\\\"12\\\" x2=\\\"3\\\" y2=\\\"12\\\"></line><line x1=\\\"21\\\" y1=\\\"12\\\" x2=\\\"23\\\" y2=\\\"12\\\"></line><line x1=\\\"4.22\\\" y1=\\\"19.78\\\" x2=\\\"5.64\\\" y2=\\\"18.36\\\"></line><line x1=\\\"18.36\\\" y1=\\\"5.64\\\" x2=\\\"19.78\\\" y2=\\\"4.22\\\"></line>\",\n\t\"sunrise\": \"<path d=\\\"M17 18a5 5 0 0 0-10 0\\\"></path><line x1=\\\"12\\\" y1=\\\"2\\\" x2=\\\"12\\\" y2=\\\"9\\\"></line><line x1=\\\"4.22\\\" y1=\\\"10.22\\\" x2=\\\"5.64\\\" y2=\\\"11.64\\\"></line><line x1=\\\"1\\\" y1=\\\"18\\\" x2=\\\"3\\\" y2=\\\"18\\\"></line><line x1=\\\"21\\\" y1=\\\"18\\\" x2=\\\"23\\\" y2=\\\"18\\\"></line><line x1=\\\"18.36\\\" y1=\\\"11.64\\\" x2=\\\"19.78\\\" y2=\\\"10.22\\\"></line><line x1=\\\"23\\\" y1=\\\"22\\\" x2=\\\"1\\\" y2=\\\"22\\\"></line><polyline points=\\\"8 6 12 2 16 6\\\"></polyline>\",\n\t\"sunset\": \"<path d=\\\"M17 18a5 5 0 0 0-10 0\\\"></path><line x1=\\\"12\\\" y1=\\\"9\\\" x2=\\\"12\\\" y2=\\\"2\\\"></line><line x1=\\\"4.22\\\" y1=\\\"10.22\\\" x2=\\\"5.64\\\" y2=\\\"11.64\\\"></line><line x1=\\\"1\\\" y1=\\\"18\\\" x2=\\\"3\\\" y2=\\\"18\\\"></line><line x1=\\\"21\\\" y1=\\\"18\\\" x2=\\\"23\\\" y2=\\\"18\\\"></line><line x1=\\\"18.36\\\" y1=\\\"11.64\\\" x2=\\\"19.78\\\" y2=\\\"10.22\\\"></line><line x1=\\\"23\\\" y1=\\\"22\\\" x2=\\\"1\\\" y2=\\\"22\\\"></line><polyline points=\\\"16 5 12 9 8 5\\\"></polyline>\",\n\t\"tablet\": \"<rect x=\\\"4\\\" y=\\\"2\\\" width=\\\"16\\\" height=\\\"20\\\" rx=\\\"2\\\" ry=\\\"2\\\" transform=\\\"rotate(180 12 12)\\\"></rect><line x1=\\\"12\\\" y1=\\\"18\\\" x2=\\\"12\\\" y2=\\\"18\\\"></line>\",\n\t\"tag\": \"<path d=\\\"M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z\\\"></path><line x1=\\\"7\\\" y1=\\\"7\\\" x2=\\\"7\\\" y2=\\\"7\\\"></line>\",\n\t\"target\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"6\\\"></circle><circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"2\\\"></circle>\",\n\t\"thermometer\": \"<path d=\\\"M14 14.76V3.5a2.5 2.5 0 0 0-5 0v11.26a4.5 4.5 0 1 0 5 0z\\\"></path>\",\n\t\"thumbs-down\": \"<path d=\\\"M10 15v4a3 3 0 0 0 3 3l4-9V2H5.72a2 2 0 0 0-2 1.7l-1.38 9a2 2 0 0 0 2 2.3zm7-13h2.67A2.31 2.31 0 0 1 22 4v7a2.31 2.31 0 0 1-2.33 2H17\\\"></path>\",\n\t\"thumbs-up\": \"<path d=\\\"M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3\\\"></path>\",\n\t\"toggle-left\": \"<rect x=\\\"1\\\" y=\\\"5\\\" width=\\\"22\\\" height=\\\"14\\\" rx=\\\"7\\\" ry=\\\"7\\\"></rect><circle cx=\\\"8\\\" cy=\\\"12\\\" r=\\\"3\\\"></circle>\",\n\t\"toggle-right\": \"<rect x=\\\"1\\\" y=\\\"5\\\" width=\\\"22\\\" height=\\\"14\\\" rx=\\\"7\\\" ry=\\\"7\\\"></rect><circle cx=\\\"16\\\" cy=\\\"12\\\" r=\\\"3\\\"></circle>\",\n\t\"trash-2\": \"<polyline points=\\\"3 6 5 6 21 6\\\"></polyline><path d=\\\"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2\\\"></path><line x1=\\\"10\\\" y1=\\\"11\\\" x2=\\\"10\\\" y2=\\\"17\\\"></line><line x1=\\\"14\\\" y1=\\\"11\\\" x2=\\\"14\\\" y2=\\\"17\\\"></line>\",\n\t\"trash\": \"<polyline points=\\\"3 6 5 6 21 6\\\"></polyline><path d=\\\"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2\\\"></path>\",\n\t\"trending-down\": \"<polyline points=\\\"23 18 13.5 8.5 8.5 13.5 1 6\\\"></polyline><polyline points=\\\"17 18 23 18 23 12\\\"></polyline>\",\n\t\"trending-up\": \"<polyline points=\\\"23 6 13.5 15.5 8.5 10.5 1 18\\\"></polyline><polyline points=\\\"17 6 23 6 23 12\\\"></polyline>\",\n\t\"triangle\": \"<path d=\\\"M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z\\\"></path>\",\n\t\"twitter\": \"<path d=\\\"M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z\\\"></path>\",\n\t\"type\": \"<polyline points=\\\"4 7 4 4 20 4 20 7\\\"></polyline><line x1=\\\"9\\\" y1=\\\"20\\\" x2=\\\"15\\\" y2=\\\"20\\\"></line><line x1=\\\"12\\\" y1=\\\"4\\\" x2=\\\"12\\\" y2=\\\"20\\\"></line>\",\n\t\"umbrella\": \"<path d=\\\"M23 12a11.05 11.05 0 0 0-22 0zm-5 7a3 3 0 0 1-6 0v-7\\\"></path>\",\n\t\"unlock\": \"<rect x=\\\"3\\\" y=\\\"11\\\" width=\\\"18\\\" height=\\\"11\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><path d=\\\"M7 11V7a5 5 0 0 1 9.9-1\\\"></path>\",\n\t\"upload-cloud\": \"<polyline points=\\\"16 16 12 12 8 16\\\"></polyline><line x1=\\\"12\\\" y1=\\\"12\\\" x2=\\\"12\\\" y2=\\\"21\\\"></line><path d=\\\"M20.39 18.39A5 5 0 0 0 18 9h-1.26A8 8 0 1 0 3 16.3\\\"></path><polyline points=\\\"16 16 12 12 8 16\\\"></polyline>\",\n\t\"upload\": \"<path d=\\\"M3 17v3a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-3\\\"></path><polyline points=\\\"16 6 12 2 8 6\\\"></polyline><line x1=\\\"12\\\" y1=\\\"2\\\" x2=\\\"12\\\" y2=\\\"16\\\"></line>\",\n\t\"user-check\": \"<path d=\\\"M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\\\"></path><circle cx=\\\"8.5\\\" cy=\\\"7\\\" r=\\\"4\\\"></circle><polyline points=\\\"17 11 19 13 23 9\\\"></polyline>\",\n\t\"user-minus\": \"<path d=\\\"M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\\\"></path><circle cx=\\\"8.5\\\" cy=\\\"7\\\" r=\\\"4\\\"></circle><line x1=\\\"23\\\" y1=\\\"11\\\" x2=\\\"17\\\" y2=\\\"11\\\"></line>\",\n\t\"user-plus\": \"<path d=\\\"M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\\\"></path><circle cx=\\\"8.5\\\" cy=\\\"7\\\" r=\\\"4\\\"></circle><line x1=\\\"20\\\" y1=\\\"8\\\" x2=\\\"20\\\" y2=\\\"14\\\"></line><line x1=\\\"23\\\" y1=\\\"11\\\" x2=\\\"17\\\" y2=\\\"11\\\"></line>\",\n\t\"user-x\": \"<path d=\\\"M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\\\"></path><circle cx=\\\"8.5\\\" cy=\\\"7\\\" r=\\\"4\\\"></circle><line x1=\\\"18\\\" y1=\\\"8\\\" x2=\\\"23\\\" y2=\\\"13\\\"></line><line x1=\\\"23\\\" y1=\\\"8\\\" x2=\\\"18\\\" y2=\\\"13\\\"></line>\",\n\t\"user\": \"<path d=\\\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\\\"></path><circle cx=\\\"12\\\" cy=\\\"7\\\" r=\\\"4\\\"></circle>\",\n\t\"users\": \"<path d=\\\"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\\\"></path><circle cx=\\\"9\\\" cy=\\\"7\\\" r=\\\"4\\\"></circle><path d=\\\"M23 21v-2a4 4 0 0 0-3-3.87\\\"></path><path d=\\\"M16 3.13a4 4 0 0 1 0 7.75\\\"></path>\",\n\t\"video-off\": \"<path d=\\\"M16 16v1a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V7a2 2 0 0 1 2-2h2m5.66 0H14a2 2 0 0 1 2 2v3.34l1 1L23 7v10\\\"></path><line x1=\\\"1\\\" y1=\\\"1\\\" x2=\\\"23\\\" y2=\\\"23\\\"></line>\",\n\t\"video\": \"<polygon points=\\\"23 7 16 12 23 17 23 7\\\"></polygon><rect x=\\\"1\\\" y=\\\"5\\\" width=\\\"15\\\" height=\\\"14\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect>\",\n\t\"voicemail\": \"<circle cx=\\\"5.5\\\" cy=\\\"11.5\\\" r=\\\"4.5\\\"></circle><circle cx=\\\"18.5\\\" cy=\\\"11.5\\\" r=\\\"4.5\\\"></circle><line x1=\\\"5.5\\\" y1=\\\"16\\\" x2=\\\"18.5\\\" y2=\\\"16\\\"></line>\",\n\t\"volume-1\": \"<polygon points=\\\"11 5 6 9 2 9 2 15 6 15 11 19 11 5\\\"></polygon><path d=\\\"M15.54 8.46a5 5 0 0 1 0 7.07\\\"></path>\",\n\t\"volume-2\": \"<polygon points=\\\"11 5 6 9 2 9 2 15 6 15 11 19 11 5\\\"></polygon><path d=\\\"M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07\\\"></path>\",\n\t\"volume-x\": \"<polygon points=\\\"11 5 6 9 2 9 2 15 6 15 11 19 11 5\\\"></polygon><line x1=\\\"23\\\" y1=\\\"9\\\" x2=\\\"17\\\" y2=\\\"15\\\"></line><line x1=\\\"17\\\" y1=\\\"9\\\" x2=\\\"23\\\" y2=\\\"15\\\"></line>\",\n\t\"volume\": \"<polygon points=\\\"11 5 6 9 2 9 2 15 6 15 11 19 11 5\\\"></polygon>\",\n\t\"watch\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"7\\\"></circle><polyline points=\\\"12 9 12 12 13.5 13.5\\\"></polyline><path d=\\\"M16.51 17.35l-.35 3.83a2 2 0 0 1-2 1.82H9.83a2 2 0 0 1-2-1.82l-.35-3.83m.01-10.7l.35-3.83A2 2 0 0 1 9.83 1h4.35a2 2 0 0 1 2 1.82l.35 3.83\\\"></path>\",\n\t\"wifi\": \"<path d=\\\"M5 11.55a11 11 0 0 1 14.08 0M1.41 8a16 16 0 0 1 21.17 0M8.52 15.11a6 6 0 0 1 6.95 0\\\"></path><line x1=\\\"12\\\" y1=\\\"19\\\" x2=\\\"12\\\" y2=\\\"19\\\"></line>\",\n\t\"wind\": \"<path d=\\\"M9.59 4.59A2 2 0 1 1 11 8H2m10.59 11.41A2 2 0 1 0 14 16H2m15.73-8.27A2.5 2.5 0 1 1 19.5 12H2\\\"></path>\",\n\t\"x-circle\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><line x1=\\\"15\\\" y1=\\\"9\\\" x2=\\\"9\\\" y2=\\\"15\\\"></line><line x1=\\\"9\\\" y1=\\\"9\\\" x2=\\\"15\\\" y2=\\\"15\\\"></line>\",\n\t\"x-square\": \"<rect x=\\\"3\\\" y=\\\"3\\\" width=\\\"18\\\" height=\\\"18\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><line x1=\\\"9\\\" y1=\\\"9\\\" x2=\\\"15\\\" y2=\\\"15\\\"></line><line x1=\\\"15\\\" y1=\\\"9\\\" x2=\\\"9\\\" y2=\\\"15\\\"></line>\",\n\t\"x\": \"<line x1=\\\"18\\\" y1=\\\"6\\\" x2=\\\"6\\\" y2=\\\"18\\\"></line><line x1=\\\"6\\\" y1=\\\"6\\\" x2=\\\"18\\\" y2=\\\"18\\\"></line>\",\n\t\"zap\": \"<polygon points=\\\"13 2 3 14 12 14 11 22 21 10 12 10 13 2\\\"></polygon>\",\n\t\"zoom-in\": \"<circle cx=\\\"11\\\" cy=\\\"11\\\" r=\\\"8\\\"></circle><line x1=\\\"21\\\" y1=\\\"21\\\" x2=\\\"16.65\\\" y2=\\\"16.65\\\"></line><line x1=\\\"11\\\" y1=\\\"8\\\" x2=\\\"11\\\" y2=\\\"14\\\"></line><line x1=\\\"8\\\" y1=\\\"11\\\" x2=\\\"14\\\" y2=\\\"11\\\"></line>\",\n\t\"zoom-out\": \"<circle cx=\\\"11\\\" cy=\\\"11\\\" r=\\\"8\\\"></circle><line x1=\\\"21\\\" y1=\\\"21\\\" x2=\\\"16.65\\\" y2=\\\"16.65\\\"></line><line x1=\\\"8\\\" y1=\\\"11\\\" x2=\\\"14\\\" y2=\\\"11\\\"></line>\"\n};\n\n/***/ }),\n/* 27 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\nvar $at  = __webpack_require__(49)(true);\n\n// 21.1.3.27 String.prototype[@@iterator]()\n__webpack_require__(19)(String, 'String', function(iterated){\n  this._t = String(iterated); // target\n  this._i = 0;                // next index\n// ********.1 %StringIteratorPrototype%.next()\n}, function(){\n  var O     = this._t\n    , index = this._i\n    , point;\n  if(index >= O.length)return {value: undefined, done: true};\n  point = $at(O, index);\n  this._i += point.length;\n  return {value: point, done: false};\n});\n\n/***/ }),\n/* 28 */\n/***/ (function(module, exports, __webpack_require__) {\n\nmodule.exports = !__webpack_require__(4) && !__webpack_require__(11)(function(){\n  return Object.defineProperty(__webpack_require__(29)('div'), 'a', {get: function(){ return 7; }}).a != 7;\n});\n\n/***/ }),\n/* 29 */\n/***/ (function(module, exports, __webpack_require__) {\n\nvar isObject = __webpack_require__(3)\n  , document = __webpack_require__(1).document\n  // in old IE typeof document.createElement is 'object'\n  , is = isObject(document) && isObject(document.createElement);\nmodule.exports = function(it){\n  return is ? document.createElement(it) : {};\n};\n\n/***/ }),\n/* 30 */\n/***/ (function(module, exports, __webpack_require__) {\n\n// 7.1.1 ToPrimitive(input [, PreferredType])\nvar isObject = __webpack_require__(3);\n// instead of the ES6 spec version, we didn't implement @@toPrimitive case\n// and the second argument - flag - preferred type is a string\nmodule.exports = function(it, S){\n  if(!isObject(it))return it;\n  var fn, val;\n  if(S && typeof (fn = it.toString) == 'function' && !isObject(val = fn.call(it)))return val;\n  if(typeof (fn = it.valueOf) == 'function' && !isObject(val = fn.call(it)))return val;\n  if(!S && typeof (fn = it.toString) == 'function' && !isObject(val = fn.call(it)))return val;\n  throw TypeError(\"Can't convert object to primitive value\");\n};\n\n/***/ }),\n/* 31 */\n/***/ (function(module, exports, __webpack_require__) {\n\n// 19.1.2.2 / 15.2.3.5 Object.create(O [, Properties])\nvar anObject    = __webpack_require__(8)\n  , dPs         = __webpack_require__(53)\n  , enumBugKeys = __webpack_require__(36)\n  , IE_PROTO    = __webpack_require__(21)('IE_PROTO')\n  , Empty       = function(){ /* empty */ }\n  , PROTOTYPE   = 'prototype';\n\n// Create object with fake `null` prototype: use iframe Object with cleared prototype\nvar createDict = function(){\n  // Thrash, waste and sodomy: IE GC bug\n  var iframe = __webpack_require__(29)('iframe')\n    , i      = enumBugKeys.length\n    , lt     = '<'\n    , gt     = '>'\n    , iframeDocument;\n  iframe.style.display = 'none';\n  __webpack_require__(57).appendChild(iframe);\n  iframe.src = 'javascript:'; // eslint-disable-line no-script-url\n  // createDict = iframe.contentWindow.Object;\n  // html.removeChild(iframe);\n  iframeDocument = iframe.contentWindow.document;\n  iframeDocument.open();\n  iframeDocument.write(lt + 'script' + gt + 'document.F=Object' + lt + '/script' + gt);\n  iframeDocument.close();\n  createDict = iframeDocument.F;\n  while(i--)delete createDict[PROTOTYPE][enumBugKeys[i]];\n  return createDict();\n};\n\nmodule.exports = Object.create || function create(O, Properties){\n  var result;\n  if(O !== null){\n    Empty[PROTOTYPE] = anObject(O);\n    result = new Empty;\n    Empty[PROTOTYPE] = null;\n    // add \"__proto__\" for Object.getPrototypeOf polyfill\n    result[IE_PROTO] = O;\n  } else result = createDict();\n  return Properties === undefined ? result : dPs(result, Properties);\n};\n\n\n/***/ }),\n/* 32 */\n/***/ (function(module, exports, __webpack_require__) {\n\n// ********* / ********* Object.keys(O)\nvar $keys       = __webpack_require__(54)\n  , enumBugKeys = __webpack_require__(36);\n\nmodule.exports = Object.keys || function keys(O){\n  return $keys(O, enumBugKeys);\n};\n\n/***/ }),\n/* 33 */\n/***/ (function(module, exports, __webpack_require__) {\n\n// fallback for non-array-like ES3 and non-enumerable old V8 strings\nvar cof = __webpack_require__(34);\nmodule.exports = Object('z').propertyIsEnumerable(0) ? Object : function(it){\n  return cof(it) == 'String' ? it.split('') : Object(it);\n};\n\n/***/ }),\n/* 34 */\n/***/ (function(module, exports) {\n\nvar toString = {}.toString;\n\nmodule.exports = function(it){\n  return toString.call(it).slice(8, -1);\n};\n\n/***/ }),\n/* 35 */\n/***/ (function(module, exports, __webpack_require__) {\n\nvar global = __webpack_require__(1)\n  , SHARED = '__core-js_shared__'\n  , store  = global[SHARED] || (global[SHARED] = {});\nmodule.exports = function(key){\n  return store[key] || (store[key] = {});\n};\n\n/***/ }),\n/* 36 */\n/***/ (function(module, exports) {\n\n// IE 8- don't enum bug keys\nmodule.exports = (\n  'constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf'\n).split(',');\n\n/***/ }),\n/* 37 */\n/***/ (function(module, exports, __webpack_require__) {\n\n// call something on iterator step with safe closing on error\nvar anObject = __webpack_require__(8);\nmodule.exports = function(iterator, fn, value, entries){\n  try {\n    return entries ? fn(anObject(value)[0], value[1]) : fn(value);\n  // 7.4.6 IteratorClose(iterator, completion)\n  } catch(e){\n    var ret = iterator['return'];\n    if(ret !== undefined)anObject(ret.call(iterator));\n    throw e;\n  }\n};\n\n/***/ }),\n/* 38 */\n/***/ (function(module, exports, __webpack_require__) {\n\n// check on default Array iterator\nvar Iterators  = __webpack_require__(13)\n  , ITERATOR   = __webpack_require__(0)('iterator')\n  , ArrayProto = Array.prototype;\n\nmodule.exports = function(it){\n  return it !== undefined && (Iterators.Array === it || ArrayProto[ITERATOR] === it);\n};\n\n/***/ }),\n/* 39 */\n/***/ (function(module, exports, __webpack_require__) {\n\nvar classof   = __webpack_require__(24)\n  , ITERATOR  = __webpack_require__(0)('iterator')\n  , Iterators = __webpack_require__(13);\nmodule.exports = __webpack_require__(6).getIteratorMethod = function(it){\n  if(it != undefined)return it[ITERATOR]\n    || it['@@iterator']\n    || Iterators[classof(it)];\n};\n\n/***/ }),\n/* 40 */\n/***/ (function(module, exports, __webpack_require__) {\n\nvar ITERATOR     = __webpack_require__(0)('iterator')\n  , SAFE_CLOSING = false;\n\ntry {\n  var riter = [7][ITERATOR]();\n  riter['return'] = function(){ SAFE_CLOSING = true; };\n  Array.from(riter, function(){ throw 2; });\n} catch(e){ /* empty */ }\n\nmodule.exports = function(exec, skipClosing){\n  if(!skipClosing && !SAFE_CLOSING)return false;\n  var safe = false;\n  try {\n    var arr  = [7]\n      , iter = arr[ITERATOR]();\n    iter.next = function(){ return {done: safe = true}; };\n    arr[ITERATOR] = function(){ return iter; };\n    exec(arr);\n  } catch(e){ /* empty */ }\n  return safe;\n};\n\n/***/ }),\n/* 41 */\n/***/ (function(module, exports) {\n\nexports.f = {}.propertyIsEnumerable;\n\n/***/ }),\n/* 42 */\n/***/ (function(module, exports) {\n\nmodule.exports = function(done, value){\n  return {value: value, done: !!done};\n};\n\n/***/ }),\n/* 43 */\n/***/ (function(module, exports, __webpack_require__) {\n\nvar redefine = __webpack_require__(9);\nmodule.exports = function(target, src, safe){\n  for(var key in src)redefine(target, key, src[key], safe);\n  return target;\n};\n\n/***/ }),\n/* 44 */\n/***/ (function(module, exports) {\n\nmodule.exports = function(it, Constructor, name, forbiddenField){\n  if(!(it instanceof Constructor) || (forbiddenField !== undefined && forbiddenField in it)){\n    throw TypeError(name + ': incorrect invocation!');\n  } return it;\n};\n\n/***/ }),\n/* 45 */\n/***/ (function(module, exports, __webpack_require__) {\n\nvar META     = __webpack_require__(16)('meta')\n  , isObject = __webpack_require__(3)\n  , has      = __webpack_require__(5)\n  , setDesc  = __webpack_require__(2).f\n  , id       = 0;\nvar isExtensible = Object.isExtensible || function(){\n  return true;\n};\nvar FREEZE = !__webpack_require__(11)(function(){\n  return isExtensible(Object.preventExtensions({}));\n});\nvar setMeta = function(it){\n  setDesc(it, META, {value: {\n    i: 'O' + ++id, // object ID\n    w: {}          // weak collections IDs\n  }});\n};\nvar fastKey = function(it, create){\n  // return primitive with prefix\n  if(!isObject(it))return typeof it == 'symbol' ? it : (typeof it == 'string' ? 'S' : 'P') + it;\n  if(!has(it, META)){\n    // can't set metadata to uncaught frozen object\n    if(!isExtensible(it))return 'F';\n    // not necessary to add metadata\n    if(!create)return 'E';\n    // add missing metadata\n    setMeta(it);\n  // return object ID\n  } return it[META].i;\n};\nvar getWeak = function(it, create){\n  if(!has(it, META)){\n    // can't set metadata to uncaught frozen object\n    if(!isExtensible(it))return true;\n    // not necessary to add metadata\n    if(!create)return false;\n    // add missing metadata\n    setMeta(it);\n  // return hash weak collections IDs\n  } return it[META].w;\n};\n// add metadata on freeze-family methods calling\nvar onFreeze = function(it){\n  if(FREEZE && meta.NEED && isExtensible(it) && !has(it, META))setMeta(it);\n  return it;\n};\nvar meta = module.exports = {\n  KEY:      META,\n  NEED:     false,\n  fastKey:  fastKey,\n  getWeak:  getWeak,\n  onFreeze: onFreeze\n};\n\n/***/ }),\n/* 46 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = toSvg;\n\nvar _icons = __webpack_require__(26);\n\nvar _icons2 = _interopRequireDefault(_icons);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar DEFAULT_OPTIONS = {\n  xmlns: 'http://www.w3.org/2000/svg',\n  width: 24,\n  height: 24,\n  viewBox: '0 0 24 24',\n  fill: 'none',\n  stroke: 'currentColor',\n  'stroke-width': 2,\n  'stroke-linecap': 'round',\n  'stroke-linejoin': 'round'\n};\n\n/**\n * Create an SVG string.\n * @param {string} key - Icon name.\n * @param {Object} options\n * @returns {string}\n */\n/**\n * @file Implements `toSvg` function.\n */\n\nfunction toSvg(key) {\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n  if (!key) {\n    throw new Error('The required `key` (icon name) parameter is missing.');\n  }\n\n  if (!_icons2.default[key]) {\n    throw new Error('No icon matching \\'' + key + '\\'. See the complete list of icons at https://feathericons.com');\n  }\n\n  var combinedOptions = Object.assign({}, DEFAULT_OPTIONS, options);\n\n  combinedOptions.class = addDefaultClassNames(combinedOptions.class, key);\n\n  var attributes = optionsToAtrributes(combinedOptions);\n\n  return '<svg ' + attributes + '>' + _icons2.default[key] + '</svg>';\n}\n\n/**\n * Add default class names.\n * @param {string} classNames - One or more class names seperated by spaces.\n * @param {string} key - Icon name.\n * @returns {string}\n */\nfunction addDefaultClassNames(classNames, key) {\n  // convert class names string into an array\n  var classNamesArray = classNames ? classNames.trim().split(/\\s+/) : [];\n\n  // use Set to avoid duplicate class names\n  var classNamesSet = new Set(classNamesArray);\n\n  // add default class names\n  classNamesSet.add('feather').add('feather-' + key);\n\n  return Array.from(classNamesSet).join(' ');\n}\n\n/**\n * Convert options object to string of html attributes.\n * @param {Object} options\n * @returns {string}\n */\nfunction optionsToAtrributes(options) {\n  var attributes = [];\n\n  Object.keys(options).forEach(function (key) {\n    attributes.push(key + '=\"' + options[key] + '\"');\n  });\n\n  return attributes.join(' ');\n}\n\n/***/ }),\n/* 47 */\n/***/ (function(module, exports, __webpack_require__) {\n\n__webpack_require__(48);\n__webpack_require__(61);\n__webpack_require__(65);\nmodule.exports = __webpack_require__(80);\n\n\n/***/ }),\n/* 48 */\n/***/ (function(module, exports, __webpack_require__) {\n\n__webpack_require__(27);\n__webpack_require__(59);\nmodule.exports = __webpack_require__(6).Array.from;\n\n/***/ }),\n/* 49 */\n/***/ (function(module, exports, __webpack_require__) {\n\nvar toInteger = __webpack_require__(18)\n  , defined   = __webpack_require__(14);\n// true  -> String#at\n// false -> String#codePointAt\nmodule.exports = function(TO_STRING){\n  return function(that, pos){\n    var s = String(defined(that))\n      , i = toInteger(pos)\n      , l = s.length\n      , a, b;\n    if(i < 0 || i >= l)return TO_STRING ? '' : undefined;\n    a = s.charCodeAt(i);\n    return a < 0xd800 || a > 0xdbff || i + 1 === l || (b = s.charCodeAt(i + 1)) < 0xdc00 || b > 0xdfff\n      ? TO_STRING ? s.charAt(i) : a\n      : TO_STRING ? s.slice(i, i + 2) : (a - 0xd800 << 10) + (b - 0xdc00) + 0x10000;\n  };\n};\n\n/***/ }),\n/* 50 */\n/***/ (function(module, exports) {\n\nmodule.exports = false;\n\n/***/ }),\n/* 51 */\n/***/ (function(module, exports) {\n\nmodule.exports = function(it){\n  if(typeof it != 'function')throw TypeError(it + ' is not a function!');\n  return it;\n};\n\n/***/ }),\n/* 52 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\nvar create         = __webpack_require__(31)\n  , descriptor     = __webpack_require__(15)\n  , setToStringTag = __webpack_require__(22)\n  , IteratorPrototype = {};\n\n// ********.1 %IteratorPrototype%[@@iterator]()\n__webpack_require__(7)(IteratorPrototype, __webpack_require__(0)('iterator'), function(){ return this; });\n\nmodule.exports = function(Constructor, NAME, next){\n  Constructor.prototype = create(IteratorPrototype, {next: descriptor(1, next)});\n  setToStringTag(Constructor, NAME + ' Iterator');\n};\n\n/***/ }),\n/* 53 */\n/***/ (function(module, exports, __webpack_require__) {\n\nvar dP       = __webpack_require__(2)\n  , anObject = __webpack_require__(8)\n  , getKeys  = __webpack_require__(32);\n\nmodule.exports = __webpack_require__(4) ? Object.defineProperties : function defineProperties(O, Properties){\n  anObject(O);\n  var keys   = getKeys(Properties)\n    , length = keys.length\n    , i = 0\n    , P;\n  while(length > i)dP.f(O, P = keys[i++], Properties[P]);\n  return O;\n};\n\n/***/ }),\n/* 54 */\n/***/ (function(module, exports, __webpack_require__) {\n\nvar has          = __webpack_require__(5)\n  , toIObject    = __webpack_require__(17)\n  , arrayIndexOf = __webpack_require__(55)(false)\n  , IE_PROTO     = __webpack_require__(21)('IE_PROTO');\n\nmodule.exports = function(object, names){\n  var O      = toIObject(object)\n    , i      = 0\n    , result = []\n    , key;\n  for(key in O)if(key != IE_PROTO)has(O, key) && result.push(key);\n  // Don't enum bug & hidden keys\n  while(names.length > i)if(has(O, key = names[i++])){\n    ~arrayIndexOf(result, key) || result.push(key);\n  }\n  return result;\n};\n\n/***/ }),\n/* 55 */\n/***/ (function(module, exports, __webpack_require__) {\n\n// false -> Array#indexOf\n// true  -> Array#includes\nvar toIObject = __webpack_require__(17)\n  , toLength  = __webpack_require__(20)\n  , toIndex   = __webpack_require__(56);\nmodule.exports = function(IS_INCLUDES){\n  return function($this, el, fromIndex){\n    var O      = toIObject($this)\n      , length = toLength(O.length)\n      , index  = toIndex(fromIndex, length)\n      , value;\n    // Array#includes uses SameValueZero equality algorithm\n    if(IS_INCLUDES && el != el)while(length > index){\n      value = O[index++];\n      if(value != value)return true;\n    // Array#toIndex ignores holes, Array#includes - not\n    } else for(;length > index; index++)if(IS_INCLUDES || index in O){\n      if(O[index] === el)return IS_INCLUDES || index || 0;\n    } return !IS_INCLUDES && -1;\n  };\n};\n\n/***/ }),\n/* 56 */\n/***/ (function(module, exports, __webpack_require__) {\n\nvar toInteger = __webpack_require__(18)\n  , max       = Math.max\n  , min       = Math.min;\nmodule.exports = function(index, length){\n  index = toInteger(index);\n  return index < 0 ? max(index + length, 0) : min(index, length);\n};\n\n/***/ }),\n/* 57 */\n/***/ (function(module, exports, __webpack_require__) {\n\nmodule.exports = __webpack_require__(1).document && document.documentElement;\n\n/***/ }),\n/* 58 */\n/***/ (function(module, exports, __webpack_require__) {\n\n// 19.1.2.9 / 15.2.3.2 Object.getPrototypeOf(O)\nvar has         = __webpack_require__(5)\n  , toObject    = __webpack_require__(23)\n  , IE_PROTO    = __webpack_require__(21)('IE_PROTO')\n  , ObjectProto = Object.prototype;\n\nmodule.exports = Object.getPrototypeOf || function(O){\n  O = toObject(O);\n  if(has(O, IE_PROTO))return O[IE_PROTO];\n  if(typeof O.constructor == 'function' && O instanceof O.constructor){\n    return O.constructor.prototype;\n  } return O instanceof Object ? ObjectProto : null;\n};\n\n/***/ }),\n/* 59 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\nvar ctx            = __webpack_require__(12)\n  , $export        = __webpack_require__(10)\n  , toObject       = __webpack_require__(23)\n  , call           = __webpack_require__(37)\n  , isArrayIter    = __webpack_require__(38)\n  , toLength       = __webpack_require__(20)\n  , createProperty = __webpack_require__(60)\n  , getIterFn      = __webpack_require__(39);\n\n$export($export.S + $export.F * !__webpack_require__(40)(function(iter){ Array.from(iter); }), 'Array', {\n  // 22.1.2.1 Array.from(arrayLike, mapfn = undefined, thisArg = undefined)\n  from: function from(arrayLike/*, mapfn = undefined, thisArg = undefined*/){\n    var O       = toObject(arrayLike)\n      , C       = typeof this == 'function' ? this : Array\n      , aLen    = arguments.length\n      , mapfn   = aLen > 1 ? arguments[1] : undefined\n      , mapping = mapfn !== undefined\n      , index   = 0\n      , iterFn  = getIterFn(O)\n      , length, result, step, iterator;\n    if(mapping)mapfn = ctx(mapfn, aLen > 2 ? arguments[2] : undefined, 2);\n    // if object isn't iterable or it's array with default iterator - use simple case\n    if(iterFn != undefined && !(C == Array && isArrayIter(iterFn))){\n      for(iterator = iterFn.call(O), result = new C; !(step = iterator.next()).done; index++){\n        createProperty(result, index, mapping ? call(iterator, mapfn, [step.value, index], true) : step.value);\n      }\n    } else {\n      length = toLength(O.length);\n      for(result = new C(length); length > index; index++){\n        createProperty(result, index, mapping ? mapfn(O[index], index) : O[index]);\n      }\n    }\n    result.length = index;\n    return result;\n  }\n});\n\n\n/***/ }),\n/* 60 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\nvar $defineProperty = __webpack_require__(2)\n  , createDesc      = __webpack_require__(15);\n\nmodule.exports = function(object, index, value){\n  if(index in object)$defineProperty.f(object, index, createDesc(0, value));\n  else object[index] = value;\n};\n\n/***/ }),\n/* 61 */\n/***/ (function(module, exports, __webpack_require__) {\n\n__webpack_require__(62);\nmodule.exports = __webpack_require__(6).Object.assign;\n\n/***/ }),\n/* 62 */\n/***/ (function(module, exports, __webpack_require__) {\n\n// 19.1.3.1 Object.assign(target, source)\nvar $export = __webpack_require__(10);\n\n$export($export.S + $export.F, 'Object', {assign: __webpack_require__(63)});\n\n/***/ }),\n/* 63 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n// 19.1.2.1 Object.assign(target, source, ...)\nvar getKeys  = __webpack_require__(32)\n  , gOPS     = __webpack_require__(64)\n  , pIE      = __webpack_require__(41)\n  , toObject = __webpack_require__(23)\n  , IObject  = __webpack_require__(33)\n  , $assign  = Object.assign;\n\n// should work with symbols and should have deterministic property order (V8 bug)\nmodule.exports = !$assign || __webpack_require__(11)(function(){\n  var A = {}\n    , B = {}\n    , S = Symbol()\n    , K = 'abcdefghijklmnopqrst';\n  A[S] = 7;\n  K.split('').forEach(function(k){ B[k] = k; });\n  return $assign({}, A)[S] != 7 || Object.keys($assign({}, B)).join('') != K;\n}) ? function assign(target, source){ // eslint-disable-line no-unused-vars\n  var T     = toObject(target)\n    , aLen  = arguments.length\n    , index = 1\n    , getSymbols = gOPS.f\n    , isEnum     = pIE.f;\n  while(aLen > index){\n    var S      = IObject(arguments[index++])\n      , keys   = getSymbols ? getKeys(S).concat(getSymbols(S)) : getKeys(S)\n      , length = keys.length\n      , j      = 0\n      , key;\n    while(length > j)if(isEnum.call(S, key = keys[j++]))T[key] = S[key];\n  } return T;\n} : $assign;\n\n/***/ }),\n/* 64 */\n/***/ (function(module, exports) {\n\nexports.f = Object.getOwnPropertySymbols;\n\n/***/ }),\n/* 65 */\n/***/ (function(module, exports, __webpack_require__) {\n\n__webpack_require__(66);\n__webpack_require__(27);\n__webpack_require__(67);\n__webpack_require__(70);\n__webpack_require__(77);\nmodule.exports = __webpack_require__(6).Set;\n\n/***/ }),\n/* 66 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n// ******** Object.prototype.toString()\nvar classof = __webpack_require__(24)\n  , test    = {};\ntest[__webpack_require__(0)('toStringTag')] = 'z';\nif(test + '' != '[object z]'){\n  __webpack_require__(9)(Object.prototype, 'toString', function toString(){\n    return '[object ' + classof(this) + ']';\n  }, true);\n}\n\n/***/ }),\n/* 67 */\n/***/ (function(module, exports, __webpack_require__) {\n\nvar $iterators    = __webpack_require__(68)\n  , redefine      = __webpack_require__(9)\n  , global        = __webpack_require__(1)\n  , hide          = __webpack_require__(7)\n  , Iterators     = __webpack_require__(13)\n  , wks           = __webpack_require__(0)\n  , ITERATOR      = wks('iterator')\n  , TO_STRING_TAG = wks('toStringTag')\n  , ArrayValues   = Iterators.Array;\n\nfor(var collections = ['NodeList', 'DOMTokenList', 'MediaList', 'StyleSheetList', 'CSSRuleList'], i = 0; i < 5; i++){\n  var NAME       = collections[i]\n    , Collection = global[NAME]\n    , proto      = Collection && Collection.prototype\n    , key;\n  if(proto){\n    if(!proto[ITERATOR])hide(proto, ITERATOR, ArrayValues);\n    if(!proto[TO_STRING_TAG])hide(proto, TO_STRING_TAG, NAME);\n    Iterators[NAME] = ArrayValues;\n    for(key in $iterators)if(!proto[key])redefine(proto, key, $iterators[key], true);\n  }\n}\n\n/***/ }),\n/* 68 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\nvar addToUnscopables = __webpack_require__(69)\n  , step             = __webpack_require__(42)\n  , Iterators        = __webpack_require__(13)\n  , toIObject        = __webpack_require__(17);\n\n// 22.1.3.4 Array.prototype.entries()\n// 22.1.3.13 Array.prototype.keys()\n// 22.1.3.29 Array.prototype.values()\n// 22.1.3.30 Array.prototype[@@iterator]()\nmodule.exports = __webpack_require__(19)(Array, 'Array', function(iterated, kind){\n  this._t = toIObject(iterated); // target\n  this._i = 0;                   // next index\n  this._k = kind;                // kind\n// 22.1.5.2.1 %ArrayIteratorPrototype%.next()\n}, function(){\n  var O     = this._t\n    , kind  = this._k\n    , index = this._i++;\n  if(!O || index >= O.length){\n    this._t = undefined;\n    return step(1);\n  }\n  if(kind == 'keys'  )return step(0, index);\n  if(kind == 'values')return step(0, O[index]);\n  return step(0, [index, O[index]]);\n}, 'values');\n\n// argumentsList[@@iterator] is %ArrayProto_values% (9.4.4.6, 9.4.4.7)\nIterators.Arguments = Iterators.Array;\n\naddToUnscopables('keys');\naddToUnscopables('values');\naddToUnscopables('entries');\n\n/***/ }),\n/* 69 */\n/***/ (function(module, exports, __webpack_require__) {\n\n// 22.1.3.31 Array.prototype[@@unscopables]\nvar UNSCOPABLES = __webpack_require__(0)('unscopables')\n  , ArrayProto  = Array.prototype;\nif(ArrayProto[UNSCOPABLES] == undefined)__webpack_require__(7)(ArrayProto, UNSCOPABLES, {});\nmodule.exports = function(key){\n  ArrayProto[UNSCOPABLES][key] = true;\n};\n\n/***/ }),\n/* 70 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\nvar strong = __webpack_require__(71);\n\n// 23.2 Set Objects\nmodule.exports = __webpack_require__(73)('Set', function(get){\n  return function Set(){ return get(this, arguments.length > 0 ? arguments[0] : undefined); };\n}, {\n  // 23.2.3.1 Set.prototype.add(value)\n  add: function add(value){\n    return strong.def(this, value = value === 0 ? 0 : value, value);\n  }\n}, strong);\n\n/***/ }),\n/* 71 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\nvar dP          = __webpack_require__(2).f\n  , create      = __webpack_require__(31)\n  , redefineAll = __webpack_require__(43)\n  , ctx         = __webpack_require__(12)\n  , anInstance  = __webpack_require__(44)\n  , defined     = __webpack_require__(14)\n  , forOf       = __webpack_require__(25)\n  , $iterDefine = __webpack_require__(19)\n  , step        = __webpack_require__(42)\n  , setSpecies  = __webpack_require__(72)\n  , DESCRIPTORS = __webpack_require__(4)\n  , fastKey     = __webpack_require__(45).fastKey\n  , SIZE        = DESCRIPTORS ? '_s' : 'size';\n\nvar getEntry = function(that, key){\n  // fast case\n  var index = fastKey(key), entry;\n  if(index !== 'F')return that._i[index];\n  // frozen object case\n  for(entry = that._f; entry; entry = entry.n){\n    if(entry.k == key)return entry;\n  }\n};\n\nmodule.exports = {\n  getConstructor: function(wrapper, NAME, IS_MAP, ADDER){\n    var C = wrapper(function(that, iterable){\n      anInstance(that, C, NAME, '_i');\n      that._i = create(null); // index\n      that._f = undefined;    // first entry\n      that._l = undefined;    // last entry\n      that[SIZE] = 0;         // size\n      if(iterable != undefined)forOf(iterable, IS_MAP, that[ADDER], that);\n    });\n    redefineAll(C.prototype, {\n      // ******** Map.prototype.clear()\n      // ******** Set.prototype.clear()\n      clear: function clear(){\n        for(var that = this, data = that._i, entry = that._f; entry; entry = entry.n){\n          entry.r = true;\n          if(entry.p)entry.p = entry.p.n = undefined;\n          delete data[entry.i];\n        }\n        that._f = that._l = undefined;\n        that[SIZE] = 0;\n      },\n      // 23.1.3.3 Map.prototype.delete(key)\n      // 23.2.3.4 Set.prototype.delete(value)\n      'delete': function(key){\n        var that  = this\n          , entry = getEntry(that, key);\n        if(entry){\n          var next = entry.n\n            , prev = entry.p;\n          delete that._i[entry.i];\n          entry.r = true;\n          if(prev)prev.n = next;\n          if(next)next.p = prev;\n          if(that._f == entry)that._f = next;\n          if(that._l == entry)that._l = prev;\n          that[SIZE]--;\n        } return !!entry;\n      },\n      // 23.2.3.6 Set.prototype.forEach(callbackfn, thisArg = undefined)\n      // 23.1.3.5 Map.prototype.forEach(callbackfn, thisArg = undefined)\n      forEach: function forEach(callbackfn /*, that = undefined */){\n        anInstance(this, C, 'forEach');\n        var f = ctx(callbackfn, arguments.length > 1 ? arguments[1] : undefined, 3)\n          , entry;\n        while(entry = entry ? entry.n : this._f){\n          f(entry.v, entry.k, this);\n          // revert to the last existing entry\n          while(entry && entry.r)entry = entry.p;\n        }\n      },\n      // ******** Map.prototype.has(key)\n      // ******** Set.prototype.has(value)\n      has: function has(key){\n        return !!getEntry(this, key);\n      }\n    });\n    if(DESCRIPTORS)dP(C.prototype, 'size', {\n      get: function(){\n        return defined(this[SIZE]);\n      }\n    });\n    return C;\n  },\n  def: function(that, key, value){\n    var entry = getEntry(that, key)\n      , prev, index;\n    // change existing entry\n    if(entry){\n      entry.v = value;\n    // create new entry\n    } else {\n      that._l = entry = {\n        i: index = fastKey(key, true), // <- index\n        k: key,                        // <- key\n        v: value,                      // <- value\n        p: prev = that._l,             // <- previous entry\n        n: undefined,                  // <- next entry\n        r: false                       // <- removed\n      };\n      if(!that._f)that._f = entry;\n      if(prev)prev.n = entry;\n      that[SIZE]++;\n      // add to index\n      if(index !== 'F')that._i[index] = entry;\n    } return that;\n  },\n  getEntry: getEntry,\n  setStrong: function(C, NAME, IS_MAP){\n    // add .keys, .values, .entries, [@@iterator]\n    // 23.1.3.4, 23.1.3.8, ********1, ********2, 23.2.3.5, 23.2.3.8, 23.2.3.10, 23.2.3.11\n    $iterDefine(C, NAME, function(iterated, kind){\n      this._t = iterated;  // target\n      this._k = kind;      // kind\n      this._l = undefined; // previous\n    }, function(){\n      var that  = this\n        , kind  = that._k\n        , entry = that._l;\n      // revert to the last existing entry\n      while(entry && entry.r)entry = entry.p;\n      // get next entry\n      if(!that._t || !(that._l = entry = entry ? entry.n : that._t._f)){\n        // or finish the iteration\n        that._t = undefined;\n        return step(1);\n      }\n      // return step by kind\n      if(kind == 'keys'  )return step(0, entry.k);\n      if(kind == 'values')return step(0, entry.v);\n      return step(0, [entry.k, entry.v]);\n    }, IS_MAP ? 'entries' : 'values' , !IS_MAP, true);\n\n    // add [@@species], ********, ********\n    setSpecies(NAME);\n  }\n};\n\n/***/ }),\n/* 72 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\nvar global      = __webpack_require__(1)\n  , dP          = __webpack_require__(2)\n  , DESCRIPTORS = __webpack_require__(4)\n  , SPECIES     = __webpack_require__(0)('species');\n\nmodule.exports = function(KEY){\n  var C = global[KEY];\n  if(DESCRIPTORS && C && !C[SPECIES])dP.f(C, SPECIES, {\n    configurable: true,\n    get: function(){ return this; }\n  });\n};\n\n/***/ }),\n/* 73 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\nvar global            = __webpack_require__(1)\n  , $export           = __webpack_require__(10)\n  , redefine          = __webpack_require__(9)\n  , redefineAll       = __webpack_require__(43)\n  , meta              = __webpack_require__(45)\n  , forOf             = __webpack_require__(25)\n  , anInstance        = __webpack_require__(44)\n  , isObject          = __webpack_require__(3)\n  , fails             = __webpack_require__(11)\n  , $iterDetect       = __webpack_require__(40)\n  , setToStringTag    = __webpack_require__(22)\n  , inheritIfRequired = __webpack_require__(74);\n\nmodule.exports = function(NAME, wrapper, methods, common, IS_MAP, IS_WEAK){\n  var Base  = global[NAME]\n    , C     = Base\n    , ADDER = IS_MAP ? 'set' : 'add'\n    , proto = C && C.prototype\n    , O     = {};\n  var fixMethod = function(KEY){\n    var fn = proto[KEY];\n    redefine(proto, KEY,\n      KEY == 'delete' ? function(a){\n        return IS_WEAK && !isObject(a) ? false : fn.call(this, a === 0 ? 0 : a);\n      } : KEY == 'has' ? function has(a){\n        return IS_WEAK && !isObject(a) ? false : fn.call(this, a === 0 ? 0 : a);\n      } : KEY == 'get' ? function get(a){\n        return IS_WEAK && !isObject(a) ? undefined : fn.call(this, a === 0 ? 0 : a);\n      } : KEY == 'add' ? function add(a){ fn.call(this, a === 0 ? 0 : a); return this; }\n        : function set(a, b){ fn.call(this, a === 0 ? 0 : a, b); return this; }\n    );\n  };\n  if(typeof C != 'function' || !(IS_WEAK || proto.forEach && !fails(function(){\n    new C().entries().next();\n  }))){\n    // create collection constructor\n    C = common.getConstructor(wrapper, NAME, IS_MAP, ADDER);\n    redefineAll(C.prototype, methods);\n    meta.NEED = true;\n  } else {\n    var instance             = new C\n      // early implementations not supports chaining\n      , HASNT_CHAINING       = instance[ADDER](IS_WEAK ? {} : -0, 1) != instance\n      // V8 ~  Chromium 40- weak-collections throws on primitives, but should return false\n      , THROWS_ON_PRIMITIVES = fails(function(){ instance.has(1); })\n      // most early implementations doesn't supports iterables, most modern - not close it correctly\n      , ACCEPT_ITERABLES     = $iterDetect(function(iter){ new C(iter); }) // eslint-disable-line no-new\n      // for early implementations -0 and +0 not the same\n      , BUGGY_ZERO = !IS_WEAK && fails(function(){\n        // V8 ~ Chromium 42- fails only with 5+ elements\n        var $instance = new C()\n          , index     = 5;\n        while(index--)$instance[ADDER](index, index);\n        return !$instance.has(-0);\n      });\n    if(!ACCEPT_ITERABLES){ \n      C = wrapper(function(target, iterable){\n        anInstance(target, C, NAME);\n        var that = inheritIfRequired(new Base, target, C);\n        if(iterable != undefined)forOf(iterable, IS_MAP, that[ADDER], that);\n        return that;\n      });\n      C.prototype = proto;\n      proto.constructor = C;\n    }\n    if(THROWS_ON_PRIMITIVES || BUGGY_ZERO){\n      fixMethod('delete');\n      fixMethod('has');\n      IS_MAP && fixMethod('get');\n    }\n    if(BUGGY_ZERO || HASNT_CHAINING)fixMethod(ADDER);\n    // weak collections should not contains .clear method\n    if(IS_WEAK && proto.clear)delete proto.clear;\n  }\n\n  setToStringTag(C, NAME);\n\n  O[NAME] = C;\n  $export($export.G + $export.W + $export.F * (C != Base), O);\n\n  if(!IS_WEAK)common.setStrong(C, NAME, IS_MAP);\n\n  return C;\n};\n\n/***/ }),\n/* 74 */\n/***/ (function(module, exports, __webpack_require__) {\n\nvar isObject       = __webpack_require__(3)\n  , setPrototypeOf = __webpack_require__(75).set;\nmodule.exports = function(that, target, C){\n  var P, S = target.constructor;\n  if(S !== C && typeof S == 'function' && (P = S.prototype) !== C.prototype && isObject(P) && setPrototypeOf){\n    setPrototypeOf(that, P);\n  } return that;\n};\n\n/***/ }),\n/* 75 */\n/***/ (function(module, exports, __webpack_require__) {\n\n// Works with __proto__ only. Old v8 can't work with null proto objects.\n/* eslint-disable no-proto */\nvar isObject = __webpack_require__(3)\n  , anObject = __webpack_require__(8);\nvar check = function(O, proto){\n  anObject(O);\n  if(!isObject(proto) && proto !== null)throw TypeError(proto + \": can't set as prototype!\");\n};\nmodule.exports = {\n  set: Object.setPrototypeOf || ('__proto__' in {} ? // eslint-disable-line\n    function(test, buggy, set){\n      try {\n        set = __webpack_require__(12)(Function.call, __webpack_require__(76).f(Object.prototype, '__proto__').set, 2);\n        set(test, []);\n        buggy = !(test instanceof Array);\n      } catch(e){ buggy = true; }\n      return function setPrototypeOf(O, proto){\n        check(O, proto);\n        if(buggy)O.__proto__ = proto;\n        else set(O, proto);\n        return O;\n      };\n    }({}, false) : undefined),\n  check: check\n};\n\n/***/ }),\n/* 76 */\n/***/ (function(module, exports, __webpack_require__) {\n\nvar pIE            = __webpack_require__(41)\n  , createDesc     = __webpack_require__(15)\n  , toIObject      = __webpack_require__(17)\n  , toPrimitive    = __webpack_require__(30)\n  , has            = __webpack_require__(5)\n  , IE8_DOM_DEFINE = __webpack_require__(28)\n  , gOPD           = Object.getOwnPropertyDescriptor;\n\nexports.f = __webpack_require__(4) ? gOPD : function getOwnPropertyDescriptor(O, P){\n  O = toIObject(O);\n  P = toPrimitive(P, true);\n  if(IE8_DOM_DEFINE)try {\n    return gOPD(O, P);\n  } catch(e){ /* empty */ }\n  if(has(O, P))return createDesc(!pIE.f.call(O, P), O[P]);\n};\n\n/***/ }),\n/* 77 */\n/***/ (function(module, exports, __webpack_require__) {\n\n// https://github.com/DavidBruant/Map-Set.prototype.toJSON\nvar $export  = __webpack_require__(10);\n\n$export($export.P + $export.R, 'Set', {toJSON: __webpack_require__(78)('Set')});\n\n/***/ }),\n/* 78 */\n/***/ (function(module, exports, __webpack_require__) {\n\n// https://github.com/DavidBruant/Map-Set.prototype.toJSON\nvar classof = __webpack_require__(24)\n  , from    = __webpack_require__(79);\nmodule.exports = function(NAME){\n  return function toJSON(){\n    if(classof(this) != NAME)throw TypeError(NAME + \"#toJSON isn't generic\");\n    return from(this);\n  };\n};\n\n/***/ }),\n/* 79 */\n/***/ (function(module, exports, __webpack_require__) {\n\nvar forOf = __webpack_require__(25);\n\nmodule.exports = function(iter, ITERATOR){\n  var result = [];\n  forOf(iter, false, result.push, result, ITERATOR);\n  return result;\n};\n\n\n/***/ }),\n/* 80 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _icons = __webpack_require__(26);\n\nvar _icons2 = _interopRequireDefault(_icons);\n\nvar _toSvg = __webpack_require__(46);\n\nvar _toSvg2 = _interopRequireDefault(_toSvg);\n\nvar _replace = __webpack_require__(81);\n\nvar _replace2 = _interopRequireDefault(_replace);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nmodule.exports = { icons: _icons2.default, toSvg: _toSvg2.default, replace: _replace2.default }; /**\n                                                                                                  * @file Exposes `feather` object.\n                                                                                                  */\n\n/***/ }),\n/* 81 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = replace;\n\nvar _icons = __webpack_require__(26);\n\nvar _icons2 = _interopRequireDefault(_icons);\n\nvar _toSvg = __webpack_require__(46);\n\nvar _toSvg2 = _interopRequireDefault(_toSvg);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n/**\n * Replace all elements that have a `data-feather` attribute with SVG markup\n * corresponding to the element's `data-feather` attribute value.\n * @param {Object} options\n */\n/**\n * @file Implements `replace` function.\n */\n\n/* global document, DOMParser */\n\nfunction replace() {\n  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n\n  if (typeof document === 'undefined') {\n    throw new Error('`feather.replace()` only works in a browser environment.');\n  }\n\n  var elementsToReplace = document.querySelectorAll('[data-feather]');\n\n  Array.from(elementsToReplace).forEach(function (element) {\n    return replaceElement(element, options);\n  });\n}\n\n/**\n * Replace single element with SVG markup\n * corresponding to the element's `data-feather` attribute value.\n * @param {Element} element\n * @param {Object} options\n */\nfunction replaceElement(element, options) {\n  var key = element.getAttribute('data-feather');\n\n  if (!key) {\n    console.error('The required `data-feather` attribute has no value.');\n    return;\n  }\n\n  if (!_icons2.default[key]) {\n    console.error('No icon matching \\'' + key + '\\'. See the complete list of icons at https://feathericons.com');\n    return;\n  }\n\n  var elementClassAttr = element.getAttribute('class');\n  var classNames = options.class ? options.class + ' ' + elementClassAttr : elementClassAttr;\n\n  var svgString = (0, _toSvg2.default)(key, Object.assign({}, options, { class: classNames }));\n  var svgDocument = new DOMParser().parseFromString(svgString, 'image/svg+xml');\n  var svgElement = svgDocument.querySelector('svg');\n\n  element.parentNode.replaceChild(svgElement, element);\n}\n\n/***/ })\n/******/ ]);\n});\n\n\n// WEBPACK FOOTER //\n// feather.min.js", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, {\n \t\t\t\tconfigurable: false,\n \t\t\t\tenumerable: true,\n \t\t\t\tget: getter\n \t\t\t});\n \t\t}\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 47);\n\n\n\n// WEBPACK FOOTER //\n// webpack/bootstrap 6069794b43f7bf4ca7d0", "var store      = require('./_shared')('wks')\n  , uid        = require('./_uid')\n  , Symbol     = require('./_global').Symbol\n  , USE_SYMBOL = typeof Symbol == 'function';\n\nvar $exports = module.exports = function(name){\n  return store[name] || (store[name] =\n    USE_SYMBOL && Symbol[name] || (USE_SYMBOL ? Symbol : uid)('Symbol.' + name));\n};\n\n$exports.store = store;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_wks.js\n// module id = 0\n// module chunks = 0", "// https://github.com/zloirock/core-js/issues/86#issuecomment-115759028\nvar global = module.exports = typeof window != 'undefined' && window.Math == Math\n  ? window : typeof self != 'undefined' && self.Math == Math ? self : Function('return this')();\nif(typeof __g == 'number')__g = global; // eslint-disable-line no-undef\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_global.js\n// module id = 1\n// module chunks = 0", "var anObject       = require('./_an-object')\n  , IE8_DOM_DEFINE = require('./_ie8-dom-define')\n  , toPrimitive    = require('./_to-primitive')\n  , dP             = Object.defineProperty;\n\nexports.f = require('./_descriptors') ? Object.defineProperty : function defineProperty(O, P, Attributes){\n  anObject(O);\n  P = toPrimitive(P, true);\n  anObject(Attributes);\n  if(IE8_DOM_DEFINE)try {\n    return dP(O, P, Attributes);\n  } catch(e){ /* empty */ }\n  if('get' in Attributes || 'set' in Attributes)throw TypeError('Accessors not supported!');\n  if('value' in Attributes)O[P] = Attributes.value;\n  return O;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_object-dp.js\n// module id = 2\n// module chunks = 0", "module.exports = function(it){\n  return typeof it === 'object' ? it !== null : typeof it === 'function';\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_is-object.js\n// module id = 3\n// module chunks = 0", "// Thank's IE8 for his funny defineProperty\nmodule.exports = !require('./_fails')(function(){\n  return Object.defineProperty({}, 'a', {get: function(){ return 7; }}).a != 7;\n});\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_descriptors.js\n// module id = 4\n// module chunks = 0", "var hasOwnProperty = {}.hasOwnProperty;\nmodule.exports = function(it, key){\n  return hasOwnProperty.call(it, key);\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_has.js\n// module id = 5\n// module chunks = 0", "var core = module.exports = {version: '2.4.0'};\nif(typeof __e == 'number')__e = core; // eslint-disable-line no-undef\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_core.js\n// module id = 6\n// module chunks = 0", "var dP         = require('./_object-dp')\n  , createDesc = require('./_property-desc');\nmodule.exports = require('./_descriptors') ? function(object, key, value){\n  return dP.f(object, key, createDesc(1, value));\n} : function(object, key, value){\n  object[key] = value;\n  return object;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_hide.js\n// module id = 7\n// module chunks = 0", "var isObject = require('./_is-object');\nmodule.exports = function(it){\n  if(!isObject(it))throw TypeError(it + ' is not an object!');\n  return it;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_an-object.js\n// module id = 8\n// module chunks = 0", "var global    = require('./_global')\n  , hide      = require('./_hide')\n  , has       = require('./_has')\n  , SRC       = require('./_uid')('src')\n  , TO_STRING = 'toString'\n  , $toString = Function[TO_STRING]\n  , TPL       = ('' + $toString).split(TO_STRING);\n\nrequire('./_core').inspectSource = function(it){\n  return $toString.call(it);\n};\n\n(module.exports = function(O, key, val, safe){\n  var isFunction = typeof val == 'function';\n  if(isFunction)has(val, 'name') || hide(val, 'name', key);\n  if(O[key] === val)return;\n  if(isFunction)has(val, SRC) || hide(val, SRC, O[key] ? '' + O[key] : TPL.join(String(key)));\n  if(O === global){\n    O[key] = val;\n  } else {\n    if(!safe){\n      delete O[key];\n      hide(O, key, val);\n    } else {\n      if(O[key])O[key] = val;\n      else hide(O, key, val);\n    }\n  }\n// add fake Function#toString for correct work wrapped methods / constructors with methods like LoDash isNative\n})(Function.prototype, TO_STRING, function toString(){\n  return typeof this == 'function' && this[SRC] || $toString.call(this);\n});\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_redefine.js\n// module id = 9\n// module chunks = 0", "var global    = require('./_global')\n  , core      = require('./_core')\n  , hide      = require('./_hide')\n  , redefine  = require('./_redefine')\n  , ctx       = require('./_ctx')\n  , PROTOTYPE = 'prototype';\n\nvar $export = function(type, name, source){\n  var IS_FORCED = type & $export.F\n    , IS_GLOBAL = type & $export.G\n    , IS_STATIC = type & $export.S\n    , IS_PROTO  = type & $export.P\n    , IS_BIND   = type & $export.B\n    , target    = IS_GLOBAL ? global : IS_STATIC ? global[name] || (global[name] = {}) : (global[name] || {})[PROTOTYPE]\n    , exports   = IS_GLOBAL ? core : core[name] || (core[name] = {})\n    , expProto  = exports[PROTOTYPE] || (exports[PROTOTYPE] = {})\n    , key, own, out, exp;\n  if(IS_GLOBAL)source = name;\n  for(key in source){\n    // contains in native\n    own = !IS_FORCED && target && target[key] !== undefined;\n    // export native or passed\n    out = (own ? target : source)[key];\n    // bind timers to global for call from export context\n    exp = IS_BIND && own ? ctx(out, global) : IS_PROTO && typeof out == 'function' ? ctx(Function.call, out) : out;\n    // extend global\n    if(target)redefine(target, key, out, type & $export.U);\n    // export\n    if(exports[key] != out)hide(exports, key, exp);\n    if(IS_PROTO && expProto[key] != out)expProto[key] = out;\n  }\n};\nglobal.core = core;\n// type bitmap\n$export.F = 1;   // forced\n$export.G = 2;   // global\n$export.S = 4;   // static\n$export.P = 8;   // proto\n$export.B = 16;  // bind\n$export.W = 32;  // wrap\n$export.U = 64;  // safe\n$export.R = 128; // real proto method for `library` \nmodule.exports = $export;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_export.js\n// module id = 10\n// module chunks = 0", "module.exports = function(exec){\n  try {\n    return !!exec();\n  } catch(e){\n    return true;\n  }\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_fails.js\n// module id = 11\n// module chunks = 0", "// optional / simple context binding\nvar aFunction = require('./_a-function');\nmodule.exports = function(fn, that, length){\n  aFunction(fn);\n  if(that === undefined)return fn;\n  switch(length){\n    case 1: return function(a){\n      return fn.call(that, a);\n    };\n    case 2: return function(a, b){\n      return fn.call(that, a, b);\n    };\n    case 3: return function(a, b, c){\n      return fn.call(that, a, b, c);\n    };\n  }\n  return function(/* ...args */){\n    return fn.apply(that, arguments);\n  };\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_ctx.js\n// module id = 12\n// module chunks = 0", "module.exports = {};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_iterators.js\n// module id = 13\n// module chunks = 0", "// 7.2.1 RequireObjectCoercible(argument)\nmodule.exports = function(it){\n  if(it == undefined)throw TypeError(\"Can't call method on  \" + it);\n  return it;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_defined.js\n// module id = 14\n// module chunks = 0", "module.exports = function(bitmap, value){\n  return {\n    enumerable  : !(bitmap & 1),\n    configurable: !(bitmap & 2),\n    writable    : !(bitmap & 4),\n    value       : value\n  };\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_property-desc.js\n// module id = 15\n// module chunks = 0", "var id = 0\n  , px = Math.random();\nmodule.exports = function(key){\n  return 'Symbol('.concat(key === undefined ? '' : key, ')_', (++id + px).toString(36));\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_uid.js\n// module id = 16\n// module chunks = 0", "// to indexed object, toObject with fallback for non-array-like ES3 strings\nvar IObject = require('./_iobject')\n  , defined = require('./_defined');\nmodule.exports = function(it){\n  return IObject(defined(it));\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_to-iobject.js\n// module id = 17\n// module chunks = 0", "// 7.1.4 ToInteger\nvar ceil  = Math.ceil\n  , floor = Math.floor;\nmodule.exports = function(it){\n  return isNaN(it = +it) ? 0 : (it > 0 ? floor : ceil)(it);\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_to-integer.js\n// module id = 18\n// module chunks = 0", "'use strict';\nvar LIBRARY        = require('./_library')\n  , $export        = require('./_export')\n  , redefine       = require('./_redefine')\n  , hide           = require('./_hide')\n  , has            = require('./_has')\n  , Iterators      = require('./_iterators')\n  , $iterCreate    = require('./_iter-create')\n  , setToStringTag = require('./_set-to-string-tag')\n  , getPrototypeOf = require('./_object-gpo')\n  , ITERATOR       = require('./_wks')('iterator')\n  , BUGGY          = !([].keys && 'next' in [].keys()) // <PERSON><PERSON> has buggy iterators w/o `next`\n  , FF_ITERATOR    = '@@iterator'\n  , KEYS           = 'keys'\n  , VALUES         = 'values';\n\nvar returnThis = function(){ return this; };\n\nmodule.exports = function(Base, NAME, Constructor, next, DEFAULT, IS_SET, FORCED){\n  $iterCreate(Constructor, NAME, next);\n  var getMethod = function(kind){\n    if(!BUGGY && kind in proto)return proto[kind];\n    switch(kind){\n      case KEYS: return function keys(){ return new Constructor(this, kind); };\n      case VALUES: return function values(){ return new Constructor(this, kind); };\n    } return function entries(){ return new Constructor(this, kind); };\n  };\n  var TAG        = NAME + ' Iterator'\n    , DEF_VALUES = DEFAULT == VALUES\n    , VALUES_BUG = false\n    , proto      = Base.prototype\n    , $native    = proto[ITERATOR] || proto[FF_ITERATOR] || DEFAULT && proto[DEFAULT]\n    , $default   = $native || getMethod(DEFAULT)\n    , $entries   = DEFAULT ? !DEF_VALUES ? $default : getMethod('entries') : undefined\n    , $anyNative = NAME == 'Array' ? proto.entries || $native : $native\n    , methods, key, IteratorPrototype;\n  // Fix native\n  if($anyNative){\n    IteratorPrototype = getPrototypeOf($anyNative.call(new Base));\n    if(IteratorPrototype !== Object.prototype){\n      // Set @@toStringTag to native iterators\n      setToStringTag(IteratorPrototype, TAG, true);\n      // fix for some old engines\n      if(!LIBRARY && !has(IteratorPrototype, ITERATOR))hide(IteratorPrototype, ITERATOR, returnThis);\n    }\n  }\n  // fix Array#{values, @@iterator}.name in V8 / FF\n  if(DEF_VALUES && $native && $native.name !== VALUES){\n    VALUES_BUG = true;\n    $default = function values(){ return $native.call(this); };\n  }\n  // Define iterator\n  if((!LIBRARY || FORCED) && (BUGGY || VALUES_BUG || !proto[ITERATOR])){\n    hide(proto, ITERATOR, $default);\n  }\n  // Plug for library\n  Iterators[NAME] = $default;\n  Iterators[TAG]  = returnThis;\n  if(DEFAULT){\n    methods = {\n      values:  DEF_VALUES ? $default : getMethod(VALUES),\n      keys:    IS_SET     ? $default : getMethod(KEYS),\n      entries: $entries\n    };\n    if(FORCED)for(key in methods){\n      if(!(key in proto))redefine(proto, key, methods[key]);\n    } else $export($export.P + $export.F * (BUGGY || VALUES_BUG), NAME, methods);\n  }\n  return methods;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_iter-define.js\n// module id = 19\n// module chunks = 0", "// 7.1.15 ToLength\nvar toInteger = require('./_to-integer')\n  , min       = Math.min;\nmodule.exports = function(it){\n  return it > 0 ? min(toInteger(it), 0x1fffffffffffff) : 0; // pow(2, 53) - 1 == 9007199254740991\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_to-length.js\n// module id = 20\n// module chunks = 0", "var shared = require('./_shared')('keys')\n  , uid    = require('./_uid');\nmodule.exports = function(key){\n  return shared[key] || (shared[key] = uid(key));\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_shared-key.js\n// module id = 21\n// module chunks = 0", "var def = require('./_object-dp').f\n  , has = require('./_has')\n  , TAG = require('./_wks')('toStringTag');\n\nmodule.exports = function(it, tag, stat){\n  if(it && !has(it = stat ? it : it.prototype, TAG))def(it, TAG, {configurable: true, value: tag});\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_set-to-string-tag.js\n// module id = 22\n// module chunks = 0", "// 7.1.13 ToObject(argument)\nvar defined = require('./_defined');\nmodule.exports = function(it){\n  return Object(defined(it));\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_to-object.js\n// module id = 23\n// module chunks = 0", "// getting tag from ******** Object.prototype.toString()\nvar cof = require('./_cof')\n  , TAG = require('./_wks')('toStringTag')\n  // ES3 wrong here\n  , ARG = cof(function(){ return arguments; }()) == 'Arguments';\n\n// fallback for IE11 Script Access Denied error\nvar tryGet = function(it, key){\n  try {\n    return it[key];\n  } catch(e){ /* empty */ }\n};\n\nmodule.exports = function(it){\n  var O, T, B;\n  return it === undefined ? 'Undefined' : it === null ? 'Null'\n    // @@toStringTag case\n    : typeof (T = tryGet(O = Object(it), TAG)) == 'string' ? T\n    // builtinTag case\n    : ARG ? cof(O)\n    // ES3 arguments fallback\n    : (B = cof(O)) == 'Object' && typeof O.callee == 'function' ? 'Arguments' : B;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_classof.js\n// module id = 24\n// module chunks = 0", "var ctx         = require('./_ctx')\n  , call        = require('./_iter-call')\n  , isArrayIter = require('./_is-array-iter')\n  , anObject    = require('./_an-object')\n  , toLength    = require('./_to-length')\n  , getIterFn   = require('./core.get-iterator-method')\n  , BREAK       = {}\n  , RETURN      = {};\nvar exports = module.exports = function(iterable, entries, fn, that, ITERATOR){\n  var iterFn = ITERATOR ? function(){ return iterable; } : getIterFn(iterable)\n    , f      = ctx(fn, that, entries ? 2 : 1)\n    , index  = 0\n    , length, step, iterator, result;\n  if(typeof iterFn != 'function')throw TypeError(iterable + ' is not iterable!');\n  // fast case for arrays with default iterator\n  if(isArrayIter(iterFn))for(length = toLength(iterable.length); length > index; index++){\n    result = entries ? f(anObject(step = iterable[index])[0], step[1]) : f(iterable[index]);\n    if(result === BREAK || result === RETURN)return result;\n  } else for(iterator = iterFn.call(iterable); !(step = iterator.next()).done; ){\n    result = call(iterator, f, step.value, entries);\n    if(result === BREAK || result === RETURN)return result;\n  }\n};\nexports.BREAK  = BREAK;\nexports.RETURN = RETURN;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_for-of.js\n// module id = 25\n// module chunks = 0", "module.exports = {\n\t\"activity\": \"<polyline points=\\\"22 12 18 12 15 21 9 3 6 12 2 12\\\"></polyline>\",\n\t\"airplay\": \"<path d=\\\"M5 17H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-1\\\"></path><polygon points=\\\"12 15 17 21 7 21 12 15\\\"></polygon>\",\n\t\"alert-circle\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><line x1=\\\"12\\\" y1=\\\"8\\\" x2=\\\"12\\\" y2=\\\"12\\\"></line><line x1=\\\"12\\\" y1=\\\"16\\\" x2=\\\"12\\\" y2=\\\"16\\\"></line>\",\n\t\"alert-octagon\": \"<polygon points=\\\"7.86 2 16.14 2 22 7.86 22 16.14 16.14 22 7.86 22 2 16.14 2 7.86 7.86 2\\\"></polygon><line x1=\\\"12\\\" y1=\\\"8\\\" x2=\\\"12\\\" y2=\\\"12\\\"></line><line x1=\\\"12\\\" y1=\\\"16\\\" x2=\\\"12\\\" y2=\\\"16\\\"></line>\",\n\t\"alert-triangle\": \"<path d=\\\"M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z\\\"></path><line x1=\\\"12\\\" y1=\\\"9\\\" x2=\\\"12\\\" y2=\\\"13\\\"></line><line x1=\\\"12\\\" y1=\\\"17\\\" x2=\\\"12\\\" y2=\\\"17\\\"></line>\",\n\t\"align-center\": \"<line x1=\\\"18\\\" y1=\\\"10\\\" x2=\\\"6\\\" y2=\\\"10\\\"></line><line x1=\\\"21\\\" y1=\\\"6\\\" x2=\\\"3\\\" y2=\\\"6\\\"></line><line x1=\\\"21\\\" y1=\\\"14\\\" x2=\\\"3\\\" y2=\\\"14\\\"></line><line x1=\\\"18\\\" y1=\\\"18\\\" x2=\\\"6\\\" y2=\\\"18\\\"></line>\",\n\t\"align-justify\": \"<line x1=\\\"21\\\" y1=\\\"10\\\" x2=\\\"3\\\" y2=\\\"10\\\"></line><line x1=\\\"21\\\" y1=\\\"6\\\" x2=\\\"3\\\" y2=\\\"6\\\"></line><line x1=\\\"21\\\" y1=\\\"14\\\" x2=\\\"3\\\" y2=\\\"14\\\"></line><line x1=\\\"21\\\" y1=\\\"18\\\" x2=\\\"3\\\" y2=\\\"18\\\"></line>\",\n\t\"align-left\": \"<line x1=\\\"17\\\" y1=\\\"10\\\" x2=\\\"3\\\" y2=\\\"10\\\"></line><line x1=\\\"21\\\" y1=\\\"6\\\" x2=\\\"3\\\" y2=\\\"6\\\"></line><line x1=\\\"21\\\" y1=\\\"14\\\" x2=\\\"3\\\" y2=\\\"14\\\"></line><line x1=\\\"17\\\" y1=\\\"18\\\" x2=\\\"3\\\" y2=\\\"18\\\"></line>\",\n\t\"align-right\": \"<line x1=\\\"21\\\" y1=\\\"10\\\" x2=\\\"7\\\" y2=\\\"10\\\"></line><line x1=\\\"21\\\" y1=\\\"6\\\" x2=\\\"3\\\" y2=\\\"6\\\"></line><line x1=\\\"21\\\" y1=\\\"14\\\" x2=\\\"3\\\" y2=\\\"14\\\"></line><line x1=\\\"21\\\" y1=\\\"18\\\" x2=\\\"7\\\" y2=\\\"18\\\"></line>\",\n\t\"anchor\": \"<circle cx=\\\"12\\\" cy=\\\"5\\\" r=\\\"3\\\"></circle><line x1=\\\"12\\\" y1=\\\"22\\\" x2=\\\"12\\\" y2=\\\"8\\\"></line><path d=\\\"M5 12H2a10 10 0 0 0 20 0h-3\\\"></path>\",\n\t\"aperture\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><line x1=\\\"14.31\\\" y1=\\\"8\\\" x2=\\\"20.05\\\" y2=\\\"17.94\\\"></line><line x1=\\\"9.69\\\" y1=\\\"8\\\" x2=\\\"21.17\\\" y2=\\\"8\\\"></line><line x1=\\\"7.38\\\" y1=\\\"12\\\" x2=\\\"13.12\\\" y2=\\\"2.06\\\"></line><line x1=\\\"9.69\\\" y1=\\\"16\\\" x2=\\\"3.95\\\" y2=\\\"6.06\\\"></line><line x1=\\\"14.31\\\" y1=\\\"16\\\" x2=\\\"2.83\\\" y2=\\\"16\\\"></line><line x1=\\\"16.62\\\" y1=\\\"12\\\" x2=\\\"10.88\\\" y2=\\\"21.94\\\"></line>\",\n\t\"arrow-down-left\": \"<line x1=\\\"18\\\" y1=\\\"6\\\" x2=\\\"6\\\" y2=\\\"18\\\"></line><polyline points=\\\"15 18 6 18 6 9\\\"></polyline>\",\n\t\"arrow-down-right\": \"<line x1=\\\"6\\\" y1=\\\"6\\\" x2=\\\"18\\\" y2=\\\"18\\\"></line><polyline points=\\\"9 18 18 18 18 9\\\"></polyline>\",\n\t\"arrow-down\": \"<line x1=\\\"12\\\" y1=\\\"4\\\" x2=\\\"12\\\" y2=\\\"20\\\"></line><polyline points=\\\"18 14 12 20 6 14\\\"></polyline>\",\n\t\"arrow-left\": \"<line x1=\\\"20\\\" y1=\\\"12\\\" x2=\\\"4\\\" y2=\\\"12\\\"></line><polyline points=\\\"10 18 4 12 10 6\\\"></polyline>\",\n\t\"arrow-right\": \"<line x1=\\\"4\\\" y1=\\\"12\\\" x2=\\\"20\\\" y2=\\\"12\\\"></line><polyline points=\\\"14 6 20 12 14 18\\\"></polyline>\",\n\t\"arrow-up-left\": \"<line x1=\\\"18\\\" y1=\\\"18\\\" x2=\\\"6\\\" y2=\\\"6\\\"></line><polyline points=\\\"15 6 6 6 6 15\\\"></polyline>\",\n\t\"arrow-up-right\": \"<line x1=\\\"6\\\" y1=\\\"18\\\" x2=\\\"18\\\" y2=\\\"6\\\"></line><polyline points=\\\"9 6 18 6 18 15\\\"></polyline>\",\n\t\"arrow-up\": \"<line x1=\\\"12\\\" y1=\\\"20\\\" x2=\\\"12\\\" y2=\\\"4\\\"></line><polyline points=\\\"6 10 12 4 18 10\\\"></polyline>\",\n\t\"at-sign\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"4\\\"></circle><path d=\\\"M16 12v1a3 3 0 0 0 6 0v-1a10 10 0 1 0-3.92 7.94\\\"></path>\",\n\t\"award\": \"<circle cx=\\\"12\\\" cy=\\\"8\\\" r=\\\"7\\\"></circle><polyline points=\\\"8.21 13.89 7 23 12 20 17 23 15.79 13.88\\\"></polyline>\",\n\t\"bar-chart-2\": \"<rect x=\\\"10\\\" y=\\\"3\\\" width=\\\"4\\\" height=\\\"18\\\"></rect><rect x=\\\"18\\\" y=\\\"8\\\" width=\\\"4\\\" height=\\\"13\\\"></rect><rect x=\\\"2\\\" y=\\\"13\\\" width=\\\"4\\\" height=\\\"8\\\"></rect>\",\n\t\"bar-chart\": \"<rect x=\\\"18\\\" y=\\\"3\\\" width=\\\"4\\\" height=\\\"18\\\"></rect><rect x=\\\"10\\\" y=\\\"8\\\" width=\\\"4\\\" height=\\\"13\\\"></rect><rect x=\\\"2\\\" y=\\\"13\\\" width=\\\"4\\\" height=\\\"8\\\"></rect>\",\n\t\"battery-charging\": \"<path d=\\\"M5 18H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h3.19M15 6h2a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-3.19\\\"></path><line x1=\\\"23\\\" y1=\\\"13\\\" x2=\\\"23\\\" y2=\\\"11\\\"></line><polyline points=\\\"11 6 7 12 13 12 9 18\\\"></polyline>\",\n\t\"battery\": \"<rect x=\\\"1\\\" y=\\\"6\\\" width=\\\"18\\\" height=\\\"12\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><line x1=\\\"23\\\" y1=\\\"13\\\" x2=\\\"23\\\" y2=\\\"11\\\"></line>\",\n\t\"bell-off\": \"<path d=\\\"M8.56 2.9A7 7 0 0 1 19 9v4m-2 4H2a3 3 0 0 0 3-3V9a7 7 0 0 1 .78-3.22M13.73 21a2 2 0 0 1-3.46 0\\\"></path><line x1=\\\"1\\\" y1=\\\"1\\\" x2=\\\"23\\\" y2=\\\"23\\\"></line>\",\n\t\"bell\": \"<path d=\\\"M22 17H2a3 3 0 0 0 3-3V9a7 7 0 0 1 14 0v5a3 3 0 0 0 3 3zm-8.27 4a2 2 0 0 1-3.46 0\\\"></path>\",\n\t\"bluetooth\": \"<polyline points=\\\"6.5 6.5 17.5 17.5 12 23 12 1 17.5 6.5 6.5 17.5\\\"></polyline>\",\n\t\"book\": \"<path d=\\\"M4 19.5A2.5 2.5 0 0 1 6.5 17H20\\\"></path><path d=\\\"M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z\\\"></path>\",\n\t\"bookmark\": \"<path d=\\\"M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z\\\"></path>\",\n\t\"box\": \"<path d=\\\"M12.89 1.45l8 4A2 2 0 0 1 22 7.24v9.53a2 2 0 0 1-1.11 1.79l-8 4a2 2 0 0 1-1.79 0l-8-4a2 2 0 0 1-1.1-1.8V7.24a2 2 0 0 1 1.11-1.79l8-4a2 2 0 0 1 1.78 0z\\\"></path><polyline points=\\\"2.32 6.16 12 11 21.68 6.16\\\"></polyline><line x1=\\\"12\\\" y1=\\\"22.76\\\" x2=\\\"12\\\" y2=\\\"11\\\"></line>\",\n\t\"briefcase\": \"<rect x=\\\"2\\\" y=\\\"7\\\" width=\\\"20\\\" height=\\\"14\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><path d=\\\"M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16\\\"></path>\",\n\t\"calendar\": \"<rect x=\\\"3\\\" y=\\\"4\\\" width=\\\"18\\\" height=\\\"18\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><line x1=\\\"16\\\" y1=\\\"2\\\" x2=\\\"16\\\" y2=\\\"6\\\"></line><line x1=\\\"8\\\" y1=\\\"2\\\" x2=\\\"8\\\" y2=\\\"6\\\"></line><line x1=\\\"3\\\" y1=\\\"10\\\" x2=\\\"21\\\" y2=\\\"10\\\"></line>\",\n\t\"camera-off\": \"<line x1=\\\"1\\\" y1=\\\"1\\\" x2=\\\"23\\\" y2=\\\"23\\\"></line><path d=\\\"M21 21H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h3m3-3h6l2 3h4a2 2 0 0 1 2 2v9.34m-7.72-2.06a4 4 0 1 1-5.56-5.56\\\"></path>\",\n\t\"camera\": \"<path d=\\\"M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z\\\"></path><circle cx=\\\"12\\\" cy=\\\"13\\\" r=\\\"4\\\"></circle>\",\n\t\"cast\": \"<path d=\\\"M2 16.1A5 5 0 0 1 5.9 20M2 12.05A9 9 0 0 1 9.95 20M2 8V6a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2h-6\\\"></path><line x1=\\\"2\\\" y1=\\\"20\\\" x2=\\\"2\\\" y2=\\\"20\\\"></line>\",\n\t\"check-circle\": \"<path d=\\\"M22 11.07V12a10 10 0 1 1-5.93-9.14\\\"></path><polyline points=\\\"23 3 12 14 9 11\\\"></polyline>\",\n\t\"check-square\": \"<polyline points=\\\"9 11 12 14 23 3\\\"></polyline><path d=\\\"M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11\\\"></path>\",\n\t\"check\": \"<polyline points=\\\"20 6 9 17 4 12\\\"></polyline>\",\n\t\"chevron-down\": \"<polyline points=\\\"6 9 12 15 18 9\\\"></polyline>\",\n\t\"chevron-left\": \"<polyline points=\\\"15 18 9 12 15 6\\\"></polyline>\",\n\t\"chevron-right\": \"<polyline points=\\\"9 18 15 12 9 6\\\"></polyline>\",\n\t\"chevron-up\": \"<polyline points=\\\"18 15 12 9 6 15\\\"></polyline>\",\n\t\"chevrons-down\": \"<polyline points=\\\"7 13 12 18 17 13\\\"></polyline><polyline points=\\\"7 6 12 11 17 6\\\"></polyline>\",\n\t\"chevrons-left\": \"<polyline points=\\\"11 17 6 12 11 7\\\"></polyline><polyline points=\\\"18 17 13 12 18 7\\\"></polyline>\",\n\t\"chevrons-right\": \"<polyline points=\\\"13 17 18 12 13 7\\\"></polyline><polyline points=\\\"6 17 11 12 6 7\\\"></polyline>\",\n\t\"chevrons-up\": \"<polyline points=\\\"17 11 12 6 7 11\\\"></polyline><polyline points=\\\"17 18 12 13 7 18\\\"></polyline>\",\n\t\"chrome\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"4\\\"></circle><line x1=\\\"21.17\\\" y1=\\\"8\\\" x2=\\\"12\\\" y2=\\\"8\\\"></line><line x1=\\\"3.95\\\" y1=\\\"6.06\\\" x2=\\\"8.54\\\" y2=\\\"14\\\"></line><line x1=\\\"10.88\\\" y1=\\\"21.94\\\" x2=\\\"15.46\\\" y2=\\\"14\\\"></line>\",\n\t\"circle\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle>\",\n\t\"clipboard\": \"<path d=\\\"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2\\\"></path><rect x=\\\"8\\\" y=\\\"2\\\" width=\\\"8\\\" height=\\\"4\\\" rx=\\\"1\\\" ry=\\\"1\\\"></rect>\",\n\t\"clock\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><polyline points=\\\"12 6 12 12 15 15\\\"></polyline>\",\n\t\"cloud-drizzle\": \"<line x1=\\\"8\\\" y1=\\\"19\\\" x2=\\\"8\\\" y2=\\\"21\\\"></line><line x1=\\\"8\\\" y1=\\\"13\\\" x2=\\\"8\\\" y2=\\\"15\\\"></line><line x1=\\\"16\\\" y1=\\\"19\\\" x2=\\\"16\\\" y2=\\\"21\\\"></line><line x1=\\\"16\\\" y1=\\\"13\\\" x2=\\\"16\\\" y2=\\\"15\\\"></line><line x1=\\\"12\\\" y1=\\\"21\\\" x2=\\\"12\\\" y2=\\\"23\\\"></line><line x1=\\\"12\\\" y1=\\\"15\\\" x2=\\\"12\\\" y2=\\\"17\\\"></line><path d=\\\"M20 16.58A5 5 0 0 0 18 7h-1.26A8 8 0 1 0 4 15.25\\\"></path>\",\n\t\"cloud-lightning\": \"<path d=\\\"M19 16.9A5 5 0 0 0 18 7h-1.26a8 8 0 1 0-11.62 9\\\"></path><polyline points=\\\"13 11 9 17 15 17 11 23\\\"></polyline>\",\n\t\"cloud-off\": \"<path d=\\\"M22.61 16.95A5 5 0 0 0 18 10h-1.26a8 8 0 0 0-7.05-6M5 5a8 8 0 0 0 4 15h9a5 5 0 0 0 1.7-.3\\\"></path><line x1=\\\"1\\\" y1=\\\"1\\\" x2=\\\"23\\\" y2=\\\"23\\\"></line>\",\n\t\"cloud-rain\": \"<line x1=\\\"16\\\" y1=\\\"13\\\" x2=\\\"16\\\" y2=\\\"21\\\"></line><line x1=\\\"8\\\" y1=\\\"13\\\" x2=\\\"8\\\" y2=\\\"21\\\"></line><line x1=\\\"12\\\" y1=\\\"15\\\" x2=\\\"12\\\" y2=\\\"23\\\"></line><path d=\\\"M20 16.58A5 5 0 0 0 18 7h-1.26A8 8 0 1 0 4 15.25\\\"></path>\",\n\t\"cloud-snow\": \"<path d=\\\"M20 17.58A5 5 0 0 0 18 8h-1.26A8 8 0 1 0 4 16.25\\\"></path><line x1=\\\"8\\\" y1=\\\"16\\\" x2=\\\"8\\\" y2=\\\"16\\\"></line><line x1=\\\"8\\\" y1=\\\"20\\\" x2=\\\"8\\\" y2=\\\"20\\\"></line><line x1=\\\"12\\\" y1=\\\"18\\\" x2=\\\"12\\\" y2=\\\"18\\\"></line><line x1=\\\"12\\\" y1=\\\"22\\\" x2=\\\"12\\\" y2=\\\"22\\\"></line><line x1=\\\"16\\\" y1=\\\"16\\\" x2=\\\"16\\\" y2=\\\"16\\\"></line><line x1=\\\"16\\\" y1=\\\"20\\\" x2=\\\"16\\\" y2=\\\"20\\\"></line>\",\n\t\"cloud\": \"<path d=\\\"M18 10h-1.26A8 8 0 1 0 9 20h9a5 5 0 0 0 0-10z\\\"></path>\",\n\t\"codepen\": \"<polygon points=\\\"12 2 22 8.5 22 15.5 12 22 2 15.5 2 8.5 12 2\\\"></polygon><line x1=\\\"12\\\" y1=\\\"22\\\" x2=\\\"12\\\" y2=\\\"15.5\\\"></line><polyline points=\\\"22 8.5 12 15.5 2 8.5\\\"></polyline><polyline points=\\\"2 15.5 12 8.5 22 15.5\\\"></polyline><line x1=\\\"12\\\" y1=\\\"2\\\" x2=\\\"12\\\" y2=\\\"8.5\\\"></line>\",\n\t\"command\": \"<path d=\\\"M18 3a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3 3 3 0 0 0 3-3 3 3 0 0 0-3-3H6a3 3 0 0 0-3 3 3 3 0 0 0 3 3 3 3 0 0 0 3-3V6a3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3h12a3 3 0 0 0 3-3 3 3 0 0 0-3-3z\\\"></path>\",\n\t\"compass\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><polygon points=\\\"16.24 7.76 14.12 14.12 7.76 16.24 9.88 9.88 16.24 7.76\\\"></polygon>\",\n\t\"copy\": \"<rect x=\\\"9\\\" y=\\\"9\\\" width=\\\"13\\\" height=\\\"13\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><path d=\\\"M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1\\\"></path>\",\n\t\"corner-down-left\": \"<polyline points=\\\"9 10 4 15 9 20\\\"></polyline><path d=\\\"M20 4v7a4 4 0 0 1-4 4H4\\\"></path>\",\n\t\"corner-down-right\": \"<polyline points=\\\"15 10 20 15 15 20\\\"></polyline><path d=\\\"M4 4v7a4 4 0 0 0 4 4h12\\\"></path>\",\n\t\"corner-left-down\": \"<polyline points=\\\"14 15 9 20 4 15\\\"></polyline><path d=\\\"M20 4h-7a4 4 0 0 0-4 4v12\\\"></path>\",\n\t\"corner-left-up\": \"<polyline points=\\\"14 9 9 4 4 9\\\"></polyline><path d=\\\"M20 20h-7a4 4 0 0 1-4-4V4\\\"></path>\",\n\t\"corner-right-down\": \"<polyline points=\\\"10 15 15 20 20 15\\\"></polyline><path d=\\\"M4 4h7a4 4 0 0 1 4 4v12\\\"></path>\",\n\t\"corner-right-up\": \"<polyline points=\\\"10 9 15 4 20 9\\\"></polyline><path d=\\\"M4 20h7a4 4 0 0 0 4-4V4\\\"></path>\",\n\t\"corner-up-left\": \"<polyline points=\\\"9 14 4 9 9 4\\\"></polyline><path d=\\\"M20 20v-7a4 4 0 0 0-4-4H4\\\"></path>\",\n\t\"corner-up-right\": \"<polyline points=\\\"15 14 20 9 15 4\\\"></polyline><path d=\\\"M4 20v-7a4 4 0 0 1 4-4h12\\\"></path>\",\n\t\"cpu\": \"<rect x=\\\"4\\\" y=\\\"4\\\" width=\\\"16\\\" height=\\\"16\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><rect x=\\\"9\\\" y=\\\"9\\\" width=\\\"6\\\" height=\\\"6\\\"></rect><line x1=\\\"9\\\" y1=\\\"1\\\" x2=\\\"9\\\" y2=\\\"4\\\"></line><line x1=\\\"15\\\" y1=\\\"1\\\" x2=\\\"15\\\" y2=\\\"4\\\"></line><line x1=\\\"9\\\" y1=\\\"20\\\" x2=\\\"9\\\" y2=\\\"23\\\"></line><line x1=\\\"15\\\" y1=\\\"20\\\" x2=\\\"15\\\" y2=\\\"23\\\"></line><line x1=\\\"20\\\" y1=\\\"9\\\" x2=\\\"23\\\" y2=\\\"9\\\"></line><line x1=\\\"20\\\" y1=\\\"14\\\" x2=\\\"23\\\" y2=\\\"14\\\"></line><line x1=\\\"1\\\" y1=\\\"9\\\" x2=\\\"4\\\" y2=\\\"9\\\"></line><line x1=\\\"1\\\" y1=\\\"14\\\" x2=\\\"4\\\" y2=\\\"14\\\"></line>\",\n\t\"credit-card\": \"<rect x=\\\"1\\\" y=\\\"4\\\" width=\\\"22\\\" height=\\\"16\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><line x1=\\\"1\\\" y1=\\\"10\\\" x2=\\\"23\\\" y2=\\\"10\\\"></line>\",\n\t\"crosshair\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><line x1=\\\"22\\\" y1=\\\"12\\\" x2=\\\"18\\\" y2=\\\"12\\\"></line><line x1=\\\"6\\\" y1=\\\"12\\\" x2=\\\"2\\\" y2=\\\"12\\\"></line><line x1=\\\"12\\\" y1=\\\"6\\\" x2=\\\"12\\\" y2=\\\"2\\\"></line><line x1=\\\"12\\\" y1=\\\"22\\\" x2=\\\"12\\\" y2=\\\"18\\\"></line>\",\n\t\"delete\": \"<path d=\\\"M21 4H8l-7 8 7 8h13a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2z\\\"></path><line x1=\\\"18\\\" y1=\\\"9\\\" x2=\\\"12\\\" y2=\\\"15\\\"></line><line x1=\\\"12\\\" y1=\\\"9\\\" x2=\\\"18\\\" y2=\\\"15\\\"></line>\",\n\t\"disc\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"3\\\"></circle>\",\n\t\"download-cloud\": \"<polyline points=\\\"8 17 12 21 16 17\\\"></polyline><line x1=\\\"12\\\" y1=\\\"12\\\" x2=\\\"12\\\" y2=\\\"21\\\"></line><path d=\\\"M20.88 18.09A5 5 0 0 0 18 9h-1.26A8 8 0 1 0 3 16.29\\\"></path>\",\n\t\"download\": \"<path d=\\\"M3 17v3a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-3\\\"></path><polyline points=\\\"8 12 12 16 16 12\\\"></polyline><line x1=\\\"12\\\" y1=\\\"2\\\" x2=\\\"12\\\" y2=\\\"16\\\"></line>\",\n\t\"droplet\": \"<path d=\\\"M12 2.69l5.66 5.66a8 8 0 1 1-11.31 0z\\\"></path>\",\n\t\"edit-2\": \"<polygon points=\\\"16 3 21 8 8 21 3 21 3 16 16 3\\\"></polygon>\",\n\t\"edit-3\": \"<polygon points=\\\"14 2 18 6 7 17 3 17 3 13 14 2\\\"></polygon><line x1=\\\"3\\\" y1=\\\"22\\\" x2=\\\"21\\\" y2=\\\"22\\\"></line>\",\n\t\"edit\": \"<path d=\\\"M20 14.66V20a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h5.34\\\"></path><polygon points=\\\"18 2 22 6 12 16 8 16 8 12 18 2\\\"></polygon>\",\n\t\"external-link\": \"<path d=\\\"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6\\\"></path><polyline points=\\\"15 3 21 3 21 9\\\"></polyline><line x1=\\\"10\\\" y1=\\\"14\\\" x2=\\\"21\\\" y2=\\\"3\\\"></line>\",\n\t\"eye-off\": \"<path d=\\\"M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24\\\"></path><line x1=\\\"1\\\" y1=\\\"1\\\" x2=\\\"23\\\" y2=\\\"23\\\"></line>\",\n\t\"eye\": \"<path d=\\\"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z\\\"></path><circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"3\\\"></circle>\",\n\t\"facebook\": \"<path d=\\\"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z\\\"></path>\",\n\t\"fast-forward\": \"<polygon points=\\\"13 19 22 12 13 5 13 19\\\"></polygon><polygon points=\\\"2 19 11 12 2 5 2 19\\\"></polygon>\",\n\t\"feather\": \"<path d=\\\"M20.24 12.24a6 6 0 0 0-8.49-8.49L5 10.5V19h8.5z\\\"></path><line x1=\\\"16\\\" y1=\\\"8\\\" x2=\\\"2\\\" y2=\\\"22\\\"></line><line x1=\\\"17\\\" y1=\\\"15\\\" x2=\\\"9\\\" y2=\\\"15\\\"></line>\",\n\t\"file-minus\": \"<path d=\\\"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z\\\"></path><polyline points=\\\"14 2 14 8 20 8\\\"></polyline><line x1=\\\"9\\\" y1=\\\"15\\\" x2=\\\"15\\\" y2=\\\"15\\\"></line>\",\n\t\"file-plus\": \"<path d=\\\"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z\\\"></path><polyline points=\\\"14 2 14 8 20 8\\\"></polyline><line x1=\\\"12\\\" y1=\\\"18\\\" x2=\\\"12\\\" y2=\\\"12\\\"></line><line x1=\\\"9\\\" y1=\\\"15\\\" x2=\\\"15\\\" y2=\\\"15\\\"></line>\",\n\t\"file-text\": \"<path d=\\\"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z\\\"></path><polyline points=\\\"14 2 14 8 20 8\\\"></polyline><line x1=\\\"16\\\" y1=\\\"13\\\" x2=\\\"8\\\" y2=\\\"13\\\"></line><line x1=\\\"16\\\" y1=\\\"17\\\" x2=\\\"8\\\" y2=\\\"17\\\"></line><polyline points=\\\"10 9 9 9 8 9\\\"></polyline>\",\n\t\"file\": \"<path d=\\\"M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z\\\"></path><polyline points=\\\"13 2 13 9 20 9\\\"></polyline>\",\n\t\"film\": \"<rect x=\\\"2\\\" y=\\\"2\\\" width=\\\"20\\\" height=\\\"20\\\" rx=\\\"2.18\\\" ry=\\\"2.18\\\"></rect><line x1=\\\"7\\\" y1=\\\"2\\\" x2=\\\"7\\\" y2=\\\"22\\\"></line><line x1=\\\"17\\\" y1=\\\"2\\\" x2=\\\"17\\\" y2=\\\"22\\\"></line><line x1=\\\"2\\\" y1=\\\"12\\\" x2=\\\"22\\\" y2=\\\"12\\\"></line><line x1=\\\"2\\\" y1=\\\"7\\\" x2=\\\"7\\\" y2=\\\"7\\\"></line><line x1=\\\"2\\\" y1=\\\"17\\\" x2=\\\"7\\\" y2=\\\"17\\\"></line><line x1=\\\"17\\\" y1=\\\"17\\\" x2=\\\"22\\\" y2=\\\"17\\\"></line><line x1=\\\"17\\\" y1=\\\"7\\\" x2=\\\"22\\\" y2=\\\"7\\\"></line>\",\n\t\"filter\": \"<polygon points=\\\"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3\\\"></polygon>\",\n\t\"flag\": \"<path d=\\\"M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z\\\"></path><line x1=\\\"4\\\" y1=\\\"22\\\" x2=\\\"4\\\" y2=\\\"15\\\"></line>\",\n\t\"folder\": \"<path d=\\\"M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z\\\"></path>\",\n\t\"github\": \"<path d=\\\"M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22\\\"></path>\",\n\t\"globe\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><line x1=\\\"2\\\" y1=\\\"12\\\" x2=\\\"22\\\" y2=\\\"12\\\"></line><path d=\\\"M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z\\\"></path>\",\n\t\"grid\": \"<rect x=\\\"3\\\" y=\\\"3\\\" width=\\\"7\\\" height=\\\"7\\\"></rect><rect x=\\\"14\\\" y=\\\"3\\\" width=\\\"7\\\" height=\\\"7\\\"></rect><rect x=\\\"14\\\" y=\\\"14\\\" width=\\\"7\\\" height=\\\"7\\\"></rect><rect x=\\\"3\\\" y=\\\"14\\\" width=\\\"7\\\" height=\\\"7\\\"></rect>\",\n\t\"hash\": \"<line x1=\\\"4\\\" y1=\\\"9\\\" x2=\\\"20\\\" y2=\\\"9\\\"></line><line x1=\\\"4\\\" y1=\\\"15\\\" x2=\\\"20\\\" y2=\\\"15\\\"></line><line x1=\\\"10\\\" y1=\\\"3\\\" x2=\\\"8\\\" y2=\\\"21\\\"></line><line x1=\\\"16\\\" y1=\\\"3\\\" x2=\\\"14\\\" y2=\\\"21\\\"></line>\",\n\t\"headphones\": \"<path d=\\\"M3 18v-6a9 9 0 0 1 18 0v6\\\"></path><path d=\\\"M21 19a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3zM3 19a2 2 0 0 0 2 2h1a2 2 0 0 0 2-2v-3a2 2 0 0 0-2-2H3z\\\"></path>\",\n\t\"heart\": \"<path d=\\\"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z\\\"></path>\",\n\t\"home\": \"<path d=\\\"M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\\\"></path><polyline points=\\\"9 22 9 12 15 12 15 22\\\"></polyline>\",\n\t\"image\": \"<rect x=\\\"3\\\" y=\\\"3\\\" width=\\\"18\\\" height=\\\"18\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><circle cx=\\\"8.5\\\" cy=\\\"8.5\\\" r=\\\"1.5\\\"></circle><polyline points=\\\"21 15 16 10 5 21\\\"></polyline>\",\n\t\"inbox\": \"<polyline points=\\\"22 13 16 13 14 16 10 16 8 13 2 13\\\"></polyline><path d=\\\"M5.47 5.19L2 13v5a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-5l-3.47-7.81A2 2 0 0 0 16.7 4H7.3a2 2 0 0 0-1.83 1.19z\\\"></path>\",\n\t\"info\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><line x1=\\\"12\\\" y1=\\\"16\\\" x2=\\\"12\\\" y2=\\\"12\\\"></line><line x1=\\\"12\\\" y1=\\\"8\\\" x2=\\\"12\\\" y2=\\\"8\\\"></line>\",\n\t\"instagram\": \"<rect x=\\\"2\\\" y=\\\"2\\\" width=\\\"20\\\" height=\\\"20\\\" rx=\\\"5\\\" ry=\\\"5\\\"></rect><path d=\\\"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z\\\"></path><line x1=\\\"17.5\\\" y1=\\\"6.5\\\" x2=\\\"17.5\\\" y2=\\\"6.5\\\"></line>\",\n\t\"layers\": \"<polygon points=\\\"12 2 2 7 12 12 22 7 12 2\\\"></polygon><polyline points=\\\"2 17 12 22 22 17\\\"></polyline><polyline points=\\\"2 12 12 17 22 12\\\"></polyline>\",\n\t\"layout\": \"<rect x=\\\"3\\\" y=\\\"3\\\" width=\\\"18\\\" height=\\\"18\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><line x1=\\\"3\\\" y1=\\\"9\\\" x2=\\\"21\\\" y2=\\\"9\\\"></line><line x1=\\\"9\\\" y1=\\\"21\\\" x2=\\\"9\\\" y2=\\\"9\\\"></line>\",\n\t\"life-buoy\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"4\\\"></circle><line x1=\\\"4.93\\\" y1=\\\"4.93\\\" x2=\\\"9.17\\\" y2=\\\"9.17\\\"></line><line x1=\\\"14.83\\\" y1=\\\"14.83\\\" x2=\\\"19.07\\\" y2=\\\"19.07\\\"></line><line x1=\\\"14.83\\\" y1=\\\"9.17\\\" x2=\\\"19.07\\\" y2=\\\"4.93\\\"></line><line x1=\\\"14.83\\\" y1=\\\"9.17\\\" x2=\\\"18.36\\\" y2=\\\"5.64\\\"></line><line x1=\\\"4.93\\\" y1=\\\"19.07\\\" x2=\\\"9.17\\\" y2=\\\"14.83\\\"></line>\",\n\t\"link-2\": \"<path d=\\\"M15 7h3a5 5 0 0 1 5 5 5 5 0 0 1-5 5h-3m-6 0H6a5 5 0 0 1-5-5 5 5 0 0 1 5-5h3\\\"></path><line x1=\\\"8\\\" y1=\\\"12\\\" x2=\\\"16\\\" y2=\\\"12\\\"></line>\",\n\t\"link\": \"<path d=\\\"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71\\\"></path><path d=\\\"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71\\\"></path>\",\n\t\"list\": \"<line x1=\\\"8\\\" y1=\\\"6\\\" x2=\\\"21\\\" y2=\\\"6\\\"></line><line x1=\\\"8\\\" y1=\\\"12\\\" x2=\\\"21\\\" y2=\\\"12\\\"></line><line x1=\\\"8\\\" y1=\\\"18\\\" x2=\\\"21\\\" y2=\\\"18\\\"></line><line x1=\\\"3\\\" y1=\\\"6\\\" x2=\\\"3\\\" y2=\\\"6\\\"></line><line x1=\\\"3\\\" y1=\\\"12\\\" x2=\\\"3\\\" y2=\\\"12\\\"></line><line x1=\\\"3\\\" y1=\\\"18\\\" x2=\\\"3\\\" y2=\\\"18\\\"></line>\",\n\t\"loader\": \"<line x1=\\\"12\\\" y1=\\\"2\\\" x2=\\\"12\\\" y2=\\\"6\\\"></line><line x1=\\\"12\\\" y1=\\\"18\\\" x2=\\\"12\\\" y2=\\\"22\\\"></line><line x1=\\\"4.93\\\" y1=\\\"4.93\\\" x2=\\\"7.76\\\" y2=\\\"7.76\\\"></line><line x1=\\\"16.24\\\" y1=\\\"16.24\\\" x2=\\\"19.07\\\" y2=\\\"19.07\\\"></line><line x1=\\\"2\\\" y1=\\\"12\\\" x2=\\\"6\\\" y2=\\\"12\\\"></line><line x1=\\\"18\\\" y1=\\\"12\\\" x2=\\\"22\\\" y2=\\\"12\\\"></line><line x1=\\\"4.93\\\" y1=\\\"19.07\\\" x2=\\\"7.76\\\" y2=\\\"16.24\\\"></line><line x1=\\\"16.24\\\" y1=\\\"7.76\\\" x2=\\\"19.07\\\" y2=\\\"4.93\\\"></line>\",\n\t\"lock\": \"<rect x=\\\"3\\\" y=\\\"11\\\" width=\\\"18\\\" height=\\\"11\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><path d=\\\"M7 11V7a5 5 0 0 1 10 0v4\\\"></path>\",\n\t\"log-in\": \"<path d=\\\"M14 22h5a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2h-5\\\"></path><polyline points=\\\"11 16 15 12 11 8\\\"></polyline><line x1=\\\"15\\\" y1=\\\"12\\\" x2=\\\"3\\\" y2=\\\"12\\\"></line>\",\n\t\"log-out\": \"<path d=\\\"M10 22H5a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h5\\\"></path><polyline points=\\\"17 16 21 12 17 8\\\"></polyline><line x1=\\\"21\\\" y1=\\\"12\\\" x2=\\\"9\\\" y2=\\\"12\\\"></line>\",\n\t\"mail\": \"<path d=\\\"M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z\\\"></path><polyline points=\\\"22,6 12,13 2,6\\\"></polyline>\",\n\t\"map-pin\": \"<path d=\\\"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z\\\"></path><circle cx=\\\"12\\\" cy=\\\"10\\\" r=\\\"3\\\"></circle>\",\n\t\"map\": \"<polygon points=\\\"1 6 1 22 8 18 16 22 23 18 23 2 16 6 8 2 1 6\\\"></polygon><line x1=\\\"8\\\" y1=\\\"2\\\" x2=\\\"8\\\" y2=\\\"18\\\"></line><line x1=\\\"16\\\" y1=\\\"6\\\" x2=\\\"16\\\" y2=\\\"22\\\"></line>\",\n\t\"maximize-2\": \"<polyline points=\\\"15 3 21 3 21 9\\\"></polyline><polyline points=\\\"9 21 3 21 3 15\\\"></polyline><line x1=\\\"21\\\" y1=\\\"3\\\" x2=\\\"14\\\" y2=\\\"10\\\"></line><line x1=\\\"3\\\" y1=\\\"21\\\" x2=\\\"10\\\" y2=\\\"14\\\"></line>\",\n\t\"maximize\": \"<path d=\\\"M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3\\\"></path>\",\n\t\"menu\": \"<line x1=\\\"3\\\" y1=\\\"12\\\" x2=\\\"21\\\" y2=\\\"12\\\"></line><line x1=\\\"3\\\" y1=\\\"6\\\" x2=\\\"21\\\" y2=\\\"6\\\"></line><line x1=\\\"3\\\" y1=\\\"18\\\" x2=\\\"21\\\" y2=\\\"18\\\"></line>\",\n\t\"message-circle\": \"<path d=\\\"M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z\\\"></path>\",\n\t\"message-square\": \"<path d=\\\"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z\\\"></path>\",\n\t\"mic-off\": \"<line x1=\\\"1\\\" y1=\\\"1\\\" x2=\\\"23\\\" y2=\\\"23\\\"></line><path d=\\\"M9 9v3a3 3 0 0 0 5.12 2.12M15 9.34V4a3 3 0 0 0-5.94-.6\\\"></path><path d=\\\"M17 16.95A7 7 0 0 1 5 12v-2m14 0v2a7 7 0 0 1-.11 1.23\\\"></path><line x1=\\\"12\\\" y1=\\\"19\\\" x2=\\\"12\\\" y2=\\\"23\\\"></line><line x1=\\\"8\\\" y1=\\\"23\\\" x2=\\\"16\\\" y2=\\\"23\\\"></line>\",\n\t\"mic\": \"<path d=\\\"M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z\\\"></path><path d=\\\"M19 10v2a7 7 0 0 1-14 0v-2\\\"></path><line x1=\\\"12\\\" y1=\\\"19\\\" x2=\\\"12\\\" y2=\\\"23\\\"></line><line x1=\\\"8\\\" y1=\\\"23\\\" x2=\\\"16\\\" y2=\\\"23\\\"></line>\",\n\t\"minimize-2\": \"<polyline points=\\\"4 14 10 14 10 20\\\"></polyline><polyline points=\\\"20 10 14 10 14 4\\\"></polyline><line x1=\\\"14\\\" y1=\\\"10\\\" x2=\\\"21\\\" y2=\\\"3\\\"></line><line x1=\\\"3\\\" y1=\\\"21\\\" x2=\\\"10\\\" y2=\\\"14\\\"></line>\",\n\t\"minimize\": \"<path d=\\\"M8 3v3a2 2 0 0 1-2 2H3m18 0h-3a2 2 0 0 1-2-2V3m0 18v-3a2 2 0 0 1 2-2h3M3 16h3a2 2 0 0 1 2 2v3\\\"></path>\",\n\t\"minus-circle\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><line x1=\\\"8\\\" y1=\\\"12\\\" x2=\\\"16\\\" y2=\\\"12\\\"></line>\",\n\t\"minus-square\": \"<rect x=\\\"3\\\" y=\\\"3\\\" width=\\\"18\\\" height=\\\"18\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><line x1=\\\"8\\\" y1=\\\"12\\\" x2=\\\"16\\\" y2=\\\"12\\\"></line>\",\n\t\"minus\": \"<line x1=\\\"5\\\" y1=\\\"12\\\" x2=\\\"19\\\" y2=\\\"12\\\"></line>\",\n\t\"monitor\": \"<rect x=\\\"2\\\" y=\\\"3\\\" width=\\\"20\\\" height=\\\"14\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><line x1=\\\"8\\\" y1=\\\"21\\\" x2=\\\"16\\\" y2=\\\"21\\\"></line><line x1=\\\"12\\\" y1=\\\"17\\\" x2=\\\"12\\\" y2=\\\"21\\\"></line>\",\n\t\"moon\": \"<path d=\\\"M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z\\\"></path>\",\n\t\"more-horizontal\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"2\\\"></circle><circle cx=\\\"20\\\" cy=\\\"12\\\" r=\\\"2\\\"></circle><circle cx=\\\"4\\\" cy=\\\"12\\\" r=\\\"2\\\"></circle>\",\n\t\"more-vertical\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"2\\\"></circle><circle cx=\\\"12\\\" cy=\\\"4\\\" r=\\\"2\\\"></circle><circle cx=\\\"12\\\" cy=\\\"20\\\" r=\\\"2\\\"></circle>\",\n\t\"move\": \"<polyline points=\\\"5 9 2 12 5 15\\\"></polyline><polyline points=\\\"9 5 12 2 15 5\\\"></polyline><polyline points=\\\"15 19 12 22 9 19\\\"></polyline><polyline points=\\\"19 9 22 12 19 15\\\"></polyline><line x1=\\\"2\\\" y1=\\\"12\\\" x2=\\\"22\\\" y2=\\\"12\\\"></line><line x1=\\\"12\\\" y1=\\\"2\\\" x2=\\\"12\\\" y2=\\\"22\\\"></line>\",\n\t\"music\": \"<path d=\\\"M9 17H5a2 2 0 0 0-2 2 2 2 0 0 0 2 2h2a2 2 0 0 0 2-2zm12-2h-4a2 2 0 0 0-2 2 2 2 0 0 0 2 2h2a2 2 0 0 0 2-2z\\\"></path><polyline points=\\\"9 17 9 5 21 3 21 15\\\"></polyline>\",\n\t\"navigation-2\": \"<polygon points=\\\"12 2 19 21 12 17 5 21 12 2\\\"></polygon>\",\n\t\"navigation\": \"<polygon points=\\\"3 11 22 2 13 21 11 13 3 11\\\"></polygon>\",\n\t\"octagon\": \"<polygon points=\\\"7.86 2 16.14 2 22 7.86 22 16.14 16.14 22 7.86 22 2 16.14 2 7.86 7.86 2\\\"></polygon>\",\n\t\"package\": \"<path d=\\\"M12.89 1.45l8 4A2 2 0 0 1 22 7.24v9.53a2 2 0 0 1-1.11 1.79l-8 4a2 2 0 0 1-1.79 0l-8-4a2 2 0 0 1-1.1-1.8V7.24a2 2 0 0 1 1.11-1.79l8-4a2 2 0 0 1 1.78 0z\\\"></path><polyline points=\\\"2.32 6.16 12 11 21.68 6.16\\\"></polyline><line x1=\\\"12\\\" y1=\\\"22.76\\\" x2=\\\"12\\\" y2=\\\"11\\\"></line><line x1=\\\"7\\\" y1=\\\"3.5\\\" x2=\\\"17\\\" y2=\\\"8.5\\\"></line>\",\n\t\"pause-circle\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><line x1=\\\"10\\\" y1=\\\"15\\\" x2=\\\"10\\\" y2=\\\"9\\\"></line><line x1=\\\"14\\\" y1=\\\"15\\\" x2=\\\"14\\\" y2=\\\"9\\\"></line>\",\n\t\"pause\": \"<rect x=\\\"6\\\" y=\\\"4\\\" width=\\\"4\\\" height=\\\"16\\\"></rect><rect x=\\\"14\\\" y=\\\"4\\\" width=\\\"4\\\" height=\\\"16\\\"></rect>\",\n\t\"percent\": \"<line x1=\\\"19\\\" y1=\\\"5\\\" x2=\\\"5\\\" y2=\\\"19\\\"></line><circle cx=\\\"6.5\\\" cy=\\\"6.5\\\" r=\\\"2.5\\\"></circle><circle cx=\\\"17.5\\\" cy=\\\"17.5\\\" r=\\\"2.5\\\"></circle>\",\n\t\"phone-call\": \"<path d=\\\"M15.05 5A5 5 0 0 1 19 8.95M15.05 1A9 9 0 0 1 23 8.94m-1 7.98v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z\\\"></path>\",\n\t\"phone-forwarded\": \"<polyline points=\\\"19 1 23 5 19 9\\\"></polyline><line x1=\\\"15\\\" y1=\\\"5\\\" x2=\\\"23\\\" y2=\\\"5\\\"></line><path d=\\\"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z\\\"></path>\",\n\t\"phone-incoming\": \"<polyline points=\\\"16 2 16 8 22 8\\\"></polyline><line x1=\\\"23\\\" y1=\\\"1\\\" x2=\\\"16\\\" y2=\\\"8\\\"></line><path d=\\\"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z\\\"></path>\",\n\t\"phone-missed\": \"<line x1=\\\"23\\\" y1=\\\"1\\\" x2=\\\"17\\\" y2=\\\"7\\\"></line><line x1=\\\"17\\\" y1=\\\"1\\\" x2=\\\"23\\\" y2=\\\"7\\\"></line><path d=\\\"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z\\\"></path>\",\n\t\"phone-off\": \"<path d=\\\"M10.68 13.31a16 16 0 0 0 3.41 2.6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7 2 2 0 0 1 1.72 2v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.42 19.42 0 0 1-3.33-2.67m-2.67-3.34a19.79 19.79 0 0 1-3.07-8.63A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91\\\"></path><line x1=\\\"23\\\" y1=\\\"1\\\" x2=\\\"1\\\" y2=\\\"23\\\"></line>\",\n\t\"phone-outgoing\": \"<polyline points=\\\"23 7 23 1 17 1\\\"></polyline><line x1=\\\"16\\\" y1=\\\"8\\\" x2=\\\"23\\\" y2=\\\"1\\\"></line><path d=\\\"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z\\\"></path>\",\n\t\"phone\": \"<path d=\\\"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z\\\"></path>\",\n\t\"pie-chart\": \"<path d=\\\"M21.21 15.89A10 10 0 1 1 8 2.83\\\"></path><path d=\\\"M22 12A10 10 0 0 0 12 2v10z\\\"></path>\",\n\t\"play-circle\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><polygon points=\\\"10 8 16 12 10 16 10 8\\\"></polygon>\",\n\t\"play\": \"<polygon points=\\\"5 3 19 12 5 21 5 3\\\"></polygon>\",\n\t\"plus-circle\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><line x1=\\\"12\\\" y1=\\\"8\\\" x2=\\\"12\\\" y2=\\\"16\\\"></line><line x1=\\\"8\\\" y1=\\\"12\\\" x2=\\\"16\\\" y2=\\\"12\\\"></line>\",\n\t\"plus-square\": \"<rect x=\\\"3\\\" y=\\\"3\\\" width=\\\"18\\\" height=\\\"18\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><line x1=\\\"12\\\" y1=\\\"8\\\" x2=\\\"12\\\" y2=\\\"16\\\"></line><line x1=\\\"8\\\" y1=\\\"12\\\" x2=\\\"16\\\" y2=\\\"12\\\"></line>\",\n\t\"plus\": \"<line x1=\\\"12\\\" y1=\\\"5\\\" x2=\\\"12\\\" y2=\\\"19\\\"></line><line x1=\\\"5\\\" y1=\\\"12\\\" x2=\\\"19\\\" y2=\\\"12\\\"></line>\",\n\t\"pocket\": \"<path d=\\\"M4 3h16a2 2 0 0 1 2 2v6a10 10 0 0 1-10 10A10 10 0 0 1 2 11V5a2 2 0 0 1 2-2z\\\"></path><polyline points=\\\"8 10 12 14 16 10\\\"></polyline>\",\n\t\"power\": \"<path d=\\\"M18.36 6.64a9 9 0 1 1-12.73 0\\\"></path><line x1=\\\"12\\\" y1=\\\"2\\\" x2=\\\"12\\\" y2=\\\"12\\\"></line>\",\n\t\"printer\": \"<polyline points=\\\"6 9 6 2 18 2 18 9\\\"></polyline><path d=\\\"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2\\\"></path><rect x=\\\"6\\\" y=\\\"14\\\" width=\\\"12\\\" height=\\\"8\\\"></rect>\",\n\t\"radio\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"2\\\"></circle><path d=\\\"M16.24 7.76a6 6 0 0 1 0 8.49m-8.48-.01a6 6 0 0 1 0-8.49m11.31-2.82a10 10 0 0 1 0 14.14m-14.14 0a10 10 0 0 1 0-14.14\\\"></path>\",\n\t\"refresh-ccw\": \"<polyline points=\\\"1 4 1 10 7 10\\\"></polyline><polyline points=\\\"23 20 23 14 17 14\\\"></polyline><path d=\\\"M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15\\\"></path>\",\n\t\"refresh-cw\": \"<polyline points=\\\"23 4 23 10 17 10\\\"></polyline><polyline points=\\\"1 20 1 14 7 14\\\"></polyline><path d=\\\"M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15\\\"></path>\",\n\t\"repeat\": \"<polyline points=\\\"17 1 21 5 17 9\\\"></polyline><path d=\\\"M3 11V9a4 4 0 0 1 4-4h14\\\"></path><polyline points=\\\"7 23 3 19 7 15\\\"></polyline><path d=\\\"M21 13v2a4 4 0 0 1-4 4H3\\\"></path>\",\n\t\"rewind\": \"<polygon points=\\\"11 19 2 12 11 5 11 19\\\"></polygon><polygon points=\\\"22 19 13 12 22 5 22 19\\\"></polygon>\",\n\t\"rotate-ccw\": \"<polyline points=\\\"1 4 1 10 7 10\\\"></polyline><path d=\\\"M3.51 15a9 9 0 1 0 2.13-9.36L1 10\\\"></path>\",\n\t\"rotate-cw\": \"<polyline points=\\\"23 4 23 10 17 10\\\"></polyline><path d=\\\"M20.49 15a9 9 0 1 1-2.12-9.36L23 10\\\"></path>\",\n\t\"save\": \"<path d=\\\"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z\\\"></path><polyline points=\\\"17 21 17 13 7 13 7 21\\\"></polyline><polyline points=\\\"7 3 7 8 15 8\\\"></polyline>\",\n\t\"scissors\": \"<circle cx=\\\"6\\\" cy=\\\"6\\\" r=\\\"3\\\"></circle><circle cx=\\\"6\\\" cy=\\\"18\\\" r=\\\"3\\\"></circle><line x1=\\\"20\\\" y1=\\\"4\\\" x2=\\\"8.12\\\" y2=\\\"15.88\\\"></line><line x1=\\\"14.47\\\" y1=\\\"14.48\\\" x2=\\\"20\\\" y2=\\\"20\\\"></line><line x1=\\\"8.12\\\" y1=\\\"8.12\\\" x2=\\\"12\\\" y2=\\\"12\\\"></line>\",\n\t\"search\": \"<circle cx=\\\"10.5\\\" cy=\\\"10.5\\\" r=\\\"7.5\\\"></circle><line x1=\\\"21\\\" y1=\\\"21\\\" x2=\\\"15.8\\\" y2=\\\"15.8\\\"></line>\",\n\t\"server\": \"<rect x=\\\"2\\\" y=\\\"2\\\" width=\\\"20\\\" height=\\\"8\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><rect x=\\\"2\\\" y=\\\"14\\\" width=\\\"20\\\" height=\\\"8\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><line x1=\\\"6\\\" y1=\\\"6\\\" x2=\\\"6\\\" y2=\\\"6\\\"></line><line x1=\\\"6\\\" y1=\\\"18\\\" x2=\\\"6\\\" y2=\\\"18\\\"></line>\",\n\t\"settings\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"3\\\"></circle><path d=\\\"M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z\\\"></path>\",\n\t\"share-2\": \"<circle cx=\\\"18\\\" cy=\\\"5\\\" r=\\\"3\\\"></circle><circle cx=\\\"6\\\" cy=\\\"12\\\" r=\\\"3\\\"></circle><circle cx=\\\"18\\\" cy=\\\"19\\\" r=\\\"3\\\"></circle><line x1=\\\"8.59\\\" y1=\\\"13.51\\\" x2=\\\"15.42\\\" y2=\\\"17.49\\\"></line><line x1=\\\"15.41\\\" y1=\\\"6.51\\\" x2=\\\"8.59\\\" y2=\\\"10.49\\\"></line>\",\n\t\"share\": \"<path d=\\\"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8\\\"></path><polyline points=\\\"16 6 12 2 8 6\\\"></polyline><line x1=\\\"12\\\" y1=\\\"2\\\" x2=\\\"12\\\" y2=\\\"15\\\"></line>\",\n\t\"shield\": \"<path d=\\\"M12 22s8-4 8-10V4l-8-2-8 2v8c0 6 8 10 8 10z\\\"></path>\",\n\t\"shuffle\": \"<polyline points=\\\"16 3 21 3 21 8\\\"></polyline><line x1=\\\"4\\\" y1=\\\"20\\\" x2=\\\"21\\\" y2=\\\"3\\\"></line><polyline points=\\\"21 16 21 21 16 21\\\"></polyline><line x1=\\\"15\\\" y1=\\\"15\\\" x2=\\\"21\\\" y2=\\\"21\\\"></line><line x1=\\\"4\\\" y1=\\\"4\\\" x2=\\\"9\\\" y2=\\\"9\\\"></line>\",\n\t\"sidebar\": \"<rect x=\\\"3\\\" y=\\\"3\\\" width=\\\"18\\\" height=\\\"18\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><line x1=\\\"9\\\" y1=\\\"3\\\" x2=\\\"9\\\" y2=\\\"21\\\"></line>\",\n\t\"skip-back\": \"<polygon points=\\\"19 20 9 12 19 4 19 20\\\"></polygon><line x1=\\\"5\\\" y1=\\\"19\\\" x2=\\\"5\\\" y2=\\\"5\\\"></line>\",\n\t\"skip-forward\": \"<polygon points=\\\"5 4 15 12 5 20 5 4\\\"></polygon><line x1=\\\"19\\\" y1=\\\"5\\\" x2=\\\"19\\\" y2=\\\"19\\\"></line>\",\n\t\"slack\": \"<path d=\\\"M22.08 9C19.81 1.41 16.54-.35 9 1.92S-.35 7.46 1.92 15 7.46 24.35 15 22.08 24.35 16.54 22.08 9z\\\"></path><line x1=\\\"12.57\\\" y1=\\\"5.99\\\" x2=\\\"16.15\\\" y2=\\\"16.39\\\"></line><line x1=\\\"7.85\\\" y1=\\\"7.61\\\" x2=\\\"11.43\\\" y2=\\\"18.01\\\"></line><line x1=\\\"16.39\\\" y1=\\\"7.85\\\" x2=\\\"5.99\\\" y2=\\\"11.43\\\"></line><line x1=\\\"18.01\\\" y1=\\\"12.57\\\" x2=\\\"7.61\\\" y2=\\\"16.15\\\"></line>\",\n\t\"slash\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><line x1=\\\"4.93\\\" y1=\\\"4.93\\\" x2=\\\"19.07\\\" y2=\\\"19.07\\\"></line>\",\n\t\"smartphone\": \"<rect x=\\\"5\\\" y=\\\"2\\\" width=\\\"14\\\" height=\\\"20\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><line x1=\\\"12\\\" y1=\\\"18\\\" x2=\\\"12\\\" y2=\\\"18\\\"></line>\",\n\t\"speaker\": \"<rect x=\\\"4\\\" y=\\\"2\\\" width=\\\"16\\\" height=\\\"20\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><circle cx=\\\"12\\\" cy=\\\"14\\\" r=\\\"4\\\"></circle><line x1=\\\"12\\\" y1=\\\"6\\\" x2=\\\"12\\\" y2=\\\"6\\\"></line>\",\n\t\"square\": \"<rect x=\\\"3\\\" y=\\\"3\\\" width=\\\"18\\\" height=\\\"18\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect>\",\n\t\"star\": \"<polygon points=\\\"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2\\\"></polygon>\",\n\t\"stop-circle\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><rect x=\\\"9\\\" y=\\\"9\\\" width=\\\"6\\\" height=\\\"6\\\"></rect>\",\n\t\"sun\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"5\\\"></circle><line x1=\\\"12\\\" y1=\\\"1\\\" x2=\\\"12\\\" y2=\\\"3\\\"></line><line x1=\\\"12\\\" y1=\\\"21\\\" x2=\\\"12\\\" y2=\\\"23\\\"></line><line x1=\\\"4.22\\\" y1=\\\"4.22\\\" x2=\\\"5.64\\\" y2=\\\"5.64\\\"></line><line x1=\\\"18.36\\\" y1=\\\"18.36\\\" x2=\\\"19.78\\\" y2=\\\"19.78\\\"></line><line x1=\\\"1\\\" y1=\\\"12\\\" x2=\\\"3\\\" y2=\\\"12\\\"></line><line x1=\\\"21\\\" y1=\\\"12\\\" x2=\\\"23\\\" y2=\\\"12\\\"></line><line x1=\\\"4.22\\\" y1=\\\"19.78\\\" x2=\\\"5.64\\\" y2=\\\"18.36\\\"></line><line x1=\\\"18.36\\\" y1=\\\"5.64\\\" x2=\\\"19.78\\\" y2=\\\"4.22\\\"></line>\",\n\t\"sunrise\": \"<path d=\\\"M17 18a5 5 0 0 0-10 0\\\"></path><line x1=\\\"12\\\" y1=\\\"2\\\" x2=\\\"12\\\" y2=\\\"9\\\"></line><line x1=\\\"4.22\\\" y1=\\\"10.22\\\" x2=\\\"5.64\\\" y2=\\\"11.64\\\"></line><line x1=\\\"1\\\" y1=\\\"18\\\" x2=\\\"3\\\" y2=\\\"18\\\"></line><line x1=\\\"21\\\" y1=\\\"18\\\" x2=\\\"23\\\" y2=\\\"18\\\"></line><line x1=\\\"18.36\\\" y1=\\\"11.64\\\" x2=\\\"19.78\\\" y2=\\\"10.22\\\"></line><line x1=\\\"23\\\" y1=\\\"22\\\" x2=\\\"1\\\" y2=\\\"22\\\"></line><polyline points=\\\"8 6 12 2 16 6\\\"></polyline>\",\n\t\"sunset\": \"<path d=\\\"M17 18a5 5 0 0 0-10 0\\\"></path><line x1=\\\"12\\\" y1=\\\"9\\\" x2=\\\"12\\\" y2=\\\"2\\\"></line><line x1=\\\"4.22\\\" y1=\\\"10.22\\\" x2=\\\"5.64\\\" y2=\\\"11.64\\\"></line><line x1=\\\"1\\\" y1=\\\"18\\\" x2=\\\"3\\\" y2=\\\"18\\\"></line><line x1=\\\"21\\\" y1=\\\"18\\\" x2=\\\"23\\\" y2=\\\"18\\\"></line><line x1=\\\"18.36\\\" y1=\\\"11.64\\\" x2=\\\"19.78\\\" y2=\\\"10.22\\\"></line><line x1=\\\"23\\\" y1=\\\"22\\\" x2=\\\"1\\\" y2=\\\"22\\\"></line><polyline points=\\\"16 5 12 9 8 5\\\"></polyline>\",\n\t\"tablet\": \"<rect x=\\\"4\\\" y=\\\"2\\\" width=\\\"16\\\" height=\\\"20\\\" rx=\\\"2\\\" ry=\\\"2\\\" transform=\\\"rotate(180 12 12)\\\"></rect><line x1=\\\"12\\\" y1=\\\"18\\\" x2=\\\"12\\\" y2=\\\"18\\\"></line>\",\n\t\"tag\": \"<path d=\\\"M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z\\\"></path><line x1=\\\"7\\\" y1=\\\"7\\\" x2=\\\"7\\\" y2=\\\"7\\\"></line>\",\n\t\"target\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"6\\\"></circle><circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"2\\\"></circle>\",\n\t\"thermometer\": \"<path d=\\\"M14 14.76V3.5a2.5 2.5 0 0 0-5 0v11.26a4.5 4.5 0 1 0 5 0z\\\"></path>\",\n\t\"thumbs-down\": \"<path d=\\\"M10 15v4a3 3 0 0 0 3 3l4-9V2H5.72a2 2 0 0 0-2 1.7l-1.38 9a2 2 0 0 0 2 2.3zm7-13h2.67A2.31 2.31 0 0 1 22 4v7a2.31 2.31 0 0 1-2.33 2H17\\\"></path>\",\n\t\"thumbs-up\": \"<path d=\\\"M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3\\\"></path>\",\n\t\"toggle-left\": \"<rect x=\\\"1\\\" y=\\\"5\\\" width=\\\"22\\\" height=\\\"14\\\" rx=\\\"7\\\" ry=\\\"7\\\"></rect><circle cx=\\\"8\\\" cy=\\\"12\\\" r=\\\"3\\\"></circle>\",\n\t\"toggle-right\": \"<rect x=\\\"1\\\" y=\\\"5\\\" width=\\\"22\\\" height=\\\"14\\\" rx=\\\"7\\\" ry=\\\"7\\\"></rect><circle cx=\\\"16\\\" cy=\\\"12\\\" r=\\\"3\\\"></circle>\",\n\t\"trash-2\": \"<polyline points=\\\"3 6 5 6 21 6\\\"></polyline><path d=\\\"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2\\\"></path><line x1=\\\"10\\\" y1=\\\"11\\\" x2=\\\"10\\\" y2=\\\"17\\\"></line><line x1=\\\"14\\\" y1=\\\"11\\\" x2=\\\"14\\\" y2=\\\"17\\\"></line>\",\n\t\"trash\": \"<polyline points=\\\"3 6 5 6 21 6\\\"></polyline><path d=\\\"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2\\\"></path>\",\n\t\"trending-down\": \"<polyline points=\\\"23 18 13.5 8.5 8.5 13.5 1 6\\\"></polyline><polyline points=\\\"17 18 23 18 23 12\\\"></polyline>\",\n\t\"trending-up\": \"<polyline points=\\\"23 6 13.5 15.5 8.5 10.5 1 18\\\"></polyline><polyline points=\\\"17 6 23 6 23 12\\\"></polyline>\",\n\t\"triangle\": \"<path d=\\\"M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z\\\"></path>\",\n\t\"twitter\": \"<path d=\\\"M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z\\\"></path>\",\n\t\"type\": \"<polyline points=\\\"4 7 4 4 20 4 20 7\\\"></polyline><line x1=\\\"9\\\" y1=\\\"20\\\" x2=\\\"15\\\" y2=\\\"20\\\"></line><line x1=\\\"12\\\" y1=\\\"4\\\" x2=\\\"12\\\" y2=\\\"20\\\"></line>\",\n\t\"umbrella\": \"<path d=\\\"M23 12a11.05 11.05 0 0 0-22 0zm-5 7a3 3 0 0 1-6 0v-7\\\"></path>\",\n\t\"unlock\": \"<rect x=\\\"3\\\" y=\\\"11\\\" width=\\\"18\\\" height=\\\"11\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><path d=\\\"M7 11V7a5 5 0 0 1 9.9-1\\\"></path>\",\n\t\"upload-cloud\": \"<polyline points=\\\"16 16 12 12 8 16\\\"></polyline><line x1=\\\"12\\\" y1=\\\"12\\\" x2=\\\"12\\\" y2=\\\"21\\\"></line><path d=\\\"M20.39 18.39A5 5 0 0 0 18 9h-1.26A8 8 0 1 0 3 16.3\\\"></path><polyline points=\\\"16 16 12 12 8 16\\\"></polyline>\",\n\t\"upload\": \"<path d=\\\"M3 17v3a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-3\\\"></path><polyline points=\\\"16 6 12 2 8 6\\\"></polyline><line x1=\\\"12\\\" y1=\\\"2\\\" x2=\\\"12\\\" y2=\\\"16\\\"></line>\",\n\t\"user-check\": \"<path d=\\\"M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\\\"></path><circle cx=\\\"8.5\\\" cy=\\\"7\\\" r=\\\"4\\\"></circle><polyline points=\\\"17 11 19 13 23 9\\\"></polyline>\",\n\t\"user-minus\": \"<path d=\\\"M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\\\"></path><circle cx=\\\"8.5\\\" cy=\\\"7\\\" r=\\\"4\\\"></circle><line x1=\\\"23\\\" y1=\\\"11\\\" x2=\\\"17\\\" y2=\\\"11\\\"></line>\",\n\t\"user-plus\": \"<path d=\\\"M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\\\"></path><circle cx=\\\"8.5\\\" cy=\\\"7\\\" r=\\\"4\\\"></circle><line x1=\\\"20\\\" y1=\\\"8\\\" x2=\\\"20\\\" y2=\\\"14\\\"></line><line x1=\\\"23\\\" y1=\\\"11\\\" x2=\\\"17\\\" y2=\\\"11\\\"></line>\",\n\t\"user-x\": \"<path d=\\\"M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\\\"></path><circle cx=\\\"8.5\\\" cy=\\\"7\\\" r=\\\"4\\\"></circle><line x1=\\\"18\\\" y1=\\\"8\\\" x2=\\\"23\\\" y2=\\\"13\\\"></line><line x1=\\\"23\\\" y1=\\\"8\\\" x2=\\\"18\\\" y2=\\\"13\\\"></line>\",\n\t\"user\": \"<path d=\\\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\\\"></path><circle cx=\\\"12\\\" cy=\\\"7\\\" r=\\\"4\\\"></circle>\",\n\t\"users\": \"<path d=\\\"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\\\"></path><circle cx=\\\"9\\\" cy=\\\"7\\\" r=\\\"4\\\"></circle><path d=\\\"M23 21v-2a4 4 0 0 0-3-3.87\\\"></path><path d=\\\"M16 3.13a4 4 0 0 1 0 7.75\\\"></path>\",\n\t\"video-off\": \"<path d=\\\"M16 16v1a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V7a2 2 0 0 1 2-2h2m5.66 0H14a2 2 0 0 1 2 2v3.34l1 1L23 7v10\\\"></path><line x1=\\\"1\\\" y1=\\\"1\\\" x2=\\\"23\\\" y2=\\\"23\\\"></line>\",\n\t\"video\": \"<polygon points=\\\"23 7 16 12 23 17 23 7\\\"></polygon><rect x=\\\"1\\\" y=\\\"5\\\" width=\\\"15\\\" height=\\\"14\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect>\",\n\t\"voicemail\": \"<circle cx=\\\"5.5\\\" cy=\\\"11.5\\\" r=\\\"4.5\\\"></circle><circle cx=\\\"18.5\\\" cy=\\\"11.5\\\" r=\\\"4.5\\\"></circle><line x1=\\\"5.5\\\" y1=\\\"16\\\" x2=\\\"18.5\\\" y2=\\\"16\\\"></line>\",\n\t\"volume-1\": \"<polygon points=\\\"11 5 6 9 2 9 2 15 6 15 11 19 11 5\\\"></polygon><path d=\\\"M15.54 8.46a5 5 0 0 1 0 7.07\\\"></path>\",\n\t\"volume-2\": \"<polygon points=\\\"11 5 6 9 2 9 2 15 6 15 11 19 11 5\\\"></polygon><path d=\\\"M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07\\\"></path>\",\n\t\"volume-x\": \"<polygon points=\\\"11 5 6 9 2 9 2 15 6 15 11 19 11 5\\\"></polygon><line x1=\\\"23\\\" y1=\\\"9\\\" x2=\\\"17\\\" y2=\\\"15\\\"></line><line x1=\\\"17\\\" y1=\\\"9\\\" x2=\\\"23\\\" y2=\\\"15\\\"></line>\",\n\t\"volume\": \"<polygon points=\\\"11 5 6 9 2 9 2 15 6 15 11 19 11 5\\\"></polygon>\",\n\t\"watch\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"7\\\"></circle><polyline points=\\\"12 9 12 12 13.5 13.5\\\"></polyline><path d=\\\"M16.51 17.35l-.35 3.83a2 2 0 0 1-2 1.82H9.83a2 2 0 0 1-2-1.82l-.35-3.83m.01-10.7l.35-3.83A2 2 0 0 1 9.83 1h4.35a2 2 0 0 1 2 1.82l.35 3.83\\\"></path>\",\n\t\"wifi\": \"<path d=\\\"M5 11.55a11 11 0 0 1 14.08 0M1.41 8a16 16 0 0 1 21.17 0M8.52 15.11a6 6 0 0 1 6.95 0\\\"></path><line x1=\\\"12\\\" y1=\\\"19\\\" x2=\\\"12\\\" y2=\\\"19\\\"></line>\",\n\t\"wind\": \"<path d=\\\"M9.59 4.59A2 2 0 1 1 11 8H2m10.59 11.41A2 2 0 1 0 14 16H2m15.73-8.27A2.5 2.5 0 1 1 19.5 12H2\\\"></path>\",\n\t\"x-circle\": \"<circle cx=\\\"12\\\" cy=\\\"12\\\" r=\\\"10\\\"></circle><line x1=\\\"15\\\" y1=\\\"9\\\" x2=\\\"9\\\" y2=\\\"15\\\"></line><line x1=\\\"9\\\" y1=\\\"9\\\" x2=\\\"15\\\" y2=\\\"15\\\"></line>\",\n\t\"x-square\": \"<rect x=\\\"3\\\" y=\\\"3\\\" width=\\\"18\\\" height=\\\"18\\\" rx=\\\"2\\\" ry=\\\"2\\\"></rect><line x1=\\\"9\\\" y1=\\\"9\\\" x2=\\\"15\\\" y2=\\\"15\\\"></line><line x1=\\\"15\\\" y1=\\\"9\\\" x2=\\\"9\\\" y2=\\\"15\\\"></line>\",\n\t\"x\": \"<line x1=\\\"18\\\" y1=\\\"6\\\" x2=\\\"6\\\" y2=\\\"18\\\"></line><line x1=\\\"6\\\" y1=\\\"6\\\" x2=\\\"18\\\" y2=\\\"18\\\"></line>\",\n\t\"zap\": \"<polygon points=\\\"13 2 3 14 12 14 11 22 21 10 12 10 13 2\\\"></polygon>\",\n\t\"zoom-in\": \"<circle cx=\\\"11\\\" cy=\\\"11\\\" r=\\\"8\\\"></circle><line x1=\\\"21\\\" y1=\\\"21\\\" x2=\\\"16.65\\\" y2=\\\"16.65\\\"></line><line x1=\\\"11\\\" y1=\\\"8\\\" x2=\\\"11\\\" y2=\\\"14\\\"></line><line x1=\\\"8\\\" y1=\\\"11\\\" x2=\\\"14\\\" y2=\\\"11\\\"></line>\",\n\t\"zoom-out\": \"<circle cx=\\\"11\\\" cy=\\\"11\\\" r=\\\"8\\\"></circle><line x1=\\\"21\\\" y1=\\\"21\\\" x2=\\\"16.65\\\" y2=\\\"16.65\\\"></line><line x1=\\\"8\\\" y1=\\\"11\\\" x2=\\\"14\\\" y2=\\\"11\\\"></line>\"\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./dist/icons.json\n// module id = 26\n// module chunks = 0", "'use strict';\nvar $at  = require('./_string-at')(true);\n\n// 21.1.3.27 String.prototype[@@iterator]()\nrequire('./_iter-define')(String, 'String', function(iterated){\n  this._t = String(iterated); // target\n  this._i = 0;                // next index\n// ********.1 %StringIteratorPrototype%.next()\n}, function(){\n  var O     = this._t\n    , index = this._i\n    , point;\n  if(index >= O.length)return {value: undefined, done: true};\n  point = $at(O, index);\n  this._i += point.length;\n  return {value: point, done: false};\n});\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/es6.string.iterator.js\n// module id = 27\n// module chunks = 0", "module.exports = !require('./_descriptors') && !require('./_fails')(function(){\n  return Object.defineProperty(require('./_dom-create')('div'), 'a', {get: function(){ return 7; }}).a != 7;\n});\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_ie8-dom-define.js\n// module id = 28\n// module chunks = 0", "var isObject = require('./_is-object')\n  , document = require('./_global').document\n  // in old IE typeof document.createElement is 'object'\n  , is = isObject(document) && isObject(document.createElement);\nmodule.exports = function(it){\n  return is ? document.createElement(it) : {};\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_dom-create.js\n// module id = 29\n// module chunks = 0", "// 7.1.1 ToPrimitive(input [, PreferredType])\nvar isObject = require('./_is-object');\n// instead of the ES6 spec version, we didn't implement @@toPrimitive case\n// and the second argument - flag - preferred type is a string\nmodule.exports = function(it, S){\n  if(!isObject(it))return it;\n  var fn, val;\n  if(S && typeof (fn = it.toString) == 'function' && !isObject(val = fn.call(it)))return val;\n  if(typeof (fn = it.valueOf) == 'function' && !isObject(val = fn.call(it)))return val;\n  if(!S && typeof (fn = it.toString) == 'function' && !isObject(val = fn.call(it)))return val;\n  throw TypeError(\"Can't convert object to primitive value\");\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_to-primitive.js\n// module id = 30\n// module chunks = 0", "// 19.1.2.2 / 15.2.3.5 Object.create(O [, Properties])\nvar anObject    = require('./_an-object')\n  , dPs         = require('./_object-dps')\n  , enumBugKeys = require('./_enum-bug-keys')\n  , IE_PROTO    = require('./_shared-key')('IE_PROTO')\n  , Empty       = function(){ /* empty */ }\n  , PROTOTYPE   = 'prototype';\n\n// Create object with fake `null` prototype: use iframe Object with cleared prototype\nvar createDict = function(){\n  // Thrash, waste and sodomy: IE GC bug\n  var iframe = require('./_dom-create')('iframe')\n    , i      = enumBugKeys.length\n    , lt     = '<'\n    , gt     = '>'\n    , iframeDocument;\n  iframe.style.display = 'none';\n  require('./_html').appendChild(iframe);\n  iframe.src = 'javascript:'; // eslint-disable-line no-script-url\n  // createDict = iframe.contentWindow.Object;\n  // html.removeChild(iframe);\n  iframeDocument = iframe.contentWindow.document;\n  iframeDocument.open();\n  iframeDocument.write(lt + 'script' + gt + 'document.F=Object' + lt + '/script' + gt);\n  iframeDocument.close();\n  createDict = iframeDocument.F;\n  while(i--)delete createDict[PROTOTYPE][enumBugKeys[i]];\n  return createDict();\n};\n\nmodule.exports = Object.create || function create(O, Properties){\n  var result;\n  if(O !== null){\n    Empty[PROTOTYPE] = anObject(O);\n    result = new Empty;\n    Empty[PROTOTYPE] = null;\n    // add \"__proto__\" for Object.getPrototypeOf polyfill\n    result[IE_PROTO] = O;\n  } else result = createDict();\n  return Properties === undefined ? result : dPs(result, Properties);\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_object-create.js\n// module id = 31\n// module chunks = 0", "// ********* / ********* Object.keys(O)\nvar $keys       = require('./_object-keys-internal')\n  , enumBugKeys = require('./_enum-bug-keys');\n\nmodule.exports = Object.keys || function keys(O){\n  return $keys(O, enumBugKeys);\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_object-keys.js\n// module id = 32\n// module chunks = 0", "// fallback for non-array-like ES3 and non-enumerable old V8 strings\nvar cof = require('./_cof');\nmodule.exports = Object('z').propertyIsEnumerable(0) ? Object : function(it){\n  return cof(it) == 'String' ? it.split('') : Object(it);\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_iobject.js\n// module id = 33\n// module chunks = 0", "var toString = {}.toString;\n\nmodule.exports = function(it){\n  return toString.call(it).slice(8, -1);\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_cof.js\n// module id = 34\n// module chunks = 0", "var global = require('./_global')\n  , SHARED = '__core-js_shared__'\n  , store  = global[SHARED] || (global[SHARED] = {});\nmodule.exports = function(key){\n  return store[key] || (store[key] = {});\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_shared.js\n// module id = 35\n// module chunks = 0", "// IE 8- don't enum bug keys\nmodule.exports = (\n  'constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf'\n).split(',');\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_enum-bug-keys.js\n// module id = 36\n// module chunks = 0", "// call something on iterator step with safe closing on error\nvar anObject = require('./_an-object');\nmodule.exports = function(iterator, fn, value, entries){\n  try {\n    return entries ? fn(anObject(value)[0], value[1]) : fn(value);\n  // 7.4.6 IteratorClose(iterator, completion)\n  } catch(e){\n    var ret = iterator['return'];\n    if(ret !== undefined)anObject(ret.call(iterator));\n    throw e;\n  }\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_iter-call.js\n// module id = 37\n// module chunks = 0", "// check on default Array iterator\nvar Iterators  = require('./_iterators')\n  , ITERATOR   = require('./_wks')('iterator')\n  , ArrayProto = Array.prototype;\n\nmodule.exports = function(it){\n  return it !== undefined && (Iterators.Array === it || ArrayProto[ITERATOR] === it);\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_is-array-iter.js\n// module id = 38\n// module chunks = 0", "var classof   = require('./_classof')\n  , ITERATOR  = require('./_wks')('iterator')\n  , Iterators = require('./_iterators');\nmodule.exports = require('./_core').getIteratorMethod = function(it){\n  if(it != undefined)return it[ITERATOR]\n    || it['@@iterator']\n    || Iterators[classof(it)];\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/core.get-iterator-method.js\n// module id = 39\n// module chunks = 0", "var ITERATOR     = require('./_wks')('iterator')\n  , SAFE_CLOSING = false;\n\ntry {\n  var riter = [7][ITERATOR]();\n  riter['return'] = function(){ SAFE_CLOSING = true; };\n  Array.from(riter, function(){ throw 2; });\n} catch(e){ /* empty */ }\n\nmodule.exports = function(exec, skipClosing){\n  if(!skipClosing && !SAFE_CLOSING)return false;\n  var safe = false;\n  try {\n    var arr  = [7]\n      , iter = arr[ITERATOR]();\n    iter.next = function(){ return {done: safe = true}; };\n    arr[ITERATOR] = function(){ return iter; };\n    exec(arr);\n  } catch(e){ /* empty */ }\n  return safe;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_iter-detect.js\n// module id = 40\n// module chunks = 0", "exports.f = {}.propertyIsEnumerable;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_object-pie.js\n// module id = 41\n// module chunks = 0", "module.exports = function(done, value){\n  return {value: value, done: !!done};\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_iter-step.js\n// module id = 42\n// module chunks = 0", "var redefine = require('./_redefine');\nmodule.exports = function(target, src, safe){\n  for(var key in src)redefine(target, key, src[key], safe);\n  return target;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_redefine-all.js\n// module id = 43\n// module chunks = 0", "module.exports = function(it, Constructor, name, forbiddenField){\n  if(!(it instanceof Constructor) || (forbiddenField !== undefined && forbiddenField in it)){\n    throw TypeError(name + ': incorrect invocation!');\n  } return it;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_an-instance.js\n// module id = 44\n// module chunks = 0", "var META     = require('./_uid')('meta')\n  , isObject = require('./_is-object')\n  , has      = require('./_has')\n  , setDesc  = require('./_object-dp').f\n  , id       = 0;\nvar isExtensible = Object.isExtensible || function(){\n  return true;\n};\nvar FREEZE = !require('./_fails')(function(){\n  return isExtensible(Object.preventExtensions({}));\n});\nvar setMeta = function(it){\n  setDesc(it, META, {value: {\n    i: 'O' + ++id, // object ID\n    w: {}          // weak collections IDs\n  }});\n};\nvar fastKey = function(it, create){\n  // return primitive with prefix\n  if(!isObject(it))return typeof it == 'symbol' ? it : (typeof it == 'string' ? 'S' : 'P') + it;\n  if(!has(it, META)){\n    // can't set metadata to uncaught frozen object\n    if(!isExtensible(it))return 'F';\n    // not necessary to add metadata\n    if(!create)return 'E';\n    // add missing metadata\n    setMeta(it);\n  // return object ID\n  } return it[META].i;\n};\nvar getWeak = function(it, create){\n  if(!has(it, META)){\n    // can't set metadata to uncaught frozen object\n    if(!isExtensible(it))return true;\n    // not necessary to add metadata\n    if(!create)return false;\n    // add missing metadata\n    setMeta(it);\n  // return hash weak collections IDs\n  } return it[META].w;\n};\n// add metadata on freeze-family methods calling\nvar onFreeze = function(it){\n  if(FREEZE && meta.NEED && isExtensible(it) && !has(it, META))setMeta(it);\n  return it;\n};\nvar meta = module.exports = {\n  KEY:      META,\n  NEED:     false,\n  fastKey:  fastKey,\n  getWeak:  getWeak,\n  onFreeze: onFreeze\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_meta.js\n// module id = 45\n// module chunks = 0", "/**\n * @file Implements `toSvg` function.\n */\n\nimport icons from '../dist/icons.json';\n\nconst DEFAULT_OPTIONS = {\n  xmlns: 'http://www.w3.org/2000/svg',\n  width: 24,\n  height: 24,\n  viewBox: '0 0 24 24',\n  fill: 'none',\n  stroke: 'currentColor',\n  'stroke-width': 2,\n  'stroke-linecap': 'round',\n  'stroke-linejoin': 'round',\n};\n\n/**\n * Create an SVG string.\n * @param {string} key - Icon name.\n * @param {Object} options\n * @returns {string}\n */\nexport default function toSvg(key, options = {}) {\n  if (!key) {\n    throw new Error('The required `key` (icon name) parameter is missing.');\n  }\n\n  if (!icons[key]) {\n    throw new Error(`No icon matching '${key}'. See the complete list of icons at https://feathericons.com`);\n  }\n\n  const combinedOptions = Object.assign({}, DEFAULT_OPTIONS, options);\n\n  combinedOptions.class = addDefaultClassNames(combinedOptions.class, key);\n\n  const attributes = optionsToAtrributes(combinedOptions);\n\n  return `<svg ${attributes}>${icons[key]}</svg>`;\n}\n\n/**\n * Add default class names.\n * @param {string} classNames - One or more class names seperated by spaces.\n * @param {string} key - Icon name.\n * @returns {string}\n */\nfunction addDefaultClassNames(classNames, key) {\n  // convert class names string into an array\n  const classNamesArray = classNames ? classNames.trim().split(/\\s+/) : [];\n\n  // use Set to avoid duplicate class names\n  const classNamesSet = new Set(classNamesArray);\n\n  // add default class names\n  classNamesSet.add('feather').add(`feather-${key}`);\n\n  return Array.from(classNamesSet).join(' ');\n}\n\n/**\n * Convert options object to string of html attributes.\n * @param {Object} options\n * @returns {string}\n */\nfunction optionsToAtrributes(options) {\n  const attributes = [];\n\n  Object.keys(options).forEach(key => {\n    attributes.push(`${key}=\"${options[key]}\"`);\n  });\n\n  return attributes.join(' ');\n}\n\n\n\n// WEBPACK FOOTER //\n// ./src/to-svg.js", "require('../../modules/es6.string.iterator');\nrequire('../../modules/es6.array.from');\nmodule.exports = require('../../modules/_core').Array.from;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/fn/array/from.js\n// module id = 48\n// module chunks = 0", "var toInteger = require('./_to-integer')\n  , defined   = require('./_defined');\n// true  -> String#at\n// false -> String#codePointAt\nmodule.exports = function(TO_STRING){\n  return function(that, pos){\n    var s = String(defined(that))\n      , i = toInteger(pos)\n      , l = s.length\n      , a, b;\n    if(i < 0 || i >= l)return TO_STRING ? '' : undefined;\n    a = s.charCodeAt(i);\n    return a < 0xd800 || a > 0xdbff || i + 1 === l || (b = s.charCodeAt(i + 1)) < 0xdc00 || b > 0xdfff\n      ? TO_STRING ? s.charAt(i) : a\n      : TO_STRING ? s.slice(i, i + 2) : (a - 0xd800 << 10) + (b - 0xdc00) + 0x10000;\n  };\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_string-at.js\n// module id = 49\n// module chunks = 0", "module.exports = false;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_library.js\n// module id = 50\n// module chunks = 0", "module.exports = function(it){\n  if(typeof it != 'function')throw TypeError(it + ' is not a function!');\n  return it;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_a-function.js\n// module id = 51\n// module chunks = 0", "'use strict';\nvar create         = require('./_object-create')\n  , descriptor     = require('./_property-desc')\n  , setToStringTag = require('./_set-to-string-tag')\n  , IteratorPrototype = {};\n\n// ********.1 %IteratorPrototype%[@@iterator]()\nrequire('./_hide')(IteratorPrototype, require('./_wks')('iterator'), function(){ return this; });\n\nmodule.exports = function(Constructor, NAME, next){\n  Constructor.prototype = create(IteratorPrototype, {next: descriptor(1, next)});\n  setToStringTag(Constructor, NAME + ' Iterator');\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_iter-create.js\n// module id = 52\n// module chunks = 0", "var dP       = require('./_object-dp')\n  , anObject = require('./_an-object')\n  , getKeys  = require('./_object-keys');\n\nmodule.exports = require('./_descriptors') ? Object.defineProperties : function defineProperties(O, Properties){\n  anObject(O);\n  var keys   = getKeys(Properties)\n    , length = keys.length\n    , i = 0\n    , P;\n  while(length > i)dP.f(O, P = keys[i++], Properties[P]);\n  return O;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_object-dps.js\n// module id = 53\n// module chunks = 0", "var has          = require('./_has')\n  , toIObject    = require('./_to-iobject')\n  , arrayIndexOf = require('./_array-includes')(false)\n  , IE_PROTO     = require('./_shared-key')('IE_PROTO');\n\nmodule.exports = function(object, names){\n  var O      = toIObject(object)\n    , i      = 0\n    , result = []\n    , key;\n  for(key in O)if(key != IE_PROTO)has(O, key) && result.push(key);\n  // Don't enum bug & hidden keys\n  while(names.length > i)if(has(O, key = names[i++])){\n    ~arrayIndexOf(result, key) || result.push(key);\n  }\n  return result;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_object-keys-internal.js\n// module id = 54\n// module chunks = 0", "// false -> Array#indexOf\n// true  -> Array#includes\nvar toIObject = require('./_to-iobject')\n  , toLength  = require('./_to-length')\n  , toIndex   = require('./_to-index');\nmodule.exports = function(IS_INCLUDES){\n  return function($this, el, fromIndex){\n    var O      = toIObject($this)\n      , length = toLength(O.length)\n      , index  = toIndex(fromIndex, length)\n      , value;\n    // Array#includes uses SameValueZero equality algorithm\n    if(IS_INCLUDES && el != el)while(length > index){\n      value = O[index++];\n      if(value != value)return true;\n    // Array#toIndex ignores holes, Array#includes - not\n    } else for(;length > index; index++)if(IS_INCLUDES || index in O){\n      if(O[index] === el)return IS_INCLUDES || index || 0;\n    } return !IS_INCLUDES && -1;\n  };\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_array-includes.js\n// module id = 55\n// module chunks = 0", "var toInteger = require('./_to-integer')\n  , max       = Math.max\n  , min       = Math.min;\nmodule.exports = function(index, length){\n  index = toInteger(index);\n  return index < 0 ? max(index + length, 0) : min(index, length);\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_to-index.js\n// module id = 56\n// module chunks = 0", "module.exports = require('./_global').document && document.documentElement;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_html.js\n// module id = 57\n// module chunks = 0", "// 19.1.2.9 / 15.2.3.2 Object.getPrototypeOf(O)\nvar has         = require('./_has')\n  , toObject    = require('./_to-object')\n  , IE_PROTO    = require('./_shared-key')('IE_PROTO')\n  , ObjectProto = Object.prototype;\n\nmodule.exports = Object.getPrototypeOf || function(O){\n  O = toObject(O);\n  if(has(O, IE_PROTO))return O[IE_PROTO];\n  if(typeof O.constructor == 'function' && O instanceof O.constructor){\n    return O.constructor.prototype;\n  } return O instanceof Object ? ObjectProto : null;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_object-gpo.js\n// module id = 58\n// module chunks = 0", "'use strict';\nvar ctx            = require('./_ctx')\n  , $export        = require('./_export')\n  , toObject       = require('./_to-object')\n  , call           = require('./_iter-call')\n  , isArrayIter    = require('./_is-array-iter')\n  , toLength       = require('./_to-length')\n  , createProperty = require('./_create-property')\n  , getIterFn      = require('./core.get-iterator-method');\n\n$export($export.S + $export.F * !require('./_iter-detect')(function(iter){ Array.from(iter); }), 'Array', {\n  // 22.1.2.1 Array.from(arrayLike, mapfn = undefined, thisArg = undefined)\n  from: function from(arrayLike/*, mapfn = undefined, thisArg = undefined*/){\n    var O       = toObject(arrayLike)\n      , C       = typeof this == 'function' ? this : Array\n      , aLen    = arguments.length\n      , mapfn   = aLen > 1 ? arguments[1] : undefined\n      , mapping = mapfn !== undefined\n      , index   = 0\n      , iterFn  = getIterFn(O)\n      , length, result, step, iterator;\n    if(mapping)mapfn = ctx(mapfn, aLen > 2 ? arguments[2] : undefined, 2);\n    // if object isn't iterable or it's array with default iterator - use simple case\n    if(iterFn != undefined && !(C == Array && isArrayIter(iterFn))){\n      for(iterator = iterFn.call(O), result = new C; !(step = iterator.next()).done; index++){\n        createProperty(result, index, mapping ? call(iterator, mapfn, [step.value, index], true) : step.value);\n      }\n    } else {\n      length = toLength(O.length);\n      for(result = new C(length); length > index; index++){\n        createProperty(result, index, mapping ? mapfn(O[index], index) : O[index]);\n      }\n    }\n    result.length = index;\n    return result;\n  }\n});\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/es6.array.from.js\n// module id = 59\n// module chunks = 0", "'use strict';\nvar $defineProperty = require('./_object-dp')\n  , createDesc      = require('./_property-desc');\n\nmodule.exports = function(object, index, value){\n  if(index in object)$defineProperty.f(object, index, createDesc(0, value));\n  else object[index] = value;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_create-property.js\n// module id = 60\n// module chunks = 0", "require('../../modules/es6.object.assign');\nmodule.exports = require('../../modules/_core').Object.assign;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/fn/object/assign.js\n// module id = 61\n// module chunks = 0", "// 19.1.3.1 Object.assign(target, source)\nvar $export = require('./_export');\n\n$export($export.S + $export.F, 'Object', {assign: require('./_object-assign')});\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/es6.object.assign.js\n// module id = 62\n// module chunks = 0", "'use strict';\n// 19.1.2.1 Object.assign(target, source, ...)\nvar getKeys  = require('./_object-keys')\n  , gOPS     = require('./_object-gops')\n  , pIE      = require('./_object-pie')\n  , toObject = require('./_to-object')\n  , IObject  = require('./_iobject')\n  , $assign  = Object.assign;\n\n// should work with symbols and should have deterministic property order (V8 bug)\nmodule.exports = !$assign || require('./_fails')(function(){\n  var A = {}\n    , B = {}\n    , S = Symbol()\n    , K = 'abcdefghijklmnopqrst';\n  A[S] = 7;\n  K.split('').forEach(function(k){ B[k] = k; });\n  return $assign({}, A)[S] != 7 || Object.keys($assign({}, B)).join('') != K;\n}) ? function assign(target, source){ // eslint-disable-line no-unused-vars\n  var T     = toObject(target)\n    , aLen  = arguments.length\n    , index = 1\n    , getSymbols = gOPS.f\n    , isEnum     = pIE.f;\n  while(aLen > index){\n    var S      = IObject(arguments[index++])\n      , keys   = getSymbols ? getKeys(S).concat(getSymbols(S)) : getKeys(S)\n      , length = keys.length\n      , j      = 0\n      , key;\n    while(length > j)if(isEnum.call(S, key = keys[j++]))T[key] = S[key];\n  } return T;\n} : $assign;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_object-assign.js\n// module id = 63\n// module chunks = 0", "exports.f = Object.getOwnPropertySymbols;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_object-gops.js\n// module id = 64\n// module chunks = 0", "require('../modules/es6.object.to-string');\nrequire('../modules/es6.string.iterator');\nrequire('../modules/web.dom.iterable');\nrequire('../modules/es6.set');\nrequire('../modules/es7.set.to-json');\nmodule.exports = require('../modules/_core').Set;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/fn/set.js\n// module id = 65\n// module chunks = 0", "'use strict';\n// ******** Object.prototype.toString()\nvar classof = require('./_classof')\n  , test    = {};\ntest[require('./_wks')('toStringTag')] = 'z';\nif(test + '' != '[object z]'){\n  require('./_redefine')(Object.prototype, 'toString', function toString(){\n    return '[object ' + classof(this) + ']';\n  }, true);\n}\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/es6.object.to-string.js\n// module id = 66\n// module chunks = 0", "var $iterators    = require('./es6.array.iterator')\n  , redefine      = require('./_redefine')\n  , global        = require('./_global')\n  , hide          = require('./_hide')\n  , Iterators     = require('./_iterators')\n  , wks           = require('./_wks')\n  , ITERATOR      = wks('iterator')\n  , TO_STRING_TAG = wks('toStringTag')\n  , ArrayValues   = Iterators.Array;\n\nfor(var collections = ['NodeList', 'DOMTokenList', 'MediaList', 'StyleSheetList', 'CSSRuleList'], i = 0; i < 5; i++){\n  var NAME       = collections[i]\n    , Collection = global[NAME]\n    , proto      = Collection && Collection.prototype\n    , key;\n  if(proto){\n    if(!proto[ITERATOR])hide(proto, ITERATOR, ArrayValues);\n    if(!proto[TO_STRING_TAG])hide(proto, TO_STRING_TAG, NAME);\n    Iterators[NAME] = ArrayValues;\n    for(key in $iterators)if(!proto[key])redefine(proto, key, $iterators[key], true);\n  }\n}\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/web.dom.iterable.js\n// module id = 67\n// module chunks = 0", "'use strict';\nvar addToUnscopables = require('./_add-to-unscopables')\n  , step             = require('./_iter-step')\n  , Iterators        = require('./_iterators')\n  , toIObject        = require('./_to-iobject');\n\n// 22.1.3.4 Array.prototype.entries()\n// 22.1.3.13 Array.prototype.keys()\n// 22.1.3.29 Array.prototype.values()\n// 22.1.3.30 Array.prototype[@@iterator]()\nmodule.exports = require('./_iter-define')(Array, 'Array', function(iterated, kind){\n  this._t = toIObject(iterated); // target\n  this._i = 0;                   // next index\n  this._k = kind;                // kind\n// 22.1.5.2.1 %ArrayIteratorPrototype%.next()\n}, function(){\n  var O     = this._t\n    , kind  = this._k\n    , index = this._i++;\n  if(!O || index >= O.length){\n    this._t = undefined;\n    return step(1);\n  }\n  if(kind == 'keys'  )return step(0, index);\n  if(kind == 'values')return step(0, O[index]);\n  return step(0, [index, O[index]]);\n}, 'values');\n\n// argumentsList[@@iterator] is %ArrayProto_values% (9.4.4.6, 9.4.4.7)\nIterators.Arguments = Iterators.Array;\n\naddToUnscopables('keys');\naddToUnscopables('values');\naddToUnscopables('entries');\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/es6.array.iterator.js\n// module id = 68\n// module chunks = 0", "// 22.1.3.31 Array.prototype[@@unscopables]\nvar UNSCOPABLES = require('./_wks')('unscopables')\n  , ArrayProto  = Array.prototype;\nif(ArrayProto[UNSCOPABLES] == undefined)require('./_hide')(ArrayProto, UNSCOPABLES, {});\nmodule.exports = function(key){\n  ArrayProto[UNSCOPABLES][key] = true;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_add-to-unscopables.js\n// module id = 69\n// module chunks = 0", "'use strict';\nvar strong = require('./_collection-strong');\n\n// 23.2 Set Objects\nmodule.exports = require('./_collection')('Set', function(get){\n  return function Set(){ return get(this, arguments.length > 0 ? arguments[0] : undefined); };\n}, {\n  // 23.2.3.1 Set.prototype.add(value)\n  add: function add(value){\n    return strong.def(this, value = value === 0 ? 0 : value, value);\n  }\n}, strong);\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/es6.set.js\n// module id = 70\n// module chunks = 0", "'use strict';\nvar dP          = require('./_object-dp').f\n  , create      = require('./_object-create')\n  , redefineAll = require('./_redefine-all')\n  , ctx         = require('./_ctx')\n  , anInstance  = require('./_an-instance')\n  , defined     = require('./_defined')\n  , forOf       = require('./_for-of')\n  , $iterDefine = require('./_iter-define')\n  , step        = require('./_iter-step')\n  , setSpecies  = require('./_set-species')\n  , DESCRIPTORS = require('./_descriptors')\n  , fastKey     = require('./_meta').fastKey\n  , SIZE        = DESCRIPTORS ? '_s' : 'size';\n\nvar getEntry = function(that, key){\n  // fast case\n  var index = fastKey(key), entry;\n  if(index !== 'F')return that._i[index];\n  // frozen object case\n  for(entry = that._f; entry; entry = entry.n){\n    if(entry.k == key)return entry;\n  }\n};\n\nmodule.exports = {\n  getConstructor: function(wrapper, NAME, IS_MAP, ADDER){\n    var C = wrapper(function(that, iterable){\n      anInstance(that, C, NAME, '_i');\n      that._i = create(null); // index\n      that._f = undefined;    // first entry\n      that._l = undefined;    // last entry\n      that[SIZE] = 0;         // size\n      if(iterable != undefined)forOf(iterable, IS_MAP, that[ADDER], that);\n    });\n    redefineAll(C.prototype, {\n      // ******** Map.prototype.clear()\n      // ******** Set.prototype.clear()\n      clear: function clear(){\n        for(var that = this, data = that._i, entry = that._f; entry; entry = entry.n){\n          entry.r = true;\n          if(entry.p)entry.p = entry.p.n = undefined;\n          delete data[entry.i];\n        }\n        that._f = that._l = undefined;\n        that[SIZE] = 0;\n      },\n      // 23.1.3.3 Map.prototype.delete(key)\n      // 23.2.3.4 Set.prototype.delete(value)\n      'delete': function(key){\n        var that  = this\n          , entry = getEntry(that, key);\n        if(entry){\n          var next = entry.n\n            , prev = entry.p;\n          delete that._i[entry.i];\n          entry.r = true;\n          if(prev)prev.n = next;\n          if(next)next.p = prev;\n          if(that._f == entry)that._f = next;\n          if(that._l == entry)that._l = prev;\n          that[SIZE]--;\n        } return !!entry;\n      },\n      // 23.2.3.6 Set.prototype.forEach(callbackfn, thisArg = undefined)\n      // 23.1.3.5 Map.prototype.forEach(callbackfn, thisArg = undefined)\n      forEach: function forEach(callbackfn /*, that = undefined */){\n        anInstance(this, C, 'forEach');\n        var f = ctx(callbackfn, arguments.length > 1 ? arguments[1] : undefined, 3)\n          , entry;\n        while(entry = entry ? entry.n : this._f){\n          f(entry.v, entry.k, this);\n          // revert to the last existing entry\n          while(entry && entry.r)entry = entry.p;\n        }\n      },\n      // ******** Map.prototype.has(key)\n      // ******** Set.prototype.has(value)\n      has: function has(key){\n        return !!getEntry(this, key);\n      }\n    });\n    if(DESCRIPTORS)dP(C.prototype, 'size', {\n      get: function(){\n        return defined(this[SIZE]);\n      }\n    });\n    return C;\n  },\n  def: function(that, key, value){\n    var entry = getEntry(that, key)\n      , prev, index;\n    // change existing entry\n    if(entry){\n      entry.v = value;\n    // create new entry\n    } else {\n      that._l = entry = {\n        i: index = fastKey(key, true), // <- index\n        k: key,                        // <- key\n        v: value,                      // <- value\n        p: prev = that._l,             // <- previous entry\n        n: undefined,                  // <- next entry\n        r: false                       // <- removed\n      };\n      if(!that._f)that._f = entry;\n      if(prev)prev.n = entry;\n      that[SIZE]++;\n      // add to index\n      if(index !== 'F')that._i[index] = entry;\n    } return that;\n  },\n  getEntry: getEntry,\n  setStrong: function(C, NAME, IS_MAP){\n    // add .keys, .values, .entries, [@@iterator]\n    // 23.1.3.4, 23.1.3.8, ********1, ********2, 23.2.3.5, 23.2.3.8, 23.2.3.10, 23.2.3.11\n    $iterDefine(C, NAME, function(iterated, kind){\n      this._t = iterated;  // target\n      this._k = kind;      // kind\n      this._l = undefined; // previous\n    }, function(){\n      var that  = this\n        , kind  = that._k\n        , entry = that._l;\n      // revert to the last existing entry\n      while(entry && entry.r)entry = entry.p;\n      // get next entry\n      if(!that._t || !(that._l = entry = entry ? entry.n : that._t._f)){\n        // or finish the iteration\n        that._t = undefined;\n        return step(1);\n      }\n      // return step by kind\n      if(kind == 'keys'  )return step(0, entry.k);\n      if(kind == 'values')return step(0, entry.v);\n      return step(0, [entry.k, entry.v]);\n    }, IS_MAP ? 'entries' : 'values' , !IS_MAP, true);\n\n    // add [@@species], ********, ********\n    setSpecies(NAME);\n  }\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_collection-strong.js\n// module id = 71\n// module chunks = 0", "'use strict';\nvar global      = require('./_global')\n  , dP          = require('./_object-dp')\n  , DESCRIPTORS = require('./_descriptors')\n  , SPECIES     = require('./_wks')('species');\n\nmodule.exports = function(KEY){\n  var C = global[KEY];\n  if(DESCRIPTORS && C && !C[SPECIES])dP.f(C, SPECIES, {\n    configurable: true,\n    get: function(){ return this; }\n  });\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_set-species.js\n// module id = 72\n// module chunks = 0", "'use strict';\nvar global            = require('./_global')\n  , $export           = require('./_export')\n  , redefine          = require('./_redefine')\n  , redefineAll       = require('./_redefine-all')\n  , meta              = require('./_meta')\n  , forOf             = require('./_for-of')\n  , anInstance        = require('./_an-instance')\n  , isObject          = require('./_is-object')\n  , fails             = require('./_fails')\n  , $iterDetect       = require('./_iter-detect')\n  , setToStringTag    = require('./_set-to-string-tag')\n  , inheritIfRequired = require('./_inherit-if-required');\n\nmodule.exports = function(NAME, wrapper, methods, common, IS_MAP, IS_WEAK){\n  var Base  = global[NAME]\n    , C     = Base\n    , ADDER = IS_MAP ? 'set' : 'add'\n    , proto = C && C.prototype\n    , O     = {};\n  var fixMethod = function(KEY){\n    var fn = proto[KEY];\n    redefine(proto, KEY,\n      KEY == 'delete' ? function(a){\n        return IS_WEAK && !isObject(a) ? false : fn.call(this, a === 0 ? 0 : a);\n      } : KEY == 'has' ? function has(a){\n        return IS_WEAK && !isObject(a) ? false : fn.call(this, a === 0 ? 0 : a);\n      } : KEY == 'get' ? function get(a){\n        return IS_WEAK && !isObject(a) ? undefined : fn.call(this, a === 0 ? 0 : a);\n      } : KEY == 'add' ? function add(a){ fn.call(this, a === 0 ? 0 : a); return this; }\n        : function set(a, b){ fn.call(this, a === 0 ? 0 : a, b); return this; }\n    );\n  };\n  if(typeof C != 'function' || !(IS_WEAK || proto.forEach && !fails(function(){\n    new C().entries().next();\n  }))){\n    // create collection constructor\n    C = common.getConstructor(wrapper, NAME, IS_MAP, ADDER);\n    redefineAll(C.prototype, methods);\n    meta.NEED = true;\n  } else {\n    var instance             = new C\n      // early implementations not supports chaining\n      , HASNT_CHAINING       = instance[ADDER](IS_WEAK ? {} : -0, 1) != instance\n      // V8 ~  Chromium 40- weak-collections throws on primitives, but should return false\n      , THROWS_ON_PRIMITIVES = fails(function(){ instance.has(1); })\n      // most early implementations doesn't supports iterables, most modern - not close it correctly\n      , ACCEPT_ITERABLES     = $iterDetect(function(iter){ new C(iter); }) // eslint-disable-line no-new\n      // for early implementations -0 and +0 not the same\n      , BUGGY_ZERO = !IS_WEAK && fails(function(){\n        // V8 ~ Chromium 42- fails only with 5+ elements\n        var $instance = new C()\n          , index     = 5;\n        while(index--)$instance[ADDER](index, index);\n        return !$instance.has(-0);\n      });\n    if(!ACCEPT_ITERABLES){ \n      C = wrapper(function(target, iterable){\n        anInstance(target, C, NAME);\n        var that = inheritIfRequired(new Base, target, C);\n        if(iterable != undefined)forOf(iterable, IS_MAP, that[ADDER], that);\n        return that;\n      });\n      C.prototype = proto;\n      proto.constructor = C;\n    }\n    if(THROWS_ON_PRIMITIVES || BUGGY_ZERO){\n      fixMethod('delete');\n      fixMethod('has');\n      IS_MAP && fixMethod('get');\n    }\n    if(BUGGY_ZERO || HASNT_CHAINING)fixMethod(ADDER);\n    // weak collections should not contains .clear method\n    if(IS_WEAK && proto.clear)delete proto.clear;\n  }\n\n  setToStringTag(C, NAME);\n\n  O[NAME] = C;\n  $export($export.G + $export.W + $export.F * (C != Base), O);\n\n  if(!IS_WEAK)common.setStrong(C, NAME, IS_MAP);\n\n  return C;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_collection.js\n// module id = 73\n// module chunks = 0", "var isObject       = require('./_is-object')\n  , setPrototypeOf = require('./_set-proto').set;\nmodule.exports = function(that, target, C){\n  var P, S = target.constructor;\n  if(S !== C && typeof S == 'function' && (P = S.prototype) !== C.prototype && isObject(P) && setPrototypeOf){\n    setPrototypeOf(that, P);\n  } return that;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_inherit-if-required.js\n// module id = 74\n// module chunks = 0", "// Works with __proto__ only. Old v8 can't work with null proto objects.\n/* eslint-disable no-proto */\nvar isObject = require('./_is-object')\n  , anObject = require('./_an-object');\nvar check = function(O, proto){\n  anObject(O);\n  if(!isObject(proto) && proto !== null)throw TypeError(proto + \": can't set as prototype!\");\n};\nmodule.exports = {\n  set: Object.setPrototypeOf || ('__proto__' in {} ? // eslint-disable-line\n    function(test, buggy, set){\n      try {\n        set = require('./_ctx')(Function.call, require('./_object-gopd').f(Object.prototype, '__proto__').set, 2);\n        set(test, []);\n        buggy = !(test instanceof Array);\n      } catch(e){ buggy = true; }\n      return function setPrototypeOf(O, proto){\n        check(O, proto);\n        if(buggy)O.__proto__ = proto;\n        else set(O, proto);\n        return O;\n      };\n    }({}, false) : undefined),\n  check: check\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_set-proto.js\n// module id = 75\n// module chunks = 0", "var pIE            = require('./_object-pie')\n  , createDesc     = require('./_property-desc')\n  , toIObject      = require('./_to-iobject')\n  , toPrimitive    = require('./_to-primitive')\n  , has            = require('./_has')\n  , IE8_DOM_DEFINE = require('./_ie8-dom-define')\n  , gOPD           = Object.getOwnPropertyDescriptor;\n\nexports.f = require('./_descriptors') ? gOPD : function getOwnPropertyDescriptor(O, P){\n  O = toIObject(O);\n  P = toPrimitive(P, true);\n  if(IE8_DOM_DEFINE)try {\n    return gOPD(O, P);\n  } catch(e){ /* empty */ }\n  if(has(O, P))return createDesc(!pIE.f.call(O, P), O[P]);\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_object-gopd.js\n// module id = 76\n// module chunks = 0", "// https://github.com/DavidBruant/Map-Set.prototype.toJSON\nvar $export  = require('./_export');\n\n$export($export.P + $export.R, 'Set', {toJSON: require('./_collection-to-json')('Set')});\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/es7.set.to-json.js\n// module id = 77\n// module chunks = 0", "// https://github.com/DavidBruant/Map-Set.prototype.toJSON\nvar classof = require('./_classof')\n  , from    = require('./_array-from-iterable');\nmodule.exports = function(NAME){\n  return function toJSON(){\n    if(classof(this) != NAME)throw TypeError(NAME + \"#toJSON isn't generic\");\n    return from(this);\n  };\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_collection-to-json.js\n// module id = 78\n// module chunks = 0", "var forOf = require('./_for-of');\n\nmodule.exports = function(iter, ITERATOR){\n  var result = [];\n  forOf(iter, false, result.push, result, ITERATOR);\n  return result;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/core-js/modules/_array-from-iterable.js\n// module id = 79\n// module chunks = 0", "/**\n * @file Exposes `feather` object.\n */\n\nimport icons from '../dist/icons.json';\nimport toSvg from './to-svg';\nimport replace from './replace';\n\nmodule.exports = { icons, toSvg, replace };\n\n\n\n// WEBPACK FOOTER //\n// ./src/index.js", "/**\n * @file Implements `replace` function.\n */\n\n/* global document, DOMParser */\n\nimport icons from '../dist/icons.json';\nimport toSvg from './to-svg';\n\n/**\n * Replace all elements that have a `data-feather` attribute with SVG markup\n * corresponding to the element's `data-feather` attribute value.\n * @param {Object} options\n */\nexport default function replace(options = {}) {\n  if (typeof document === 'undefined') {\n    throw new Error('`feather.replace()` only works in a browser environment.');\n  }\n\n  const elementsToReplace = document.querySelectorAll('[data-feather]');\n\n  Array.from(elementsToReplace).forEach(element => replaceElement(element, options));\n}\n\n/**\n * Replace single element with SVG markup\n * corresponding to the element's `data-feather` attribute value.\n * @param {Element} element\n * @param {Object} options\n */\nfunction replaceElement(element, options) {\n  const key = element.getAttribute('data-feather');\n\n  if (!key) {\n    console.error('The required `data-feather` attribute has no value.');\n    return;\n  }\n\n  if (!icons[key]) {\n    console.error(`No icon matching '${key}'. See the complete list of icons at https://feathericons.com`);\n    return;\n  }\n\n  const elementClassAttr = element.getAttribute('class');\n  const classNames = (\n    options.class ? `${options.class} ${elementClassAttr}` : elementClassAttr\n  );\n\n  const svgString = toSvg(key, Object.assign({}, options, { class: classNames }));\n  const svgDocument = new DOMParser().parseFromString(svgString, 'image/svg+xml');\n  const svgElement = svgDocument.querySelector('svg');\n\n  element.parentNode.replaceChild(svgElement, element);\n}\n\n\n\n// WEBPACK FOOTER //\n// ./src/replace.js"], "sourceRoot": ""}