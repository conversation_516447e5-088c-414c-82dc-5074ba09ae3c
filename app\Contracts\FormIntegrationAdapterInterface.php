<?php

namespace App\Contracts;

use App\Models\FormIntegrationSetting;

interface FormIntegrationAdapterInterface
{
    /**
     * Transform form data according to the integration setting
     *
     * @param array $formData The submitted form data
     * @param FormIntegrationSetting $integrationSetting The integration configuration
     * @return array The transformed data ready for target system submission
     */
    public function transformFormData(array $formData, FormIntegrationSetting $integrationSetting): array;

    /**
     * Validate the form data against the integration setting
     *
     * @param array $formData The submitted form data
     * @param FormIntegrationSetting $integrationSetting The integration configuration
     * @return array Array of validation errors (empty if valid)
     */
    public function validateFormData(array $formData, FormIntegrationSetting $integrationSetting): array;

    /**
     * Get the target system this adapter supports
     *
     * @return string The target system name (e.g., 'CSI', 'SAP')
     */
    public function getSupportedTarget(): string;

    /**
     * Get supported process types for this target system
     *
     * @return array Array of supported process types
     */
    public function getSupportedProcesses(): array;

    /**
     * Prepare data for API submission
     *
     * @param array $transformedData The transformed form data
     * @param FormIntegrationSetting $integrationSetting The integration configuration
     * @return array The final data structure for API submission
     */
    public function prepareApiData(array $transformedData, FormIntegrationSetting $integrationSetting): array;

    /**
     * Handle data type conversion based on endpoint field requirements
     *
     * @param mixed $value The value to convert
     * @param array $fieldDefinition The endpoint field definition
     * @return mixed The converted value
     */
    public function convertDataType($value, array $fieldDefinition);
}
