<?php

namespace App\Models;

use App\Traits\DataTableFilter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class DataCaptureField extends Model
{
    use HasFactory, SoftDeletes, DataTableFilter;

    protected $fillable = [
        'title',
        'key',
        'icon',
        'schema',
        'process_type',
        'tenant_id',
    ];

    protected $casts = [
        'schema' => 'json',
    ];
    public static function dataTableColumns(): array
    {
        return [
            [
                'data' => 'DT_RowIndex',
                'name' => 'DT_RowIndex',
                'title' => '#',
                'orderable' => false,
                'searchable' => false,
                'filter' => false,
                'width' => '10',
            ],
            [
                'data' => 'title',
                'name' => 'title',
                'title' => __('cruds.fields.field_title'),
                'filter' => 'text',
            ],
            [
                'data' => 'key',
                'name' => 'key',
                'title' => __('cruds.fields.field_key'),
                'filter' => 'text',
            ],
            [
                'data' => 'process_type',
                'name' => 'process_type',
                'title' => __('cruds.fields.process_type'),
                'filter' => 'text',
            ],
            [
                'data' => 'updated_at',
                'name' => 'updated_at',
                'title' => __('cruds.fields.updated_at'),
                'class' => 'text-right',
                'filter' => 'date_range',
            ],
            [
                'data' => 'action',
                'name' => 'action',
                'title' => __('global.actions'),
                'orderable' => false,
                'searchable' => false,
                'filter' => false,
                'width' => '150',
            ],
        ];
    }

    public static function getComponents()
    {
        $templates = DataCaptureField::orderBy('title', 'asc')->get();
        $components = [];
        foreach ($templates as $template) {
            $components[$template->key] = [
                "title" => $template->title,
                "key" => $template->key,
                "icon" => $template->icon,
                "schema" => json_decode($template->schema, true)
            ];
        }
        return $components;
    }
    public function getCreatedAtAttribute($value)
    {
        return \Carbon\Carbon::parse($value)->format('m/d/Y');
    }
    public function getUpdatedAtAttribute($value)
    {
        return \Carbon\Carbon::parse($value)->format('m/d/Y');
    }
}
