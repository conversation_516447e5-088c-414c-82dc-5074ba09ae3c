<nav class="page-sidebar {{ 1 == 1 ? 'sp_admin' : '' }}" data-pages="sidebar">
    <!-- BEGIN SIDEBAR MENU TOP TRAY CONTENT-->

    <!-- <PERSON>ND SIDEBAR MENU TOP TRAY CONTENT-->
    <!-- BEGIN SIDEBAR MENU HEADER-->
    <div class="sidebar-header">
        <a href="#">
            <h4 style="color:#fff;display: inline;font-weight: 600;">{{ env('APP_NAME') }}</h4>
        </a>
        <!--<img src="/logo.png" alt="logo" class="brand" data-src="/logo.png" data-src-retina="/logo.png" width="78" height="22">-->
        <div class="sidebar-header-controls">
            <!--            <button type="button" class="btn btn-xs sidebar-slide-toggle btn-link m-l-20 hidden-md-down" data-pages-toggle="#appMenu"><i class="fa fa fa-angle-down fs-16"></i>
            </button>-->
            <button type="button" class="btn btn-link hidden-md-down" data-toggle-pin="sidebar"><i
                    class="fa fs-12"></i>
            </button>
        </div>
    </div>
    <!-- END SIDEBAR MENU HEADER-->
    <!-- START SIDEBAR MENU -->
    <div class="sidebar-menu">
        <!-- BEGIN SIDEBAR MENU ITEMS-->
        <ul class="menu-items">








            {{-- Endpoint Configuration Menu - TODO: Restrict to Super Admin when user management is ready --}}
            @if (auth()->check())
                {{-- <li class="m-t-30 {{ request()->is('incoming*') ? 'active' : '' }}">
                    <a href="{{ route('incoming') }}" class="">
                        <span class="title">{{ trans('global.menu.incoming') }}</span>
                    </a>

                </li> --}}
                @foreach (\App\Models\Form::$form_modules as $key => $module)
                    <li class="{{$loop->iteration==1?"m-t-30":""}}  {{ request()->is($key) ? 'active' : '' }}">
                        <a href="{{ route('mobile.forms', $key) }}" class="">
                            <span class="title">{{ trans("global.menu.$key") }}</span>
                        </a>
                        {{-- <span class="icon-thumbnail">
                        <i class="fa fa-cogs"></i>
                    </span> --}}
                    </li>
                @endforeach
            @endif
            <li class="">
                <a href="/" class="">
                    <span class="title">{{ trans('global.menu.switch_to_web') }}</span>
                </a>
                <span class="icon-thumbnail">
                    <i class="fa fa-web"></i>
                </span>
            </li>

        </ul>
        <div class="clearfix"></div>
    </div>

    <!-- END SIDEBAR MENU -->
</nav>
