@extends('layouts.admin')
@section('pageTitle', __('global.view') . ' ' . __('cruds.fieldMappingConfiguration.title_singular'))

@push('styles')
<style>
    .required:after {
        content: " *";
        color: red;
    }
    .help-block {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 0.25rem;
    }
    .form-group {
        margin-bottom: 1.5rem;
    }
    .card-header h4, .card-header h5 {
        margin: 0;
        color: #495057;
    }
    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid rgba(0,0,0,.125);
        padding: 0.75rem 1.25rem;
    }
    .card-title {
        font-size: 1.1rem;
        font-weight: 600;
    }
    .form-label {
        font-weight: 500;
        margin-bottom: 0.25rem;
        color: #495057;
    }
    .form-control-plaintext {
        padding: 0.375rem 0;
        margin-bottom: 0;
        line-height: 1.5;
        background-color: transparent;
        border: 0;
        min-height: 1.5rem;
    }
    .table th {
        background-color: #f8f9fa;
        border-top: 1px solid #dee2e6;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.75rem;
        letter-spacing: 0.5px;
    }
    .badge {
        font-weight: 500;
        padding: 0.35em 0.65em;
        font-size: 0.75em;
    }
</style>
@endpush

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header separator">
                    <div class="card-title mainheading">
                        <h4>{{ __('global.view') }} {{ __('cruds.fieldMappingConfiguration.title_singular') }}</h4>
                    </div>
                    <div class="card-controls">
                        <ul>
                            <li>
                                <a href="{{ route('admin.field-mapping-configurations.edit', $fieldMappingConfiguration->id) }}" class="btn btn-primary btn-sm">
                                    <i class="fa fa-edit me-1"></i> {{ __('global.edit') }}
                                </a>
                            </li>
                            <li>
                                <x-buttons.back :route="route('admin.field-mapping-configurations.index')" :text="__('global.back_to_list')" />
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Basic Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">{{ __('cruds.fieldMappingConfiguration.fields.basic_information') }}</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-4">
                                        <label class="form-label text-muted small mb-1">{{ __('cruds.fieldMappingConfiguration.fields.name') }}</label>
                                        <p class="form-control-plaintext font-weight-medium">{{ $fieldMappingConfiguration->name ?? 'N/A' }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-4">
                                        <label class="form-label text-muted small mb-1">{{ __('cruds.fieldMappingConfiguration.fields.status') }}</label>
                                        <p class="form-control-plaintext">
                                            @if($fieldMappingConfiguration->is_active)
                                                <span class="badge bg-success">{{ __('cruds.fields.active') }}</span>
                                            @else
                                                <span class="badge bg-secondary">{{ __('cruds.fields.inactive') }}</span>
                                            @endif
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <div class="form-group mb-0">
                                        <label class="form-label text-muted small mb-1">{{ __('cruds.fieldMappingConfiguration.fields.description') }}</label>
                                        <p class="form-control-plaintext">{{ $fieldMappingConfiguration->description ?: 'N/A' }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">{{ __('global.created_by') }}</label>
                                <p class="form-control-plaintext">{{ $fieldMappingConfiguration->createdBy->name ?? 'System' }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            
                            <div class="form-group">
                                <label class="form-label">{{ __('global.created_at') }}</label>
                                <p class="form-control-plaintext">
                                    {{ $fieldMappingConfiguration->created_at ? \Carbon\Carbon::parse($fieldMappingConfiguration->created_at)->format('Y-m-d H:i:s') : 'N/A' }}
                                </p>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">{{ __('global.updated_by') }}</label>
                                <p class="form-control-plaintext">{{ $fieldMappingConfiguration->updatedBy->name ?? 'System' }}</p>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">{{ __('global.updated_at') }}</label>
                                <p class="form-control-plaintext">
                                    {{ $fieldMappingConfiguration->updated_at ? \Carbon\Carbon::parse($fieldMappingConfiguration->updated_at)->format('Y-m-d H:i:s') : 'N/A' }}
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Form Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">{{ __('cruds.fieldMappingConfiguration.fields.form_information') }}</h5>
                        </div>
                        <div class="card-body">
                            @if($fieldMappingConfiguration->form)
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-4">
                                            <label class="form-label text-muted small mb-1">{{ __('cruds.form.fields.title') }}</label>
                                            <p class="form-control-plaintext font-weight-medium">
                                                {{ $fieldMappingConfiguration->form->title ?? 'N/A' }}
                                            </p>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-4">
                                            <label class="form-label text-muted small mb-1">{{ __('cruds.form.fields.status') }}</label>
                                            <p class="form-control-plaintext">
                                                @if($fieldMappingConfiguration->form->is_active)
                                                    <span class="badge bg-success">{{ __('cruds.fields.active') }}</span>
                                                @else
                                                    <span class="badge bg-secondary">{{ __('cruds.fields.inactive') }}</span>
                                                @endif
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-0">
                                            <label class="form-label text-muted small mb-1">{{ __('cruds.form.fields.form_type') }}</label>
                                            <p class="form-control-plaintext">
                                                <span class="badge bg-info">{{ $fieldMappingConfiguration->form->form_type ?? 'N/A' }}</span>
                                            </p>
                                        </div>
                                    </div>
                                </div>    
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">{{ __('cruds.form.fields.user_group') }}</label>
                                            <p class="form-control-plaintext">
                                                {{ $fieldMappingConfiguration->form->userGroup->name ?? __('global.not_assigned') }}
                                            </p>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label class="form-label">{{ __('global.created_at') }}</label>
                                            <p class="form-control-plaintext">
                                                {{ $fieldMappingConfiguration->form->created_at ? \Carbon\Carbon::parse($fieldMappingConfiguration->form->created_at)->format('Y-m-d H:i:s') : 'N/A' }}
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Form Fields -->
                                <div class="mt-4">
                                    <h6 class="mb-3">{{ __('cruds.form.fields.fields') }}</h6>
                                    <div class="row">
                                        @if(isset($fieldMappingConfiguration->form_fields) && (is_array($fieldMappingConfiguration->form_fields) || $fieldMappingConfiguration->form_fields instanceof \Countable))
                                            @foreach($fieldMappingConfiguration->form_fields as $field)
                                            <div class="col-md-4 mb-3">
                                                <div class="border p-3 rounded bg-light">
                                                    <div class="d-flex justify-content-between align-items-start">
                                                        <strong class="mb-1">{{ $field['label'] ?? $field['key'] }}</strong>
                                                        @if($field['required'] ?? false)
                                                            <span class="badge bg-danger">{{ __('cruds.fields.required') }}</span>
                                                        @endif
                                                    </div>
                                                    <div class="text-muted small mt-1">
                                                        <div>{{ __('cruds.fields.key') }}: <code>{{ $field['key'] ?? 'N/A' }}</code></div>
                                                        <div>{{ __('cruds.fields.type') }}: {{ $field['type'] ?? 'N/A' }}</div>
                                                    </div>
                                                </div>
                                            </div>
                                            @endforeach
                                        @else
                                            <div class="col-12">
                                                <div class="alert alert-info">
                                                    <i class="fa fa-info-circle"></i> {{ __('global.no_data_available') }}
                                                </div>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            @else
                                <div class="alert alert-warning">
                                    <i class="fa fa-exclamation-triangle"></i> {{ __('global.associated_form_not_found') }}
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Endpoint Configuration Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">{{ __('cruds.fieldMappingConfiguration.fields.integration_configuration') }}</h5>
                        </div>
                        <div class="card-body">
                            @if($fieldMappingConfiguration->endpointConfiguration)
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-4">
                                            <label class="form-label text-muted small mb-1">{{ trans('cruds.endpointConfiguration.fields.name') }}</label>
                                            <p class="form-control-plaintext font-weight-medium">{{ $fieldMappingConfiguration->endpointConfiguration->name ?? 'N/A' }}</p>
                                        </div>
                                        
                                        <div class="form-group mb-4">
                                            <label class="form-label text-muted small mb-1">{{ trans('cruds.endpointConfiguration.fields.external_system') }}</label>
                                            <p class="form-control-plaintext">
                                                @if($fieldMappingConfiguration->endpointConfiguration->external_system_name ?? false)
                                                    <span class="badge bg-info">{{ $fieldMappingConfiguration->endpointConfiguration->external_system_name }}</span>
                                                @else
                                                    <span class="badge bg-secondary">{{ $fieldMappingConfiguration->endpointConfiguration->external_system }}</span>
                                                @endif
                                            </p>
                                        </div>
                                        
                                        <div class="form-group mb-0">
                                            <label class="form-label text-muted small mb-1">{{ trans('cruds.endpointConfiguration.fields.integration_method') }}</label>
                                            <p class="form-control-plaintext">
                                                <span class="badge bg-secondary">{{ $fieldMappingConfiguration->endpointConfiguration->integration_method }}</span>
                                            </p>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="form-group mb-4">
                                            <label class="form-label text-muted small mb-1">{{ trans('cruds.endpointConfiguration.fields.process_type') }}</label>
                                            <p class="form-control-plaintext">
                                                <span class="badge bg-info">{{ $fieldMappingConfiguration->endpointConfiguration->process_type }}</span>
                                            </p>
                                        </div>
                                        
                                        <div class="form-group mb-4">
                                            <label class="form-label text-muted small mb-1">{{ trans('cruds.endpointConfiguration.fields.endpoint_type') }}</label>
                                            <p class="form-control-plaintext">
                                                <span class="badge bg-secondary">{{ $fieldMappingConfiguration->endpointConfiguration->endpoint_type }}</span>
                                            </p>
                                        </div>
                                        
                                        <div class="form-group mb-0">
                                            <label class="form-label text-muted small mb-1">{{ trans('cruds.endpointConfiguration.fields.status') }}</label>
                                            <p class="form-control-plaintext">
                                                @if($fieldMappingConfiguration->endpointConfiguration->is_active)
                                                    <span class="badge bg-success">{{ trans('cruds.fields.active') }}</span>
                                                @else
                                                    <span class="badge bg-secondary">{{ trans('cruds.fields.inactive') }}</span>
                                                @endif
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row mt-4">
                                    <div class="col-12">
                                        <div class="form-group mb-0">
                                            <label class="form-label text-muted small mb-1">{{ trans('cruds.endpointConfiguration.fields.url') }}</label>
                                            <p class="form-control-plaintext">
                                                <code>{{ $fieldMappingConfiguration->endpointConfiguration->url }}</code>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            @else
                                <div class="alert alert-warning">
                                    <i class="fa fa-exclamation-triangle"></i> {{ __('global.associated_endpoint_not_found') }}
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Field Mappings -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">{{ __('cruds.fieldMappingConfiguration.fields.field_mappings') }}</h5>
                        </div>
                        <div class="card-body p-0">
                            @if($fieldMappingConfiguration->field_mappings && count($fieldMappingConfiguration->field_mappings) > 0)
                                <div class="table-responsive">
                                    <table class="table table-hover align-middle mb-0">
                                        <thead class="table-light">
                                            <tr>
                                                <th width="35%" class="ps-3">{{ __('cruds.fieldMapping.fields.form_field') }}</th>
                                                <th width="35%">{{ __('cruds.fieldMapping.fields.endpoint_field') }}</th>
                                                <th width="15%" class="text-center">{{ __('cruds.fieldMapping.fields.form_field_type') }}</th>
                                                <th width="15%" class="text-center pe-3">{{ __('cruds.fieldMapping.fields.endpoint_field_type') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($fieldMappingConfiguration->field_mappings as $formField => $endpointField)
                                                @php
                                                    $formFieldInfo = collect($fieldMappingConfiguration->form_fields)->firstWhere('key', $formField) ?? 
                                                                   collect($fieldMappingConfiguration->form_fields)->firstWhere('name', $formField);
                                                    $endpointFieldInfo = collect($fieldMappingConfiguration->endpoint_fields ?? [])->firstWhere('field', $endpointField) ?? 
                                                                         collect($fieldMappingConfiguration->endpoint_fields ?? [])->firstWhere('name', $endpointField);
                                                @endphp
                                                <tr>
                                                    <td class="ps-3">
                                                        <div class="d-flex flex-column">
                                                            <strong class="text-primary">{{ $formFieldInfo['label'] ?? $formField }}</strong>
                                                            <small class="text-muted">{{ $formField }}</small>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="d-flex flex-column">
                                                            <span class="font-weight-medium">{{ $endpointFieldInfo['label'] ?? $endpointField }}</span>
                                                            <small class="text-muted">{{ $endpointField }}</small>
                                                            @if($endpointFieldInfo && ($endpointFieldInfo['required'] ?? false))
                                                                <small class="text-danger"><i class="fa fa-asterisk"></i> {{ __('cruds.fields.required') }}</small>
                                                            @endif
                                                        </div>
                                                    </td>
                                                    <td class="text-center">
                                                        @if($formFieldInfo && ($formFieldInfo['type'] ?? false))
                                                            <span class="badge bg-secondary">{{ $formFieldInfo['type'] }}</span>
                                                        @else
                                                            <span class="badge bg-light text-muted">N/A</span>
                                                        @endif
                                                    </td>
                                                    <td class="text-center pe-3">
                                                        @if($endpointFieldInfo && ($endpointFieldInfo['type'] ?? false))
                                                            <span class="badge bg-info">{{ $endpointFieldInfo['type'] }}</span>
                                                        @else
                                                            <span class="badge bg-light text-muted">N/A</span>
                                                        @endif
                                                    </td>
                                                </tr>
                                                @if(($endpointFieldInfo && ($endpointFieldInfo['description'] ?? false)) || ($formFieldInfo && ($formFieldInfo['description'] ?? false)))
                                                <tr class="bg-light">
                                                    <td colspan="4" class="small text-muted p-2 ps-3">
                                                        @if($formFieldInfo && ($formFieldInfo['description'] ?? false))
                                                            <div><strong>Form Field:</strong> {{ $formFieldInfo['description'] }}</div>
                                                        @endif
                                                        @if($endpointFieldInfo && ($endpointFieldInfo['description'] ?? false))
                                                            <div class="mt-1"><strong>Endpoint Field:</strong> {{ $endpointFieldInfo['description'] }}</div>
                                                        @endif
                                                    </td>
                                                </tr>
                                                @endif
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="alert alert-info m-3">
                                    <i class="fa fa-info-circle me-2"></i> {{ __('global.no_field_mappings_configured') }}
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Validation Status -->
                    <div class="mt-4">
                        @if(!empty($validationErrors))
                            <div class="card border-warning">
                                <div class="card-header bg-warning text-white">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h5 class="mb-0">
                                            <i class="fa fa-exclamation-triangle me-2"></i> {{ __('cruds.fieldMappingConfiguration.validation_issues') }}
                                        </h5>
                                        <span class="badge bg-white text-warning">{{ count($validationErrors) }} {{ __('global.issues') }}</span>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="alert alert-warning mb-0">
                                        <ul class="mb-0 ps-3">
                                            @foreach($validationErrors as $error)
                                                <li class="mb-1">{{ $error }}</li>
                                            @endforeach
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        @else
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0">
                                        <i class="fa fa-check-circle me-2"></i> {{ __('cruds.fieldMappingConfiguration.validation_status') }}
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="alert alert-success d-flex align-items-center mb-0">
                                        <i class="fa fa-check-circle fa-2x me-3"></i>
                                        <div>
                                            <h5 class="alert-heading">{{ __('cruds.fieldMappingConfiguration.valid_configuration') }}</h5>
                                            <p class="mb-0">{{ __('cruds.fieldMappingConfiguration.configuration_valid_message') }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('styles')
<style>
.badge {
    font-size: 0.75em;
}

.table th {
    background-color: #f8f9fa;
    border-top: none;
}

.card {
    box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 3px rgba(0,0,0,.2);
    margin-bottom: 1rem;
}
</style>
@endsection
