<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class FieldMappingConfiguration extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'form_id',
        'integration_configuration_id',
        'field_mappings',
        'description',
        'is_active',
        'state',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    protected $casts = [
        'field_mappings' => 'array',
        'is_active' => 'boolean',
        'updated_at' => 'datetime',
    ];

    /**
     * The attributes that should be mutated to dates.
     *
     * @var array
     */
    protected $dates = [
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * Get the available state options.
     *
     * @return array
     */
    public static function getStateOptions()
    {
        return [
            'draft' => 'Draft',
            'finalized' => 'Finalized',
        ];
    }

    /**
     * Get the form that owns the field mapping configuration.
     */
    public function form()
    {
        return $this->belongsTo(Form::class);
    }

    /**
     * Get the integration configuration that owns the field mapping configuration.
     */
    public function integrationConfiguration()
    {
        return $this->belongsTo(IntegrationConfiguration::class, 'integration_configuration_id');
    }

    /**
     * Scope a query to only include active configurations.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get the user who created the configuration.
     */
    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated the field mapping configuration.
     */
    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get the user who deleted the configuration.
     */
    public function deleter()
    {
        return $this->belongsTo(User::class, 'deleted_by');
    }

    /**
     * Get the columns for the DataTable
     *
     * @return array
     */
    public static function getDataTableColumns() : array
    {
        return [
            [
                'data' => 'DT_RowIndex',
                'name' => 'DT_RowIndex',
                'title' => __('#'),
                'filter' => false,
                'searchable' => false,
            ],
            [
                'data' => 'name',
                'name' => 'name',
                'title' => __('cruds.fieldMappingConfiguration.fields.name'),
                'searchable' => true,
                'orderable' => true,
                'filter' => 'text'
            ],
            [
                'data' => 'form.title',
                'name' => 'form.title',
                'title' => __('cruds.fieldMappingConfiguration.fields.form'),
                'searchable' => true,
                'orderable' => true,
                'filter' => 'text'
            ],
            [
                'data' => 'endpoint_configuration',
                'name' => 'endpoint_configuration',
                'title' => __('cruds.fieldMappingConfiguration.fields.endpoint_configuration'),
                'searchable' => true,
                'orderable' => true,
                'filter' => 'text'
            ],
            [
                'data' => 'external_system',
                'name' => 'external_system',
                'title' => __('cruds.fieldMappingConfiguration.fields.external_system'),
                'searchable' => true,
                'orderable' => true,
                'filter' => 'select',
                'options' => \App\Models\IntegrationConfiguration::getExternalSystemOptions()
            ],
            [
                'data' => 'integration_method',
                'name' => 'integration_method',
                'title' => __('cruds.fieldMappingConfiguration.fields.integration_method'),
                'searchable' => true,
                'orderable' => true,
                'filter' => 'select',
                'options' => \App\Models\IntegrationConfiguration::getIntegrationMethodOptions()
            ],
            [
                'data' => 'process_type',
                'name' => 'process_type',
                'title' => __('cruds.fieldMappingConfiguration.fields.process_type'),
                'searchable' => true,
                'orderable' => true,
                'filter' => 'select',
                'options' => \App\Models\IntegrationConfiguration::getProcessTypeOptions()
            ],
            [
                'data' => 'status',
                'name' => 'status',
                'title' => __('cruds.fieldMappingConfiguration.fields.status'),
                'searchable' => false,
                'orderable' => true,
                'filter' => 'select',
                'options' => [
                    '1' => 'Active',
                    '0' => 'Inactive'
                ]
            ],
            [
                'data' => 'updated_by',
                'name' => 'updatedBy.name',
                'title' => __('global.updated_by'),
                'searchable' => true,
                'orderable' => true,
                'filter' => 'text'
            ],
            [
                'data' => 'updated_at',
                'name' => 'updated_at',
                'title' => __('global.updated_at'),
                'searchable' => false,
                'orderable' => true,
                'filter' => 'date_range',
                'className' => 'text-right',
            ],
            [
                'data' => 'actions',
                'name' => 'actions',
                'title' => __('global.actions'),
                'searchable' => false,
                'orderable' => false,
                'className' => 'text-center',
                'exportable' => false,
                'printable' => false,
                'width' => '100px',
                'filter' => false
            ]
        ];
    }
}
