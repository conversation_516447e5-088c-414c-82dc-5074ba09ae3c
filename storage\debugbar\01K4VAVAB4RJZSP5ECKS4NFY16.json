{"__meta": {"id": "01K4VAVAB4RJZSP5ECKS4NFY16", "datetime": "2025-09-11 02:49:34", "utime": **********.82068, "method": "GET", "uri": "/admin/field-mapping-configurations", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 11, "start": 1757558972.980175, "end": **********.820694, "duration": 1.8405189514160156, "duration_str": "1.84s", "measures": [{"label": "Booting", "start": 1757558972.980175, "relative_start": 0, "end": **********.17095, "relative_end": **********.17095, "duration": 0.*****************, "duration_str": "191ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.170961, "relative_start": 0.*****************, "end": **********.820695, "relative_end": 9.5367431640625e-07, "duration": 1.****************, "duration_str": "1.65s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.192244, "relative_start": 0.*****************, "end": **********.197065, "relative_end": **********.197065, "duration": 0.004821062088012695, "duration_str": "4.82ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: admin.field-mapping-configurations.index", "start": **********.770493, "relative_start": 1.****************, "end": **********.770493, "relative_end": **********.770493, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.add", "start": **********.794803, "relative_start": 1.****************, "end": **********.794803, "relative_end": **********.794803, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.buttons.button", "start": **********.798804, "relative_start": 1.****************, "end": **********.798804, "relative_end": **********.798804, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.datatable", "start": **********.799665, "relative_start": 1.8194899559020996, "end": **********.799665, "relative_end": **********.799665, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.admin", "start": **********.81461, "relative_start": 1.834434986114502, "end": **********.81461, "relative_end": **********.81461, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: include.sidebar", "start": **********.815661, "relative_start": 1.8354859352111816, "end": **********.815661, "relative_end": **********.815661, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: include.header", "start": **********.816845, "relative_start": 1.836669921875, "end": **********.816845, "relative_end": **********.816845, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: partials.alerts", "start": **********.817442, "relative_start": 1.8372669219970703, "end": **********.817442, "relative_end": **********.817442, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 22889296, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "9.x", "tooltip": {"Laravel Version": "9.52.20", "PHP Version": "8.1.15", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 8, "nb_templates": 8, "templates": [{"name": "admin.field-mapping-configurations.index", "param_count": null, "params": [], "start": **********.770439, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/admin/field-mapping-configurations/index.blade.phpadmin.field-mapping-configurations.index", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fadmin%2Ffield-mapping-configurations%2Findex.blade.php:1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "components.buttons.add", "param_count": null, "params": [], "start": **********.794749, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/add.blade.phpcomponents.buttons.add", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fadd.blade.php:1", "ajax": false, "filename": "add.blade.php", "line": "?"}}, {"name": "components.buttons.button", "param_count": null, "params": [], "start": **********.798751, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/buttons/button.blade.phpcomponents.buttons.button", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fbuttons%2Fbutton.blade.php:1", "ajax": false, "filename": "button.blade.php", "line": "?"}}, {"name": "components.datatable", "param_count": null, "params": [], "start": **********.799618, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/components/datatable.blade.phpcomponents.datatable", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fcomponents%2Fdatatable.blade.php:1", "ajax": false, "filename": "datatable.blade.php", "line": "?"}}, {"name": "layouts.admin", "param_count": null, "params": [], "start": **********.81456, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/layouts/admin.blade.phplayouts.admin", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Flayouts%2Fadmin.blade.php:1", "ajax": false, "filename": "admin.blade.php", "line": "?"}}, {"name": "include.sidebar", "param_count": null, "params": [], "start": **********.815612, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/include/sidebar.blade.phpinclude.sidebar", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Finclude%2Fsidebar.blade.php:1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}}, {"name": "include.header", "param_count": null, "params": [], "start": **********.816799, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/include/header.blade.phpinclude.header", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Finclude%2Fheader.blade.php:1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "partials.alerts", "param_count": null, "params": [], "start": **********.817391, "type": "blade", "hash": "bladeD:\\Git Data Capture\\application\\resources\\views/partials/alerts.blade.phppartials.alerts", "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fresources%2Fviews%2Fpartials%2Falerts.blade.php:1", "ajax": false, "filename": "alerts.blade.php", "line": "?"}}]}, "queries": {"count": 2, "nb_statements": 1, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0050999999999999995, "accumulated_duration_str": "5.1ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 15, "namespace": "middleware", "name": "auth", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}], "start": **********.709299, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php:168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "dc_local", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.718083, "duration": 0.0050999999999999995, "duration_str": "5.1ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Git Data Capture\\application\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php:59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "dc_local", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fapp%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 1}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8001/admin/field-mapping-configurations", "action_name": "admin.field-mapping-configurations.index", "controller_action": "App\\Http\\Controllers\\Admin\\FieldMappingConfigurationsController@index", "uri": "GET admin/field-mapping-configurations", "controller": "App\\Http\\Controllers\\Admin\\FieldMappingConfigurationsController@index<a href=\"vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fapp%2FHttp%2FControllers%2FAdmin%2FFieldMappingConfigurationsController.php:30\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "/admin", "file": "<a href=\"vscode://file/D%3A%2FGit%20Data%20Capture%2Fapplication%2Fapp%2FHttp%2FControllers%2FAdmin%2FFieldMappingConfigurationsController.php:30\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/FieldMappingConfigurationsController.php:30-38</a>", "middleware": "web, auth, user_type:admin", "duration": "1.84s", "peak_memory": "24MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-615822398 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-615822398\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-30911482 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-30911482\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2045444421 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko/20100101 Firefox/142.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"63 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"61 characters\">http://localhost:8001/admin/integration-configurations/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImhyVGVGeFgxMG95cWFOQ0xpczJZbnc9PSIsInZhbHVlIjoiaForZnlCbENITzlwNlc4S3lFMnNNTUNmNDA1NWl4OU5CVnd2eEpnSE9GSWIzZUx5eUJpaUw5YUFnWGZJSVl1UjF6VTVLcUUweFlJaXY0OVlCTForcTVDWFhSYlRoclVMcjZIVTVaSnpPOGZYZTN6cERLK21MMEhCdld2RVoydDciLCJtYWMiOiI4OTg2NmJlMjliMTFkZmIxZWEzZWU4MjQwYzNmNzRlY2U5NmMzMGMyNGZjY2FiMGY0ZGNiZjdjNmZhZGZlOTIxIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ik1KOWJKMVp3QThwN3FRdGV2TFEzaWc9PSIsInZhbHVlIjoia1NNWk4zOWFOcUxUeEw3cmtpeHdiNHVhZ3hLQ1BvV21GbmtEYWpDN1BpNFc0NHNzSWUrdVkrVW1ORDlrU1padWNjeGVtM0FMR1MyNUFOTTFMZUJmNTdKY0pwK3hUL3BtSjBnMjZLOXNTYzdML2VxNXpreUxSZnJnRHlUbTl3dGkiLCJtYWMiOiI1NDdlNjNlNDZiN2I5MzU1ZTlhODdiN2M4N2VkMTk5NzJiYzI3MmY0MmUyMDI4OTFlNDhhNWU0YWJkNmM2YmUzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=0, i</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2045444421\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2051739021 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4RbyFb2oU1pjc4UNp6CmU470XASLVRa4SNAgSVgT</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3qWAZMAIgZlFqvqcm0sdt0aBVP4ghvpst3qVJvce</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2051739021\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1082323821 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 11 Sep 2025 02:49:34 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1082323821\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1421557349 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4RbyFb2oU1pjc4UNp6CmU470XASLVRa4SNAgSVgT</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"61 characters\">http://localhost:8001/admin/integration-configurations/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1757556567</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1421557349\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8001/admin/field-mapping-configurations", "action_name": "admin.field-mapping-configurations.index", "controller_action": "App\\Http\\Controllers\\Admin\\FieldMappingConfigurationsController@index"}, "badge": null}}