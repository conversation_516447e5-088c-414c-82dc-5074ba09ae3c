<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('field_definitions', function (Blueprint $table) {
            // Add tenant_id with index
            if (!Schema::hasColumn('field_definitions', 'tenant_id')) {
                $table->unsignedBigInteger('tenant_id')->nullable()->after('id');
                $table->index('tenant_id');
            }

            // Add other columns if they don't exist
            $columns = [
                'name' => 'string',
                'label' => 'string',
                'type' => 'string',
                'options' => 'json',
                'is_required' => 'boolean',
                'validation_rules' => 'string',
                'sort_order' => 'integer',
                'is_active' => 'boolean',
                'description' => 'text'
            ];

            foreach ($columns as $column => $type) {
                if (!Schema::hasColumn('field_definitions', $column)) {
                    $columnMethod = $type === 'json' ? 'json' : $type;
                    $table->{$columnMethod}($column)->nullable($type !== 'boolean' && $type !== 'integer');
                }
            }

            // Add unique index for tenant_id and name
            // We'll use a try-catch to handle the case where the index might already exist
            try {
                $table->unique(['tenant_id', 'name'], 'field_definitions_tenant_id_name_unique');
            } catch (\Exception $e) {
                // Index might already exist, we can ignore this error
                \Log::info('Index field_definitions_tenant_id_name_unique might already exist');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // We won't drop columns in the down method to prevent data loss
        // If needed, these can be dropped manually
    }
};
