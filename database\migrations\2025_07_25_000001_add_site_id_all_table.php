<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            // $table->id();
            $table->unsignedBigInteger('tenant_id');
            // $table->unsignedBigInteger('created_by');
            // $table->unsignedBigInteger('modified_by');
        });
        Schema::table('forms', function (Blueprint $table) {
            // $table->id();
            $table->unsignedBigInteger('tenant_id');
            // $table->unsignedBigInteger('created_by');
            // $table->unsignedBigInteger('modified_by');
            $table->dropForeign('user_group_id');
            $table->dropColumn('user_group_id');
        });
        Schema::table('form_field_templates', function (Blueprint $table) {
            // $table->id();
            $table->unsignedBigInteger('tenant_id');
            // $table->unsignedBigInteger('created_by');
            // $table->unsignedBigInteger('modified_by');
        });
        Schema::table('user_groups', function (Blueprint $table) {
            // $table->id();
            $table->unsignedBigInteger('tenant_id');
            // $table->unsignedBigInteger('created_by');
            // $table->unsignedBigInteger('modified_by');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Schema::dropIfExists('form_user_group');
        Schema::table('users', function (Blueprint $table) {
            // $table->id();
            $table->dropColumn('tenant_id');
            // $table->unsignedBigInteger('created_by');
            // $table->unsignedBigInteger('modified_by');
        });
        Schema::table('forms', function (Blueprint $table) {
            // $table->id();
            $table->dropColumn('tenant_id');
        });
        Schema::table('form_field_templates', function (Blueprint $table) {
            // $table->id();
            $table->dropColumn('tenant_id');
            // $table->unsignedBigInteger('created_by');
            // $table->unsignedBigInteger('modified_by');
        });
        Schema::table('user_groups', function (Blueprint $table) {
            // $table->id();
            $table->dropColumn('tenant_id');
            // $table->unsignedBigInteger('created_by');
            // $table->unsignedBigInteger('modified_by');
        });
    }
};
