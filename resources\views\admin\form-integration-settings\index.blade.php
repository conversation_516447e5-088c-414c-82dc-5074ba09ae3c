@extends('layouts.admin')
@section('pageTitle', 'Form Integration Settings')

@push('styles')
<style>
    .card-header h4 {
        margin: 0;
        color: #495057;
    }
</style>
@endpush

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card card-default">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h4>Form Integration Settings</h4>
                        </div>
                        <div class="col-auto">
                            <x-buttons.add 
                                :route="route('admin.form-integration-settings.create')"
                                text="Create New Integration"
                                class="btn-primary"
                            />
                        </div>
                    </div>
                </div>





                <!-- Data Table -->
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped" id="form-integration-settings-table" width="100%">
                            <thead class="thead-light">
                                <tr>
                                    <th>Name</th>
                                    <th>Form</th>
                                    <th>Configuration</th>
                                    <th>External System</th>
                                    <th>Integration Method</th>
                                    <th>Process Type</th>
                                    <th>Status</th>
                                    <th>Created By</th>
                                    <th>Created At</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if(isset($integrationSettings) && $integrationSettings->count() > 0)
                                    @foreach($integrationSettings as $setting)
                                    <tr>
                                        <td>{{ $setting->name }}</td>
                                        <td>{{ $setting->form ? $setting->form->title : 'N/A' }}</td>
                                        <td>{{ $setting->endpointConfiguration ? $setting->endpointConfiguration->name : 'N/A' }}</td>
                                        <td>{{ $setting->endpointConfiguration ? ($setting->endpointConfiguration->external_system_name ?? $setting->endpointConfiguration->external_system) : 'N/A' }}</td>
                                        <td>{{ $setting->endpointConfiguration ? $setting->endpointConfiguration->integration_method : 'N/A' }}</td>
                                        <td>{{ $setting->endpointConfiguration ? $setting->endpointConfiguration->process_type : 'N/A' }}</td>
                                        <td>
                                            @if($setting->is_active)
                                                <span class="badge badge-success">Active</span>
                                            @else
                                                <span class="badge badge-secondary">Inactive</span>
                                            @endif
                                        </td>
                                        <td>{{ $setting->createdBy ? $setting->createdBy->name : 'System' }}</td>
                                        <td>{{ $setting->created_at->format('Y-m-d H:i') }}</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('admin.form-integration-settings.show', $setting->id) }}" class="btn btn-sm btn-info" title="View Details">
                                                    <i class="fa fa-eye"></i>
                                                </a>
                                                <a href="{{ route('admin.form-integration-settings.edit', $setting->id) }}" class="btn btn-sm btn-primary" title="Edit">
                                                    <i class="fa fa-edit"></i>
                                                </a>
                                                <form action="{{ route('admin.form-integration-settings.destroy', $setting->id) }}" method="POST" style="display: inline-block;">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="button" class="btn btn-sm btn-danger delete-btn" title="Delete">
                                                        <i class="fa fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                @else
                                    <tr>
                                        <td colspan="9" class="text-center">No integration settings found</td>
                                    </tr>
                                @endif
                            </tbody>
                        </table>
                    </div>
                </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    console.log('Initializing DataTable...');
    console.log('Table element found:', $('#form-integration-settings-table').length);

    // Initialize DataTable with static data for now
    var table = $('#form-integration-settings-table').DataTable({
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
        responsive: false,
        scrollX: true,
        autoWidth: false,
        order: [[7, 'desc']],
        language: {
            emptyTable: 'No integration settings found',
            zeroRecords: 'No matching integration settings found'
        },
        columnDefs: [
            { width: '15%', targets: 0 },
            { width: '12%', targets: 1 },
            { width: '12%', targets: 2 },
            { width: '10%', targets: 3 },
            { width: '10%', targets: 4 },
            { width: '8%', targets: 5 },
            { width: '10%', targets: 6 },
            { width: '10%', targets: 7 },
            { width: '13%', targets: 8 }
        ]
    });

    console.log('DataTable initialized:', table);

    // Handle delete confirmation
    $(document).on('click', '.delete-btn', function(e) {
        e.preventDefault();

        if (confirm('Are you sure you want to delete this form integration setting?')) {
            $(this).closest('form').submit();
        }
    });
});
</script>
@endsection

@section('styles')
<style>
.card {
    box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 3px rgba(0,0,0,.2);
    margin-bottom: 1rem;
}

.bg-primary { background-color: #007bff !important; }
.bg-success { background-color: #28a745 !important; }
.bg-warning { background-color: #ffc107 !important; }
.bg-info { background-color: #17a2b8 !important; }

.table th {
    background-color: #f8f9fa;
    border-top: none;
}

.badge {
    font-size: 0.75em;
}

/* Fix table width and layout issues */
#form-integration-settings-table {
    width: 100% !important;
    margin: 0;
    table-layout: fixed;
}

.table-responsive {
    overflow-x: auto;
    width: 100%;
    margin: 0;
}

.card {
    width: 100%;
    margin: 0 auto;
    max-width: none;
}

.card-body {
    padding: 1.25rem;
    width: 100%;
}

/* Override admin layout to ensure full width */
.page-content-wrapper {
    margin-left: 0 !important;
    padding-left: 0 !important;
}

.page-content-wrapper .content {
    padding-left: 0 !important;
    padding-right: 0 !important;
    margin-left: 0 !important;
    width: 100% !important;
}

.page-content-wrapper .content .container-fluid {
    width: 100% !important;
    max-width: none !important;
    padding-left: 15px !important;
    padding-right: 15px !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
}

/* Override any sidebar-related margins */
.page-container {
    margin-left: 0 !important;
}

.content {
    margin-left: 0 !important;
    padding-left: 0 !important;
}

/* Ensure Bootstrap grid takes full width */
.row {
    margin-left: 0 !important;
    margin-right: 0 !important;
    width: 100%;
}

.col-12 {
    padding-left: 0 !important;
    padding-right: 0 !important;
    width: 100%;
    max-width: 100%;
}

/* Force table to use available space */
.table {
    margin-bottom: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .table-responsive {
        border: none;
    }
}
</style>
@endsection
