@props([
    'route' => '#',
    'text' => null,
    'size' => 'sm',
    'class' => '',
    'icon' => 'trash',
    'title' => null,
    'target' => '_self',
    'confirm' => null,
    'method' => 'DELETE',
    'iconOnly' => true, // Default to icon only for listing views
])

@php
    $confirmMessage = $confirm ?? __('global.are_you_sure');
    $title = $title ?? __('global.delete');
    $text = $text ?? (!$iconOnly ? __('global.delete') : '');
    
    $data = [
        'method' => $method,
        'confirm' => $confirmMessage,
    ];
@endphp

@if($method === 'DELETE')
    <form action="{{ $route }}" method="POST" style="display: inline;">
        @csrf
        @method('DELETE')
        <x-buttons.button 
            type="submit"
            variant="danger" 
            :size="$size"
            :icon="$icon"
            :class="$class"
            :title="$title"
            :data="$data"
            :iconOnly="$iconOnly"
        >
            {{ $text }}
        </x-buttons.button>
    </form>
@else
    <x-buttons.button 
        :href="$route"
        variant="danger" 
        :size="$size"
        :icon="$icon"
        :class="$class"
        :title="$title"
        :target="$target"
        :data="$data"
        :iconOnly="$iconOnly"
        data-bs-toggle="tooltip"
    >
        {{ $text }}
    </x-buttons.button>
@endif
