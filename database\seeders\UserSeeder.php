<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Tenant; // Add this line if you have a Tenant model
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Set default tenant ID to 1
        $tenantId = 1;

        // Create Super Admin User if not exists
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Super Admin',
                'password' => Hash::make('Admin@1234'),
                'type' => 2, // Assuming 2 is super admin type
                'email_verified_at' => now(),
                'tenant_id' => $tenantId,
            ]
        );

        // Create Admin User if not exists
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin',
                'password' => Hash::make('Admin@1234'),
                'type' => 1, // Assuming 1 is admin type
                'email_verified_at' => now(),
                'tenant_id' => $tenantId,
            ]
        );
        
        // Create a regular user for testing
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Test User',
                'password' => Hash::make('User@1234'),
                'type' => 3, // Regular user type
                'email_verified_at' => now(),
                'tenant_id' => $tenantId,
            ]
        );
    }
}
