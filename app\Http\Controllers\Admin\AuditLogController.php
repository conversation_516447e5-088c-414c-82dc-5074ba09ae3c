<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AuditLog;
use App\Models\FormSubmission;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Yajra\DataTables\Facades\DataTables;

class AuditLogController extends Controller
{
    /**
     * Display a listing of audit logs
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $query = AuditLog::with(['actor', 'formSubmission.form', 'formSubmissionSync.formIntegrationSetting'])
                ->orderBy('created_at', 'desc');

            // Apply filters
            if ($request->has('event_type') && $request->event_type !== '') {
                $query->where('event_type', $request->event_type);
            }

            if ($request->has('actor_type') && $request->actor_type !== '') {
                $query->where('actor_type', $request->actor_type);
            }

            if ($request->has('date_from') && $request->date_from !== '') {
                $query->where('created_at', '>=', $request->date_from);
            }

            if ($request->has('date_to') && $request->date_to !== '') {
                $query->where('created_at', '<=', $request->date_to . ' 23:59:59');
            }

            if ($request->has('submission_uuid') && $request->submission_uuid !== '') {
                $query->whereHas('formSubmission', function ($q) use ($request) {
                    $q->where('uuid', 'like', '%' . $request->submission_uuid . '%');
                });
            }

            return DataTables::of($query)
                ->addColumn('event_type_badge', function ($row) {
                    $color = $row->getEventColor();
                    return "<span class=\"badge badge-{$color}\">{$row->getEventTypeLabel()}</span>";
                })
                ->addColumn('actor_info', function ($row) {
                    $actorType = $row->getActorTypeLabel();
                    $actorName = $row->getActorName();
                    $color = match ($row->actor_type) {
                        'user' => 'primary',
                        'admin' => 'warning',
                        'system' => 'secondary',
                        default => 'light'
                    };
                    return "<span class=\"badge badge-{$color}\">{$actorType}</span><br><small>{$actorName}</small>";
                })
                ->addColumn('submission_info', function ($row) {
                    if ($row->formSubmission) {
                        $formTitle = $row->formSubmission->form->title ?? 'Unknown Form';
                        $submissionUuid = substr($row->formSubmission->uuid, 0, 8) . '...';
                        return "<strong>{$formTitle}</strong><br><small>{$submissionUuid}</small>";
                    }
                    return '<span class="text-muted">N/A</span>';
                })
                ->addColumn('integration_info', function ($row) {
                    if ($row->formSubmissionSync && $row->formSubmissionSync->formIntegrationSetting) {
                        $integrationName = $row->formSubmissionSync->formIntegrationSetting->name;
                        $targetSystem = $row->formSubmissionSync->getTargetSystem();
                        return "<strong>{$integrationName}</strong><br><small>{$targetSystem}</small>";
                    }
                    return '<span class="text-muted">N/A</span>';
                })
                ->addColumn('action', function ($row) {
                    return '<button type="button" class="btn btn-sm btn-info view-details" data-id="' . $row->id . '">
                        <i class="fas fa-eye"></i> Details
                    </button>';
                })
                ->editColumn('created_at', function ($row) {
                    return $row->created_at->format('Y-m-d H:i:s');
                })
                ->editColumn('event_description', function ($row) {
                    return strlen($row->event_description) > 50
                        ? substr($row->event_description, 0, 50) . '...'
                        : $row->event_description;
                })
                ->rawColumns(['event_type_badge', 'actor_info', 'submission_info', 'integration_info', 'action'])
                ->make(true);
        }

        // Get filter options
        $eventTypes = AuditLog::select('event_type')
            ->distinct()
            ->orderBy('event_type')
            ->pluck('event_type')
            ->map(function ($eventType) {
                return [
                    'value' => $eventType,
                    'label' => ucwords(str_replace('_', ' ', $eventType))
                ];
            });

        $actorTypes = [
            ['value' => 'user', 'label' => 'User'],
            ['value' => 'admin', 'label' => 'Admin'],
            ['value' => 'system', 'label' => 'System'],
        ];

        return view('admin.audit-logs.index', compact('eventTypes', 'actorTypes'));
    }

    /**
     * Show audit log details
     */
    public function show(int $id): JsonResponse
    {
        try {
            $auditLog = AuditLog::with([
                'actor',
                'formSubmission.form',
                'formSubmissionSync.formIntegrationSetting.endpointConfiguration'
            ])->findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $auditLog->id,
                    'uuid' => $auditLog->uuid,
                    'event_type' => $auditLog->event_type,
                    'event_type_label' => $auditLog->getEventTypeLabel(),
                    'event_description' => $auditLog->event_description,
                    'actor_type' => $auditLog->actor_type,
                    'actor_type_label' => $auditLog->getActorTypeLabel(),
                    'actor_name' => $auditLog->getActorName(),
                    'created_at' => $auditLog->created_at->format('Y-m-d H:i:s'),
                    'ip_address' => $auditLog->ip_address,
                    'user_agent' => $auditLog->user_agent,
                    'old_values' => $auditLog->old_values,
                    'new_values' => $auditLog->new_values,
                    'metadata' => $auditLog->metadata,
                    'form_submission' => $auditLog->formSubmission ? [
                        'uuid' => $auditLog->formSubmission->uuid,
                        'status' => $auditLog->formSubmission->status,
                        'form_title' => $auditLog->formSubmission->form->title ?? 'Unknown',
                        'submitted_at' => $auditLog->formSubmission->submitted_at->format('Y-m-d H:i:s'),
                    ] : null,
                    'integration' => $auditLog->formSubmissionSync ? [
                        'name' => $auditLog->formSubmissionSync->formIntegrationSetting->name ?? 'Unknown',
                        'target_system' => $auditLog->formSubmissionSync->getTargetSystem(),
                        'status' => $auditLog->formSubmissionSync->status,
                    ] : null,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Audit log not found',
            ], 404);
        }
    }

    /**
     * Get audit log statistics
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            $hours = $request->input('hours', 24);
            $since = now()->subHours($hours);

            $stats = [
                'total_events' => AuditLog::where('created_at', '>=', $since)->count(),
                'user_events' => AuditLog::where('created_at', '>=', $since)
                    ->where('actor_type', 'user')->count(),
                'admin_events' => AuditLog::where('created_at', '>=', $since)
                    ->where('actor_type', 'admin')->count(),
                'system_events' => AuditLog::where('created_at', '>=', $since)
                    ->where('actor_type', 'system')->count(),
            ];

            // Event types breakdown
            $eventTypes = AuditLog::where('created_at', '>=', $since)
                ->selectRaw('event_type, COUNT(*) as count')
                ->groupBy('event_type')
                ->orderBy('count', 'desc')
                ->get()
                ->map(function ($item) {
                    return [
                        'event_type' => $item->event_type,
                        'label' => ucwords(str_replace('_', ' ', $item->event_type)),
                        'count' => $item->count,
                    ];
                });

            // Recent activity (last 10 events)
            $recentActivity = AuditLog::with(['actor', 'formSubmission.form'])
                ->orderBy('created_at', 'desc')
                ->limit(10)
                ->get()
                ->map(function ($log) {
                    return [
                        'event_type_label' => $log->getEventTypeLabel(),
                        'actor_name' => $log->getActorName(),
                        'created_at' => $log->created_at->diffForHumans(),
                        'description' => $log->event_description,
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => [
                    'statistics' => $stats,
                    'event_types' => $eventTypes,
                    'recent_activity' => $recentActivity,
                    'period_hours' => $hours,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve statistics: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Export audit logs
     */
    public function export(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'format' => 'required|in:csv,json',
                'event_type' => 'sometimes|string',
                'actor_type' => 'sometimes|string',
                'date_from' => 'sometimes|date',
                'date_to' => 'sometimes|date',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors(),
                ], 422);
            }

            $query = AuditLog::with(['actor', 'formSubmission.form', 'formSubmissionSync.formIntegrationSetting']);

            // Apply filters
            if ($request->has('event_type')) {
                $query->where('event_type', $request->input('event_type'));
            }

            if ($request->has('actor_type')) {
                $query->where('actor_type', $request->input('actor_type'));
            }

            if ($request->has('date_from')) {
                $query->where('created_at', '>=', $request->input('date_from'));
            }

            if ($request->has('date_to')) {
                $query->where('created_at', '<=', $request->input('date_to') . ' 23:59:59');
            }

            $auditLogs = $query->orderBy('created_at', 'desc')->get();

            $format = $request->input('format');
            $filename = 'audit_logs_' . now()->format('Y-m-d_H-i-s') . '.' . $format;

            if ($format === 'csv') {
                return $this->exportCsv($auditLogs, $filename);
            } else {
                return $this->exportJson($auditLogs, $filename);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Export failed: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Export audit logs as CSV
     */
    protected function exportCsv($auditLogs, $filename)
    {
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function () use ($auditLogs) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'UUID', 'Event Type', 'Event Description', 'Actor Type', 'Actor Name',
                'Form Title', 'Submission UUID', 'Integration Name', 'IP Address', 'Created At'
            ]);

            foreach ($auditLogs as $log) {
                fputcsv($file, [
                    $log->uuid,
                    $log->getEventTypeLabel(),
                    $log->event_description,
                    $log->getActorTypeLabel(),
                    $log->getActorName(),
                    $log->formSubmission->form->title ?? 'N/A',
                    $log->formSubmission->uuid ?? 'N/A',
                    $log->formSubmissionSync->formIntegrationSetting->name ?? 'N/A',
                    $log->ip_address ?? 'N/A',
                    $log->created_at->format('Y-m-d H:i:s'),
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Export audit logs as JSON
     */
    protected function exportJson($auditLogs, $filename)
    {
        $data = $auditLogs->map(function ($log) {
            return [
                'uuid' => $log->uuid,
                'event_type' => $log->event_type,
                'event_type_label' => $log->getEventTypeLabel(),
                'event_description' => $log->event_description,
                'actor_type' => $log->actor_type,
                'actor_name' => $log->getActorName(),
                'created_at' => $log->created_at,
                'ip_address' => $log->ip_address,
                'user_agent' => $log->user_agent,
                'old_values' => $log->old_values,
                'new_values' => $log->new_values,
                'metadata' => $log->metadata,
                'form_submission' => $log->formSubmission ? [
                    'uuid' => $log->formSubmission->uuid,
                    'form_title' => $log->formSubmission->form->title,
                ] : null,
                'integration' => $log->formSubmissionSync ? [
                    'name' => $log->formSubmissionSync->formIntegrationSetting->name,
                    'target_system' => $log->formSubmissionSync->getTargetSystem(),
                ] : null,
            ];
        });

        $headers = [
            'Content-Type' => 'application/json',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        return response()->json($data, 200, $headers);
    }
}
