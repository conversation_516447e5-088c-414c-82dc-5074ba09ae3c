<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class FormSubmission extends Model
{
    use HasFactory;

    protected $fillable = [
        'uuid',
        'form_id',
        'user_id',
        'submission_data',
        'status',
        'submitted_at',
        'processed_at',
        'completed_at',
        'total_integrations',
        'successful_integrations',
        'failed_integrations',
        'metadata',
    ];

    protected $casts = [
        'submission_data' => 'array',
        'metadata' => 'array',
        'submitted_at' => 'datetime',
        'processed_at' => 'datetime',
        'completed_at' => 'datetime',
    ];

    protected $dates = [
        'submitted_at',
        'processed_at',
        'completed_at',
    ];

    // Status constants
    const STATUS_PENDING = 'pending';
    const STATUS_PROCESSING = 'processing';
    const STATUS_COMPLETED = 'completed';
    const STATUS_FAILED = 'failed';
    const STATUS_PARTIAL = 'partial';

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->uuid)) {
                $model->uuid = (string) Str::uuid();
            }
            if (empty($model->submitted_at)) {
                $model->submitted_at = now();
            }
        });
    }

    /**
     * Get the form that this submission belongs to
     */
    public function form(): BelongsTo
    {
        return $this->belongsTo(Form::class);
    }

    /**
     * Get the user who submitted this form
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get all sync records for this submission
     */
    public function syncs(): HasMany
    {
        return $this->hasMany(FormSubmissionSync::class);
    }

    /**
     * Get audit logs for this submission
     */
    public function auditLogs(): HasMany
    {
        return $this->hasMany(AuditLog::class);
    }

    /**
     * Get error logs for this submission
     */
    public function errorLogs(): HasMany
    {
        return $this->hasMany(ErrorLog::class);
    }

    /**
     * Scope to filter by status
     */
    public function scopeWithStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to filter by form
     */
    public function scopeForForm($query, $formId)
    {
        return $query->where('form_id', $formId);
    }

    /**
     * Scope to filter by user
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to filter by date range
     */
    public function scopeSubmittedBetween($query, $startDate, $endDate)
    {
        return $query->whereBetween('submitted_at', [$startDate, $endDate]);
    }

    /**
     * Check if submission is pending
     */
    public function isPending(): bool
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * Check if submission is processing
     */
    public function isProcessing(): bool
    {
        return $this->status === self::STATUS_PROCESSING;
    }

    /**
     * Check if submission is completed
     */
    public function isCompleted(): bool
    {
        return $this->status === self::STATUS_COMPLETED;
    }

    /**
     * Check if submission has failed
     */
    public function isFailed(): bool
    {
        return $this->status === self::STATUS_FAILED;
    }

    /**
     * Check if submission is partially completed
     */
    public function isPartial(): bool
    {
        return $this->status === self::STATUS_PARTIAL;
    }

    /**
     * Update submission status based on sync results
     */
    public function updateStatusFromSyncs(): void
    {
        $this->refresh();

        $totalSyncs = $this->syncs()->count();
        $completedSyncs = $this->syncs()->where('status', \App\Models\FormSubmissionSync::STATUS_COMPLETED)->count();
        $failedSyncs = $this->syncs()->where('status', \App\Models\FormSubmissionSync::STATUS_FAILED)->count();
        $processingSyncs = $this->syncs()->whereIn('status', [
            \App\Models\FormSubmissionSync::STATUS_PROCESSING,
            \App\Models\FormSubmissionSync::STATUS_PENDING,
            \App\Models\FormSubmissionSync::STATUS_RETRYING
        ])->count();

        $this->total_integrations = $totalSyncs;
        $this->successful_integrations = $completedSyncs;
        $this->failed_integrations = $failedSyncs;

        if ($processingSyncs > 0) {
            $this->status = self::STATUS_PROCESSING;
        } elseif ($completedSyncs === $totalSyncs && $totalSyncs > 0) {
            $this->status = self::STATUS_COMPLETED;
            $this->completed_at = now();
        } elseif ($failedSyncs === $totalSyncs && $totalSyncs > 0) {
            $this->status = self::STATUS_FAILED;
        } elseif ($completedSyncs > 0 && $failedSyncs > 0) {
            $this->status = self::STATUS_PARTIAL;
        }

        $this->save();
    }

    /**
     * Get submission progress percentage
     */
    public function getProgressPercentage(): int
    {
        if ($this->total_integrations === 0) {
            return 0;
        }

        return (int) round(($this->successful_integrations / $this->total_integrations) * 100);
    }

    /**
     * Get human-readable status
     */
    public function getStatusLabel(): string
    {
        return match ($this->status) {
            self::STATUS_PENDING => 'Pending',
            self::STATUS_PROCESSING => 'Processing',
            self::STATUS_COMPLETED => 'Completed',
            self::STATUS_FAILED => 'Failed',
            self::STATUS_PARTIAL => 'Partially Completed',
            default => 'Unknown',
        };
    }

    /**
     * Get status color for UI
     */
    public function getStatusColor(): string
    {
        return match ($this->status) {
            self::STATUS_PENDING => 'warning',
            self::STATUS_PROCESSING => 'info',
            self::STATUS_COMPLETED => 'success',
            self::STATUS_FAILED => 'danger',
            self::STATUS_PARTIAL => 'warning',
            default => 'secondary',
        };
    }
}
