<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Form;
use App\Models\FormSubmission;
use App\Services\FormSubmissionService;
use App\Services\AuditLoggerService;
use App\Services\ErrorLoggerService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class FormSubmissionController extends Controller
{
    protected FormSubmissionService $formSubmissionService;
    protected AuditLoggerService $auditLogger;
    protected ErrorLoggerService $errorLogger;

    public function __construct(
        FormSubmissionService $formSubmissionService,
        AuditLoggerService $auditLogger,
        ErrorLoggerService $errorLogger
    ) {
        $this->formSubmissionService = $formSubmissionService;
        $this->auditLogger = $auditLogger;
        $this->errorLogger = $errorLogger;
    }

    /**
     * Submit a form
     */
    public function submit(Request $request, int $formId): JsonResponse
    {
        try {
            // Find the form
            $form = Form::where('id', $formId)
                ->where('is_active', true)
                ->firstOrFail();

            // Validate request
            $validator = Validator::make($request->all(), [
                'data' => 'required|array',
                'metadata' => 'sometimes|array',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            // Get authenticated user (optional)
            $user = Auth::user();

            // Prepare metadata
            $metadata = array_merge($request->input('metadata', []), [
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'source' => 'api',
                'submitted_via' => 'form_submission_api',
            ]);

            // Submit the form
            $submission = $this->formSubmissionService->submitForm(
                $request->input('data'),
                $form,
                $user,
                $metadata
            );

            return response()->json([
                'success' => true,
                'message' => 'Form submitted successfully',
                'data' => [
                    'submission_uuid' => $submission->uuid,
                    'status' => $submission->status,
                    'status_label' => $submission->getStatusLabel(),
                    'submitted_at' => $submission->submitted_at,
                    'total_integrations' => $submission->total_integrations,
                ],
            ], 201);

        } catch (ValidationException $e) {
            $this->errorLogger->logValidationError(
                $e->errors(),
                null,
                [
                    'form_id' => $formId,
                    'user_id' => Auth::id(),
                    'request_data' => $request->all(),
                ]
            );

            return response()->json([
                'success' => false,
                'message' => 'Form validation failed',
                'errors' => $e->errors(),
            ], 422);

        } catch (\Exception $e) {
            $this->errorLogger->logError(
                \App\Models\ErrorLog::LEVEL_ERROR,
                \App\Models\ErrorLog::TYPE_SYSTEM_ERROR,
                $e->getMessage(),
                [
                    'form_id' => $formId,
                    'user_id' => Auth::id(),
                    'exception_class' => get_class($e),
                    'stack_trace' => $e->getTraceAsString(),
                ]
            );

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while submitting the form',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error',
            ], 500);
        }
    }

    /**
     * Get submission status
     */
    public function status(string $uuid): JsonResponse
    {
        try {
            $status = $this->formSubmissionService->getSubmissionStatus($uuid);

            return response()->json([
                'success' => true,
                'data' => $status,
            ]);

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Submission not found',
            ], 404);

        } catch (\Exception $e) {
            $this->errorLogger->logError(
                \App\Models\ErrorLog::LEVEL_ERROR,
                \App\Models\ErrorLog::TYPE_SYSTEM_ERROR,
                $e->getMessage(),
                [
                    'submission_uuid' => $uuid,
                    'exception_class' => get_class($e),
                ]
            );

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving submission status',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error',
            ], 500);
        }
    }

    /**
     * Get submission history for authenticated user
     */
    public function history(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'Authentication required',
                ], 401);
            }

            $validator = Validator::make($request->all(), [
                'page' => 'sometimes|integer|min:1',
                'per_page' => 'sometimes|integer|min:1|max:100',
                'status' => 'sometimes|string|in:pending,processing,completed,failed,partial',
                'form_id' => 'sometimes|integer|exists:forms,id',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $query = FormSubmission::where('user_id', $user->id)
                ->with(['form', 'syncs.formIntegrationSetting'])
                ->orderBy('submitted_at', 'desc');

            // Apply filters
            if ($request->has('status')) {
                $query->where('status', $request->input('status'));
            }

            if ($request->has('form_id')) {
                $query->where('form_id', $request->input('form_id'));
            }

            $perPage = $request->input('per_page', 15);
            $submissions = $query->paginate($perPage);

            $data = $submissions->map(function ($submission) {
                return [
                    'uuid' => $submission->uuid,
                    'status' => $submission->status,
                    'status_label' => $submission->getStatusLabel(),
                    'submitted_at' => $submission->submitted_at,
                    'completed_at' => $submission->completed_at,
                    'progress_percentage' => $submission->getProgressPercentage(),
                    'total_integrations' => $submission->total_integrations,
                    'successful_integrations' => $submission->successful_integrations,
                    'failed_integrations' => $submission->failed_integrations,
                    'form' => [
                        'id' => $submission->form->id,
                        'title' => $submission->form->title,
                    ],
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $data,
                'pagination' => [
                    'current_page' => $submissions->currentPage(),
                    'last_page' => $submissions->lastPage(),
                    'per_page' => $submissions->perPage(),
                    'total' => $submissions->total(),
                    'from' => $submissions->firstItem(),
                    'to' => $submissions->lastItem(),
                ],
            ]);

        } catch (\Exception $e) {
            $this->errorLogger->logError(
                \App\Models\ErrorLog::LEVEL_ERROR,
                \App\Models\ErrorLog::TYPE_SYSTEM_ERROR,
                $e->getMessage(),
                [
                    'user_id' => Auth::id(),
                    'exception_class' => get_class($e),
                ]
            );

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving submission history',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error',
            ], 500);
        }
    }

    /**
     * Get available forms for submission
     */
    public function availableForms(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();

            $query = Form::where('is_active', true)
                ->select(['id', 'title', 'created_at']);

            // If user is authenticated, filter by user groups
            if ($user) {
                $query->where(function ($q) use ($user) {
                    $q->whereHas('userGroups', function ($userGroupQuery) use ($user) {
                        $userGroupQuery->where('user_group_id', $user->user_group_id);
                    })->orWhereNull('user_group_id');
                });
            } else {
                // For unauthenticated users, only show forms without user group restrictions
                $query->whereNull('user_group_id');
            }

            $forms = $query->orderBy('title')->get();

            return response()->json([
                'success' => true,
                'data' => $forms->map(function ($form) {
                    return [
                        'id' => $form->id,
                        'title' => $form->title,
                        'created_at' => $form->created_at,
                    ];
                }),
            ]);

        } catch (\Exception $e) {
            $this->errorLogger->logError(
                \App\Models\ErrorLog::LEVEL_ERROR,
                \App\Models\ErrorLog::TYPE_SYSTEM_ERROR,
                $e->getMessage(),
                [
                    'user_id' => Auth::id(),
                    'exception_class' => get_class($e),
                ]
            );

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving available forms',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error',
            ], 500);
        }
    }

    /**
     * Get form definition for rendering
     */
    public function formDefinition(int $formId): JsonResponse
    {
        try {
            $form = Form::where('id', $formId)
                ->where('is_active', true)
                ->firstOrFail();

            // Check user access if authenticated
            $user = Auth::user();
            if ($user && $form->user_group_id && $form->user_group_id !== $user->user_group_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Access denied to this form',
                ], 403);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $form->id,
                    'title' => $form->title,
                    'content' => $form->content,
                ],
            ]);

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Form not found',
            ], 404);

        } catch (\Exception $e) {
            $this->errorLogger->logError(
                \App\Models\ErrorLog::LEVEL_ERROR,
                \App\Models\ErrorLog::TYPE_SYSTEM_ERROR,
                $e->getMessage(),
                [
                    'form_id' => $formId,
                    'user_id' => Auth::id(),
                    'exception_class' => get_class($e),
                ]
            );

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving form definition',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error',
            ], 500);
        }
    }
}
