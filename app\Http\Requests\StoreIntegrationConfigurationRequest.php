<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreIntegrationConfigurationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        // TODO: Re-enable permission checking when user management system is ready
        // return auth()->check() && auth()->user()->type === 1; // TYPE_SUPER_ADMIN
        return auth()->check(); // Temporarily allow any authenticated user
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => 'required|string|max:255|unique:integration_configurations,name',
            'endpoint_alias' => 'required|string|max:100|unique:integration_configurations,endpoint_alias',
            'external_system' => 'required|string|max:100',
            'integration_method' => 'required|string|max:50',
            'process_type' => 'required|string|max:100',
            'endpoint_url' => 'required|string|max:500',
            'request_fields' => 'required|json',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'name.required' => 'The configuration name is required.',
            'name.unique' => 'A configuration with this name already exists.',
            'endpoint_alias.required' => 'The endpoint alias is required.',
            'endpoint_alias.unique' => 'An endpoint with this alias already exists.',
            'endpoint_alias.max' => 'The endpoint alias must not exceed 100 characters.',
            'external_system.required' => 'Please select an external system.',
            'integration_method.required' => 'Please select an integration method.',
            'process_type.required' => 'Please select a process type.',
            'endpoint_url.required' => 'The integration endpoint is required.',
            'endpoint_url.max' => 'The endpoint URL must not exceed 500 characters.',
            'request_fields.required' => 'Please define at least one request field.',
            'request_fields.json' => 'The request fields must be valid JSON.',
            'target_type.required' => 'Please select an ERP system.',
            'target_type.in' => 'Invalid ERP selection.',
            'process_selection.required' => 'Please select a process type.',
            'process_selection.in' => 'Invalid process selection.',
            'endpoint_type.required' => 'Please select an endpoint type.',
            'endpoint_type.in' => 'Invalid endpoint type selection.',
        ];
    }
}
