<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class UpdateFormFieldTemplateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        $user = Auth::user();
        return $user->isAdmin() || $user->isSuperAdmin();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $formFieldTemplateId = $this->route('form_field_template') ? $this->route('form_field_template')->id : null;

        return [
            'title' => 'required|string|max:255',
            'key' => 'required|string|max:255|unique:form_field_templates,key,' . $formFieldTemplateId,
            'icon' => 'required|string|max:255',
            'schema' => 'required|json',
            'schema_label' => 'required|string',
            'schema_type' => 'required|string',
            'schema_key' => 'required|string',
            'schema_input' => 'boolean',
            'schema_required' => 'boolean',
            'options' => 'json',
            'options.*.label' => 'required_with:options|string',
            'options.*.value' => 'required_with:options|string',
        ];
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        // Convert individual schema fields to JSON
        if ($this->has('schema_label') || $this->has('schema_type') || $this->has('schema_key')) {
            $schema = [
                'label' => $this->input('schema_label', ''),
                'type' => $this->input('schema_type', ''),
                'key' => $this->input('schema_key', ''),
                'input' => true, // Always true for form components
                'validate' => [
                    'required' => $this->boolean('schema_required')
                ]
            ];

            // Add options for select, checkbox, and radio components
            $type = $this->input('schema_type');
            if (in_array($type, ['select', 'radio']) && $this->has('options')) {
                $options = [];
                foreach (json_decode($this->input('options'), true) as $option) {
                    if (!empty($option['label']) && !empty($option['value'])) {
                        $options[] = [
                            'label' => $option['label'],
                            'value' => $option['value']
                        ];
                    }
                }

                if (!empty($options)) {

                    if (in_array($type, ['radio'])) {
                        $schema['values'] = $options;
                    } else {
                        $schema['data'] = [
                            'values' => $options
                        ];
                    }
                }
            }

            $this->merge([
                'schema' => json_encode($schema)
            ]);
        }
    }
}
