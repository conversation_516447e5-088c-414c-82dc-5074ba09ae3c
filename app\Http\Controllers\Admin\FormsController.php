<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Log;
use App\Http\Requests\StoreFormRequest;
use App\Http\Requests\UpdateFormRequest;
use App\Models\DataCaptureField;
use App\Models\IntegrationConfiguration;
use App\Models\Form;
use App\Models\FormFieldTemplate;
use App\Models\UserGroup;
use App\Services\FormioHelper;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Support\Facades\Auth;

class FormsController extends Controller

{

    protected $helperService;

    public function __construct(\App\Services\HelperService $helperService)
    {
        $this->helperService = $helperService;
    }

    public function index(Request $request)
    {
        $columns = Form::dataTableColumns();

        if ($request->ajax()) {
            $user = Auth::user();
            $query = Form::with(['userGroup', 'updater', 'redirectToForm']);
            $query = $query->where('is_standard', 0);
            $query = $query->select('forms.*');

            // ->accessibleByUser($user)
            // ->orderBy('created_at', 'desc');
            // $this->applyFilters($query, $request);
            // $query = DataTableFilter::apply($query, $request, $columns);
            $query = $query->applyDataTableFilters($request);

            // $sampleDate = now();
            // $formattedDate = $this->helperService->formatDate($sampleDate);

            return DataTables::of($query)
                ->addIndexColumn()
                ->addColumn('action', function ($row) use ($user) {
                    $actions = [];
                    // $actions['view'] = route('admin.forms.preview', $row->id);
                    if ($user->isAdmin() || $user->isSuperAdmin()) {
                        if ($row->is_standard && Auth::user()->isSuperAdmin() || !$row->is_standard) {
                            $actions['delete'] = route('admin.forms.destroy', $row->id);
                            $actions['edit'] = route('admin.forms.edit', $row->id);
                        }
                        $actions['clone'] = route('admin.forms.clone', $row->id);
                    }
                    // dd($actions);
                    $html = view('partials.datatablesActions', compact('actions', 'row'))->render();

                    return $html;
                })
                ->editColumn('device_view', function ($row) {
                    return ucwords($row->device_view);
                })
                ->editColumn('module', function ($row) {
                    return ucwords($row->module);
                })
                ->addColumn('user_group', function ($row) {
                    return $row->userGroup ? $row->userGroup->name : 'No Group';
                })
                ->addColumn('creator_name', function ($row) {
                    return $row->creator ? $row->creator->name : 'Unknown';
                })
                ->addColumn('status', function ($row) {
                    return $row->status == 'published' ?
                        '<span class="badge badge-success">Published</span>' :
                        '<span class="badge badge-danger">Draft</span>';
                })
                ->addColumn('redirect_to', function ($row) {
                    return $row->redirectToForm ? $row->redirectToForm->title : '-';
                })
                // ->editColumn('created_at', function ($row) {
                //     return $row->created_at->format('Y-m-d H:i:s');
                // })
                ->rawColumns(['action', 'status'])
                ->make(true);
        }

        return view('admin.forms.index', compact('columns'));
    }

    public function getStandardForms(Request $request)
    {
        $columns = Form::standardFormsDataTableColumns();
        // dd($columns);
        if ($request->ajax()) {
            $user = Auth::user();
            $query = Form::with(['userGroup', 'creator', 'redirectToForm']);
            $query = $query->where('is_standard', 1);
            // ->accessibleByUser($user)
            // ->orderBy('created_at', 'desc');
            // $this->applyFilters($query, $request);
            // $query = DataTableFilter::apply($query, $request, $columns);
            $query = $query->applyDataTableFilters($request);

            // $sampleDate = now();
            // $formattedDate = $this->helperService->formatDate($sampleDate);

            return DataTables::of($query)
                ->addIndexColumn()
                ->addColumn('action', function ($row) use ($user) {
                    $actions = [];
                    $actions['view'] = route('admin.forms.preview', $row->id);
                    if ($user->isAdmin() || $user->isSuperAdmin()) {
                        if ($row->is_standard && Auth::user()->isSuperAdmin() || !$row->is_standard) {
                            $actions['delete'] = route('admin.forms.destroy', $row->id);
                            $actions['edit'] = route('admin.forms.edit', $row->id);
                        }
                        $actions['clone'] = route('admin.forms.clone', $row->id);
                    }
                    // dd($actions);
                    $html = view('partials.datatablesActions', compact('actions', 'row'))->render();

                    return $html;
                })

                ->editColumn('module', function ($row) {
                    return ucwords($row->module);
                })

                ->addColumn('redirect_to', function ($row) {
                    return $row->redirectToForm ? $row->redirectToForm->title : '-';
                })
                // ->editColumn('created_at', function ($row) {
                //     return $row->created_at->format('Y-m-d H:i:s');
                // })
                ->rawColumns(['action', 'status'])
                ->make(true);
        }

        return view('admin.forms.standard.index', compact('columns'));
    }
    /**
     * Apply DataTables column filters to the query.
     */
    protected function applyFilters(&$query, Request $request): void
    {
        if (!$request->has('columns')) {
            return;
        }
        // dd($request->get('columns'));
        foreach ($request->get('columns') as $col) {
            $colName = $col['name'] ?? null;
            $searchVal = $col['search']['value'] ?? null;

            if (!empty($colName) && !empty($searchVal)) {
                switch ($colName) {
                    case 'device_view':
                        $query->where('device_view', strtolower($searchVal));
                        break;

                    case 'is_active':
                        // Map human labels to DB values

                        $mapped = strtolower($searchVal) === 'active' ? 1 : 0;
                        // dd($mapped);
                        $query->where('is_active', $mapped);
                        break;


                    case 'module':
                        // dd($searchVal);
                        $query->where('module', 'like', "%{$searchVal}%");
                        break;

                    case 'creator.name':
                        $query->whereHas('creator', function ($q) use ($searchVal) {
                            $q->where('name', 'like', "%{$searchVal}%");
                        });
                        break;

                    case 'user_group.name':
                        $query->whereHas('userGroup', function ($q) use ($searchVal) {
                            $q->where('name', 'like', "%{$searchVal}%");
                        });
                        break;

                    case 'created_at':
                        $query->whereDate('created_at', $searchVal);
                        break;
                }
            }
        }
    }
    public function create()
    {
        $user = Auth::user();
        $components = FormFieldTemplate::getComponents();
        $systemFields = FormioHelper::getSystemFields();
        $dcfields = DataCaptureField::getComponents();

        // Only admin users can create forms
        if (!$user->isAdmin() && !$user->isSuperAdmin()) {
            abort(403, 'Unauthorized action.');
        }
        $processOptions = IntegrationConfiguration::getProcessOptions();

        $userGroups = UserGroup::active()->pluck('name', 'id');
        $forms = Form::pluck('title', 'id');
        // $form= new Form();
        return view('admin.forms.create', compact('userGroups', 'dcfields', 'processOptions', 'components', 'forms', 'systemFields'));
    }

    public function store(StoreFormRequest $request)
    {
        $user = Auth::user();
        $data = $request->validated();
        $data['created_by'] = $user->id;
        $data['device_view'] = "mobile";
        // 🚨 Only superadmin can set is_standard
        if (Auth::user()->isSuperAdmin()) {
            $data['is_standard'] = true;
        } else {
            $data['is_standard'] = false;
        }

        // Assign tenant_id automatically if tenant user
        $data['tenant_id'] = Auth::user()->tenant_id ?? 0;


        // Start database transaction
        return \DB::transaction(function () use ($request, $data) {
            // Create the form
            $form = Form::create($data);

            // Sync user groups
            if ($request->has('user_groups')) {
                $form->userGroups()->sync($request->user_groups);
            }

            // Process field definitions if any
            if ($request->has('field_definitions') && is_array($request->field_definitions)) {
                $this->processFieldDefinitions($request->field_definitions);
            }

            if ($form->is_standard) {
                return redirect()->route('admin.forms.getStandardForms')
                    ->with('success', 'Form updated successfully.');
            }
            return redirect()->route('admin.forms.index')
                ->with('success', 'Form created successfully.');
        });
    }

    public function show(Form $form)
    {
        $user = Auth::user();

        // Check if user can access this form
        if (!$user->isAdmin() && !$user->isSuperAdmin()) {
            $canAccess = $form->user_group_id == $user->user_group_id ||
                $form->userGroups->contains('id', $user->user_group_id) ||
                ($form->user_group_id === null && $form->userGroups->isEmpty());

            if (!$canAccess) {
                abort(403, 'Unauthorized action.');
            }
        }

        $form->load(['userGroup', 'creator']);
        return view('admin.forms.show', compact('form'));
    }

    public function edit(Form $form)
    {
        $user = Auth::user();
        $components = FormFieldTemplate::getComponents();
        $systemFields = FormioHelper::getSystemFields();
        $dcfields = DataCaptureField::getComponents();
        // dd($dcfields);
        // Only admin users can edit forms
        if ($form->is_standard && !Auth::user()->isSuperAdmin()) {
            abort(403, 'Unauthorized action.');
        }
        if (!$user->isAdmin() && !$user->isSuperAdmin()) {
            abort(403, 'Unauthorized action.');
        }
        $processOptions = IntegrationConfiguration::getProcessOptions();

        $userGroups = UserGroup::active()->pluck('name', 'id');
        $forms = Form::pluck('title', 'id');
        return view('admin.forms.edit', compact('form', 'dcfields', 'userGroups', 'processOptions', 'components', 'forms', 'systemFields'));
    }

    public function update(UpdateFormRequest $request, Form $form)
    {
        $data = $request->validated();
        if (Auth::user()->isSuperAdmin()) {
            $data['is_standard'] = 1;
        } else {
            $data['is_standard'] = 0;
        }

        // Assign tenant_id automatically if tenant user
        // $data['tenant_id'] = Auth::user()->tenant_id ?? 0;
        // dd($data, $request->all());
        $form->update($data);

        // Sync user groups
        if ($request->has('user_groups')) {
            $form->userGroups()->sync($request->user_groups);
        } else {
            $form->userGroups()->detach();
        }

        if ($form->is_standard) {
            return redirect()->route('admin.forms.getStandardForms')
                ->with('success', 'Form updated successfully.');
        }
        return redirect()->route('admin.forms.index')
            ->with('success', 'Form updated successfully.');
    }

    public function destroy(Form $form)
    {
        $user = Auth::user();

        // Only admin users can delete forms
        if (!$user->isAdmin() && !$user->isSuperAdmin()) {
            abort(403, 'Unauthorized action.');
        }
        $formOld = $form;
        $form->delete();

        if ($formOld->is_standard) {
            return redirect()->route('admin.forms.getStandardForms')
                ->with('success', 'Form updated successfully.');
        }
        return redirect()->route('admin.forms.index')
            ->with('success', 'Form deleted successfully.');
    }

    public function preview(Form $form)
    {
        $user = Auth::user();

        // Check if user can access this form
        if (!$user->isAdmin() && !$user->isSuperAdmin()) {
            $canAccess = $form->user_group_id == $user->user_group_id ||
                $form->userGroups->contains('id', $user->user_group_id) ||
                ($form->user_group_id === null && $form->userGroups->isEmpty());

            if (!$canAccess) {
                abort(403, 'Unauthorized action.');
            }
        }

        return view('admin.forms.preview', compact('form'));
    }

    public function render(Form $form)
    {
        $user = Auth::user();

        // Check if user can access this form
        if (!$user->isAdmin() && !$user->isSuperAdmin()) {
            $canAccess = $form->userGroups->contains('id', $user->user_group_id) ||
                ($form->user_group_id === null && $form->userGroups->isEmpty());

            if (!$canAccess) {
                abort(403, 'Unauthorized action.');
            }
        }
        $backUrl = route('incoming');
        return view('mobile.forms.render', compact('form', 'backUrl'));
    }
    public function submit(Request $request)
    {
        $user = Auth::user();
        dd($request->all());
        // Check if user can access this form
        if (!$user->isAdmin() && !$user->isSuperAdmin()) {
            $canAccess = $form->userGroups->contains('id', $user->user_group_id) ||
                ($form->user_group_id === null && $form->userGroups->isEmpty());

            if (!$canAccess) {
                abort(403, 'Unauthorized action.');
            }
        }
        $backUrl = route('incoming');
        return view('mobile.forms.render', compact('form', 'backUrl'));
    }
    public function callIntegeration(string $integrationAlias, Request $request)
    {
        // $tnr= ['item_num' => "a", 'from_loc' => "b", 'order_line' => "line 1", 'uom' => "uom", 'qty' => "12"];
        $entries=[
            ['item_num'=>"a", 'from_loc' => "b", 'order_line' => "line 1", 'uom' => "uom",'qty'=>"12"],
            ['item_num' => "b", 'from_loc' => "bb", 'order_line' => "line 2", 'uom' => "uom", 'qty' => "32"],
        ];
        return response()->json(['lineEntry' => $entries]);

        $integration = IntegrationConfiguration::where('alias', $integrationAlias)->firstOrFail();

        $url = $request->get('endpointUrl')
            ? rtrim($integration->base_url, '/') . '/' . ltrim($request->get('endpointUrl'), '/')
            : rtrim($integration->base_url, '/') . '/' . ltrim($integration->endpoint_url, '/');

        $payload = $request->get('params', []);

        $client = new Client();
        $resp = $client->request($integration->method ?? 'POST', $url, [
            'headers' => [
                'Authorization' => 'Bearer ' . $integration->credentials,
                'Content-Type'  => 'application/json',
            ],
            'json' => $payload,
        ]);

        return response()->json(json_decode($resp->getBody()->getContents(), true));
    }


    public function clone($id)
    {
        $form = Form::findOrFail($id);

        // Clone the form
        $clonedForm = $form->replicate();

        // Update the title
        $clonedForm->title = $form->title . ' (copy)';
        $clonedForm->is_standard = 0;
        $clonedForm->status = "draft";
        $clonedForm->tenant_id = auth()->user()->id;
        // Optionally modify slug or unique keys if needed
        // $clonedForm->slug = Str::slug($clonedForm->title) . '-' . Str::random(4);

        // Save the cloned form
        $clonedForm->save();

        return redirect()->route('admin.forms.edit', $clonedForm)->with('success', 'Form cloned successfully.');
    }
}
