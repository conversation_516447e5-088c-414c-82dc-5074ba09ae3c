@extends('layouts.admin')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-exclamation-triangle"></i> Error Logs
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-primary btn-sm" id="refreshTable">
                            <i class="fas fa-sync"></i> Refresh
                        </button>
                        <button type="button" class="btn btn-info btn-sm" data-toggle="modal" data-target="#statisticsModal">
                            <i class="fas fa-chart-bar"></i> Statistics
                        </button>
                        <button type="button" class="btn btn-success btn-sm" data-toggle="modal" data-target="#exportModal">
                            <i class="fas fa-download"></i> Export
                        </button>
                    </div>
                </div>

                <!-- Filters -->
                <div class="card-body border-bottom">
                    <form id="filterForm" class="row">
                        <div class="col-md-2">
                            <label for="error_level">Error Level</label>
                            <select class="form-control form-control-sm" id="error_level" name="error_level">
                                <option value="">All Levels</option>
                                @foreach($errorLevels as $level)
                                    <option value="{{ $level['value'] }}">{{ $level['label'] }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="error_type">Error Type</label>
                            <select class="form-control form-control-sm" id="error_type" name="error_type">
                                <option value="">All Types</option>
                                @foreach($errorTypes as $type)
                                    <option value="{{ $type['value'] }}">{{ $type['label'] }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="resolved">Status</label>
                            <select class="form-control form-control-sm" id="resolved" name="resolved">
                                <option value="">All</option>
                                <option value="false">Unresolved</option>
                                <option value="true">Resolved</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="submission_uuid">Submission UUID</label>
                            <input type="text" class="form-control form-control-sm" id="submission_uuid" name="submission_uuid" placeholder="Search UUID...">
                        </div>
                        <div class="col-md-2">
                            <label for="date_from">Date From</label>
                            <input type="date" class="form-control form-control-sm" id="date_from" name="date_from">
                        </div>
                        <div class="col-md-2">
                            <label for="date_to">Date To</label>
                            <input type="date" class="form-control form-control-sm" id="date_to" name="date_to">
                        </div>
                    </form>
                    <div class="row mt-2">
                        <div class="col-12">
                            <button type="submit" form="filterForm" class="btn btn-primary btn-sm">
                                <i class="fas fa-search"></i> Filter
                            </button>
                            <button type="button" class="btn btn-secondary btn-sm" id="clearFilters">
                                <i class="fas fa-times"></i> Clear
                            </button>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <div class="table-responsive">
                        <table id="errorLogsTable" class="table table-bordered table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Level</th>
                                    <th>Type</th>
                                    <th>Message</th>
                                    <th>Form Submission</th>
                                    <th>Integration</th>
                                    <th>Status</th>
                                    <th>Created At</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Error Log Details Modal -->
<div class="modal fade" id="errorLogModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Error Log Details</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" id="errorLogDetails">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Resolve Error Modal -->
<div class="modal fade" id="resolveErrorModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Resolve Error</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="resolveErrorForm">
                <div class="modal-body">
                    <input type="hidden" id="resolve_error_uuid" name="error_uuid">
                    <div class="form-group">
                        <label for="resolution_notes">Resolution Notes <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="resolution_notes" name="resolution_notes" rows="4" 
                                  placeholder="Describe how this error was resolved..." required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check"></i> Mark as Resolved
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Statistics Modal -->
<div class="modal fade" id="statisticsModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Error Log Statistics</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" id="statisticsContent">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Export Modal -->
<div class="modal fade" id="exportModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Export Error Logs</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="exportForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="export_format">Format</label>
                        <select class="form-control" id="export_format" name="format" required>
                            <option value="csv">CSV</option>
                            <option value="json">JSON</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="export_error_level">Error Level</label>
                        <select class="form-control" id="export_error_level" name="error_level">
                            <option value="">All Levels</option>
                            @foreach($errorLevels as $level)
                                <option value="{{ $level['value'] }}">{{ $level['label'] }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="export_error_type">Error Type</label>
                        <select class="form-control" id="export_error_type" name="error_type">
                            <option value="">All Types</option>
                            @foreach($errorTypes as $type)
                                <option value="{{ $type['value'] }}">{{ $type['label'] }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="export_resolved">Status</label>
                        <select class="form-control" id="export_resolved" name="resolved">
                            <option value="">All</option>
                            <option value="false">Unresolved</option>
                            <option value="true">Resolved</option>
                        </select>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="export_date_from">Date From</label>
                                <input type="date" class="form-control" id="export_date_from" name="date_from">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="export_date_to">Date To</label>
                                <input type="date" class="form-control" id="export_date_to" name="date_to">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-download"></i> Export
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Initialize DataTable
    var table = $('#errorLogsTable').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '{{ route("admin.error-logs.index") }}',
            data: function(d) {
                d.error_level = $('#error_level').val();
                d.error_type = $('#error_type').val();
                d.resolved = $('#resolved').val();
                d.submission_uuid = $('#submission_uuid').val();
                d.date_from = $('#date_from').val();
                d.date_to = $('#date_to').val();
            }
        },
        columns: [
            { data: 'error_level_badge', name: 'error_level', orderable: true },
            { data: 'error_type_badge', name: 'error_type', orderable: true },
            { data: 'error_message', name: 'error_message', orderable: false },
            { data: 'submission_info', name: 'form_submission_id', orderable: false },
            { data: 'integration_info', name: 'form_submission_sync_id', orderable: false },
            { data: 'resolved_status', name: 'resolved', orderable: true },
            { data: 'created_at', name: 'created_at', orderable: true },
            { data: 'action', name: 'action', orderable: false, searchable: false }
        ],
        order: [[6, 'desc']],
        pageLength: 25,
        responsive: true,
        dom: 'Bfrtip',
        buttons: []
    });

    // Filter form submission
    $('#filterForm').on('submit', function(e) {
        e.preventDefault();
        table.draw();
    });

    // Clear filters
    $('#clearFilters').on('click', function() {
        $('#filterForm')[0].reset();
        table.draw();
    });

    // Refresh table
    $('#refreshTable').on('click', function() {
        table.ajax.reload();
    });

    // View error log details
    $(document).on('click', '.view-details', function() {
        var id = $(this).data('id');
        
        $.ajax({
            url: '{{ route("admin.error-logs.show", ":id") }}'.replace(':id', id),
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    var data = response.data;
                    var html = `
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Error Information</h6>
                                <table class="table table-sm">
                                    <tr><td><strong>UUID:</strong></td><td>${data.uuid}</td></tr>
                                    <tr><td><strong>Level:</strong></td><td><span class="badge badge-${data.error_level === 'critical' ? 'dark' : data.error_level === 'error' ? 'danger' : data.error_level === 'warning' ? 'warning' : 'info'}">${data.error_level_label}</span></td></tr>
                                    <tr><td><strong>Type:</strong></td><td><span class="badge badge-secondary">${data.error_type_label}</span></td></tr>
                                    <tr><td><strong>Code:</strong></td><td>${data.error_code || 'N/A'}</td></tr>
                                    <tr><td><strong>Created At:</strong></td><td>${data.created_at}</td></tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6>Resolution Status</h6>
                                <table class="table table-sm">
                                    <tr><td><strong>Resolved:</strong></td><td><span class="badge badge-${data.resolved ? 'success' : 'danger'}">${data.resolved ? 'Yes' : 'No'}</span></td></tr>`;
                    
                    if (data.resolved) {
                        html += `
                            <tr><td><strong>Resolved At:</strong></td><td>${data.resolved_at}</td></tr>
                            <tr><td><strong>Resolved By:</strong></td><td>${data.resolver_name}</td></tr>`;
                    }
                    
                    html += `</table></div></div>`;
                    
                    html += `
                        <div class="row mt-3">
                            <div class="col-12">
                                <h6>Error Message</h6>
                                <div class="alert alert-danger">${data.error_message}</div>
                            </div>
                        </div>`;

                    if (data.form_submission) {
                        html += `
                            <div class="row mt-3">
                                <div class="col-12">
                                    <h6>Form Submission</h6>
                                    <table class="table table-sm">
                                        <tr><td><strong>UUID:</strong></td><td>${data.form_submission.uuid}</td></tr>
                                        <tr><td><strong>Form:</strong></td><td>${data.form_submission.form_title}</td></tr>
                                        <tr><td><strong>Status:</strong></td><td>${data.form_submission.status}</td></tr>
                                        <tr><td><strong>Submitted At:</strong></td><td>${data.form_submission.submitted_at}</td></tr>
                                    </table>
                                </div>
                            </div>`;
                    }

                    if (data.integration) {
                        html += `
                            <div class="row mt-3">
                                <div class="col-12">
                                    <h6>Integration</h6>
                                    <table class="table table-sm">
                                        <tr><td><strong>Name:</strong></td><td>${data.integration.name}</td></tr>
                                        <tr><td><strong>Target System:</strong></td><td>${data.integration.target_system}</td></tr>
                                        <tr><td><strong>Status:</strong></td><td>${data.integration.status}</td></tr>
                                    </table>
                                </div>
                            </div>`;
                    }

                    if (data.stack_trace) {
                        html += `
                            <div class="row mt-3">
                                <div class="col-12">
                                    <h6>Stack Trace</h6>
                                    <pre class="bg-light p-2" style="max-height: 300px; overflow-y: auto;">${data.stack_trace}</pre>
                                </div>
                            </div>`;
                    }

                    if (data.context_data) {
                        html += `
                            <div class="row mt-3">
                                <div class="col-12">
                                    <h6>Context Data</h6>
                                    <pre class="bg-light p-2">${JSON.stringify(data.context_data, null, 2)}</pre>
                                </div>
                            </div>`;
                    }

                    if (data.resolution_notes) {
                        html += `
                            <div class="row mt-3">
                                <div class="col-12">
                                    <h6>Resolution Notes</h6>
                                    <div class="alert alert-success">${data.resolution_notes}</div>
                                </div>
                            </div>`;
                    }

                    $('#errorLogDetails').html(html);
                    $('#errorLogModal').modal('show');
                }
            },
            error: function() {
                alert('Failed to load error log details');
            }
        });
    });

    // Resolve error
    $(document).on('click', '.resolve-error', function() {
        var uuid = $(this).data('uuid');
        $('#resolve_error_uuid').val(uuid);
        $('#resolveErrorModal').modal('show');
    });

    // Submit resolve form
    $('#resolveErrorForm').on('submit', function(e) {
        e.preventDefault();
        
        var uuid = $('#resolve_error_uuid').val();
        var notes = $('#resolution_notes').val();
        
        $.ajax({
            url: '{{ route("admin.error-logs.resolve", ":uuid") }}'.replace(':uuid', uuid),
            method: 'POST',
            data: {
                resolution_notes: notes,
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    $('#resolveErrorModal').modal('hide');
                    $('#resolveErrorForm')[0].reset();
                    table.ajax.reload();
                    alert('Error resolved successfully');
                } else {
                    alert('Failed to resolve error: ' + response.message);
                }
            },
            error: function() {
                alert('Failed to resolve error');
            }
        });
    });

    // Load statistics
    $('#statisticsModal').on('show.bs.modal', function() {
        $('#statisticsContent').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading...</div>');
        
        $.ajax({
            url: '{{ route("admin.error-logs.statistics") }}',
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    var data = response.data;
                    var html = `
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Error Statistics (Last ${data.period_hours} hours)</h6>
                                <table class="table table-sm">
                                    <tr><td>Total Errors:</td><td><strong>${data.statistics.total_errors}</strong></td></tr>
                                    <tr><td>Critical:</td><td><strong class="text-danger">${data.statistics.critical_errors}</strong></td></tr>
                                    <tr><td>Errors:</td><td><strong class="text-warning">${data.statistics.error_level_errors}</strong></td></tr>
                                    <tr><td>Warnings:</td><td><strong class="text-info">${data.statistics.warning_errors}</strong></td></tr>
                                    <tr><td>Resolved:</td><td><strong class="text-success">${data.statistics.resolved_errors}</strong></td></tr>
                                    <tr><td>Unresolved:</td><td><strong class="text-danger">${data.statistics.unresolved_errors}</strong></td></tr>
                                    <tr><td>Resolution Rate:</td><td><strong>${data.statistics.resolution_rate}%</strong></td></tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6>Error Types</h6>
                                <table class="table table-sm">`;
                    
                    Object.entries(data.statistics.error_types).forEach(function([type, count]) {
                        var label = type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                        html += `<tr><td>${label}:</td><td><strong>${count}</strong></td></tr>`;
                    });
                    
                    html += `</table></div></div>`;
                    
                    if (data.critical_errors.length > 0) {
                        html += `
                            <div class="row mt-3">
                                <div class="col-12">
                                    <h6 class="text-danger">Critical Unresolved Errors</h6>
                                    <div class="list-group">`;
                        
                        data.critical_errors.forEach(function(error) {
                            html += `
                                <div class="list-group-item list-group-item-danger">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">${error.form_title}</h6>
                                        <small>${error.created_at}</small>
                                    </div>
                                    <p class="mb-1">${error.error_message}</p>
                                    <small>UUID: ${error.uuid}</small>
                                </div>`;
                        });
                        
                        html += `</div></div></div>`;
                    }
                    
                    if (data.trends.length > 0) {
                        html += `
                            <div class="row mt-3">
                                <div class="col-12">
                                    <h6>7-Day Trend</h6>
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Date</th>
                                                <th>Total</th>
                                                <th>Critical</th>
                                                <th>Resolved</th>
                                            </tr>
                                        </thead>
                                        <tbody>`;
                        
                        data.trends.forEach(function(trend) {
                            html += `
                                <tr>
                                    <td>${trend.date}</td>
                                    <td>${trend.total}</td>
                                    <td class="text-danger">${trend.critical}</td>
                                    <td class="text-success">${trend.resolved}</td>
                                </tr>`;
                        });
                        
                        html += `</tbody></table></div></div>`;
                    }
                    
                    $('#statisticsContent').html(html);
                }
            },
            error: function() {
                $('#statisticsContent').html('<div class="alert alert-danger">Failed to load statistics</div>');
            }
        });
    });

    // Export form submission
    $('#exportForm').on('submit', function(e) {
        e.preventDefault();
        
        var formData = $(this).serialize();
        var url = '{{ route("admin.error-logs.export") }}?' + formData;
        
        // Create a temporary link to trigger download
        var link = document.createElement('a');
        link.href = url;
        link.download = '';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        $('#exportModal').modal('hide');
    });
});
</script>
@endpush
