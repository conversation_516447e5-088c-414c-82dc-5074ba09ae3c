<?php

namespace App\Models;

use App\Traits\DataTableFilter;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, DataTableFilter;

    const TYPE_USER = 3;
    const TYPE_ADMIN = 2;
    const TYPE_SUPER_ADMIN = 1;
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'user_group_id',
        'type',
        'tenant_id',
        'updated_by',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        ''
    ];

    public static function dataTableColumns(): array
    {
        return [
            [
                'data' => 'DT_RowIndex',
                'name' => 'DT_RowIndex',
                'title' => '#',
                'orderable' => false,
                'searchable' => false,
                'filter' => false,
                'width' => '10',
            ],
            [
                'data' => 'name',
                'name' => 'name',
                'title' => __('cruds.fields.name'),
                'filter' => 'text',
            ],
            [
                'data' => 'email',
                'name' => 'email',
                'title' => __('cruds.fields.email'),
                'filter' => 'text',
            ],
            [
                'data' => 'user_group',
                'name' => 'userGroup.name',
                'title' => __('cruds.fields.user_group'),
                'filter' => 'relation',
                'filter_callback' => function ($query, $value) {
                    return $query->whereHas('userGroup', function ($q) use ($value) {
                        $q->whereRaw("LOWER(name) like ?", ['%' . strtolower($value) . '%']);
                    });
                },
            ],
            [
                'data' => 'role_type',
                'name' => 'type',
                'title' => __('cruds.fields.type'),
                'filter' => 'select',
                'options' => [
                    'admin' => __('Admin'),
                    'user' => __('User'),
                    'manager' => __('Manager'),
                ],
            ],
            [
                'data' => 'updated_at',
                'name' => 'updated_at',
                'title' => __('global.updated_at'),
                'class' => 'text-right',
                'filter' => 'date_range',
            ],
            [
                'data' => 'action',
                'name' => 'action',
                'title' => __('global.actions'),
                'orderable' => false,
                'searchable' => false,
                'filter' => false,
                'width' => '150',
            ],
        ];
    }

    public function isAdmin()
    {
        return $this->type === self::TYPE_ADMIN || $this->type === self::TYPE_SUPER_ADMIN;
    }

    public function isSuperAdmin()
    {
        return $this->type === self::TYPE_SUPER_ADMIN;
    }

    public function isUser()
    {
        return $this->type === self::TYPE_USER;
    }

    public function userGroup()
    {
        return $this->belongsTo(UserGroup::class);
    }
    public function getCreatedAtAttribute($value)
    {
        return \Carbon\Carbon::parse($value)->format('m/d/Y');
    }
    public function getUpdatedAtAttribute($value)
    {
        return \Carbon\Carbon::parse($value)->format('m/d/Y');
    }
}
