<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $tableName = 'field_mapping_configurations';
        $oldColumn = 'endpoint_configuration_id';
        $newColumn = 'integration_configuration_id';
        $foreignTable = 'integration_configurations';
        
        // Check if the table exists
        if (!Schema::hasTable($tableName)) {
            return;
        }

        // Check if the column is already renamed
        if (Schema::hasColumn($tableName, $newColumn)) {
            // Column is already renamed, ensure the foreign key exists
            $this->ensureForeignKeyExists($tableName, $newColumn, $foreignTable);
            return;
        }

        // Check if the old column exists
        if (!Schema::hasColumn($tableName, $oldColumn)) {
            // Neither old nor new column exists, nothing to do
            return;
        }

        // Use shorter constraint names to avoid MySQL's 64-character limit
        $foreignKey = "fk_{$tableName}_{$oldColumn}";
        $newForeignKey = "fk_{$tableName}_int_conf";

        // Drop old foreign key if it exists
        $this->dropForeignKeyIfExists($tableName, $foreignKey);
        
        // Rename the column
        DB::statement("ALTER TABLE `{$tableName}` CHANGE `{$oldColumn}` `{$newColumn}` BIGINT UNSIGNED NULL");

        // Add new foreign key
        $this->ensureForeignKeyExists($tableName, $newColumn, $foreignTable, $newForeignKey);
    }

    /**
     * Drop a foreign key if it exists
     */
    protected function dropForeignKeyIfExists($table, $constraintName)
    {
        $constraint = DB::selectOne(
            "SELECT CONSTRAINT_NAME 
            FROM information_schema.TABLE_CONSTRAINTS 
            WHERE CONSTRAINT_SCHEMA = DATABASE() 
            AND TABLE_NAME = ? 
            AND CONSTRAINT_NAME = ?
            AND CONSTRAINT_TYPE = 'FOREIGN KEY'",
            [$table, $constraintName]
        );

        if ($constraint) {
            DB::statement("ALTER TABLE `{$table}` DROP FOREIGN KEY `{$constraintName}`");
        }
    }

    /**
     * Ensure a foreign key exists
     */
    protected function ensureForeignKeyExists($table, $column, $foreignTable, $constraintName = null)
    {
        if ($constraintName === null) {
            $constraintName = "fk_{$table}_{$column}";
        }

        $constraint = DB::selectOne(
            "SELECT CONSTRAINT_NAME 
            FROM information_schema.TABLE_CONSTRAINTS 
            WHERE CONSTRAINT_SCHEMA = DATABASE() 
            AND TABLE_NAME = ? 
            AND CONSTRAINT_NAME = ?
            AND CONSTRAINT_TYPE = 'FOREIGN KEY'",
            [$table, $constraintName]
        );

        if (!$constraint) {
            DB::statement("
                ALTER TABLE `{$table}`
                ADD CONSTRAINT `{$constraintName}`
                FOREIGN KEY (`{$column}`)
                REFERENCES `{$foreignTable}` (`id`)
                ON DELETE CASCADE
            ");
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        $tableName = 'field_mapping_configurations';
        $oldColumn = 'endpoint_configuration_id';
        $newColumn = 'integration_configuration_id';
        $foreignTable = 'integration_configurations';
        // Use shorter constraint names to avoid MySQL's 64-character limit
        $foreignKey = "fk_{$tableName}_{$oldColumn}";
        $newForeignKey = "fk_{$tableName}_int_conf";

        // Check if new foreign key exists before dropping
        $newForeignKeyExists = DB::selectOne(
            "SELECT COUNT(*) as count 
            FROM information_schema.TABLE_CONSTRAINTS 
            WHERE CONSTRAINT_SCHEMA = DATABASE() 
            AND TABLE_NAME = '{$tableName}' 
            AND CONSTRAINT_NAME = '{$newForeignKey}'
            AND CONSTRAINT_TYPE = 'FOREIGN KEY'"
        )->count > 0;

        if ($newForeignKeyExists) {
            DB::statement("ALTER TABLE `{$tableName}` DROP FOREIGN KEY `{$newForeignKey}`");
        }
        
        // Rename back to original
        DB::statement("ALTER TABLE `{$tableName}` CHANGE `{$newColumn}` `{$oldColumn}` BIGINT UNSIGNED NULL");

        // Recreate original foreign key
        DB::statement("
            ALTER TABLE `{$tableName}`
            ADD CONSTRAINT `{$foreignKey}`
            FOREIGN KEY (`{$oldColumn}`)
            REFERENCES `{$foreignTable}` (`id`)
            ON DELETE CASCADE
        ");
    }
};
