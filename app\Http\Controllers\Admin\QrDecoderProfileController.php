<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\QrProfileStoreRequest;
use App\Models\QrDecoderField;
use App\Models\QrDecoderProfile;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;

class QrDecoderProfileController extends Controller
{
    public function index(Request $request)
    {
        // $profiles = QrDecoderProfile::paginate(10);
        if ($request->ajax()) {
            $query = QrDecoderProfile::orderBy('created_at', 'desc');
            $query = $query->applyDataTableFilters($request);

            return DataTables::of($query)
                ->addIndexColumn()
                // ->addColumn('checkbox', function ($row) {
                //     return '<input type="checkbox" name="ids[]" value="' . $row->id . '" data-entry-id="' . $row->id . '">';
                // })
                ->addColumn('action', function ($row) {


                    $actions = [];
                    // $actions['view'] = route('admin.qr-decoder-profiles.show', $row->id);
                    // if ($user->isAdmin() || $user->isSuperAdmin()) {
                    $actions['edit'] = route('admin.qr-decoder-profiles.edit', $row->id);
                    $actions['delete'] = route('admin.qr-decoder-profiles.destroy', $row->id);
                    // }
                    $html = view('partials.datatablesActions', compact('actions', 'row'))->render();

                    return $html;
                })
                ->addColumn('status', function ($row) {
                    return $row->is_active ?
                        '<span class="badge badge-success">Active</span>' :
                        '<span class="badge badge-danger">Inactive</span>';
                })

                // ->editColumn('created_at', function ($row) {
                //     return $row->created_at->format('Y-m-d H:i:s');
                // })
                ->rawColumns(['checkbox', 'action', 'status'])
                ->make(true);
        }

        return view('admin.qr-decoder-profiles.index');
    }

    public function create()
    {
        return view('admin.qr-decoder-profiles.create');
    }

    public function store(QrProfileStoreRequest $request)
    {
        DB::transaction(function () use ($request) {
            $profile = QrDecoderProfile::create($request->only([
                'name',
                'process_type',
                'mode',
                'delimiter',
                'sample',
                'vendor_num',
                'enabled'
            ]));
            // foreach ($request->input('fields', []) as $order => $f) {
            //     $f['order'] = $f['order'] ?? $order;
            //     $profile->fields()->create($f);
            // }
        });
        return redirect()->route('admin.qr-decoder-profiles.index')->with('success', 'Profile created.');
    }

    public function edit(Request $request, $id)
    {
        $qr_profile = QrDecoderProfile::findOrFail($id);
        // $qr_profile->load('fields');
        // dd($id);
        return view('admin.qr-decoder-profiles.edit', compact('qr_profile'));
    }

    public function update(QrProfileStoreRequest $request, QrDecoderProfile $qr_profile)
    {
        DB::transaction(function () use ($request, $qr_profile) {
            $qr_profile->update($request->only(['name', 'process_name', 'mode', 'delimiter', 'sample_qr', 'vendor_num', 'enabled']));
            $qr_profile->fields()->delete();
            foreach ($request->input('fields', []) as $order => $f) {
                $f['order'] = $f['order'] ?? $order;
                $qr_profile->fields()->create($f);
            }
        });
        return redirect()->route('admin.qr-decoder-profiles.index')->with('success', 'Profile updated.');
    }

    public function destroy(QrDecoderProfile $qr_profile)
    {
        $qr_profile->delete();
        return redirect()->route('admin.qr-decoder-profiles.index')->with('success', 'Profile deleted.');
    }
}
