@extends('layouts.admin')
@section('pageTitle', trans('cruds.usergroup.title'))

@section('styles')
    @parent
@endsection

@section('content')
    <div class="card card-default">
        <div class="card-header separator">
            <div class="card-title mainheading">
                <h4>{{ trans('cruds.usergroup.title') }} {{ trans('global.list') }}</h4>
            </div>
            <div class="card-controls">
                <ul>
                    <li>
                        <x-add-button :url="route('admin.usergroups.create')" :text="trans('cruds.usergroup.title_singular')" />
                    </li>
                </ul>
            </div>
        </div>


        <div class="card-block">
            @php
                $columns = \App\Models\UserGroup::dataTableColumns();
            @endphp

            <x-datatable :columns="$columns" :ajax="route('admin.usergroups.index')" :order="[[4, 'desc']]" />
        </div>
    </div>
@endsection

@section('scripts')
    @parent

    <script>
        $(function() {


            // Handle select all checkbox
            $('#select-all').on('click', function() {
                var rows = table.rows({
                    'search': 'applied'
                }).nodes();
                $('input[type="checkbox"]', rows).prop('checked', this.checked);
            });
        });
    </script>
@endsection
