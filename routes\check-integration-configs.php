<?php

use Illuminate\Support\Facades\Route;
use App\Models\IntegrationConfiguration;

Route::get('/check-integration-configs', function () {
    // Get all integration configurations
    $configs = \App\Models\IntegrationConfiguration::all();
    
    // Get the available process options from the model
    $processOptions = IntegrationConfiguration::getProcessOptions();
    
    echo "<h1>Integration Configurations</h1>";
    echo "<table border='1' cellpadding='5' cellspacing='0'>";
    echo "<tr><th>ID</th><th>Name</th><th>Target Type</th><th>Process Selection</th><th>Integration Type</th><th>Active</th></tr>";
    
    foreach ($configs as $config) {
        echo "<tr>";
        echo "<td>{$config->id}</td>";
        echo "<td>{$config->name}</td>";
        echo "<td>" . ($config->target_type ?: 'N/A') . "</td>";
        echo "<td>" . ($processOptions[$config->process_selection] ?? $config->process_selection) . "</td>";
        echo "<td>" . ($config->endpoint_type ?: 'N/A') . "</td>";
        echo "<td>" . ($config->is_active ? 'Yes' : 'No') . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    return ''; // Return empty string to avoid additional output
})->middleware(['auth', 'user_type:super_admin']);
