@extends('layouts.admin')
@section('pageTitle', trans('global.create') . ' ' . trans('cruds.usergroup.title_singular'))

@section('content')
    <div class="card card-default">
        <div class="card-header separator">
            <div class="card-title mainheading">
                <h4>{{ trans('global.create') }} {{ trans('cruds.usergroup.title_singular') }}</h4>
            </div>
            <div class="card-controls">
                <ul>
                    <li>
                        <x-cancel-button :url="route('admin.usergroups.index')" :text="trans('global.back_to_list')" />
                    </li>
                </ul>
            </div>
        </div>

        <div class="card-block">
            @if ($errors->any())
                <div class="alert alert-danger">
                    <ul>
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <form method="POST" action="{{ route('admin.usergroups.store') }}">
                @csrf

                <div class="form-group row">
                    <label for="name" class="col-md-2 col-form-label">{{ trans('cruds.fields.name') }} <span
                            class="text-danger">*</span></label>
                    <div class="col-md-4">
                        <input type="text" class="form-control" id="name" name="name" value="{{ old('name') }}"
                            required>
                    </div>
                    <label for="is_active" class="col-md-2 col-form-label">{{ trans('cruds.fields.status') }}</label>
                    <div class="col-md-4">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="is_active" name="is_active" value="1"
                                {{ old('is_active', true) ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_active">
                                {{ trans('cruds.fields.active') }}
                            </label>
                        </div>
                    </div>
                </div>

                <div class="form-group row">
                    <label for="description" class="col-md-2 col-form-label">{{ trans('cruds.fields.description') }}</label>
                    <div class="col-md-4">
                        <textarea class="form-control" id="description" name="description" rows="3">{{ old('description') }}</textarea>
                    </div>
                    <div class="col-md-2">
                    </div>
                    <div class="col-md-4">

                    </div>
                </div>



                <div class="form-group row">
                    <div class="col-md-10 offset-md-2 text-right">

                        <x-cancel-button :url="route('admin.usergroups.index')" :text="trans('global.cancel')" />
                        <x-save-button />
                    </div>
                </div>
            </form>
        </div>
    </div>
@endsection
