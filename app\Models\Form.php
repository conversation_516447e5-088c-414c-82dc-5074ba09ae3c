<?php

namespace App\Models;

use App\Traits\DataTableFilter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Form extends BaseModel
{
    use HasFactory, SoftDeletes, DataTableFilter;

    protected $fillable = [
        'title',
        'content',
        'user_group_id',
        'created_by',
        'updated_by',
        'is_active',
        'redirect_to',
        'device_view',
        'module',
        'tenant_id',
        'status',
        'process_type',
        'is_standard',
        'process_type',
    ];


    protected $casts = [
        'content' => 'json',
        'is_active' => 'boolean',
    ];

    public static $form_views = [
        'desktop' => "Desktop",
        'tablet' => "Tablet",
        'mobile' => "Mobile",
    ];
    public static $form_modules = [
        'incoming' => "Incoming",
        'outgoing' => "Outgoing",
        'warehouse' => "Warehouse",
    ];
    public static function dataTableColumns(): array
    {
        return [
            [
                'data' => 'DT_RowIndex',
                'name' => 'DT_RowIndex',
                'title' => __('#'),
                'filter' => false,
                'searchable' => false,
            ],
            [
                'data' => 'title',
                'name' => 'title',
                'title' => __('cruds.fields.title'),
                'filter' => 'text'
            ],
            [
                'data' => 'process_type',
                'name' => 'process_type',
                'title' => __('cruds.fields.process_type'),
                'filter' => 'text'
            ],
            // [
            //     'data' => 'device_view',
            //     'name' => 'device_view',
            //     'title' => __('Device View'),
            //     'filter' => 'select',
            //     'options' => ['Mobile', 'Tablet', 'Desktop']
            // ],
            [
                'data' => 'module',
                'name' => 'module',
                'title' => __('cruds.fields.module'),
                'filter' => 'text'
            ],
            // ✅ relationship filter

            [
                'data' => 'status',
                'name' => 'status',
                'title' =>  __('cruds.fields.status'),
                'filter' => 'select',
                'options' => ['draft' => 'Draft', 'published' => 'Published']
            ],
            [
                'data' => 'updater.name',
                'name' => 'updater.name',
                'title' =>  __('cruds.fields.updated_by'),
                'filter' => 'relation',
                'filter_callback' => function ($query, $value) {
                    return $query->whereHas('updater', function ($q) use ($value) {
                        $q->whereRaw("LOWER(name) like ?", ['%' . strtolower($value) . '%']);
                    });
                },
            ],
            [
                'data' => 'updated_at',
                'name' => 'forms.updated_at',
                'title' =>  __('cruds.fields.updated_at'),
                'filter' => 'date_range',
                'className' => 'text-right',
            ],
            [
                'data' => 'action',
                'name' => 'action',
                'title' => __('global.actions'),
                'orderable' => false,
                'searchable' => false,
                'filter' => false,
            ],
        ];
    }
    public static function standardFormsDataTableColumns(): array
    {
        return [
            [
                'data' => 'DT_RowIndex',
                'name' => 'DT_RowIndex',
                'title' => __('#'),
                'filter' => false,
                'searchable' => false,
            ],
            [
                'data' => 'title',
                'name' => 'title',
                'title' => __('cruds.fields.title'),
                'filter' => 'text'
            ],
            [
                'data' => 'process_type',
                'name' => 'process_type',
                'title' => __('cruds.fields.process_type'),
                'filter' => 'text'
            ],
            // [
            //     'data' => 'device_view',
            //     'name' => 'device_view',
            //     'title' => __('Device View'),
            //     'filter' => 'select',
            //     'options' => ['Mobile', 'Tablet', 'Desktop']
            // ],
            [
                'data' => 'module',
                'name' => 'module',
                'title' => __('cruds.fields.module'),
                'filter' => 'text'
            ],

            [
                'data' => 'updated_at',
                'name' => 'forms.updated_at',
                'title' => __('cruds.fields.updated_at'),
                'filter' => 'date_range',
                'className' => 'text-right',
            ],
            [
                'data' => 'action',
                'name' => 'action',
                'title' => __('global.actions'),
                'orderable' => false,
                'searchable' => false,
                'filter' => false,
            ],
        ];
    }
    public function userGroup()
    {
        return $this->belongsTo(UserGroup::class);
    }

    public function userGroups()
    {
        return $this->belongsToMany(UserGroup::class, 'form_user_group');
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function redirectToForm()
    {
        return $this->belongsTo(Form::class, 'redirect_to');
    }

    public function formsThatRedirectToThis()
    {
        return $this->hasMany(Form::class, 'redirect_to');
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    // Scope helpers
    public function scopePublished($query)
    {
        return $query->where('status', 'published');
    }

    public function scopeStandard($query)
    {
        return $query->where('is_standard', true);
    }

    public function scopeTenantForms($query, $tenantId)
    {
        return $query->where('tenant_id', $tenantId)->where('is_standard', false);
    }
    public function scopeForUserGroup($query, $userGroupId)
    {
        return $query->where(function ($q) use ($userGroupId) {
            $q->where('user_group_id', $userGroupId)
                ->orWhereHas('userGroups', function ($q) use ($userGroupId) {
                    $q->where('user_groups.id', $userGroupId);
                });
        });
    }

    public function scopeAccessibleByUser($query, User $user)
    {
        if ($user->isAdmin() || $user->isSuperAdmin()) {
            return $query;
        }

        return $query->where(function ($q) use ($user) {
            $q->where('user_group_id', $user->user_group_id)
                ->orWhereHas('userGroups', function ($q) use ($user) {
                    $q->where('user_groups.id', $user->user_group_id);
                })
                ->orWhere(function ($q) {
                    $q->whereNull('user_group_id')
                        ->doesntHave('userGroups');
                });
        });
    }
}
