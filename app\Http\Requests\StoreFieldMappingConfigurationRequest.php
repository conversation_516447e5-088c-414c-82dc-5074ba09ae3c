<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreFieldMappingConfigurationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true; // You may adjust the authorization logic as needed
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string'],
            'is_active' => ['boolean'],
            'form_id' => ['required', 'exists:forms,id'],
            'integration_configuration_id' => ['required', 'exists:integration_configurations,id'],
            'field_mappings' => ['required', 'array', 'min:1'],
            'field_mappings.*.form_field' => ['required', 'string'],
            'field_mappings.*.endpoint_field' => ['required', 'string'],
            'field_mappings.*.transformation' => ['nullable', 'array'],
        ];
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        // If field_mappings is a JSON string, decode it to an array
        if ($this->has('field_mappings') && is_string($this->field_mappings)) {
            $this->merge([
                'field_mappings' => json_decode($this->field_mappings, true) ?? []
            ]);
        }
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'name.required' => 'The configuration name is required.',
            'form_id.required' => 'Please select a form.',
            'form_id.exists' => 'The selected form does not exist.',
            'integration_configuration_id.required' => 'Please select an integration configuration.',
            'integration_configuration_id.exists' => 'The selected integration configuration does not exist.',
            'field_mappings.required' => 'At least one field mapping is required.',
        ];
    }
}
