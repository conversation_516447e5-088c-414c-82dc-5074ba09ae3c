class FieldMappingTransformations {
    constructor() {
        this.transformations = [];
        this.initialized = false;
    }

    /**
     * Initialize the transformation system
     */
    init() {
        if (this.initialized) return;
        
        // Load available transformations from the server or use default
        this.loadTransformations();
        
        // Initialize tooltips
        $(document).tooltip({
            selector: '[data-toggle="tooltip"]',
            container: 'body'
        });
        
        this.initialized = true;
    }
    
    /**
     * Get all available transformation options
     * @returns {Array} Array of transformation objects
     */
    getTransformationOptions() {
        return this.transformations.map(t => ({
            value: t.value,
            label: t.label,
            description: t.description,
            hasParams: t.params && t.params.length > 0
        }));
    }
    
    /**
     * Get transformation configuration by type
     * @param {string} type - The transformation type
     * @returns {Object|null} Transformation configuration or null if not found
     */
    getTransformationConfig(type) {
        if (!type) return null;
        return this.transformations.find(t => t.value === type) || null;
    }
    
    /**
     * Get transformation data for a field mapping container
     * @param {jQuery} $container - The container element
     * @returns {Object|null} Transformation data or null if no transformation
     */
    getTransformationData($container) {
        const $row = $container.closest('tr');
        const fieldKey = $row.data('field-key') || $container.find('.endpoint-field').val();
        
        if (!fieldKey) return null;
        
        // Check if we have a saved transformation for this field
        const savedMapping = window.fieldMappings && window.fieldMappings[fieldKey];
        if (savedMapping && savedMapping.transform) {
            return savedMapping.transform;
        }
        
        // Check for inline transformation (from the select element)
        const $transformType = $container.find('.transform-type');
        const transformType = $transformType.val();
        
        if (!transformType) return null;
        
        return {
            type: transformType,
            params: {}
        };
    }
    
    /**
     * Apply a transformation to a value
     * @param {*} value - The value to transform
     * @param {Object} transform - The transformation configuration
     * @param {Object} formData - Optional form data for transformations that reference other fields
     * @returns {*} The transformed value
     */
    applyTransformation(value, transform, formData = {}) {
        if (!transform || !transform.type) return value;
        
        const transformConfig = this.getTransformationConfig(transform.type);
        if (!transformConfig) return value;
        
        try {
            switch (transform.type) {
                case 'trim':
                    return typeof value === 'string' ? value.trim() : value;
                    
                case 'uppercase':
                    return typeof value === 'string' ? value.toUpperCase() : value;
                    
                case 'lowercase':
                    return typeof value === 'string' ? value.toLowerCase() : value;
                    
                case 'capitalize':
                    if (typeof value !== 'string') return value;
                    return value.replace(/\b\w/g, char => char.toUpperCase());
                    
                case 'concatenate':
                    if (!transform.params || !transform.params.fields) return value;
                    const fields = transform.params.fields.split(',').map(f => f.trim());
                    const separator = transform.params.separator || ' ';
                    return fields.map(field => formData[field] || '').join(separator);
                    
                case 'format_date':
                    if (!value || !transform.params || !transform.params.from_format || !transform.params.to_format) {
                        return value;
                    }
                    return this._formatDate(value, transform.params.from_format, transform.params.to_format);
                    
                case 'map_values':
                    if (!transform.params || !transform.params.mapping) return value;
                    return transform.params.mapping[value] !== undefined ? transform.params.mapping[value] : value;
                    
                case 'round': {
                    const num = parseFloat(value);
                    if (isNaN(num)) return value;
                    const decimals = transform.params && transform.params.decimals !== undefined ? 
                        parseInt(transform.params.decimals, 10) : 0;
                    const method = transform.params && transform.params.method || 'round';
                    const roundFactor = Math.pow(10, decimals);
                    
                    switch (method) {
                        case 'floor':
                            return Math.floor(num * roundFactor) / roundFactor;
                        case 'ceil':
                            return Math.ceil(num * roundFactor) / roundFactor;
                        default: // round
                            return Math.round(num * roundFactor) / roundFactor;
                    }
                }
                    
                case 'multiply':
                    const multiplier = transform.params && transform.params.factor ? 
                        parseFloat(transform.params.factor) : 1;
                    return parseFloat(value) * multiplier;
                    
                case 'divide':
                    const divisor = transform.params && transform.params.divisor ? 
                        parseFloat(transform.params.divisor) : 1;
                    if (divisor === 0) {
                        return transform.params.default !== undefined ? 
                            parseFloat(transform.params.default) : value;
                    }
                    return parseFloat(value) / divisor;
                    
                case 'custom':
                    if (!transform.params || !transform.params.script) return value;
                    try {
                        // In a real implementation, this would be executed in a sandboxed environment
                        // For security, this is just a placeholder
                        console.warn('Custom script execution is disabled in this demo');
                        return value;
                    } catch (e) {
                        console.error('Error executing custom transformation:', e);
                        return value;
                    }
                    
                default:
                    return value;
            }
        } catch (e) {
            console.error('Error applying transformation:', e);
            return value;
        }
    }
    
    /**
     * Format a date string from one format to another
     * @private
     */
    _formatDate(dateStr, fromFormat, toFormat) {
        // This is a simplified implementation
        // In a real app, you would use a proper date library like moment.js or date-fns
        try {
            // Simple format conversion (YYYY-MM-DD to DD/MM/YYYY and vice versa)
            if (fromFormat === 'Y-m-d' && toFormat === 'd/m/Y') {
                const parts = dateStr.split('-');
                if (parts.length === 3) {
                    return `${parts[2]}/${parts[1]}/${parts[0]}`;
                }
            } else if (fromFormat === 'd/m/Y' && toFormat === 'Y-m-d') {
                const parts = dateStr.split('/');
                if (parts.length === 3) {
                    return `${parts[2]}-${parts[1]}-${parts[0]}`;
                }
            } else if (fromFormat === 'timestamp' && toFormat === 'Y-m-d') {
                const date = new Date(parseInt(dateStr, 10) * 1000);
                return date.toISOString().split('T')[0];
            } else if (fromFormat === 'Y-m-d' && toFormat === 'timestamp') {
                return Math.floor(new Date(dateStr).getTime() / 1000).toString();
            }
            
            return dateStr; // Return as-is if we can't convert
        } catch (e) {
            console.error('Error formatting date:', e);
            return dateStr;
        }
    }
    
    /**
     * Load available transformations from the server
     */
    loadTransformations() {
        // This would typically be an AJAX call to get transformations from the server
        // For now, we'll use a predefined list of transformations
        this.transformations = [
            {
                value: 'none',
                label: 'No Transformation',
                description: 'No transformation will be applied',
                params: []
            },
            {
                value: 'trim',
                label: 'Trim Whitespace',
                description: 'Remove whitespace from the beginning and end of the value',
                params: []
            },
            {
                value: 'uppercase',
                label: 'Uppercase',
                description: 'Convert the value to uppercase',
                params: []
            },
            {
                value: 'lowercase',
                label: 'Lowercase',
                description: 'Convert the value to lowercase',
                params: []
            },
            {
                value: 'capitalize',
                label: 'Capitalize',
                description: 'Capitalize the first letter of each word',
                params: []
            },
            {
                value: 'concatenate',
                label: 'Concatenate Fields',
                description: 'Combine multiple field values into one',
                params: [
                    {
                        name: 'fields',
                        type: 'text',
                        label: 'Fields to concatenate',
                        description: 'Comma-separated list of field names',
                        required: true,
                        placeholder: 'field1,field2,field3'
                    },
                    {
                        name: 'separator',
                        type: 'text',
                        label: 'Separator',
                        description: 'Character(s) to insert between field values',
                        required: false,
                        default: ' ',
                        placeholder: 'separator (default: space)'
                    }
                ]
            },
            {
                value: 'format_date',
                label: 'Format Date',
                description: 'Convert date from one format to another',
                params: [
                    {
                        name: 'from_format',
                        type: 'select',
                        label: 'From Format',
                        description: 'Current date format',
                        required: true,
                        options: [
                            { value: 'Y-m-d', label: 'YYYY-MM-DD' },
                            { value: 'd/m/Y', label: 'DD/MM/YYYY' },
                            { value: 'm/d/Y', label: 'MM/DD/YYYY' },
                            { value: 'Y-m-d H:i:s', label: 'YYYY-MM-DD HH:MM:SS' },
                            { value: 'timestamp', label: 'Unix Timestamp' }
                        ]
                    },
                    {
                        name: 'to_format',
                        type: 'select',
                        label: 'To Format',
                        description: 'Desired date format',
                        required: true,
                        options: [
                            { value: 'Y-m-d', label: 'YYYY-MM-DD' },
                            { value: 'd/m/Y', label: 'DD/MM/YYYY' },
                            { value: 'm/d/Y', label: 'MM/DD/YYYY' },
                            { value: 'Y-m-d H:i:s', label: 'YYYY-MM-DD HH:MM:SS' },
                            { value: 'F j, Y', label: 'Month Day, Year' },
                            { value: 'timestamp', label: 'Unix Timestamp' }
                        ]
                    }
                ]
            },
            {
                value: 'map_values',
                label: 'Map Values',
                description: 'Map specific values to other values',
                params: [
                    {
                        name: 'mapping',
                        type: 'key-value',
                        label: 'Value Mappings',
                        description: 'Map source values to target values',
                        required: true,
                        keyLabel: 'Source Value',
                        valueLabel: 'Target Value',
                        default: {}
                    },
                    {
                        name: 'case_sensitive',
                        type: 'checkbox',
                        label: 'Case Sensitive',
                        description: 'Match values with case sensitivity',
                        default: false
                    }
                ]
            },
            {
                value: 'round',
                label: 'Round Number',
                description: 'Round a numeric value to a specified number of decimal places',
                params: [
                    {
                        name: 'decimals',
                        type: 'number',
                        label: 'Decimal Places',
                        description: 'Number of decimal places to round to',
                        required: true,
                        default: 0,
                        min: 0,
                        max: 10
                    },
                    {
                        name: 'method',
                        type: 'select',
                        label: 'Rounding Method',
                        description: 'How to round the number',
                        required: true,
                        default: 'round',
                        options: [
                            { value: 'round', label: 'Standard Rounding' },
                            { value: 'floor', label: 'Round Down' },
                            { value: 'ceil', label: 'Round Up' }
                        ]
                    }
                ]
            },
            {
                value: 'multiply',
                label: 'Multiply',
                description: 'Multiply the value by a factor',
                params: [
                    {
                        name: 'factor',
                        type: 'number',
                        label: 'Multiplier',
                        description: 'Number to multiply by',
                        required: true,
                        default: 1,
                        step: 'any'
                    }
                ]
            },
            {
                value: 'divide',
                label: 'Divide',
                description: 'Divide the value by a number',
                params: [
                    {
                        name: 'divisor',
                        type: 'number',
                        label: 'Divisor',
                        description: 'Number to divide by',
                        required: true,
                        default: 1,
                        step: 'any'
                    },
                    {
                        name: 'default',
                        type: 'number',
                        label: 'Default Value',
                        description: 'Value to use if division by zero occurs',
                        required: false,
                        step: 'any'
                    }
                ]
            },
            {
                value: 'custom',
                label: 'Custom Script',
                description: 'Apply a custom transformation using JavaScript',
                params: [
                    {
                        name: 'script',
                        type: 'code',
                        label: 'JavaScript Code',
                        description: 'Custom JavaScript function that returns the transformed value',
                        required: true,
                        language: 'javascript',
                        placeholder: 'function(value, formData) {\n  // Your transformation logic here\n  return value;\n}'
                    }
                ]
            }
        ];
    }
    
    /**
     * Get HTML for transformation select element
     * @param {string} fieldName - The name of the field
     * @param {string} currentValue - The currently selected value
     * @returns {string} HTML for the select element
     */
    getTransformationSelect(fieldName, currentValue = null) {
        const selectId = `transform_${fieldName}`;
        let html = `
            <div class="transformation-container">
                <div class="input-group input-group-sm mb-2">
                    <select class="form-select form-select-sm transform-type" 
                            id="${selectId}" 
                            name="transformations[${fieldName}][type]"
                            data-field="${fieldName}">
                        <option value="">No Transformation</option>`;
        
        this.getTransformationOptions().forEach(option => {
            const selected = currentValue === option.value ? 'selected' : '';
            html += `<option value="${option.value}" ${selected} 
                    data-has-params="${option.hasParams}"
                    title="${option.description}">
                    ${option.label}
                </option>`;
        });
        
        html += `
                    </select>
                    <button type="button" class="btn btn-outline-secondary btn-sm show-params" 
                            data-bs-toggle="modal" data-bs-target="#transformConfigModal"
                            style="display: none;">
                        <i class="fas fa-cog"></i>
                    </button>
                </div>
                <div id="${selectId}_params" class="transform-params"></div>
            </div>`;
            
        return html;
    }
    
    /**
     * Show parameters for the selected transformation
     * @param {jQuery} $select - The select element that triggered the change
     */
    showTransformationParams($select) {
        const fieldName = $select.data('field');
        const transformationType = $select.val();
        const $paramsContainer = $(`#${$select.attr('id')}_params`);
        const $showParamsBtn = $select.closest('.input-group').find('.show-params');
        
        // Hide the configure button by default
        $showParamsBtn.hide();
        $paramsContainer.empty();
        
        if (!transformationType) return;
        
        const transformConfig = this.getTransformationConfig(transformationType);
        if (!transformConfig || !transformConfig.params || transformConfig.params.length === 0) {
            return;
        }
        
        // Show the configure button if there are parameters
        $showParamsBtn.show();
        
        // Store the field name on the button for later reference
        $showParamsBtn.data('field', fieldName);
    }
    
    /**
     * Get HTML for a parameter input field
     * @param {string} fieldName - The name of the field
     * @param {Object} param - The parameter configuration
     * @param {*} currentValue - The current value of the parameter
     * @returns {string} HTML for the parameter input
     */
    getParamInput(fieldName, param, currentValue = null) {
        const inputName = `transformations[${fieldName}][params][${param.name}]`;
        const inputId = `transform_${fieldName}_${param.name}`;
        
        let html = `<div class="mb-3">
            <label for="${inputId}" class="form-label">${param.label}</label>`;
            
        if (param.description) {
            html += `<div class="form-text mb-1">${param.description}</div>`;
        }
        
        switch (param.type) {
            case 'select':
                html += `<select class="form-select" id="${inputId}" name="${inputName}">`;
                param.options.forEach(option => {
                    const selected = (currentValue !== null && currentValue === option.value) || 
                                   (currentValue === null && param.default === option.value) ? 'selected' : '';
                    html += `<option value="${option.value}" ${selected}>${option.label}</option>`;
                });
                html += '</select>';
                break;
                
            case 'checkbox':
                const checked = currentValue !== null ? currentValue : (param.default || false);
                html += `
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" 
                               id="${inputId}" name="${inputName}" 
                               ${checked ? 'checked' : ''}>
                        <label class="form-check-label" for="${inputId}">
                            ${param.label}
                        </label>
                    </div>`;
                break;
                
            case 'key-value':
                return this.getKeyValueInput(inputId, fieldName, param, currentValue);
                
            case 'code':
                html += `
                    <div class="code-editor-container">
                        <textarea class="form-control code-editor" 
                                 id="${inputId}" 
                                 name="${inputName}" 
                                 rows="6"
                                 placeholder="${param.placeholder || ''}">${currentValue || ''}</textarea>
                    </div>`;
                break;
                
            default: // text, number, etc.
                const value = currentValue !== null ? currentValue : (param.default || '');
                html += `
                    <input type="${param.type || 'text'}" 
                           class="form-control" 
                           id="${inputId}" 
                           name="${inputName}" 
                           value="${value}"
                           placeholder="${param.placeholder || ''}"
                           ${param.required ? 'required' : ''}
                           ${param.min !== undefined ? `min="${param.min}"` : ''}
                           ${param.max !== undefined ? `max="${param.max}"` : ''}
                           ${param.step ? `step="${param.step}"` : ''}>`;
        }
        
        html += '</div>';
        return html;
    }
    
    /**
     * Get HTML for a key-value parameter input
     * @param {string} id - The base ID for the input
     * @param {string} fieldName - The name of the field
     * @param {Object} param - The parameter configuration
     * @param {Object} currentValue - The current key-value pairs
     * @returns {string} HTML for the key-value input
     */
    getKeyValueInput(id, fieldName, param, currentValue = {}) {
        const pairs = currentValue && Object.keys(currentValue).length > 0 ? 
            currentValue : 
            (param.default || { '': '' });
            
        let html = `
            <div class="key-value-input" id="${id}">
                <div class="key-value-pairs">`;
        
        Object.entries(pairs).forEach(([key, value], index) => {
            const keyId = `${id}_key_${index}`;
            const valueId = `${id}_value_${index}`;
            
            html += `
                <div class="key-value-pair row g-2 mb-2" data-index="${index}">
                    <div class="col-md-5">
                        <input type="text" class="form-control form-control-sm key-input" 
                               placeholder="${param.keyLabel || 'Key'}" 
                               value="${key}">
                    </div>
                    <div class="col-md-5">
                        <input type="text" class="form-control form-control-sm value-input" 
                               placeholder="${param.valueLabel || 'Value'}" 
                               value="${value}">
                    </div>
                    <div class="col-md-2">
                        <button type="button" class="btn btn-sm btn-outline-danger remove-pair">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>`;
        });
        
        html += `
                </div>
                <div class="mt-2">
                    <button type="button" class="btn btn-sm btn-outline-secondary add-pair">
                        <i class="fas fa-plus"></i> Add Pair
                    </button>
                </div>
                <input type="hidden" name="transformations[${fieldName}][params][${param.name}]" 
                       id="${id}_hidden" value='${JSON.stringify(pairs)}'>
            </div>`;
            
        return html;
    }
    
    /**
     * Initialize event handlers for transformation UI elements
     */
    initEventHandlers() {
        // Toggle transformation parameters
        $(document).on('change', '.transform-type', (e) => {
            const $select = $(e.target);
            this.showTransformationParams($select);
        });
        
        // Show transformation configuration modal
        $(document).on('click', '.show-params', (e) => {
            e.preventDefault();
            
            const $button = $(e.target).closest('.show-params');
            const fieldName = $button.data('field');
            const $select = $(`#transform_${fieldName}`);
            const transformType = $select.val();
            
            if (!transformType) return;
            
            const transformConfig = this.getTransformationConfig(transformType);
            if (!transformConfig || !transformConfig.params || transformConfig.params.length === 0) {
                return;
            }
            
            // Get current transformation data
            const $row = $select.closest('tr');
            const fieldKey = $row.data('field-key') || $row.find('.endpoint-field').val();
            const savedMapping = window.fieldMappings && window.fieldMappings[fieldKey];
            const currentParams = savedMapping && savedMapping.transform ? 
                savedMapping.transform.params : {};
            
            // Build the modal content
            let modalContent = `
                <div class="modal-header">
                    <h5 class="modal-title">${transformConfig.label} Configuration</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p class="text-muted">${transformConfig.description}</p>
                    <form id="transformConfigForm">`;
            
            // Add parameter inputs
            transformConfig.params.forEach(param => {
                const paramValue = currentParams[param.name] !== undefined ? 
                    currentParams[param.name] : 
                    (param.default !== undefined ? param.default : null);
                
                modalContent += this.getParamInput(fieldName, param, paramValue);
            });
            
            modalContent += `
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary save-transform">Save Changes</button>
                </div>`;
            
            // Update the modal content
            const $modal = $('#transformConfigModal');
            $modal.html(modalContent);
            
            // Initialize code editor if needed
            if ($('.code-editor').length > 0) {
                // In a real implementation, you would initialize a code editor here
                // For example, using CodeMirror or similar
                console.log('Initialize code editor here');
            }
            
            // Initialize key-value pair handlers
            this.initKeyValueHandlers();
            
            // Show the modal
            $modal.modal('show');
        });
        
        // Save transformation configuration
        $(document).on('click', '.save-transform', (e) => {
            e.preventDefault();
            
            const $modal = $('#transformConfigModal');
            const $form = $('#transformConfigForm');
            const $select = $(`.transform-type[data-field="${$modal.data('field')}"]`);
            const fieldName = $select.data('field');
            const transformType = $select.val();
            
            if (!transformType) {
                $modal.modal('hide');
                return;
            }
            
            // Collect form data
            const formData = {};
            $form.find('input, select, textarea').each(function() {
                const $input = $(this);
                const name = $input.attr('name');
                
                if (!name) return;
                
                // Handle different input types
                if ($input.attr('type') === 'checkbox') {
                    formData[name] = $input.is(':checked');
                } else if ($input.hasClass('key-value-hidden')) {
                    // Key-value pairs are handled separately
                    return;
                } else {
                    formData[name] = $input.val();
                }
            });
            
            // Update the transformation data
            const $row = $select.closest('tr');
            const fieldKey = $row.data('field-key') || $row.find('.endpoint-field').val();
            
            if (!window.fieldMappings) {
                window.fieldMappings = {};
            }
            
            if (!window.fieldMappings[fieldKey]) {
                window.fieldMappings[fieldKey] = {};
            }
            
            window.fieldMappings[fieldKey].transform = {
                type: transformType,
                params: formData
            };
            
            // Update the UI to show that the transformation is configured
            $select.addClass('is-valid');
            
            // Close the modal
            $modal.modal('hide');
            
            // Update the hidden field mappings input
            if (typeof updateFieldMappingsInput === 'function') {
                updateFieldMappingsInput();
            }
        });
    }
    
    /**
     * Initialize event handlers for key-value pair inputs
     */
    initKeyValueHandlers() {
        // Add new key-value pair
        $(document).on('click', '.add-pair', function() {
            const $container = $(this).closest('.key-value-input');
            const $pairsContainer = $container.find('.key-value-pairs');
            const index = $pairsContainer.children().length;
            
            const html = `
                <div class="key-value-pair row g-2 mb-2" data-index="${index}">
                    <div class="col-md-5">
                        <input type="text" class="form-control form-control-sm key-input" placeholder="Key">
                    </div>
                    <div class="col-md-5">
                        <input type="text" class="form-control form-control-sm value-input" placeholder="Value">
                    </div>
                    <div class="col-md-2">
                        <button type="button" class="btn btn-sm btn-outline-danger remove-pair">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>`;
                
            $pairsContainer.append(html);
            updateKeyValueHiddenInput($container);
        });
        
        // Remove key-value pair
        $(document).on('click', '.remove-pair', function() {
            const $pair = $(this).closest('.key-value-pair');
            $pair.remove();
            
            // Update indices
            const $container = $(this).closest('.key-value-input');
            $container.find('.key-value-pair').each(function(index) {
                $(this).attr('data-index', index);
            });
            
            updateKeyValueHiddenInput($container);
        });
        
        // Update hidden input when key or value changes
        $(document).on('change', '.key-input, .value-input', function() {
            const $container = $(this).closest('.key-value-input');
            updateKeyValueHiddenInput($container);
        });
        
        /**
         * Update the hidden input with the current key-value pairs
         * @param {jQuery} $container - The key-value input container
         */
        function updateKeyValueHiddenInput($container) {
            const $pairs = $container.find('.key-value-pair');
            const data = {};
            
            $pairs.each(function() {
                const $pair = $(this);
                const key = $pair.find('.key-input').val();
                const value = $pair.find('.value-input').val();
                
                if (key) {
                    data[key] = value;
                }
            });
            
            $container.find('input[type="hidden"]').val(JSON.stringify(data));
        }
    }
}

// Initialize the transformation system when the document is ready
$(document).ready(function() {
    window.fieldMappingTransformations = new FieldMappingTransformations();
    window.fieldMappingTransformations.init();
    window.fieldMappingTransformations.initEventHandlers();
});
