<?php

namespace App\Services;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class FieldTransformationService
{
    /**
     * Apply transformation to a field value
     *
     * @param mixed $value
     * @param array|null $transformation
     * @return mixed
     */
    public function transform($value, ?array $transformation = null)
    {
        if (empty($transformation) || empty($transformation['type'])) {
            return $value;
        }

        try {
            $method = 'apply' . str_replace('_', '', ucwords($transformation['type'], '_'));
            if (method_exists($this, $method)) {
                return $this->$method($value, $transformation['params'] ?? []);
            }
            
            // For custom transformations
            if ($transformation['type'] === 'custom' && !empty($transformation['script'])) {
                return $this->applyCustom($value, $transformation['script']);
            }
            
            return $value;
        } catch (\Exception $e) {
            Log::error('Transformation failed', [
                'value' => $value,
                'transformation' => $transformation,
                'error' => $e->getMessage()
            ]);
            return $value;
        }
    }

    /**
     * Get available transformation types with their parameters
     *
     * @return array
     */
    public function getAvailableTransformations(): array
    {
        return [
            [
                'value' => 'trim',
                'label' => 'Trim Whitespace',
                'description' => 'Remove whitespace from the beginning and end of the value',
                'params' => []
            ],
            [
                'value' => 'uppercase',
                'label' => 'Uppercase',
                'description' => 'Convert the value to uppercase',
                'params' => []
            ],
            [
                'value' => 'lowercase',
                'label' => 'Lowercase',
                'description' => 'Convert the value to lowercase',
                'params' => []
            ],
            [
                'value' => 'concatenate',
                'label' => 'Concatenate',
                'description' => 'Join multiple values with a separator',
                'params' => [
                    [
                        'name' => 'fields',
                        'type' => 'text',
                        'label' => 'Field Names',
                        'description' => 'Comma-separated field names to concatenate',
                        'required' => true
                    ],
                    [
                        'name' => 'separator',
                        'type' => 'text',
                        'label' => 'Separator',
                        'description' => 'String to join values with (default: space)',
                        'default' => ' ',
                        'required' => false
                    ]
                ]
            ],
            [
                'value' => 'format_date',
                'label' => 'Format Date',
                'description' => 'Format a date string',
                'params' => [
                    [
                        'name' => 'from_format',
                        'type' => 'text',
                        'label' => 'From Format',
                        'description' => 'Current date format (e.g., Y-m-d, m/d/Y)',
                        'default' => 'Y-m-d',
                        'required' => true
                    ],
                    [
                        'name' => 'to_format',
                        'type' => 'text',
                        'label' => 'To Format',
                        'description' => 'Desired date format (e.g., Y-m-d, m/d/Y)',
                        'default' => 'Y-m-d',
                        'required' => true
                    ]
                ]
            ],
            [
                'value' => 'map_values',
                'label' => 'Value Mapping',
                'description' => 'Map values using a key-value mapping',
                'params' => [
                    [
                        'name' => 'mapping',
                        'type' => 'key-value',
                        'label' => 'Value Mapping',
                        'description' => 'Map source values to target values',
                        'required' => true
                    ]
                ]
            ],
            [
                'value' => 'round',
                'label' => 'Round Number',
                'description' => 'Round a number to specified decimal places',
                'params' => [
                    [
                        'name' => 'decimals',
                        'type' => 'number',
                        'label' => 'Decimal Places',
                        'description' => 'Number of decimal places to round to',
                        'default' => 0,
                        'required' => true
                    ]
                ]
            ],
            [
                'value' => 'format_number',
                'label' => 'Format Number',
                'description' => 'Format a number with decimal and thousands separators',
                'params' => [
                    [
                        'name' => 'decimals',
                        'type' => 'number',
                        'label' => 'Decimal Places',
                        'description' => 'Number of decimal places',
                        'default' => 2,
                        'required' => true
                    ],
                    [
                        'name' => 'decimal_separator',
                        'type' => 'text',
                        'label' => 'Decimal Separator',
                        'default' => '.',
                        'required' => true
                    ],
                    [
                        'name' => 'thousands_separator',
                        'type' => 'text',
                        'label' => 'Thousands Separator',
                        'default' => ',',
                        'required' => true
                    ]
                ]
            ],
            [
                'value' => 'multiply',
                'label' => 'Multiply',
                'description' => 'Multiply by a factor',
                'params' => [
                    [
                        'name' => 'factor',
                        'type' => 'number',
                        'label' => 'Multiplier',
                        'description' => 'Value to multiply by',
                        'default' => 1,
                        'required' => true
                    ]
                ]
            ],
            [
                'value' => 'divide',
                'label' => 'Divide',
                'description' => 'Divide by a value',
                'params' => [
                    [
                        'name' => 'divisor',
                        'type' => 'number',
                        'label' => 'Divisor',
                        'description' => 'Value to divide by',
                        'default' => 1,
                        'required' => true
                    ]
                ]
            ],
            [
                'value' => 'custom',
                'label' => 'Custom Script',
                'description' => 'Write custom transformation logic',
                'params' => [
                    [
                        'name' => 'script',
                        'type' => 'code',
                        'label' => 'PHP Code',
                        'description' => 'Custom PHP code that returns the transformed value. Use $value for the input value.',
                        'required' => true
                    ]
                ]
            ]
        ];
    }

    // Transformation methods
    protected function applyTrim($value, array $params = [])
    {
        return is_string($value) ? trim($value) : $value;
    }

    protected function applyUppercase($value, array $params = [])
    {
        return is_string($value) ? mb_strtoupper($value, 'UTF-8') : $value;
    }

    protected function applyLowercase($value, array $params = [])
    {
        return is_string($value) ? mb_strtolower($value, 'UTF-8') : $value;
    }

    protected function applyConcatenate($value, array $params = [])
    {
        if (!is_array($params['fields'] ?? null)) {
            $fields = array_map('trim', explode(',', $params['fields'] ?? ''));
        } else {
            $fields = $params['fields'];
        }
        
        $values = [];
        foreach ($fields as $field) {
            $values[] = $this->getValueFromContext($field, $params['context'] ?? []);
        }
        
        $separator = $params['separator'] ?? ' ';
        return implode($separator, array_filter($values, function($v) { 
            return $v !== null && $v !== ''; 
        }));
    }

    protected function applyFormatDate($value, array $params = [])
    {
        if (empty($value)) {
            return $value;
        }

        try {
            $fromFormat = $params['from_format'] ?? 'Y-m-d';
            $toFormat = $params['to_format'] ?? 'Y-m-d';
            
            $date = Carbon::createFromFormat($fromFormat, $value);
            return $date->format($toFormat);
        } catch (\Exception $e) {
            return $value;
        }
    }

    protected function applyMapValues($value, array $params = [])
    {
        $mapping = $params['mapping'] ?? [];
        return $mapping[$value] ?? $value;
    }

    protected function applyRound($value, array $params = [])
    {
        if (!is_numeric($value)) {
            return $value;
        }
        
        $decimals = (int)($params['decimals'] ?? 0);
        return round((float)$value, $decimals);
    }

    protected function applyFormatNumber($value, array $params = [])
    {
        if (!is_numeric($value)) {
            return $value;
        }
        
        $decimals = (int)($params['decimals'] ?? 2);
        $decimalSeparator = $params['decimal_separator'] ?? '.';
        $thousandsSeparator = $params['thousands_separator'] ?? ',';
        
        return number_format((float)$value, $decimals, $decimalSeparator, $thousandsSeparator);
    }

    protected function applyMultiply($value, array $params = [])
    {
        if (!is_numeric($value)) {
            return $value;
        }
        
        $factor = (float)($params['factor'] ?? 1);
        return $value * $factor;
    }

    protected function applyDivide($value, array $params = [])
    {
        if (!is_numeric($value)) {
            return $value;
        }
        
        $divisor = (float)($params['divisor'] ?? 1);
        if ($divisor == 0) {
            return $value; // Avoid division by zero
        }
        
        return $value / $divisor;
    }

    protected function applyCustom($value, $script)
    {
        try {
            // Security: Sanitize the script to prevent direct file access
            if (preg_match('/\b(require|include|eval|file_get_contents|file_put_contents|fopen|fwrite|fclose|unlink|rmdir|system|exec|shell_exec|passthru|`.*`|\\$_(GET|POST|REQUEST|COOKIE|SERVER|ENV|FILES))\s*\(/i', $script)) {
                throw new \Exception('Potentially dangerous code detected');
            }
            
            // Create a closure with limited scope
            $func = function($value) use ($script) {
                return eval('return ' . $script . ';');
            };
            
            return $func($value);
        } catch (\Exception $e) {
            Log::error('Custom transformation failed', [
                'value' => $value,
                'script' => $script,
                'error' => $e->getMessage()
            ]);
            return $value;
        }
    }

    /**
     * Get a value from the context by key
     */
    protected function getValueFromContext(string $key, array $context)
    {
        return $context[$key] ?? null;
    }
}
