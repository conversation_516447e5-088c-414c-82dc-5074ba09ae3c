<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Check if the table exists and has the erp_selection column
        if (Schema::hasTable('endpoint_configurations') && 
            Schema::hasColumn('endpoint_configurations', 'erp_selection') &&
            !Schema::hasColumn('endpoint_configurations', 'target_type')) {
            
            // Rename erp_selection column to target_type in endpoint_configurations table
            Schema::table('endpoint_configurations', function (Blueprint $table) {
                $table->renameColumn('erp_selection', 'target_type');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Only revert if the target_type column exists and erp_selection doesn't
        if (Schema::hasTable('endpoint_configurations') && 
            Schema::hasColumn('endpoint_configurations', 'target_type') &&
            !Schema::hasColumn('endpoint_configurations', 'erp_selection')) {
            
            // Revert target_type column back to erp_selection
            Schema::table('endpoint_configurations', function (Blueprint $table) {
                $table->renameColumn('target_type', 'erp_selection');
            });
        }
    }
};
