<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Rename columns
        Schema::table('endpoint_configurations', function (Blueprint $table) {
            // Drop old columns that will be replaced
            $table->dropColumn(['url', 'body_data_field', 'target_type', 'process_selection', 'endpoint_type']);
            
            // Add new columns
            $table->string('external_system')->after('name')->comment('External system identifier (e.g., csi_10, sap_b1)');
            $table->string('integration_method')->after('external_system')->comment('Integration method (api, file, message_queue, stored_procedure)');
            $table->string('process_type')->after('integration_method')->comment('Process type (e.g., misc_issue, po_receipt)');
            $table->string('endpoint_url', 500)->after('process_type')->nullable()->comment('Endpoint URL, file path, queue name, or connection string');
            $table->json('request_fields')->after('endpoint_url')->nullable()->comment('JSON structure of required request fields');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('endpoint_configurations', function (Blueprint $table) {
            // Drop new columns
            $table->dropColumn(['external_system', 'integration_method', 'process_type', 'endpoint_url', 'request_fields']);
            
            // Re-add old columns with their original structure
            $table->string('url', 500)->after('name')->comment('API endpoint path');
            $table->json('body_data_field')->nullable()->after('url')->comment('JSON body data field mapping');
            $table->enum('erp_selection', ['CSI', 'SAP'])->default('CSI')->after('body_data_field')->comment('ERP system selection');
            $table->enum('process_selection', ['Misc Issue', 'Misc Receipt', 'Quantity Move'])->after('erp_selection')->comment('Process type selection');
            $table->enum('endpoint_type', ['API', 'Stored Procedure'])->default('API')->after('process_selection')->comment('Endpoint type selection');
        });
    }
};
