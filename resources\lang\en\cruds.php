<?php

return [


    'fields' => [
        'email' => 'Email',
        'component_title' => 'Component Name',
        'component_key' => 'Component Key',
        'component_defination' => "Component Definition",

        //d
        'deleted_at' => 'Deleted at',
        'deleted_at_helper' => ' ',
        //i
        'id' => 'ID',
        'id_helper' => ' ',
        //f
        'field_title' => 'Field Name',
        'field_key' => 'Field Key',
        'field_defination' => "Field Definition",
        //n
        'name' => 'Name',
        //p
        'password' => 'Password',
        'password_confirmation' => 'Confirm Password',

        //role

        'role' => 'Role',
        //u
        'user_group' => 'User Group',
        'password_help_text' => 'Leave blank to keep current password',
        'email_verified' => 'Email Verified',
        'verified' => 'Verified',
        'not_verified' => 'Not Verified',
        'no_group_assigned' => 'No Group Assigned',
        'user_group_information' => 'User Group Information',
        'group_name' => 'Group Name',
        'description' => 'Description',
        'status' => 'Status',
        'status_helper' => ' ',
        'active' => 'Active',
        'inactive' => 'Inactive',
        'total_members' => 'Total Members',
        'unknown' => 'Unknown',
        'users_count' => 'Users Count',
        'created_at' => 'Created At',
        'created_by' => 'Created By',
        'updated_at' => 'Updated At',
        'updated_by' => 'Updated By',
        'updated_at_helper' => ' ',
        'title' => 'Title',
        'type' => "Type",
        'form_title' => 'Form Title',
        'is_active' => 'Status',
        'created_by' => 'Created By',
        'form_structure' => 'Form Structure',
        'all_groups' => 'All Groups',
        'group_information' => 'Group Information',
        'preview_form' => 'Preview Form',
        'submit_form' => 'Submit Form',
        'redirect_to' => 'Redirect To',
        'no_redirect' => 'No Redirect',

        'form_elements' => 'Form Elements',
        'back_to_details' => 'Back to Details',
        'edit_form' => 'Edit Form',
        'form_preview' => 'Form Preview',
        'form_details' => 'Form Details',
        'field_name' => 'Field Name',
        'field_key' => 'Field Key',
        'data_type' => 'Data Type',
        'max_length' => 'Max Length',
        'required' => 'Required',
        'default' => 'Default',
        'value' => 'Value',
        'label' => "Label",
        'key' => "Key",
        'required' => 'Required',
        'module' => "Module",
        'device_view' => "Device View",
        'process_type' => 'Process Type',
        'standard' => "Standard",

        'profile_name' => "Profile Name",
        'mode' => "Mode",
        'vendor_num' => "Vendor No.",
        'sample' => 'Sample',
        'delimiter' => "Delimiter",

    ],
    'userManagement' => [
        'title' => 'User management',
        'title_singular' => 'User management',
    ],
    'permission' => [
        'title' => 'Permissions',
        'title_singular' => 'Permission',

    ],
    'role' => [
        'title' => 'Roles',
        'title_singular' => 'Role',

    ],
    'user' => [
        'title' => 'Users',
        'title_singular' => 'User',
    ],
    'usergroup' => [
        'title' => 'User Groups',
        'title_singular' => 'User Group'
    ],
    'qr-decoder-profiles' => [
        'title' => 'QR Decoder Profiles',
        'title_singular' => 'QR Decoder Profile'
    ],
    'form' => [
        'title' => 'Forms',
        'title' => 'Forms',
        'title_singular' => 'Form'
    ],
    'form_field_template' => [
        'title' => 'Component Templates',
        'title_singular' => 'Component Template'
    ],
    'data_capture_field' => [
        'title' => 'Data Capture Fields',
        'title_singular' => 'Data Capture Field'
    ],
    'integrationConfiguration' => [
        'title' => 'Integration Configurations',
        'title_singular' => 'Integration Configuration',
        'fields' => [
            'id' => 'ID',
            'name' => 'Configuration Name',
            'name_placeholder' => 'Enter a unique name for this configuration',
            'name_helper' => 'A unique name to identify this integration configuration',
            'description' => 'Description',
            'description_placeholder' => 'Enter a description for this integration configuration',
            'description_helper' => 'A brief description of what this integration does',

            'external_system' => 'Target System',
            'external_system_label' => 'Target System',
            'external_system_helper' => 'The target system to integrate with',
            'process_type' => 'Process Type',
            'process_type_label' => 'Process Type',
            'process_type_helper' => 'The type of business process',
            'process_selection' => 'Process Type',
            'process_selection_helper' => 'The type of business process',
            'endpoint_type' => 'Endpoint Type',
            'endpoint_type_label' => 'Endpoint Type',
            'endpoint_type_helper' => 'The type of endpoint for this integration',
            'integration_method' => 'Integration Method',
            'integration_method_label' => 'Integration Method',
            'integration_method_helper' => 'The method used for integration (API, File, etc.)',
            'endpoint_url' => 'Endpoint URL',
            'endpoint_url_placeholder' => 'Enter the endpoint URL or connection string',
            'endpoint_url_helper' => 'The URL or connection string for the integration endpoint',
            'request_fields' => 'Request Fields',
            'request_fields_helper' => 'Define the fields required for the request',
            'body_data_field_helper' => 'Define the fields required for the request',
            'is_active' => 'Status',
            'is_active_helper' => 'Whether this integration is active or not',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'created_by' => 'Created By',
            'updated_by' => 'Updated By',

        ],
        'buttons' => [
            'import_json' => 'Import JSON',
            'export_json' => 'Export JSON',
            'add_field' => 'Add Field',
            'remove_field' => 'Remove Field',
            'save' => 'Save Configuration',
            'cancel' => 'Cancel',
        ],
        'messages' => [
            'confirm_delete' => 'Are you sure you want to delete this configuration?',
            'delete_success' => 'Configuration deleted successfully',
            'save_success' => 'Configuration saved successfully',
            'import_success' => 'Fields imported successfully',
            'import_error' => 'Error importing fields',
        ],
    ],
    'fieldMappingConfiguration' => [
        'title' => 'Field Mapping Configurations',
        'title_singular' => 'Field Mapping Configuration',
        'fields' => [
            'id' => 'ID',
            'id_helper' => ' ',
            'name' => 'Mapping Name',
            'name_helper' => 'A unique name for this mapping configuration',
            'description' => 'Description',
            'description_helper' => 'A brief description of this mapping',
            'form' => 'Form',
            'form_helper' => 'Select the form to integrate',
            'filters' => 'Filters',
            'endpoint_configuration' => 'Integration Configuration',
            'integration_configuration_id' => 'Integration Configuration',
            'integration_configuration_id_helper' => 'Select the integration configuration to use',
            'external_system' => 'Target System',
            'integration_method' => 'Integration Method',
            'process_type' => 'Process Type',
            'field_mappings' => 'Field Mappings',
            'field_mappings_helper' => 'Map form fields to endpoint fields',
            'status' => 'Status',
            'status_helper' => 'Enable or disable this mapping',
            'created_at' => 'Created At',
            'created_at_helper' => ' ',
            'updated_at' => 'Updated At',
            'updated_at_helper' => ' ',
            'deleted_at' => 'Deleted At',
            'deleted_at_helper' => ' ',
            'form_field' => 'Form Field',
            'endpoint_field' => 'Endpoint Field',
            'default_value' => 'Default Value',
            'default_value_placeholder' => 'Enter default value if no mapping',
            'state' => 'State',
            'state_helper' => 'Draft: Configuration is in progress. Finalized: Configuration is complete and ready for use.',
            'required' => 'Required',
            'add_mapping' => 'Add Mapping',
            'remove_mapping' => 'Remove',
            'no_fields_available' => 'No fields available',
            'select_form_first' => 'Please select a form first',
            'select_endpoint_first' => 'Please select an endpoint configuration first',
            'no_mappings_defined' => 'No field mappings defined',
            'import_mappings' => 'Import Mappings',
            'export_mappings' => 'Export Mappings',
            'suggest_mappings' => 'Suggest Mappings',
            'mapping_suggested' => 'Mapping suggested',
            'mapping_applied' => 'Mapping applied',
            'error_loading_fields' => 'Error loading fields',
            'error_saving_mappings' => 'Error saving field mappings',
        ],
        'message' => [
            'select_form_and_endpoint_to_configure_mappings' => 'Please configure field mappings',
        ],
    ],
];
