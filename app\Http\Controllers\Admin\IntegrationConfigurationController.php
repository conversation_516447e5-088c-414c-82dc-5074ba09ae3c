<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreIntegrationConfigurationRequest;
use App\Http\Requests\UpdateIntegrationConfigurationRequest;
use App\Models\IntegrationConfiguration;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class IntegrationConfigurationController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        // TODO: Re-enable permission checking when user management system is ready
        // abort_if(auth()->user()->type !== 1, Response::HTTP_FORBIDDEN, '403 Forbidden');

        $integrations = IntegrationConfiguration::with(['createdBy', 'updatedBy'])
            ->orderBy('created_at', 'desc')
            ->get();

        return view('admin.integration-configurations.index', compact('integrations'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        // TODO: Re-enable permission checking when user management system is ready
        // abort_if(auth()->user()->type !== 1, Response::HTTP_FORBIDDEN, '403 Forbidden');

        $externalSystemOptions = IntegrationConfiguration::getExternalSystemOptions();
        $processTypeOptions = IntegrationConfiguration::getProcessTypeOptions();
        $integrationMethodOptions = IntegrationConfiguration::getIntegrationMethodOptions();

        return view('admin.integration-configurations.create', [
            'externalSystemOptions' => $externalSystemOptions,
            'processTypeOptions' => $processTypeOptions,
            'integrationMethodOptions' => $integrationMethodOptions,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \App\Http\Requests\StoreIntegrationConfigurationRequest  $request
     * @return \Illuminate\Http\Response
     */
    /**
     * Process JSON fields in the request data.
     *
     * @param  array  $data
     * @return array
     */
    protected function processJsonFields($data)
    {
        $jsonFields = ['body_data_field', 'field_mapping', 'request_fields'];
        
        foreach ($jsonFields as $field) {
            if (isset($data[$field])) {
                if (is_string($data[$field])) {
                    try {
                        $decoded = json_decode($data[$field], true);
                        $data[$field] = (json_last_error() === JSON_ERROR_NONE) ? $decoded : null;
                    } catch (\Exception $e) {
                        $data[$field] = null;
                    }
                } elseif (is_array($data[$field])) {
                    // Already an array, ensure it's properly formatted
                    $data[$field] = !empty($data[$field]) ? $data[$field] : null;
                }
            }
        }
        
        return $data;
    }

    public function store(StoreIntegrationConfigurationRequest $request)
    {
        $data = $this->processJsonFields($request->validated());

        // Set audit fields
        $data['created_by'] = auth()->id();
        $data['updated_by'] = auth()->id();

        $integrationConfiguration = IntegrationConfiguration::create($data);

        return redirect()->route('admin.integration-configurations.index')
            ->with('success', 'Integration Configuration created successfully.');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\IntegrationConfiguration  $integrationConfiguration
     * @return \Illuminate\Http\Response
     */
    public function show(IntegrationConfiguration $integrationConfiguration)
    {
        // TODO: Re-enable permission checking when user management system is ready
        // abort_if(auth()->user()->type !== 1, Response::HTTP_FORBIDDEN, '403 Forbidden');

        $integrationConfiguration->load(['createdBy', 'updatedBy']);

        return view('admin.integration-configurations.show', compact('integrationConfiguration'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\IntegrationConfiguration  $integrationConfiguration
     * @return \Illuminate\Http\Response
     */
    public function edit(IntegrationConfiguration $integrationConfiguration)
    {
        // TODO: Re-enable permission checking when user management system is ready
        // abort_if(auth()->user()->type !== 1, Response::HTTP_FORBIDDEN, '403 Forbidden');

        $externalSystemOptions = IntegrationConfiguration::getExternalSystemOptions();
        $processTypeOptions = IntegrationConfiguration::getProcessTypeOptions();
        $integrationMethodOptions = IntegrationConfiguration::getIntegrationMethodOptions();

        return view('admin.integration-configurations.edit', [
            'integrationConfiguration' => $integrationConfiguration,
            'externalSystemOptions' => $externalSystemOptions,
            'processTypeOptions' => $processTypeOptions,
            'integrationMethodOptions' => $integrationMethodOptions,
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \App\Http\Requests\UpdateIntegrationConfigurationRequest  $request
     * @param  \App\Models\IntegrationConfiguration  $integrationConfiguration
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateIntegrationConfigurationRequest $request, IntegrationConfiguration $integrationConfiguration)
    {
        $data = $this->processJsonFields($request->validated());

        // Set audit field for update
        $data['updated_by'] = auth()->id();

        $integrationConfiguration->update($data);

        return redirect()->route('admin.integration-configurations.index')
            ->with('success', 'Integration Configuration updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\IntegrationConfiguration  $integrationConfiguration
     * @return \Illuminate\Http\Response
     */
    public function destroy(IntegrationConfiguration $integrationConfiguration)
    {
        // TODO: Re-enable permission checking when user management system is ready
        // abort_if(auth()->user()->type !== 1, Response::HTTP_FORBIDDEN, '403 Forbidden');

        $integrationConfiguration->delete();

        return redirect()->route('admin.integration-configurations.index')
            ->with('message', 'Integration Configuration deleted successfully.');
    }
}
