<?php

namespace App\Services;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class FormioHelper
{
    public static function getSystemFields(): array
    {
        $fields = config('formio.system_fields');

        foreach ($fields as &$field) {
            switch ($field['defaultValue']) {
                case '{{TENANT_ID}}':
                    $field['defaultValue'] = Auth::user()->tenant_id ?? '';
                    break;

                case '{{USER_ID}}':
                    $field['defaultValue'] = Auth::id();
                    break;

                case '{{TIMESTAMP}}':
                    $field['defaultValue'] = now()->toIso8601String();
                    break;
                case '{{FORM_ID}}':
                    $field['defaultValue'] = request()->route('form')?->id ?? '';
                    break;
                case '{{PROCESS_CODE}}':
                    $form = request()->route('form'); // assuming route has {form}
                    $field['defaultValue'] = $form?->process_code ?? '';
                    break;
                case '{{UUID}}':
                    $field['defaultValue'] = (string) Str::uuid();
                    break;
                case '{{DEVICE_ID}}':
                    // If mobile app sends it, take that
                    if (request()->has('device_id')) {
                        $field['defaultValue'] = request()->get('device_id');
                    } else {
                        // Otherwise fallback to hashed browser fingerprint
                        $ip = request()->ip();
                        $ua = request()->header('User-Agent');
                        $field['defaultValue'] = hash('sha256', $ip . $ua);
                    }
                    break;
            }
        }

        return $fields;
    }
    public static function injectValuesIntoSubmission(array $submissionData): array
    {
        foreach (self::getSystemFields() as $field) {
            $submissionData[$field['key']] = $field['defaultValue'];
        }

        return $submissionData;
    }
}
