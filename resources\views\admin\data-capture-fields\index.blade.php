@extends('layouts.admin')
@section('pageTitle', __('cruds.data_capture_field.title'))

@section('content')
    <div class="card card-default">
        <div class="card-header separator">
            <div class="card-title mainheading">
                <h4>{{ __('cruds.data_capture_field.title') }}</h4>
            </div>
            <div class="card-controls">
                <ul>
                    <li>
                        <x-add-button :url="route('superadmin.data-capture-fields.create')"
                                      :text="__('cruds.data_capture_field.title_singular')" />
                    </li>
                </ul>
            </div>
        </div>

        <div class="card-block">
            @php
                $columns = \App\Models\DataCaptureField::dataTableColumns();
            @endphp

            <x-datatable
                :columns="$columns"
                :ajax="route('superadmin.data-capture-fields.index')"
                :order="[[4, 'desc']]"
            />
        </div>
    </div>
@endsection
