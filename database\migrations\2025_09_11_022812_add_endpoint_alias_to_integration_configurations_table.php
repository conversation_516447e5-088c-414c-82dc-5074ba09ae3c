<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Check if column already exists
        if (!Schema::hasColumn('integration_configurations', 'endpoint_alias')) {
            Schema::table('integration_configurations', function (Blueprint $table) {
                $table->string('endpoint_alias', 100)->after('name')->nullable()->comment('Unique alias for the endpoint in format: [tenant-id]-[external-system]-[config-name]');
            });
        }

        // Generate endpoint aliases for existing records that don't have one
        $configurations = \App\Models\IntegrationConfiguration::whereNull('endpoint_alias')->orWhere('endpoint_alias', '')->get();
        foreach ($configurations as $config) {
            $tenantId = 'dc'; // Default tenant ID
            $cleanExternalSystem = strtolower(preg_replace('/[^a-z0-9]/', '', $config->external_system));
            $cleanConfigName = strtolower(preg_replace('/[^a-z0-9]/', '', $config->name));

            $alias = "{$tenantId}-{$cleanExternalSystem}-{$cleanConfigName}";

            // Ensure uniqueness by appending a number if needed
            $originalAlias = $alias;
            $counter = 1;
            while (\App\Models\IntegrationConfiguration::where('endpoint_alias', $alias)->where('id', '!=', $config->id)->exists()) {
                $alias = "{$originalAlias}-{$counter}";
                $counter++;
            }

            $config->update(['endpoint_alias' => $alias]);
        }

        // Add unique constraint if it doesn't exist
        $indexes = Schema::getConnection()->getDoctrineSchemaManager()->listTableIndexes('integration_configurations');
        $hasUniqueConstraint = false;
        foreach ($indexes as $index) {
            if ($index->isUnique() && in_array('endpoint_alias', $index->getColumns())) {
                $hasUniqueConstraint = true;
                break;
            }
        }

        if (!$hasUniqueConstraint) {
            Schema::table('integration_configurations', function (Blueprint $table) {
                $table->unique('endpoint_alias');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('integration_configurations', function (Blueprint $table) {
            $table->dropColumn('endpoint_alias');
        });
    }
};
